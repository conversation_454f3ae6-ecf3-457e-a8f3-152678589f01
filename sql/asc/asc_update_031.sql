create view denormalized_transactions as
with instrument_details as (select 167626                             as instrumentid,
                                   'Emerging Markets Stock Portfolio' as fund_name,
                                   'JQ'                               as fund_id
                            union
                            select 167625 as instrumentid, 'International Stock Portfolio' as fund_name, 'JP' as fund_id
                            union
                            select 167624 as instrumentid, 'Total Stock Market Portfolio' as fund_name, 'JO' as fund_id
                            union
                            select 167627 as instrumentid, 'Dividend Stock Portfolio' as fund_name, 'JR' as fund_id
                            union
                            select 167628 as instrumentid, 'REIT Portfolio' as fund_name, 'JS' as fund_id
                            union
                            select 167629                            as instrumentid,
                                   'Emerging Markets Bond Portfolio' as fund_name,
                                   'JT'                              as fund_id
                            union
                            select 167631                                                as instrumentid,
                                   'Short-Term Inflation-Protected Securities Portfolio' as fund_name,
                                   'JV'                                                  as fund_id
                            union
                            select 167630 as instrumentid, 'Corporate Bond Portfolio' as fund_name, 'JU' as fund_id
                            union
                            select 167632                          as instrumentid,
                                   'Short Treasury Bond Portfolio' as fund_name,
                                   'JW'                            as fund_id)
select t.account_number,
       t.transaction_group_id,
       tg.created_at                                                   as group_created_at,
       tg.state                                                        as group_state,
       t.id                                                            as transaction_id,
       t.created_at                                                    as transaction_created_at,
       t.state                                                         as transaction_state,
       json_value(t.transaction, '$.type')                             as transaction_type,
       str_to_date(json_value(t.transaction, '$.tradeDate'), '%Y%m%d') as trade_date,
       json_value(t.transaction, '$.fundId')                           as fund_id,
       instrumentid,
       fund_name,
       json_value(t.transaction, '$.amountType')                       as amount_type,
       cast(json_value(t.transaction, '$.amount') as decimal(10, 2))   as amount,
       t.transaction,
       t.reject_reasons,
       json_value(t.transaction, '$.originalTransactionGroupId')       as original_transaction_group_id
from transaction_groups tg
         join transactions t on tg.id = t.transaction_group_id
         left join instrument_details on json_value(t.transaction, '$.fundId') = instrument_details.fund_id;


insert into schema_log (schema_version, date) values (31, now());
