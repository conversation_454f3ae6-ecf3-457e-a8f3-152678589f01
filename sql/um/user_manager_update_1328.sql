create table deposit_match_payouts (
  id bigint(20) not null auto_increment,
  user_id bigint(20) not null,
  created_at datetime not null,
  state varchar(255) not null,
  payout_date date not null,
  incentive_id bigint(20) not null,
  referral_id bigint(20) default null,
  payment_id bigint(20) default null,
  primary key (id),
  index deposit_match_payout_payout_date (payout_date),
  unique key deposit_match_payout_user_incentive_payout_date (user_id, incentive_id, payout_date),
  constraint deposit_match_payouts_user_id foreign key (user_id) references users (id),
  constraint deposit_match_payouts_incentive_id foreign key (incentive_id) references incentives (id),
  constraint deposit_match_payouts_referral_id foreign key (referral_id) references referrals (id),
  constraint deposit_match_payouts_payment_id foreign key (payment_id) references cash_payment_incentives (id)
) ENGINE=InnoDB default CHARSET=utf8mb4;

create table version_deposit_match_payout_state (
  id bigint(20) not null auto_increment,
  deposit_match_payout_id bigint(20) not null,
  version int(11) not null,
  created_at datetime not null,
  deleted tinyint(1) default null,
  value varchar(255) not null,
  primary key (id),
  constraint version_deposit_match_payout_state_payout_id
    foreign key (deposit_match_payout_id)
    references deposit_match_payouts (id)
) ENGINE=InnoDB default CHARSET=utf8mb4;

insert into schema_log (schema_version, date) values (1328, now());