CREATE TABLE exempt_ofac_customers
(
    id          BIGINT(20) NOT NULL AUTO_INCREMENT,
    customer_id BIGINT(20) NOT NULL,
    exemption_reason VARCHAR(255) NOT NULL,
    deleted_at  DATETIME NULL,
    PRIMARY KEY (id),
    CONSTRAINT exempt_ofac_customers_customer_id FOREIGN KEY (customer_id) REFERENCES customers (id),
    UNIQUE KEY exempt_ofac_customers_customer_id_unique (customer_id),
    INDEX exempt_ofac_customers_exemption_reason (exemption_reason),
    INDEX exempt_ofac_customers_deleted_at (deleted_at)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

INSERT INTO schema_log (schema_version, date)VALUES (1152, now());