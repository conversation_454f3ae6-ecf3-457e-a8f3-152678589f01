CREATE TABLE escheatment_files (
    id                            BIGINT        NOT NULL  AUTO_INCREMENT,
    effective_date                DATE          NOT NULL,
    created_at                    DATETIME      NOT NULL,
    state                         VARCHAR(255)  NOT NULL,
    type                          VARCHAR(255)  NOT NULL,
    encrypted_file_path           VARCHAR(255)  NOT NULL,
    PRIMARY KEY (id),
    UNIQUE KEY escheatment_file_path (encrypted_file_path)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

INSERT INTO schema_log (schema_version, date) VALUES (1357, now());