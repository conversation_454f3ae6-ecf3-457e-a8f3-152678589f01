CREATE TABLE customer_account_checking_features (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  created_at datetime NOT NULL,
  customer_account_id bigint(20) NOT NULL,
  feature VARCHAR(255) NOT NULL,
  vendor VARCHAR(255) NOT NULL,
  status VARCHAR(255) NOT NULL,
  PRIMARY KEY (id),
  INDEX customer_account_checking_feature_status (status),
  CONSTRAINT customer_account_checking_feature_customer_account_id FOREIGN KEY (customer_account_id) REFERENCES customer_accounts (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE version_customer_account_checking_feature_status (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    customer_account_checking_feature_id BIGINT(20) NOT NULL,
    version INT(11) NOT NULL DEFAULT 0,
    created_at DATETIME NOT NULL,
    deleted TINYINT(1) NOT NULL,
    value VARCHAR(255) NOT NULL,
    PRIMARY KEY (id),
    INDEX version_customer_account_checking_feature_status_created_at (created_at),
    CONSTRAINT version_feature_status_feature_id FOREIGN KEY (customer_account_checking_feature_id) REFERENCES customer_account_checking_features (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

INSERT INTO schema_log (schema_version, date) VALUES (1323, now());