CREATE TABLE account_request_voyager_record (
    id bigint(20) NOT NULL AUTO_INCREMENT,
    account_request_id bigint(20) NOT NULL,
    voyager_record_id bigint(20) NOT NULL,
    PRIMARY KEY (id),
    UNIQUE (account_request_id),
    UNIQUE (voyager_record_id),
    FOREIGN KEY arvr_account_request_id (account_request_id) REFERENCES account_requests (id),
    FOREIGN KEY arvr_voyager_record_id (voyager_record_id) REFERENCES voyager_records (id),
    INDEX arvr_account_request_id (account_request_id),
    INDEX arvr_voyager_record_id (voyager_record_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

insert into schema_log (schema_version, date) values (1351, now());