CREATE TABLE affiliate_links_to_time_based_incentives (
    affiliate_link_id BIGINT(20) NOT NULL,
    time_based_incentive_id BIGINT(20) NOT NULL,
    PRIMARY KEY (affiliate_link_id, time_based_incentive_id),
    CONSTRAINT affiliate_links_to_time_based_incentives_link_id FOREIGN KEY (affiliate_link_id) REFERENCES affiliate_links (id),
    CONSTRAINT affiliate_links_to_time_based_incentives_tbi_id FOREIGN KEY (time_based_incentive_id) REFERENCES time_based_incentives (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

INSERT INTO schema_log (schema_version, date) VALUES (1326, now());