CREATE TABLE time_based_incentives (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  created_at datetime NOT NULL,
  incentive_id bigint(20) NOT NULL,
  name VARCHAR(255) NOT NULL,
  trigger_type VARCHAR(255) NOT NULL,
  days_until_expiration INTEGER NOT NULL,
  PRIMARY KEY (id),
  UNIQUE KEY time_based_incentives_name (name),
  CONSTRAINT time_based_incentives_incentive_id FOREIGN KEY (incentive_id) REFERENCES incentives (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE referrals_to_time_based_incentives (
    referral_id BIGINT(20) NOT NULL,
    time_based_incentive_id BIGINT(20) NOT NULL,
    PRIMARY KEY (referral_id, time_based_incentive_id),
    CONSTRAINT referrals_to_time_based_incentives_referral_id FOREIGN KEY (referral_id) REFERENCES referrals (id),
    CONSTRAINT referrals_to_time_based_incentives_tbi_id FOREIGN KEY (time_based_incentive_id) REFERENCES time_based_incentives (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

INSERT INTO schema_log (schema_version, date) VALUES (1325, now());