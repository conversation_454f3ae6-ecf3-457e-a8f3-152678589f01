-- Points system
create table points_actions (
	id bigint not null, 
	name char(255) not null, 
	points integer not null, 
	period integer not null, 
	primary key (id),
	unique (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

create table points_awards (
    id bigint not null,
    date datetime not null,
    user_id bigint not null,
    points_action_id bigint not null,
    primary key (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

alter table points_awards
    add index FKDF8FBC924385338C (points_action_id),
    add constraint FKDF8FBC924385338C foreign key (points_action_id) references points_actions (id);

alter table points_awards 
	add index (user_id), 
	add constraint foreign key (user_id) references users (id);
	
alter table users add column points integer not null default 0;

insert into schema_log (schema_version, date) values (7, now());
