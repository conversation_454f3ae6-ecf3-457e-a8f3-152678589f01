CREATE TABLE user_control_categories (
    id            BIGINT(20) NOT NULL AUTO_INCREMENT,
    created_at    DATETIME NOT NULL,
    user_id       BIGINT(20) NOT NULL,
    status        VARCHAR(255) NOT NULL,
    control_category VARCHAR(255) NOT NULL,
    taos_admin       VARCHAR(255) NOT NULL,
    details       MEDIUMTEXT NOT NULL,
    PRIMARY KEY (id),
    INDEX user_control_categories_user_id_status_control_category (user_id, status, control_category),
    CONSTRAINT user_control_categories_user_id FOREIGN KEY (user_id) REFERENCES users (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE version_user_control_category_details (
    id bigint(20) not null auto_increment,
    user_control_category_id bigint(20) not null,
    version int(11) not null,
    created_at datetime NOT NULL,
    deleted tinyint(1) NOT NULL,
    status VARCHAR(255),
    taos_admin VARCHAR(255),
    PRIMARY KEY (id),
    KEY version_user_control_category_details_user_control_category_id (user_control_category_id),
    CONSTRAINT version_user_control_category_details_user_control_category_id
        FOREIGN KEY (user_control_category_id)
        REFERENCES user_control_categories (id)
) engine=InnoDB default charset=utf8mb4;

insert into schema_log (schema_version, date) values (1353, now());
