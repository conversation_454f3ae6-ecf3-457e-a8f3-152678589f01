/*
db=user
host=sv217
table=discounts
ddl="ADD (created_at DATETIME DEFAULT NULL, last_updated_at DATETIME DEFAULT NULL)"

action=dry-run
pt-online-schema-change --alter "$ddl" --charset utf8 --ask-pass \
  D=$db,t=$table,A=utf8,h=$host,u=kaching --$action

action=execute
pt-online-schema-change --alter "$ddl" --charset utf8 --ask-pass \
  D=$db,t=$table,A=utf8,h=$host,u=kaching --$action
*/

ALTER TABLE discounts
ADD (created_at DATETIME DEFAULT NULL,
     last_updated_at DATETIME DEFAULT NULL)
;

INSERT INTO schema_log (schema_version, DATE) VALUES (593, now());
