CREATE TABLE fpsl_accounts (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  customer_account_id bigint(20) NOT NULL,
  created_at datetime NOT NULL,
  opt_in varchar(255) NOT NULL,
  PRIMARY KEY (id),
  CONSTRAINT fpsl_account_id_customer_account_id FOREIGN KEY (customer_account_id) REFERENCES customer_accounts (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE version_fpsl_account_opt_ins (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  fpsl_account_id bigint(20) NOT NULL,
  version int(11) NOT NULL,
  created_at datetime NOT NULL,
  deleted tinyint(1) DEFAULT NULL,
  value varchar(255) NOT NULL,
  PRIMARY KEY (id),
  CONSTRAINT version_fpsl_account_opt_ins_fpsl_account_id FOREIGN KEY (fpsl_account_id) REFERENCES fpsl_accounts (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

insert into schema_log (schema_version, date) values (1330, now());