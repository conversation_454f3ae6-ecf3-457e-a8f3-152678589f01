ALTER TABLE green_dot_payment_instruments
  ADD COLUMN renewal_status varchar(255) DEFAULT NULL,
  ADD INDEX green_dot_payment_instruments_renewal_status(renewal_status);

CREATE TABLE version_green_dot_payment_instruments_renewal_status (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  green_dot_payment_instrument_id bigint(20) NOT NULL,
  version int(11) NOT NULL,
  created_at datetime NOT NULL,
  deleted tinyint(1) DEFAULT NULL,
  value varchar(255) NOT NULL,
  PRIMARY KEY (id),
  CONSTRAINT version_gdpi_renewal_status_green_dot_payment_instrument_id
  FOREIGN KEY (green_dot_payment_instrument_id) REFERENCES green_dot_payment_instruments (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

INSERT INTO schema_log (schema_version, DATE) VALUES (1331, now());