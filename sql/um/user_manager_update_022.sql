drop table if exists followings;

create table followings (
  follower_id bigint not null,
  followed_id bigint not null,
  primary key (follower_id, followed_id)
) engine = InnoDB;

alter table followings add constraint FK8737600243914276 foreign key (follower_id) references users(id); 
alter table followings add constraint FKfollowed foreign key (followed_id) references users(id); 

-- log
insert into schema_log (schema_version, date) values (22, now());
