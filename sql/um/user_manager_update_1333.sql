create table fpsl_applications (
    id               BIGINT(20)     NOT NULL auto_increment,
    created_at       DATETIME       NOT NULL,
    user_id          BIGINT(20)     NOT NULL,
    state            VARCHAR(255)   NOT NULL,
    primary key (id),
    CONSTRAINT fpsl_applications_user_id FOREIGN KEY (user_id) REFERENCES users (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

create table version_fpsl_application_state (
    id                    BIGINT(20)     NOT NULL auto_increment,
    fpsl_application_id   BIGINT(20)     NOT NULL,
    version               INT(11)        NOT NULL,
    created_at            DATETIME       NOT NULL,
    deleted               TINYINT(1)     NOT NULL,
    value                 VARCHAR(255)   NOT NULL,
    primary key (id),
    CONSTRAINT fkey_version_fpsl_application_state FOREIGN KEY (fpsl_application_id) REFERENCES fpsl_applications (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

INSERT INTO schema_log (schema_version, DATE) VALUES (1333, now());