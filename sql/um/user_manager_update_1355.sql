create table operation_submission_requests (
    id bigint(20) not null auto_increment,
    type varchar(255) not null,
    created_at datetime not null,
    finished_at datetime,
    state varchar(255) not null,
    operation_entity_id bigint(20) not null,
    primary key (id)
) ENGINE=InnoDB default charset=utf8mb4;

create table version_operation_submission_request_state (
    id bigint(20) not null auto_increment,
    operation_submission_request_id bigint(20) not null,
    version int(11) not null,
    created_at datetime not null,
    deleted tinyint(1) default null,
    value varchar(255) not null,
    primary key (id),
    constraint version_operation_submission_request_state_request_id
        foreign key (operation_submission_request_id)
        references operation_submission_requests (id)
) ENGINE=InnoDB default charset=utf8mb4;

insert into schema_log (schema_version, date) values (1355, now());