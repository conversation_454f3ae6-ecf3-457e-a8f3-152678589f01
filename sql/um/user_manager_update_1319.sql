CREATE TABLE referral_affiliate_tracking (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  created_at datetime NOT NULL,
  guest_id varchar(255) NOT NULL,
  affiliate_link_id bigint(20) DEFAULT NULL,
  referral_id bigint(20) DEFAULT NULL,
  PRIMARY KEY (id),
  UNIQUE KEY referral_affiliate_tracking_guest_id (guest_id),
  FOREIGN KEY referral_affiliate_tracking_affiliate_link_id (affiliate_link_id) REFERENCES affiliate_links (id),
  FOREIGN KEY referral_affiliate_tracking_referral_id (referral_id) REFERENCES referrals (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

INSERT INTO schema_log (schema_version, date) VALUES (1319, now());