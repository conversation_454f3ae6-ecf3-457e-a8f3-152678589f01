/*
 * Break out contra account details so that we can encrypt
 */
ALTER TABLE selling_plans
  ADD transfer_account_details TEXT DEFAULT NULL AFTER details;

CREATE TABLE version_selling_plans_transfer_account_details (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  selling_plan_id bigint(20) NOT NULL,
  version int(11) NOT NULL,
  created_at datetime NOT NULL,
  deleted tinyint(4) NOT NULL,
  value text DEFAULT NULL,
  PRIMARY KEY (id),
  CONSTRAINT version_selling_plans_transfer_account_details_selling_plan_id FOREIGN KEY (selling_plan_id) REFERENCES selling_plans (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO schema_log (schema_version, DATE) VALUES (650, NOW());