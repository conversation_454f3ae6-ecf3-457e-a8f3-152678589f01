/*
Have to remove portfolio_id uniqueness in users table (this column will be dropped soon)

mysql> select * from information_schema.table_constraints where TABLE_NAME="users";
+--------------------+-------------------+---------------------------+--------------+------------+-----------------+
| CONSTRAINT_CATALOG | CONSTRAINT_SCHEMA | CONSTRAINT_NAME           | TABLE_SCHEMA | TABLE_NAME | CONSTRAINT_TYPE |
+--------------------+-------------------+---------------------------+--------------+------------+-----------------+
| NULL               | user              | PRIMARY                   | user         | users      | PRIMARY KEY     | 
| NULL               | user              | portfolio_idx             | user         | users      | UNIQUE          | 
| NULL               | user              | genius_id                 | user         | users      | UNIQUE          | 
| NULL               | user              | customer_id               | user         | users      | UNIQUE          | 
| NULL               | user              | marketplace_email_address | user         | users      | UNIQUE          | 
| NULL               | user              | vanity_path_idx           | user         | users      | UNIQUE          | 
| NULL               | user              | user_marketplace_f1       | user         | users      | FOREIGN KEY     | 
+--------------------+-------------------+---------------------------+--------------+------------+-----------------+
7 <USER> <GROUP> set (0.47 sec)
*/

alter table users drop index portfolio_idx;

insert into schema_log values (178, now(), "removed unique constraint on portfolio_id");
