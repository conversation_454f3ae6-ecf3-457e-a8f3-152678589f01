create table sweepstakes_campaigns (
  id bigint(20) not null auto_increment,
  rules varchar(255) not null,
  start_date date not null,
  end_date date not null,
  prize_amount decimal(14,2) default null,
  created_at datetime not null,
  deleted_at datetime default null,
  hidden tinyint(1) not null default 0,
  test tinyint(1) not null default 0,
  primary key (id),
  index start_date_end_date_hidden (start_date, end_date, hidden)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

create table sweepstakes_entries (
  id bigint(20) not null auto_increment,
  user_id bigint(20) not null,
  campaign_id varchar(255) not null,
  created_at datetime not null,
  state varchar(255) not null,
  referral_id bigint(20) default null,
  customer_account_id bigint(20) default null,
  primary key (id),
  index campaign_id_user_id (campaign_id, user_id),
  constraint sweepstakes_entries_user_id foreign key (user_id) references users (id),
  constraint sweepstakes_entries_referral_id foreign key (referral_id) references referrals (id),
  constraint sweepstakes_entries_customer_account_id foreign key (customer_account_id) references customer_accounts (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

create table version_sweepstakes_entry_state (
  id bigint(20) not null auto_increment,
  sweepstakes_entry_id bigint(20) not null,
  version int not null,
  created_at datetime not null,
  deleted tinyint(1) not null default 0,
  value varchar(255) not null,
  primary key (id),
  key (created_at),
  constraint fkey_sweepstakes_entry_id foreign key (sweepstakes_entry_id) references sweepstakes_entries (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

create table sweepstakes_opt_outs (
  id bigint(20) not null auto_increment,
  user_id bigint(20) not null,
  created_at datetime not null,
  state varchar(255) not null,
  primary key (id),
  index user_id_state (user_id, state)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

create table version_sweepstakes_opt_out_state (
  id bigint(20) not null auto_increment,
  sweepstakes_opt_out_id bigint(20) not null,
  version int not null,
  created_at datetime not null,
  deleted tinyint(1) not null default 0,
  value varchar(255) not null,
  primary key (id),
  key (created_at),
  constraint fkey_sweepstakes_opt_out_id foreign key (sweepstakes_opt_out_id) references sweepstakes_opt_outs (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

insert into schema_log (schema_version, date) values (889, now());