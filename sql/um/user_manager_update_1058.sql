/*
<PERSON><PERSON><PERSON> [user]> select table_name AS Table, round(((data_length + index_length) /1024/1024), 2) Size in MB FROM information_schema.TABLES WHERE table_schema = "user" AND table_name="account_tax_filenames"
+-----------------------+------------+
| Table                 | Size in MB |
+-----------------------+------------+
| account_tax_filenames | 387.59     |
+-----------------------+------------+
1 <USER> <GROUP> set
Time: 0.029s

MariaDB [user]> select count(1) from account_tax_filenames
+----------+
| count(1) |
+----------+
| 2859925  |
+----------+
1 <USER> <GROUP> set
Time: 1.576s

MariaDB [user]> select table_name, column_name, referenced_table_name, referenced_column_name from information_schema.key_column_usage where referenced_table_name is not null and referenced_column_name is not null and table_sc
                hema = 'user' and table_name = 'account_tax_filenames'
+------------+-------------+-----------------------+------------------------+
| table_name | column_name | referenced_table_name | referenced_column_name |
+------------+-------------+-----------------------+------------------------+
0 <USER> <GROUP> set
Time: 0.021s

MariaDB [user]> desc account_tax_filenames
+-----------------------------+--------------+------+-----+---------------------+----------------+
| Field                       | Type         | Null | Key | Default             | Extra          |
+-----------------------------+--------------+------+-----+---------------------+----------------+
| id                          | bigint(20)   | NO   | PRI | <null>              | auto_increment |
| account_id                  | bigint(20)   | NO   | MUL | <null>              |                |
| year                        | smallint(6)  | NO   |     | <null>              |                |
| type                        | varchar(255) | NO   |     | <null>              |                |
| custodian                   | varchar(255) | NO   |     | INTERACTIVE_BROKERS |                |
| encryption                  | varchar(255) | NO   |     | RUBY                |                |
| filename                    | varchar(255) | NO   |     | <null>              |                |
| downloaded_at               | datetime     | YES  |     | <null>              |                |
| doc_date                    | date         | YES  |     | <null>              |                |
| active                      | tinyint(1)   | NO   |     | 1                   |                |
| is_email_queued_for_sending | tinyint(1)   | NO   |     | 1                   |                |
| activated_at                | date         | YES  |     | <null>              |                |
| email_queued_for_sending_at | date         | YES  |     | <null>              |                |
+-----------------------------+--------------+------+-----+---------------------+----------------+
13 <USER> <GROUP> set
Time: 0.026s

# Dry Run
pt-online-schema-change --alter "MODIFY activated_at DATETIME" --charset utf8 --ask-pass --recurse 0 --recursion-method none --set-vars lock_wait_timeout=2 --alter-foreign-keys-method drop_swap --no-check-foreign-keys D=user,t=account_tax_filenames,A=utf8,h=localhost,u=wfadmin --dry-run
pt-online-schema-change --alter "MODIFY email_queued_for_sending_at DATETIME" --charset utf8 --ask-pass --recurse 0 --recursion-method none --set-vars lock_wait_timeout=2 --alter-foreign-keys-method drop_swap --no-check-foreign-keys D=user,t=account_tax_filenames,A=utf8,h=localhost,u=wfadmin --dry-run

# Execution
pt-online-schema-change --alter "MODIFY activated_at DATETIME" --charset utf8 --ask-pass --recurse 0 --recursion-method none --set-vars lock_wait_timeout=2 --alter-foreign-keys-method drop_swap --no-check-foreign-keys D=user,t=account_tax_filenames,A=utf8,h=localhost,u=wfadmin --execute
pt-online-schema-change --alter "MODIFY email_queued_for_sending_at DATETIME" --charset utf8 --ask-pass --recurse 0 --recursion-method none --set-vars lock_wait_timeout=2 --alter-foreign-keys-method drop_swap --no-check-foreign-keys D=user,t=account_tax_filenames,A=utf8,h=localhost,u=wfadmin --execute

 */

ALTER TABLE account_tax_filenames MODIFY activated_at DATETIME;
ALTER TABLE account_tax_filenames MODIFY email_queued_for_sending_at DATETIME;

INSERT INTO schema_log (schema_version, date) VALUES (1058, now());