create table voyager_records (
    id               bigint(20) not null auto_increment,
    user_id          bigint(20) not null,
    created_at       datetime not null,
    current_step     varchar(255) not null,
    voyage_type      varchar(255) not null default '',
    state            varchar(255) not null,
    primary key (id),
    INDEX voyager_records_user_id (user_id),
    INDEX voyager_records_voyager_type (voyage_type),
    constraint voyager_records_user_id foreign key (user_id) references users (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

create table version_voyager_record_state(
    id                bigint(20) not null auto_increment,
    voyager_record_id bigint(20) not null,
    version           int(11) not null,
    created_at        datetime not null,
    deleted           tinyint(1) not null,
    value             varchar(255) not null,
    primary key (id),
    constraint foreign key (voyager_record_id) references voyager_records (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

create table version_voyager_record_current_step(
    id                bigint(20) not null auto_increment,
    voyager_record_id bigint(20) not null,
    version           int(11) not null,
    created_at        datetime not null,
    deleted           tinyint(1) not null,
    value             varchar(255) not null,
    primary key (id),
    constraint foreign key (voyager_record_id) references voyager_records (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

insert into schema_log (schema_version, date) values (1335, now());