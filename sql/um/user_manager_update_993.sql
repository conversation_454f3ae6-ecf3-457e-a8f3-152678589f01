/*

MariaDB [user]> select table_name, round(((data_length + index_length) / 1024 / 1024), 2) as size_in_mb from information_schema.TABLES where table_schema = 'user' and table_name = 'users';
+------------+------------+
| table_name | size_in_mb |
+------------+------------+
| users      | 1269.56    |
+------------+------------+
1 <USER> <GROUP> set
Time: 0.026s

MariaDB [user]> select count(*) from users;
+----------+
| count(*) |
+----------+
| 5220186  |
+----------+
1 <USER> <GROUP> set
Time: 0.851s

MariaDB [user]> select table_name, column_name, referenced_table_name, referenced_column_name from information_schema.key_column_usage where referenced_table_name is not null and referenced_column_name is not null and
                                table_schema = 'user' and table_name = 'users';
+------------+------------------+-----------------------+------------------------+
| table_name | column_name      | referenced_table_name | referenced_column_name |
+------------+------------------+-----------------------+------------------------+
| users      | primary_email_id | user_emails           | id                     |
+------------+------------------+-----------------------+------------------------+
1 <USER> <GROUP> set
Time: 0.033s

MariaDB [user]> desc users;
+------------------+--------------+------+-----+---------+----------------+
| Field            | Type         | Null | Key | Default | Extra          |
+------------------+--------------+------+-----+---------+----------------+
| id               | bigint(20)   | NO   | PRI | <null>  | auto_increment |
| version          | bigint(20)   | NO   |     | 0       |                |
| state            | varchar(255) | NO   | MUL | <null>  |                |
| first_name       | varchar(255) | YES  |     | <null>  |                |
| last_name        | varchar(255) | YES  |     | <null>  |                |
| creation_time    | datetime     | NO   |     | <null>  |                |
| last_login       | datetime     | YES  |     | <null>  |                |
| mfa_required     | tinyint(1)   | NO   |     | 0       |                |
| primary_email_id | bigint(20)   | YES  | UNI | <null>  |                |
| password_salt    | bigint(20)   | NO   |     | 0       |                |
| password_hash    | char(60)     | YES  |     | <null>  |                |
| password_kdf     | tinyint(4)   | NO   |     | 1       |                |
| auth_provider    | varchar(255) | YES  |     | CORE    |                |
+------------------+--------------+------+-----+---------+----------------+
13 <USER> <GROUP> set
Time: 0.019s

# stop check slave icinga checks
# stop slaves

# Dry Run
pt-online-schema-change --alter "ADD COLUMN data_environment VARCHAR(255) NOT NULL DEFAULT 'FAKE' AFTER auth_provider" --charset utf8 --ask-pass D=user,t=users,A=utf8,h=localhost,u=wfadmin --alter-foreign-keys-method drop_swap --dry-run

# Execution
pt-online-schema-change --alter "ADD COLUMN data_environment VARCHAR(255) NOT NULL DEFAULT 'FAKE' AFTER auth_provider" --charset utf8 --ask-pass D=user,t=users,A=utf8,h=localhost,u=wfadmin --alter-foreign-keys-method drop_swap --execution

# restart slaves
# restart check slaves icinga checks

*/

ALTER TABLE users ADD COLUMN data_environment VARCHAR(255) NOT NULL DEFAULT 'FAKE' AFTER auth_provider;

INSERT INTO schema_log (schema_version, DATE) VALUES (993, now());
