CREATE TABLE backup_code_groups(
     id              BIGINT(20) NOT NULL auto_increment,
     created_at      DATETIME NOT NULL,
     user_id         BIGINT(20) NOT NULL,
     delivery_method VARCHAR(255) NOT NULL,
     kdf             TINYINT(4) NOT NULL,
     PRIMARY KEY (id),
     <PERSON>E<PERSON> backup_code_groups_created_at (created_at),
     KEY backup_code_groups_user_id (user_id),
     KEY backup_code_groups_delivery_method (delivery_method),
     CONSTRAINT backup_code_groups_user_id FOREIGN KEY (user_id) REFERENCES users (id)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;

CREATE TABLE backup_codes(
     id                    BIGINT(20) NOT NULL auto_increment,
     backup_code_group_id  BIGINT(20) NOT NULL,
     created_at            DATETIME NOT NULL,
     expires_at            DATETIME DEFAULT NULL,
     action                VARCHAR(255) DEFAULT NULL,
     initiator             <PERSON><PERSON><PERSON><PERSON>(255) DEFAULT NULL,
     salt                  VARCHAR(255) NOT NULL,
     backup_code_hash      VARCHAR(255) NOT NULL,
     PRIMARY KEY (id),
     <PERSON>E<PERSON> backup_codes_backup_code_group_id (backup_code_group_id),
     KEY backup_codes_created_at (created_at),
     KEY backup_codes_expires_at (expires_at),
     CONSTRAINT backup_codes_backup_code_group_id FOREIGN KEY (backup_code_group_id) REFERENCES backup_code_groups (id)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;

CREATE TABLE version_backup_code_details(
     id             BIGINT(20) NOT NULL auto_increment,
     backup_code_id BIGINT(20) NOT NULL,
     version        INT(11) NOT NULL,
     created_at     DATETIME NOT NULL,
     deleted        TINYINT(1) NOT NULL,
     expires_at     DATETIME DEFAULT NULL,
     action         VARCHAR(255) DEFAULT NULL,
     initiator      VARCHAR(255) DEFAULT NULL,
     PRIMARY KEY (id),
     KEY version_backup_code_details_backup_code_id (backup_code_id),
     KEY version_backup_code_details_created_at (created_at),
     CONSTRAINT version_backup_code_details_backup_code_id FOREIGN KEY (backup_code_id) REFERENCES backup_codes (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

INSERT INTO schema_log (schema_version, DATE) VALUES (1342, now());