CREATE TABLE fpsl_application_recommendations (
    id                  BIGINT(20)    NOT NULL AUTO_INCREMENT,
    fpsl_application_id BIGINT(20)    NOT NULL,
    created_at          DATETIME      NOT NULL,
    decision            VARCHAR(255)  NOT NULL,
    PRIMARY KEY (id),
    UNIQUE KEY fpsl_application_recommendations_fpsl_application_id (fpsl_application_id),
    CONSTRAINT fpsl_application_recommendations_fpsl_application_id
    FOREIGN KEY (fpsl_application_id) REFERENCES fpsl_applications (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE version_fpsl_application_recommendation_decision (
    id                                  BIGINT(20)  NOT NULL AUTO_INCREMENT,
    fpsl_application_recommendation_id  BIGINT(20)  NOT NULL,
    version                             INT(11)     NOT NULL,
    created_at                          DATETIME    NOT NULL,
    deleted                             TINYINT(1)  NOT NULL,
    value                              VARCHAR(255) NOT NULL,
    primary key (id),
    CONSTRAINT fkey_fpsl_application_recommendation_id
    FOREIGN KEY (fpsl_application_recommendation_id) REFERENCES fpsl_application_recommendations (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

insert into schema_log (schema_version, date) values (1340, now());