/*
 * ONG-2761 affiliate links
 */

create table affiliate_links(
	id bigint(20) not null auto_increment,
	user_id bigint(20) not null,
	incentive varchar(255) not null,
	url varchar(255),
	state varchar(255) not null,
	PRIMARY KEY (id),
	foreign key (user_id) references users (id),
	unique index affiliate_links_url (url)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

alter table referrals
  add column type varchar(255) not null default 'EMAIL' after id,
  add column affiliate_link_id bigint(20) after user_id,
  add constraint referrals_affiliate_link_id foreign key (affiliate_link_id) references affiliate_links (id);

insert into schema_log values(422, now());
