/*

mysql> show create table customer_accounts;

CREATE TABLE customer_accounts (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  user_id bigint(20) NOT NULL,
  customer_id bigint(20) NOT NULL,
  application_id bigint(20) DEFAULT NULL,
  bi_accountid bigint(20) DEFAULT NULL,
  sub_account_type varchar(255) DEFAULT NULL,
  account_state varchar(255) DEFAULT NULL,
  account_minimum decimal(14,2) NOT NULL,
  account_name varchar(255) DEFAULT NULL,
  display_name varchar(255) DEFAULT NULL,
  created_at datetime NOT NULL,
  last_update_time datetime NOT NULL,
  custodian varchar(255) DEFAULT NULL,
  asset_allocation mediumtext,
  current_penson_account_details_id bigint(20) DEFAULT NULL,
  transferred_from_customer_account_id bigint(20) DEFAULT NULL,
  tlh_eligibility_granted datetime DEFAULT NULL,
  tlh_state varchar(255) DEFAULT NULL,
  PRIMARY KEY (id),
  UNIQUE KEY customer_accounts_customer_id (customer_id),
  UNIQUE KEY account_id (bi_accountid),
  UNIQUE KEY customer_accounts_application_id (application_id),
  KEY customer_accounts_user_id (user_id),
  KEY customer_accounts_custodian (custodian),
  KEY customer_accounts_transferred_from_customer_account_id (transferred_from_customer_account_id),
  CONSTRAINT customer_accounts_user_id FOREIGN KEY (user_id) REFERENCES users (id),
  CONSTRAINT customer_accounts_customer_id FOREIGN KEY (customer_id) REFERENCES customers (id),
  CONSTRAINT customer_accounts_ibfk_1 FOREIGN KEY (application_id) REFERENCES applications (id),
  CONSTRAINT customer_accounts_transferred_from_customer_account_id FOREIGN KEY (transferred_from_customer_account_id) REFERENCES customer_accounts (id)
) ENGINE=InnoDB AUTO_INCREMENT=66432 DEFAULT CHARSET=utf8

*/

alter table customer_accounts
  modify column custodian varchar(255) NOT NULL,
  modify column sub_account_type varchar(255) NOT NULL,
  modify column account_state varchar(255) NOT NULL;

insert into schema_log values(407, now());
