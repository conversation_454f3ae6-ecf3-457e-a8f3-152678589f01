CREATE TABLE green_dot_account_users (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  green_dot_account_id bigint(20) NOT NULL,
  green_dot_user_id bigint(20) NOT NULL,
  state varchar(255) NOT NULL,
  PRIMARY KEY (id),
  UNIQUE KEY green_dot_account_users_account_id_user_id (green_dot_account_id, green_dot_user_id),
  FOREIGN KEY green_dot_account_users_account_id (green_dot_account_id) REFERENCES green_dot_accounts (id),
  FOREIGN KEY green_dot_account_users_user_id (green_dot_user_id) REFERENCES green_dot_users (id),
  INDEX green_dot_account_users_green_dot_user_id (green_dot_user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE version_green_dot_account_users_state (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  green_dot_account_users_id bigint(20) NOT NULL,
  version int(11) NOT NULL,
  created_at datetime NOT NULL,
  deleted tinyint(1) DEFAULT NULL,
  value varchar(255),
  PRIMARY KEY (id),
  CONSTRAINT version_green_dot_account_users_state_green_dot_account_users_id FOREIGN KEY (green_dot_account_users_id) REFERENCES green_dot_account_users (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

INSERT INTO schema_log (schema_version, date) VALUES (1312, now());