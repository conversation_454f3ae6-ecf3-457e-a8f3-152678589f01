/**
MariaDB [user]> SHOW CREATE TABLE queued_idv_check_requests;
+---------------------------+------------------------------------------------------------------------------------------------------------------------+
| Table                     | Create Table                                                                                                           |
+---------------------------+------------------------------------------------------------------------------------------------------------------------+
| queued_idv_check_requests | CREATE TABLE `queued_idv_check_requests` (                                                                             |
|                           |   `id` bigint(20) NOT NULL AUTO_INCREMENT,                                                                             |
|                           |   `idv_check_id` bigint(20) NOT NULL,                                                                                  |
|                           |   `created_at` datetime NOT NULL,                                                                                      |
|                           |   `polled_time` datetime DEFAULT NULL,                                                                                 |
|                           |   `sent_time` datetime DEFAULT NULL,                                                                                   |
|                           |   `ignored_at` datetime DEFAULT NULL,                                                                                  |
|                           |   `error_flag` tinyint(1) DEFAULT 0,                                                                                   |
|                           |   `request_payload` longtext DEFAULT NULL,                                                                             |
|                           |   `response_payload` mediumtext DEFAULT NULL,                                                                          |
|                           |   `vendor` varchar(255) NOT NULL,                                                                                      |
|                           |   PRIMARY KEY (`id`),                                                                                                  |
|                           |   KEY `fkey_queued_idv_check_requests_idv_check_id` (`idv_check_id`),                                                  |
|                           |   CONSTRAINT `fkey_queued_idv_check_requests_idv_check_id` FOREIGN KEY (`idv_check_id`) REFERENCES `idv_checks` (`id`) |
|                           | ) ENGINE=InnoDB AUTO_INCREMENT=98 DEFAULT CHARSET=utf8                                                                 |
+---------------------------+------------------------------------------------------------------------------------------------------------------------+
1 <USER> <GROUP> set
Time: 0.025s

MariaDB [user]> SHOW CREATE TABLE idv_checks;
+------------+---------------------------------------------------------------------------------------------------------------------------------------------------+
| Table      | Create Table                                                                                                                                      |
+------------+---------------------------------------------------------------------------------------------------------------------------------------------------+
| idv_checks | CREATE TABLE `idv_checks` (                                                                                                                       |
|            |   `id` bigint(20) NOT NULL AUTO_INCREMENT,                                                                                                        |
|            |   `created_at` datetime NOT NULL,                                                                                                                 |
|            |   `user_id` bigint(20) NOT NULL,                                                                                                                  |
|            |   `user_session_id` bigint(20) DEFAULT NULL,                                                                                                      |
|            |   `type` varchar(255) NOT NULL,                                                                                                                   |
|            |   `state` varchar(255) NOT NULL,                                                                                                                  |
|            |   `final_decision` varchar(255) DEFAULT NULL,                                                                                                     |
|            |   `front_id_attachment_id` bigint(20) DEFAULT NULL,                                                                                               |
|            |   `back_id_attachment_id` bigint(20) DEFAULT NULL,                                                                                                |
|            |   `maybe_selfie_attachment_id` bigint(20) DEFAULT NULL,                                                                                           |
|            |   `maybe_selfie_attachment_video_id` bigint(20) DEFAULT NULL,                                                                                     |
|            |   `submission_metadata` mediumtext DEFAULT NULL,                                                                                                  |
|            |   PRIMARY KEY (`id`),                                                                                                                             |
|            |   KEY `fkey_idv_checks_user_id` (`user_id`),                                                                                                      |
|            |   KEY `fkey_idv_checks_user_session_id` (`user_session_id`),                                                                                      |
|            |   KEY `fkey_idv_checks_front_id_attachment_id` (`front_id_attachment_id`),                                                                        |
|            |   KEY `fkey_idv_checks_maybe_selfie_attachment_id` (`maybe_selfie_attachment_id`),                                                                |
|            |   KEY `fkey_idv_checks_maybe_selfie_attachment_video_id` (`maybe_selfie_attachment_video_id`),                                                    |
|            |   KEY `fkey_idv_checks_back_id_attachment_id` (`back_id_attachment_id`),
|            |   CONSTRAINT `fkey_idv_checks_back_id_attachment_id` FOREIGN KEY (`back_id_attachment_id`) REFERENCES `attachments` (`id`),                       |
|            |   CONSTRAINT `fkey_idv_checks_front_id_attachment_id` FOREIGN KEY (`front_id_attachment_id`) REFERENCES `attachments` (`id`),                     |
|            |   CONSTRAINT `fkey_idv_checks_maybe_selfie_attachment_id` FOREIGN KEY (`maybe_selfie_attachment_id`) REFERENCES `attachments` (`id`),             |
|            |   CONSTRAINT `fkey_idv_checks_maybe_selfie_attachment_video_id` FOREIGN KEY (`maybe_selfie_attachment_video_id`) REFERENCES `attachments` (`id`), |
|            |   CONSTRAINT `fkey_idv_checks_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`),                                                         |
|            |   CONSTRAINT `fkey_idv_checks_user_session_id` FOREIGN KEY (`user_session_id`) REFERENCES `sessions` (`id`)                                       |
|            | ) ENGINE=InnoDB AUTO_INCREMENT=98 DEFAULT CHARSET=utf8                                                                                            |
+------------+---------------------------------------------------------------------------------------------------------------------------------------------------+
1 <USER> <GROUP> set
Time: 0.022s
 */

CREATE INDEX queued_idv_check_requests_created_at ON queued_idv_check_requests (created_at);
CREATE INDEX idv_checks_created_at ON idv_checks (created_at);

INSERT INTO schema_log (schema_version, date) VALUES (1000, now());