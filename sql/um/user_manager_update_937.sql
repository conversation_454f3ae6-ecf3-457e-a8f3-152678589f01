/*

MariaDB [user]> select table_name, round(((data_length + index_length) / 1024 / 1024), 2) as size_in_mb from information_schema.TABLES where table_schema = 'user' and table_name = 'pushes';
+------------+------------+
| table_name | size_in_mb |
+------------+------------+
| pushes     | 23803.97   |
+------------+------------+
1 <USER> <GROUP> set

MariaDB [user]> select count(*) from pushes;
+----------+
| count(*) |
+----------+
| 29136437 |
+----------+
1 <USER> <GROUP> set
Time: 25.025s

MariaDB [user]> select table_name, column_name, referenced_table_name, referenced_column_name from information_schema.key_column_usage where referenced_table_name is not null and referenced_column_name is not null and table_schema = 'user' and table_name = 'pushes';
+------------+-------------+-----------------------+------------------------+
| table_name | column_name | referenced_table_name | referenced_column_name |
+------------+-------------+-----------------------+------------------------+
| pushes     | user_id     | users                 | id                     |
| pushes     | recipient   | push_registrations    | token                  |
+------------+-------------+-----------------------+------------------------+
2 <USER> <GROUP> set

MariaDB [user]> describe pushes;
+-----------------+--------------+------+-----+---------+----------------+
| Field           | Type         | Null | Key | Default | Extra          |
+-----------------+--------------+------+-----+---------+----------------+
| id              | bigint(20)   | NO   | PRI | <null>  | auto_increment |
| created_at      | datetime     | YES  |     | <null>  |                |
| recipient       | varchar(255) | YES  | MUL | <null>  |                |
| content         | varchar(255) | YES  |     | <null>  |                |
| message_type    | varchar(255) | YES  |     | <null>  |                |
| type            | varchar(255) | NO   |     | <null>  |                |
| scheduled_time  | datetime     | YES  |     | <null>  |                |
| formcode        | varchar(255) | YES  |     | <null>  |                |
| revision        | varchar(255) | YES  |     | <null>  |                |
| higher_priority | tinyint(1)   | NO   |     | 0       |                |
| title           | varchar(255) | YES  |     | <null>  |                |
| error_flag      | tinyint(1)   | NO   | MUL | 0       |                |
| user_id         | bigint(20)   | YES  | MUL | <null>  |                |
| additional_data | text         | YES  |     | <null>  |                |
+-----------------+--------------+------+-----+---------+----------------+
14 <USER> <GROUP> set

# stop check slave icinga checks
# stop slaves

# Dry Run
pt-online-schema-change --alter "add column reason varchar(255) default null after scheduled_time" --charset utf8 --ask-pass D=user,t=pushes,A=utf8,h=localhost,u=wfadmin --alter-foreign-keys-method drop_swap --dry-run

# Execution
pt-online-schema-change --alter "add column reason varchar(255) default null after scheduled_time" --charset utf8 --ask-pass D=user,t=pushes,A=utf8,h=localhost,u=wfadmin --alter-foreign-keys-method drop_swap --execution

# restart slaves
# restart check slaves icinga checks

*/

# pt-online-schema-change
alter table pushes
  add column reason varchar(255) default null after scheduled_time;

insert into schema_log (schema_version, date) values (937, now());
