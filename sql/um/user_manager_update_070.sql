create table todo_items (
  id bigint not null,
  query text not null,
  creation_time datetime not null,
  next_eligible_time datetime,
  last_attempt_start_time datetime,
  last_attempt_duration_ms int,
  result tinyint(1),
  failures smallint not null default 0,
  primary key (id),
  index todo_items_creation_time (creation_time),
  index todo_items_next_eligible_time (next_eligible_time)
) engine=InnoDB;

create table todo_failures (
  id bigint not null,
  todo_item_id bigint not null,
  failure_time datetime not null,
  primary key (todo_item_id, failure_time),
  foreign key (todo_item_id) references todo_items (id)
) engine=InnoDB;

insert into schema_log (schema_version, date) values (70, now());
