/*
mysql> select max(created_at) from account_statements where validated = 1 and not deleted;
+---------------------+
| max(created_at)     |
+---------------------+
| 2015-04-10 20:57:01 |
+---------------------+
1 <USER> <GROUP> set (0.39 sec)

mysql> select count(*) from account_statements where validated = true and not deleted and created_at <= '2015-04-10 20:57:01';
+----------+
| count(*) |
+----------+
|   661906 |
+----------+
1 <USER> <GROUP> set (1.01 sec)

mysql> select count(*) from account_statements where validated = true and not deleted;
+----------+
| count(*) |
+----------+
|   661906 |
+----------+
1 <USER> <GROUP> set (0.31 sec)

  MARK ALL VALIDATED STATEMENTS AS VALIDATED */


update account_statements set state = 'VALIDATED'
where validated = true and not deleted and created_at <= '2015-04-10 20:57:01'

/*
mysql> select max(created_at) from account_statements where deleted;
+---------------------+
| max(created_at)     |
+---------------------+
| 2015-04-10 20:52:31 |
+---------------------+
1 <USER> <GROUP> set (0.27 sec)

mysql> select count(*) from account_statements where deleted;
+----------+
| count(*) |
+----------+
|    80362 |
+----------+
1 <USER> <GROUP> set (0.00 sec)

mysql> select count(*) from account_statements where deleted and created_at <= '2015-04-10 20:52:31';
+----------+
| count(*) |
+----------+
|    80362 |
+----------+
1 <USER> <GROUP> set (0.93 sec)

  MARK ALL DELETED STATEMENTS AS DELETED */

update account_statements set state = 'DELETED'
where deleted and created_at <= '2015-04-10 20:52:31';

/* BACK POPULATE VERSION TABLE */
insert into version_account_statement_state (statement_id, version, created_at, deleted, value)
  select
    acst.id,
    0,
    now(),
    false,
    acst.state
  from account_statements acst left outer join
    version_account_statement_state vacst on acst.id = vacst.statement_id
  where vacst.statement_id is null;