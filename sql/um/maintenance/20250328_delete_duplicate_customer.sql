/*
MariaDB [user]> select id, userid, type, creation_time, state from customers where customer_account_id = ********;
+----------+------------------+-------------------+---------------------+------------+
| id       | userid           | type              | creation_time       | state      |
+----------+------------------+-------------------+---------------------+------------+
| ******** | **************** | primary           | 2025-03-20 13:45:33 | REGISTERED |
| ******** | <null>           | authorized-signer | 2025-03-27 20:17:29 | REJECTED   |
| ******** | <null>           | authorized-signer | 2025-03-27 20:18:25 | REGISTERED |
+----------+------------------+-------------------+---------------------+------------+
3 <USER> <GROUP> set
*/
DELETE FROM customers WHERE id = ********;