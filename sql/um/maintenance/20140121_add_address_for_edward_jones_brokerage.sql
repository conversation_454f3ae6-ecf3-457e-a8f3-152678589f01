# mysql> select * from brokerages where id in (5948, 8056);
# +------+-----------------+-----------------------------+-----------------------------+---------+----------+-------+------+------+---------------+
# | id   | clearing_number | firm_name                   | clearing_firm_name          | address | zip_code | state | city | acat | self_clearing |
# +------+-----------------+-----------------------------+-----------------------------+---------+----------+-------+------+------+---------------+
# | 5948 | 5012            | <PERSON> / <PERSON>          | <PERSON> / <PERSON>          | NULL    | NULL     | NULL  | N<PERSON>LL |    0 |             1 |
# | 8056 | 1871            | Edward D. <PERSON> & Co., L.P. | Edward D. Jones & Co., L.P. | NULL    | NULL     | NULL  | NULL |    0 |             1 |
# +------+-----------------+-----------------------------+-----------------------------+---------+----------+-------+------+------+---------------+
# 2 rows in set (0.00 sec)


update brokerages set address = "12555 Manchester Road", zip_code = "63131", state = "MO", city = "Saint Louis" where id in (5948, 8056) limit 2;