CREATE TABLE blog_items (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  title varchar(255) NOT NULL,
  url text,
  body text,
  publish_date datetime DEFAULT NULL,
  state varchar(255) NOT NULL,
  admin varchar(255) NOT NULL,
  last_updated datetime NOT NULL,
  image_url text,
  PRIMARY KEY (`id`),
  KEY blog_items_state (state),
  KEY blog_items_last_updated (last_updated)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE blog_item_experiences (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  blog_item_id bigint(20) NOT NULL,
  experience varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY blog_item_id (blog_item_id),
  CONSTRAINT FOREIGN KEY (blog_item_id) REFERENCES blog_items (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE blog_item_tags (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  blog_item_id bigint(20) NOT NULL,
  tag varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY blog_item_id (blog_item_id),
  CONSTRAINT FOREIGN KEY (blog_item_id) REFERENCES blog_items (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO schema_log (schema_version, date) VALUES (537, now());
