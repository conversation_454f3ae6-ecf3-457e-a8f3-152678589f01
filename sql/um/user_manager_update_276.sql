-- CREATE TABLE `signature_investors` (
--   `id` bigint(20) NOT NULL auto_increment,
--   `userid` bigint(20) NOT NULL,
--   `creation_time` datetime NOT NULL,
--   `state` varchar(255) NOT NULL,
--   `kind` varchar(255) NOT NULL,
--   `investor_type` varchar(255) NOT NULL,
--   `minimum_investment_elsewhere` decimal(14,2) default NULL,
--   `self_invested` decimal(16,10) default NULL,
--   `warn_email_date` date default NULL,
--   `warn_status` varchar(255) default NULL,
--   `alerts_status` varchar(255) default NULL,
--   `fields` mediumtext,
--   `logo_path` varchar(255) default NULL,
--   PRIMARY KEY  (`id`),
--   UNIQUE KEY `userid` (`userid`),
--   KEY `signature_investors_state` (`state`)
-- ) ENGINE=InnoDB DEFAULT CHARSET=utf8

alter table signature_investors
  drop key signature_investors_state,
  drop column state,
  drop column warn_email_date,
  drop column warn_status,
  drop column alerts_status;

insert into schema_log values (276, now());
