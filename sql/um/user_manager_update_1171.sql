alter table wftw_validations
  add column company_id bigint(20) default null,
  add column apy_boost_award_id bigint(20) default null,
  add column fee_waiver_award_id bigint(20) default null,
  add constraint coworker_incentive_company_id foreign key (company_id) references coworker_incentive_companies (id),
  add constraint coworker_incentive_apy_boost_award_id foreign key (apy_boost_award_id) references cash_interest_rewards (id),
  add constraint coworker_incentive_fee_waiver_award_id foreign key (fee_waiver_award_id) references discounts (id);

insert into schema_log (schema_version, date) values (1171, now());