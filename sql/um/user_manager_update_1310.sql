CREATE TABLE version_merchandise_instances_last_dismissed_at (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  merchandise_instance_id BIGINT(20) NOT NULL,
  version INT(11) NOT NULL,
  created_at DATETIME NOT NULL,
  deleted TINYINT(1) NOT NULL,
  value DATETIME DEFAULT NULL,

  PRIMARY KEY (id),
  KEY (merchandise_instance_id),
  CONSTRAINT vmilda_merchandise_instance_id FOREIGN KEY (merchandise_instance_id) REFERENCES merchandise_instances (id)
) ENGINE=InnoDB default CHARSET=utf8mb4;

INSERT INTO schema_log (schema_version, date) VALUES (1310, now());