CREATE TABLE detailed_nav_breakdowns (
  id                         BIGINT        NOT NULL AUTO_INCREMENT,
  current_nav                DECIMAL(20,4) NOT NULL,
  previous_day_nav           DECIMAL(20,4) NOT NULL,
  daily_change               DECIMAL(20,4) NOT NULL,
  net_assets                 DECIMAL(20,4) NOT NULL,
  shares_outstanding         DECIMAL(22,4) NOT NULL,
  distribution_rate          DECIMAL(20,2) NOT NULL,
  effective_date             DATE          NOT NULL,
  created_at                 DATETIME      NOT NULL,
  internally_managed_fund_id BIGINT        NOT NULL,
  PRIMARY KEY (id),
  CONSTRAINT detailed_nav_breakdowns_internally_managed_fund_id FOREIGN KEY (internally_managed_fund_id) REFERENCES internally_managed_funds (id),
  INDEX detailed_nav_breakdowns_effective_date (effective_date)
 )
  ENGINE = InnoDB
  DEFAULT CHARSET = utf8;

INSERT INTO schema_log (schema_version, date) VALUES (46, now());