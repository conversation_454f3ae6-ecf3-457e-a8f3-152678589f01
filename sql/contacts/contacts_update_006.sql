DROP TABLE IF EXISTS online_contact_recommendations;

CREATE TABLE online_contact_recommendations (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  userid BIGINT(20) NOT NULL,
  contact_recommendation BLOB NOT NULL,
  user_contact_permission_id BIGINT(20) NOT NULL,
  created_at DATETIME NOT NULL,
  state VARCHAR(255) NOT NULL,
  score DECIMAL(16, 8) NOT NULL,
  PRIMARY KEY (id),
  INDEX userid (userid),
  INDEX userid_user_contact_permission_id (userid, user_contact_permission_id),
  INDEX userid_score (userid, score)
) engine=InnoDB default charset=utf8;

INSERT INTO schema_log (schema_version, date) VALUES (6, NOW());
