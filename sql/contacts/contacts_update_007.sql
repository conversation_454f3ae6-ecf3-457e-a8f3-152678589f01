ALTER TABLE user_contacts_permissions
   DROP INDEX userid_contact_source_identifier,
   ADD UNIQUE KEY `user_id_contact_source_contact_source_owner_identifier`
      (`userid`,`contact_source`,`contact_source_owner_identifier`),
   ADD latest_upload_version INTEGER DEFAULT NULL;

ALTER TABLE contacts
   ADD user_contact_permission_id BIGINT(20) DEFAULT NULL,
   ADD upload_version INTEGER DEFAULT NULL,
   ADD INDEX `user_contact_permission_id` (`user_contact_permission_id`);

INSERT INTO schema_log (schema_version, date) VALUES (7, NOW());
