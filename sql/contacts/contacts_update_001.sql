create database contacts character set 'utf8' collate utf8_general_ci;

use contacts;

create table schema_log (
  schema_version int not null default 0,
  date datetime not null,
  unique(schema_version)
) engine=InnoDB;

create table contacts (
  id bigint not null AUTO_INCREMENT,
  userid bigint not null,
  created_at datetime not null,
  contact_source varchar(64) not null,
  details blob not null,
  index contacts_created_at (created_at),
  primary key (id)
) engine=InnoDB;

insert into schema_log (schema_version, date) values (1, now());
