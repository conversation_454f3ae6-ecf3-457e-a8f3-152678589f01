ALTER TABLE disabled_recommendations DROP FOREIGN KEY disabled_recommendations_viewed_recommendation_id;
DROP TABLE IF EXISTS viewed_recommendations;
DROP TABLE IF EXISTS disabled_recommendations;

CREATE TABLE disabled_contact_recommendations (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  userid BIGINT(20) NOT NULL,
  type VARCHAR(255) NOT NULL,
  details BLOB NOT NULL,
  disabled_at DATETIME NOT NULL,
  PRIMARY KEY (id),
  INDEX userid (userid)
) engine=InnoDB default charset=utf8;

INSERT INTO schema_log (schema_version, date) VALUES (5, NOW());
