drop table if exists online_contact_recommendations, version_user_contacts_permissions, user_contacts_permissions;

create table online_contact_recommendations (
  userid bigint not null,
  contact_recommendations blob not null,
  algorithm varchar(16) not null,
  created_at datetime not null,
  updated_at datetime not null,
  last_source_added_at datetime not null,
  primary key (userid)
) engine=InnoDB default charset=utf8;

create table user_contacts_permissions (
  id bigint not null AUTO_INCREMENT,
  userid bigint not null,
  contact_source varchar(64) not null,
  contact_source_owner_identifier varchar(128) not null,
  permission varchar(255) not null,
  is_deleting tinyint(1) not null,
  primary key (id),
  unique index userid_contact_source_identifier (userid, contact_source_owner_identifier)
) engine=InnoDB default charset=utf8;

create table version_user_contacts_permissions (
  id bigint not null AUTO_INCREMENT,
  users_contacts_permissions_id bigint(20) not null,
  version int(11) not null,
  created_at datetime not null,
  deleted tinyint(1) not null,
  value varchar(255) not null,
  primary key(id),
  key users_contacts_permissions_id (users_contacts_permissions_id),
  key created_at (created_at),
  constraint users_contacts_permissions_id foreign key (users_contacts_permissions_id) references user_contacts_permissions (id)
) engine=InnoDB default charset=utf8;

insert into schema_log (schema_version, date) values (2, now());
