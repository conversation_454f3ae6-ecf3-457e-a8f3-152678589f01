CREATE TABLE viewed_recommendations (
  id BIGINT NOT NULL AUTO_INCREMENT,
  userid BIGINT NOT NULL,
  recommendation_details BLOB NOT NULL,
  algorithm VARCHAR(64) NOT NULL,
  viewed_at DATETIME NOT NULL,
  INDEX viewed_at (viewed_at),
  PRIMARY KEY (id)
) engine=InnoDB;

CREATE TABLE disabled_recommendations (
  id BIGINT NOT NULL AUTO_INCREMENT,
  userid BIGINT NOT NULL,
  disabled_at DATETIME NOT NULL,
  viewed_recommendation_id BIGINT NOT NULL,
  PRIMARY KEY (id),
  INDEX userid (userid),
  INDEX disabled_at (disabled_at),
  CONSTRAINT disabled_recommendations_viewed_recommendation_id FOREIGN KEY (viewed_recommendation_id)
  	  REFERENCES viewed_recommendations (id)
) engine=InnoDB;

INSERT INTO schema_log (schema_version, date) VALUES (4, NOW());
