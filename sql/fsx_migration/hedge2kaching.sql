DROP DATABASE if exists kaching;

CREATE DATABASE kaching CHARACTER SET 'utf8' COLLATE utf8_general_ci;

drop table if exists kaching.balances;
drop table if exists kaching.daily_gains;
drop table if exists kaching.lots;
drop table if exists kaching.orders;
drop table if exists kaching.portfolios;
drop table if exists kaching.positions;
drop table if exists kaching.stocks;
drop table if exists kaching.trades;
drop table if exists kaching.watchlist;

create table kaching.balances (id bigint not null, total_value numeric(14,2) not null,
  total_cost numeric(14,2) not null, cash_deposit numeric(14,2) not null,
  cash_withdrawal numeric(14,2), date datetime not null, portfolio_id bigint,
  primary key (id));

create table kaching.daily_gains (id bigint not null, amount numeric(14,2) not null,
  percent numeric(16,10) not null, date datetime not null, portfolio_id bigint,
  primary key (id));

create table kaching.lots (id bigint not null, position_id bigint not null,
  quantity numeric(12,0) not null, cost numeric(14,2) not null,
  commission numeric(14,2) not null, buy_date datetime not null,
  last_split datetime, last_dividend datetime, style varchar(255),
  market_cap varchar(255), industry varchar(255), country varchar(255),
  primary key (id));

create table kaching.orders (id bigint not null, type varchar(255) not null,
  portfolio_id bigint not null, date datetime not null, exp_date datetime,
  stock_id bigint, quantity numeric(12,0), expires datetime,
  limit_price numeric(14,2), primary key (id));

create table kaching.portfolios (id bigint not null, creation_date datetime not null,
  long_cash numeric(14,2) not null, short_cash numeric(14,2) not null,
  primary key (id));

create table kaching.positions (id bigint not null, type varchar(255) not null,
  stock_id bigint not null, agg_quantity numeric(12,0) not null,
  agg_cost numeric(14,2) not null, agg_commission numeric(14,0) not null,
  portfolio_id bigint not null, primary key (id),
  unique (type, stock_id, portfolio_id));

create table kaching.stocks (id bigint not null, symbol varchar(10) not null,
  market varchar(20), primary key (id), unique (symbol, market));

create table kaching.trades (id bigint not null, type varchar(255) not null,
  date datetime not null, price numeric(14,2), stock_id bigint,
  commission numeric(14,2), quantity numeric(14,2), percentage numeric(14,2),
  style varchar(255), market_cap varchar(255), industry varchar(255),
  country varchar(255), portfolio_id bigint, primary key (id));

create table kaching.watchlist (id bigint not null, stock_id bigint not null,
  added datetime not null, portfolio_id bigint not null, primary key (id));

INSERT INTO kaching.stocks (id, symbol)
   SELECT stock_id, ticker FROM hedge.stock_prices;

ALTER TABLE kaching.watchlist CHANGE id id BIGINT( 20 ) NOT NULL AUTO_INCREMENT;
INSERT INTO kaching.watchlist (stock_id, added, portfolio_id)
   SELECT fw.stock_id, fw.date, fw.face_id FROM hedge.facebook_watchlist fw;
ALTER TABLE kaching.watchlist CHANGE id id BIGINT( 20 ) NOT NULL;
   
INSERT INTO kaching.trades (id, type, date, price, stock_id, commission, quantity, portfolio_id)
   (SELECT trans_id, lower(trade_type), date_processed, sale_price, stock_id, commission, shares, face_id FROM hedge.facebook_transactions ft WHERE ft.active='C');

ALTER TABLE kaching.positions CHANGE id id BIGINT( 20 ) NOT NULL AUTO_INCREMENT;
INSERT INTO kaching.positions (type, stock_id, agg_quantity, agg_cost, portfolio_id)
   SELECT trade_type, stock_id, SUM(shares), SUM(shares * sale_price), face_id FROM hedge.facebook_portfolio fp
   WHERE trade_type = 'Buy' AND sale_price <> 0
   GROUP BY face_id, stock_id, trade_type;
INSERT INTO kaching.positions (type, stock_id, agg_quantity, agg_cost, portfolio_id)
   SELECT trade_type, stock_id, -SUM(shares), -SUM(shares * sale_price), face_id FROM hedge.facebook_portfolio fp
   WHERE trade_type = 'Short' AND sale_price <> 0
   GROUP BY face_id, stock_id, trade_type;
ALTER TABLE kaching.positions CHANGE id id BIGINT( 20 ) NOT NULL;

ALTER TABLE kaching.lots CHANGE id id BIGINT( 20 ) NOT NULL AUTO_INCREMENT;
INSERT INTO kaching.lots (position_id, quantity, cost, buy_date)
   SELECT kp.id, fp.shares, fp.sale_price * fp.shares, fp.date FROM kaching.positions kp, hedge.facebook_portfolio fp
   WHERE kp.portfolio_id=fp.face_id AND kp.stock_id=fp.stock_id AND kp.type=fp.trade_type and kp.type='Buy';
INSERT INTO kaching.lots (position_id, quantity, cost, buy_date)
   SELECT kp.id, -(fp.shares), -(fp.sale_price * fp.shares), fp.date FROM kaching.positions kp, hedge.facebook_portfolio fp
   WHERE kp.portfolio_id=fp.face_id AND kp.stock_id=fp.stock_id AND kp.type=fp.trade_type and kp.type='Short';
ALTER TABLE kaching.lots CHANGE id id BIGINT( 20 ) NOT NULL;

UPDATE kaching.positions kl SET kl.type = 'L' WHERE kl.type = "Buy"; 
UPDATE kaching.positions kl SET kl.type = 'S' WHERE kl.type = "Short"; 

INSERT INTO kaching.portfolios (id, creation_date, long_cash)
   SELECT fp.face_id, fp.date_created, fp.cash_available FROM hedge.facebook_portfolio_metadata fp;

INSERT INTO kaching.portfolios (id, creation_date, long_cash, short_cash)
   SELECT kp.portfolio_id, fp.date_created, fp.cash_available - SUM(kp.agg_cost), SUM(kp.agg_cost) FROM kaching.positions kp, hedge.facebook_portfolio_metadata fp
   WHERE kp.type='S' AND fp.face_id=kp.portfolio_id
   GROUP BY kp.portfolio_id, kp.type
ON DUPLICATE KEY UPDATE long_cash=VALUES(long_cash), short_cash=VALUES(short_cash);

INSERT INTO kaching.orders (id, type, portfolio_id, date, stock_id, quantity)
   (SELECT trans_id, lower(trade_type), face_id, date_placed, stock_id, shares FROM hedge.facebook_transactions ft WHERE ft.active='O');

DELETE FROM kaching.watchlist WHERE stock_id = 0;

ALTER TABLE kaching.portfolios ADD `last_login` DATETIME NULL DEFAULT NULL AFTER `short_cash` ,
ADD `session_key` VARCHAR( 256 ) NULL DEFAULT NULL AFTER `last_login` ;

UPDATE kaching.portfolios kp, hedge.facebook_users fu SET kp.last_login = fu.last_login_date, kp.session_key = fu.session_key WHERE kp.id = fu.face_id;