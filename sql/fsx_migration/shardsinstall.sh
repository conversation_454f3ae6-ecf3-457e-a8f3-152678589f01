#!/bin/sh
if [ $# -ne 1 ]; then
  echo "shardinstall n installs a shard from a shardn.tar.gz file"
  exit 1
fi
cp shard$1.sql.tar.gz /tmp
cd /tmp
gunzip shard$1.sql.tar.gz
tar -xvf shard$1.sql.tar
cat << EOF > setup.sql
DROP DATABASE if exists shard$1;
CREATE DATABASE shard$1 CHARACTER SET 'utf8' COLLATE utf8_general_ci;
EOF
echo "password for database shard$1"
read PASSWORD
echo "creating database shard$1"
mysql -u kaching -D mysql -p$PASSWORD < setup.sql
echo "inserting content"
mysql -u kaching -D shard$1 -p$PASSWORD < shard$1.sql
rm setup.sql
rm shard$1.sql
rm shard$1.sql.tar
