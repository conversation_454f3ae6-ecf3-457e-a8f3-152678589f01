DROP DATABASE if exists shard0;
DROP DATABASE if exists shard1;
DROP DATABASE if exists shard2;
DROP DATABASE if exists shard3;
DROP DATABASE if exists shard4;
DROP DATABASE if exists shard5;
DROP DATABASE if exists shard6;
DROP DATABASE if exists shard7;
DROP DATABASE if exists shard8;
DROP DATABASE if exists shard9;

CREATE DATABASE shard0 CHARACTER SET 'utf8' COLLATE utf8_general_ci;
CREATE DATABASE shard1 CHARACTER SET 'utf8' COLLATE utf8_general_ci;
CREATE DATABASE shard2 CHARACTER SET 'utf8' COLLATE utf8_general_ci;
CREATE DATABASE shard3 CHARACTER SET 'utf8' COLLATE utf8_general_ci;
CREATE DATABASE shard4 CHARACTER SET 'utf8' COLLATE utf8_general_ci;
CREATE DATABASE shard5 CHARACTER SET 'utf8' COLLATE utf8_general_ci;
CREATE DATABASE shard6 CHARACTER SET 'utf8' COLLATE utf8_general_ci;
CREATE DATABASE shard7 CHARACTER SET 'utf8' COLLATE utf8_general_ci;
CREATE DATABASE shard8 CHARACTER SET 'utf8' COLLATE utf8_general_ci;
CREATE DATABASE shard9 CHARACTER SET 'utf8' COLLATE utf8_general_ci;

CREATE TABLE shard0.watchlist engine = InnoDB SELECT * FROM kaching.watchlist k WHERE MOD(k.portfolio_id, 10) = 0;
CREATE TABLE shard1.watchlist engine = InnoDB SELECT * FROM kaching.watchlist k WHERE MOD(k.portfolio_id, 10) = 1;
CREATE TABLE shard2.watchlist engine = InnoDB SELECT * FROM kaching.watchlist k WHERE MOD(k.portfolio_id, 10) = 2;
CREATE TABLE shard3.watchlist engine = InnoDB SELECT * FROM kaching.watchlist k WHERE MOD(k.portfolio_id, 10) = 3;
CREATE TABLE shard4.watchlist engine = InnoDB SELECT * FROM kaching.watchlist k WHERE MOD(k.portfolio_id, 10) = 4;
CREATE TABLE shard5.watchlist engine = InnoDB SELECT * FROM kaching.watchlist k WHERE MOD(k.portfolio_id, 10) = 5;
CREATE TABLE shard6.watchlist engine = InnoDB SELECT * FROM kaching.watchlist k WHERE MOD(k.portfolio_id, 10) = 6;
CREATE TABLE shard7.watchlist engine = InnoDB SELECT * FROM kaching.watchlist k WHERE MOD(k.portfolio_id, 10) = 7;
CREATE TABLE shard8.watchlist engine = InnoDB SELECT * FROM kaching.watchlist k WHERE MOD(k.portfolio_id, 10) = 8;
CREATE TABLE shard9.watchlist engine = InnoDB SELECT * FROM kaching.watchlist k WHERE MOD(k.portfolio_id, 10) = 9;

ALTER TABLE shard0.watchlist ADD PRIMARY KEY ( `id` );
ALTER TABLE shard1.watchlist ADD PRIMARY KEY ( `id` );
ALTER TABLE shard2.watchlist ADD PRIMARY KEY ( `id` );
ALTER TABLE shard3.watchlist ADD PRIMARY KEY ( `id` );
ALTER TABLE shard4.watchlist ADD PRIMARY KEY ( `id` );
ALTER TABLE shard5.watchlist ADD PRIMARY KEY ( `id` );
ALTER TABLE shard6.watchlist ADD PRIMARY KEY ( `id` );
ALTER TABLE shard7.watchlist ADD PRIMARY KEY ( `id` );
ALTER TABLE shard8.watchlist ADD PRIMARY KEY ( `id` );
ALTER TABLE shard9.watchlist ADD PRIMARY KEY ( `id` );

CREATE TABLE shard0.trades engine = InnoDB SELECT * FROM kaching.trades k WHERE MOD(k.portfolio_id, 10) = 0;
CREATE TABLE shard1.trades engine = InnoDB SELECT * FROM kaching.trades k WHERE MOD(k.portfolio_id, 10) = 1;
CREATE TABLE shard2.trades engine = InnoDB SELECT * FROM kaching.trades k WHERE MOD(k.portfolio_id, 10) = 2;
CREATE TABLE shard3.trades engine = InnoDB SELECT * FROM kaching.trades k WHERE MOD(k.portfolio_id, 10) = 3;
CREATE TABLE shard4.trades engine = InnoDB SELECT * FROM kaching.trades k WHERE MOD(k.portfolio_id, 10) = 4;
CREATE TABLE shard5.trades engine = InnoDB SELECT * FROM kaching.trades k WHERE MOD(k.portfolio_id, 10) = 5;
CREATE TABLE shard6.trades engine = InnoDB SELECT * FROM kaching.trades k WHERE MOD(k.portfolio_id, 10) = 6;
CREATE TABLE shard7.trades engine = InnoDB SELECT * FROM kaching.trades k WHERE MOD(k.portfolio_id, 10) = 7;
CREATE TABLE shard8.trades engine = InnoDB SELECT * FROM kaching.trades k WHERE MOD(k.portfolio_id, 10) = 8;
CREATE TABLE shard9.trades engine = InnoDB SELECT * FROM kaching.trades k WHERE MOD(k.portfolio_id, 10) = 9;

ALTER TABLE shard0.trades ADD PRIMARY KEY ( `id` );
ALTER TABLE shard1.trades ADD PRIMARY KEY ( `id` );
ALTER TABLE shard2.trades ADD PRIMARY KEY ( `id` );
ALTER TABLE shard3.trades ADD PRIMARY KEY ( `id` );
ALTER TABLE shard4.trades ADD PRIMARY KEY ( `id` );
ALTER TABLE shard5.trades ADD PRIMARY KEY ( `id` );
ALTER TABLE shard6.trades ADD PRIMARY KEY ( `id` );
ALTER TABLE shard7.trades ADD PRIMARY KEY ( `id` );
ALTER TABLE shard8.trades ADD PRIMARY KEY ( `id` );
ALTER TABLE shard9.trades ADD PRIMARY KEY ( `id` );

CREATE TABLE shard0.positions engine = InnoDB SELECT * FROM kaching.positions k WHERE MOD(k.portfolio_id, 10) = 0;
CREATE TABLE shard1.positions engine = InnoDB SELECT * FROM kaching.positions k WHERE MOD(k.portfolio_id, 10) = 1;
CREATE TABLE shard2.positions engine = InnoDB SELECT * FROM kaching.positions k WHERE MOD(k.portfolio_id, 10) = 2;
CREATE TABLE shard3.positions engine = InnoDB SELECT * FROM kaching.positions k WHERE MOD(k.portfolio_id, 10) = 3;
CREATE TABLE shard4.positions engine = InnoDB SELECT * FROM kaching.positions k WHERE MOD(k.portfolio_id, 10) = 4;
CREATE TABLE shard5.positions engine = InnoDB SELECT * FROM kaching.positions k WHERE MOD(k.portfolio_id, 10) = 5;
CREATE TABLE shard6.positions engine = InnoDB SELECT * FROM kaching.positions k WHERE MOD(k.portfolio_id, 10) = 6;
CREATE TABLE shard7.positions engine = InnoDB SELECT * FROM kaching.positions k WHERE MOD(k.portfolio_id, 10) = 7;
CREATE TABLE shard8.positions engine = InnoDB SELECT * FROM kaching.positions k WHERE MOD(k.portfolio_id, 10) = 8;
CREATE TABLE shard9.positions engine = InnoDB SELECT * FROM kaching.positions k WHERE MOD(k.portfolio_id, 10) = 9;

ALTER TABLE shard0.positions ADD PRIMARY KEY ( `id` );
ALTER TABLE shard1.positions ADD PRIMARY KEY ( `id` );
ALTER TABLE shard2.positions ADD PRIMARY KEY ( `id` );
ALTER TABLE shard3.positions ADD PRIMARY KEY ( `id` );
ALTER TABLE shard4.positions ADD PRIMARY KEY ( `id` );
ALTER TABLE shard5.positions ADD PRIMARY KEY ( `id` );
ALTER TABLE shard6.positions ADD PRIMARY KEY ( `id` );
ALTER TABLE shard7.positions ADD PRIMARY KEY ( `id` );
ALTER TABLE shard8.positions ADD PRIMARY KEY ( `id` );
ALTER TABLE shard9.positions ADD PRIMARY KEY ( `id` );

alter table shard0.positions add unique (portfolio_id, stock_id, type);
alter table shard1.positions add unique (portfolio_id, stock_id, type);
alter table shard2.positions add unique (portfolio_id, stock_id, type);
alter table shard3.positions add unique (portfolio_id, stock_id, type);
alter table shard4.positions add unique (portfolio_id, stock_id, type);
alter table shard5.positions add unique (portfolio_id, stock_id, type);
alter table shard6.positions add unique (portfolio_id, stock_id, type);
alter table shard7.positions add unique (portfolio_id, stock_id, type);
alter table shard8.positions add unique (portfolio_id, stock_id, type);
alter table shard9.positions add unique (portfolio_id, stock_id, type);

CREATE TABLE shard0.portfolios engine = InnoDB SELECT * FROM kaching.portfolios k WHERE MOD(k.id, 10) = 0;
CREATE TABLE shard1.portfolios engine = InnoDB SELECT * FROM kaching.portfolios k WHERE MOD(k.id, 10) = 1;
CREATE TABLE shard2.portfolios engine = InnoDB SELECT * FROM kaching.portfolios k WHERE MOD(k.id, 10) = 2;
CREATE TABLE shard3.portfolios engine = InnoDB SELECT * FROM kaching.portfolios k WHERE MOD(k.id, 10) = 3;
CREATE TABLE shard4.portfolios engine = InnoDB SELECT * FROM kaching.portfolios k WHERE MOD(k.id, 10) = 4;
CREATE TABLE shard5.portfolios engine = InnoDB SELECT * FROM kaching.portfolios k WHERE MOD(k.id, 10) = 5;
CREATE TABLE shard6.portfolios engine = InnoDB SELECT * FROM kaching.portfolios k WHERE MOD(k.id, 10) = 6;
CREATE TABLE shard7.portfolios engine = InnoDB SELECT * FROM kaching.portfolios k WHERE MOD(k.id, 10) = 7;
CREATE TABLE shard8.portfolios engine = InnoDB SELECT * FROM kaching.portfolios k WHERE MOD(k.id, 10) = 8;
CREATE TABLE shard9.portfolios engine = InnoDB SELECT * FROM kaching.portfolios k WHERE MOD(k.id, 10) = 9;

ALTER TABLE shard0.portfolios ADD PRIMARY KEY ( `id` );
ALTER TABLE shard1.portfolios ADD PRIMARY KEY ( `id` );
ALTER TABLE shard2.portfolios ADD PRIMARY KEY ( `id` );
ALTER TABLE shard3.portfolios ADD PRIMARY KEY ( `id` );
ALTER TABLE shard4.portfolios ADD PRIMARY KEY ( `id` );
ALTER TABLE shard5.portfolios ADD PRIMARY KEY ( `id` );
ALTER TABLE shard6.portfolios ADD PRIMARY KEY ( `id` );
ALTER TABLE shard7.portfolios ADD PRIMARY KEY ( `id` );
ALTER TABLE shard8.portfolios ADD PRIMARY KEY ( `id` );
ALTER TABLE shard9.portfolios ADD PRIMARY KEY ( `id` );

CREATE TABLE shard0.lots engine = InnoDB SELECT kl.* FROM kaching.lots kl, shard0.positions kp WHERE kl.position_id = kp.id;
CREATE TABLE shard1.lots engine = InnoDB SELECT kl.* FROM kaching.lots kl, shard1.positions kp WHERE kl.position_id = kp.id;
CREATE TABLE shard2.lots engine = InnoDB SELECT kl.* FROM kaching.lots kl, shard2.positions kp WHERE kl.position_id = kp.id;
CREATE TABLE shard3.lots engine = InnoDB SELECT kl.* FROM kaching.lots kl, shard3.positions kp WHERE kl.position_id = kp.id;
CREATE TABLE shard4.lots engine = InnoDB SELECT kl.* FROM kaching.lots kl, shard4.positions kp WHERE kl.position_id = kp.id;
CREATE TABLE shard5.lots engine = InnoDB SELECT kl.* FROM kaching.lots kl, shard5.positions kp WHERE kl.position_id = kp.id;
CREATE TABLE shard6.lots engine = InnoDB SELECT kl.* FROM kaching.lots kl, shard6.positions kp WHERE kl.position_id = kp.id;
CREATE TABLE shard7.lots engine = InnoDB SELECT kl.* FROM kaching.lots kl, shard7.positions kp WHERE kl.position_id = kp.id;
CREATE TABLE shard8.lots engine = InnoDB SELECT kl.* FROM kaching.lots kl, shard8.positions kp WHERE kl.position_id = kp.id;
CREATE TABLE shard9.lots engine = InnoDB SELECT kl.* FROM kaching.lots kl, shard9.positions kp WHERE kl.position_id = kp.id;

CREATE TABLE shard0.stocks engine = InnoDB SELECT * FROM kaching.stocks;
CREATE TABLE shard1.stocks engine = InnoDB SELECT * FROM kaching.stocks;
CREATE TABLE shard2.stocks engine = InnoDB SELECT * FROM kaching.stocks;
CREATE TABLE shard3.stocks engine = InnoDB SELECT * FROM kaching.stocks;
CREATE TABLE shard4.stocks engine = InnoDB SELECT * FROM kaching.stocks;
CREATE TABLE shard5.stocks engine = InnoDB SELECT * FROM kaching.stocks;
CREATE TABLE shard6.stocks engine = InnoDB SELECT * FROM kaching.stocks;
CREATE TABLE shard7.stocks engine = InnoDB SELECT * FROM kaching.stocks;
CREATE TABLE shard8.stocks engine = InnoDB SELECT * FROM kaching.stocks;
CREATE TABLE shard9.stocks engine = InnoDB SELECT * FROM kaching.stocks;

ALTER TABLE shard0.stocks ADD PRIMARY KEY ( `id` );
ALTER TABLE shard1.stocks ADD PRIMARY KEY ( `id` );
ALTER TABLE shard2.stocks ADD PRIMARY KEY ( `id` );
ALTER TABLE shard3.stocks ADD PRIMARY KEY ( `id` );
ALTER TABLE shard4.stocks ADD PRIMARY KEY ( `id` );
ALTER TABLE shard5.stocks ADD PRIMARY KEY ( `id` );
ALTER TABLE shard6.stocks ADD PRIMARY KEY ( `id` );
ALTER TABLE shard7.stocks ADD PRIMARY KEY ( `id` );
ALTER TABLE shard8.stocks ADD PRIMARY KEY ( `id` );
ALTER TABLE shard9.stocks ADD PRIMARY KEY ( `id` );

CREATE TABLE shard0.balances engine = InnoDB SELECT kl.* FROM kaching.balances kl, shard0.portfolios kp WHERE kl.portfolio_id = kp.id;
CREATE TABLE shard1.balances engine = InnoDB SELECT kl.* FROM kaching.balances kl, shard1.portfolios kp WHERE kl.portfolio_id = kp.id;
CREATE TABLE shard2.balances engine = InnoDB SELECT kl.* FROM kaching.balances kl, shard2.portfolios kp WHERE kl.portfolio_id = kp.id;
CREATE TABLE shard3.balances engine = InnoDB SELECT kl.* FROM kaching.balances kl, shard3.portfolios kp WHERE kl.portfolio_id = kp.id;
CREATE TABLE shard4.balances engine = InnoDB SELECT kl.* FROM kaching.balances kl, shard4.portfolios kp WHERE kl.portfolio_id = kp.id;
CREATE TABLE shard5.balances engine = InnoDB SELECT kl.* FROM kaching.balances kl, shard5.portfolios kp WHERE kl.portfolio_id = kp.id;
CREATE TABLE shard6.balances engine = InnoDB SELECT kl.* FROM kaching.balances kl, shard6.portfolios kp WHERE kl.portfolio_id = kp.id;
CREATE TABLE shard7.balances engine = InnoDB SELECT kl.* FROM kaching.balances kl, shard7.portfolios kp WHERE kl.portfolio_id = kp.id;
CREATE TABLE shard8.balances engine = InnoDB SELECT kl.* FROM kaching.balances kl, shard8.portfolios kp WHERE kl.portfolio_id = kp.id;
CREATE TABLE shard9.balances engine = InnoDB SELECT kl.* FROM kaching.balances kl, shard9.portfolios kp WHERE kl.portfolio_id = kp.id;

ALTER TABLE shard0.balances ADD PRIMARY KEY ( `id` );
ALTER TABLE shard1.balances ADD PRIMARY KEY ( `id` );
ALTER TABLE shard2.balances ADD PRIMARY KEY ( `id` );
ALTER TABLE shard3.balances ADD PRIMARY KEY ( `id` );
ALTER TABLE shard4.balances ADD PRIMARY KEY ( `id` );
ALTER TABLE shard5.balances ADD PRIMARY KEY ( `id` );
ALTER TABLE shard6.balances ADD PRIMARY KEY ( `id` );
ALTER TABLE shard7.balances ADD PRIMARY KEY ( `id` );
ALTER TABLE shard8.balances ADD PRIMARY KEY ( `id` );
ALTER TABLE shard9.balances ADD PRIMARY KEY ( `id` );

CREATE TABLE shard0.daily_gains engine = InnoDB SELECT kl.* FROM kaching.daily_gains kl, shard0.portfolios kp WHERE kl.portfolio_id = kp.id;
CREATE TABLE shard1.daily_gains engine = InnoDB SELECT kl.* FROM kaching.daily_gains kl, shard1.portfolios kp WHERE kl.portfolio_id = kp.id;
CREATE TABLE shard2.daily_gains engine = InnoDB SELECT kl.* FROM kaching.daily_gains kl, shard2.portfolios kp WHERE kl.portfolio_id = kp.id;
CREATE TABLE shard3.daily_gains engine = InnoDB SELECT kl.* FROM kaching.daily_gains kl, shard3.portfolios kp WHERE kl.portfolio_id = kp.id;
CREATE TABLE shard4.daily_gains engine = InnoDB SELECT kl.* FROM kaching.daily_gains kl, shard4.portfolios kp WHERE kl.portfolio_id = kp.id;
CREATE TABLE shard5.daily_gains engine = InnoDB SELECT kl.* FROM kaching.daily_gains kl, shard5.portfolios kp WHERE kl.portfolio_id = kp.id;
CREATE TABLE shard6.daily_gains engine = InnoDB SELECT kl.* FROM kaching.daily_gains kl, shard6.portfolios kp WHERE kl.portfolio_id = kp.id;
CREATE TABLE shard7.daily_gains engine = InnoDB SELECT kl.* FROM kaching.daily_gains kl, shard7.portfolios kp WHERE kl.portfolio_id = kp.id;
CREATE TABLE shard8.daily_gains engine = InnoDB SELECT kl.* FROM kaching.daily_gains kl, shard8.portfolios kp WHERE kl.portfolio_id = kp.id;
CREATE TABLE shard9.daily_gains engine = InnoDB SELECT kl.* FROM kaching.daily_gains kl, shard9.portfolios kp WHERE kl.portfolio_id = kp.id;

ALTER TABLE shard0.daily_gains ADD PRIMARY KEY ( `id` );
ALTER TABLE shard1.daily_gains ADD PRIMARY KEY ( `id` );
ALTER TABLE shard2.daily_gains ADD PRIMARY KEY ( `id` );
ALTER TABLE shard3.daily_gains ADD PRIMARY KEY ( `id` );
ALTER TABLE shard4.daily_gains ADD PRIMARY KEY ( `id` );
ALTER TABLE shard5.daily_gains ADD PRIMARY KEY ( `id` );
ALTER TABLE shard6.daily_gains ADD PRIMARY KEY ( `id` );
ALTER TABLE shard7.daily_gains ADD PRIMARY KEY ( `id` );
ALTER TABLE shard8.daily_gains ADD PRIMARY KEY ( `id` );
ALTER TABLE shard9.daily_gains ADD PRIMARY KEY ( `id` );

create index index_percent on shard0.daily_gains (percent);
create index index_percent on shard1.daily_gains (percent);
create index index_percent on shard2.daily_gains (percent);
create index index_percent on shard3.daily_gains (percent);
create index index_percent on shard4.daily_gains (percent);
create index index_percent on shard5.daily_gains (percent);
create index index_percent on shard6.daily_gains (percent);
create index index_percent on shard7.daily_gains (percent);
create index index_percent on shard8.daily_gains (percent);
create index index_percent on shard9.daily_gains (percent);

CREATE TABLE shard0.orders engine = InnoDB SELECT * FROM kaching.orders k WHERE MOD(k.portfolio_id, 10) = 0;
CREATE TABLE shard1.orders engine = InnoDB SELECT * FROM kaching.orders k WHERE MOD(k.portfolio_id, 10) = 1;
CREATE TABLE shard2.orders engine = InnoDB SELECT * FROM kaching.orders k WHERE MOD(k.portfolio_id, 10) = 2;
CREATE TABLE shard3.orders engine = InnoDB SELECT * FROM kaching.orders k WHERE MOD(k.portfolio_id, 10) = 3;
CREATE TABLE shard4.orders engine = InnoDB SELECT * FROM kaching.orders k WHERE MOD(k.portfolio_id, 10) = 4;
CREATE TABLE shard5.orders engine = InnoDB SELECT * FROM kaching.orders k WHERE MOD(k.portfolio_id, 10) = 5;
CREATE TABLE shard6.orders engine = InnoDB SELECT * FROM kaching.orders k WHERE MOD(k.portfolio_id, 10) = 6;
CREATE TABLE shard7.orders engine = InnoDB SELECT * FROM kaching.orders k WHERE MOD(k.portfolio_id, 10) = 7;
CREATE TABLE shard8.orders engine = InnoDB SELECT * FROM kaching.orders k WHERE MOD(k.portfolio_id, 10) = 8;
CREATE TABLE shard9.orders engine = InnoDB SELECT * FROM kaching.orders k WHERE MOD(k.portfolio_id, 10) = 9;

ALTER TABLE shard0.orders ADD PRIMARY KEY ( `id` );
ALTER TABLE shard1.orders ADD PRIMARY KEY ( `id` );
ALTER TABLE shard2.orders ADD PRIMARY KEY ( `id` );
ALTER TABLE shard3.orders ADD PRIMARY KEY ( `id` );
ALTER TABLE shard4.orders ADD PRIMARY KEY ( `id` );
ALTER TABLE shard5.orders ADD PRIMARY KEY ( `id` );
ALTER TABLE shard6.orders ADD PRIMARY KEY ( `id` );
ALTER TABLE shard7.orders ADD PRIMARY KEY ( `id` );
ALTER TABLE shard8.orders ADD PRIMARY KEY ( `id` );
ALTER TABLE shard9.orders ADD PRIMARY KEY ( `id` );

-- constraints on shard 0
alter table shard0.balances add index FK8D456D1751C8ADE0 (portfolio_id),
  add constraint FK8D456D1751C8ADE0 foreign key (portfolio_id)
  references portfolios (id);
alter table shard0.daily_gains add index FK501AF2E51C8ADE0 (portfolio_id),
  add constraint FK501AF2E51C8ADE0 foreign key (portfolio_id)
  references portfolios (id);
alter table shard0.lots add index FK32C742CA551734 (position_id),
  add constraint FK32C742CA551734 foreign key (position_id)
  references positions (id);
alter table shard0.orders add index FKC3DF62E54CAF3620 (stock_id),
  add constraint FKC3DF62E54CAF3620 foreign key (stock_id)
  references stocks (id);
alter table shard0.orders add index FKC3DF62E551C8ADE0 (portfolio_id),
  add constraint FKC3DF62E551C8ADE0 foreign key (portfolio_id)
  references portfolios (id);
alter table shard0.positions add index FK65C08C6A4CAF3620 (stock_id),
  add constraint FK65C08C6A4CAF3620 foreign key (stock_id)
  references stocks (id);
alter table shard0.positions add index FK65C08C6A51C8ADE0 (portfolio_id),
  add constraint FK65C08C6A51C8ADE0 foreign key (portfolio_id)
  references portfolios (id);
alter table shard0.trades add index FKCC663B8F4CAF3620 (stock_id),
  add constraint FKCC663B8F4CAF3620 foreign key (stock_id)
  references stocks (id);
alter table shard0.trades add index FKCC663B8F51C8ADE0 (portfolio_id),
  add constraint FKCC663B8F51C8ADE0 foreign key (portfolio_id)
  references portfolios (id);
alter table shard0.watchlist add index FKEF5075ED4CAF3620 (stock_id),
  add constraint FKEF5075ED4CAF3620 foreign key (stock_id)
  references stocks (id);
alter table shard0.watchlist add index FKEF5075ED51C8ADE0 (portfolio_id),
  add constraint FKEF5075ED51C8ADE0 foreign key (portfolio_id)
  references portfolios (id);

-- constraints on shard 0
alter table shard1.balances add index FK8D456D1751C8ADE0 (portfolio_id),
  add constraint FK8D456D1751C8ADE0 foreign key (portfolio_id)
  references portfolios (id);
alter table shard1.daily_gains add index FK501AF2E51C8ADE0 (portfolio_id),
  add constraint FK501AF2E51C8ADE0 foreign key (portfolio_id)
  references portfolios (id);
alter table shard1.lots add index FK32C742CA551734 (position_id),
  add constraint FK32C742CA551734 foreign key (position_id)
  references positions (id);
alter table shard1.orders add index FKC3DF62E54CAF3620 (stock_id),
  add constraint FKC3DF62E54CAF3620 foreign key (stock_id)
  references stocks (id);
alter table shard1.orders add index FKC3DF62E551C8ADE0 (portfolio_id),
  add constraint FKC3DF62E551C8ADE0 foreign key (portfolio_id)
  references portfolios (id);
alter table shard1.positions add index FK65C08C6A4CAF3620 (stock_id),
  add constraint FK65C08C6A4CAF3620 foreign key (stock_id)
  references stocks (id);
alter table shard1.positions add index FK65C08C6A51C8ADE0 (portfolio_id),
  add constraint FK65C08C6A51C8ADE0 foreign key (portfolio_id)
  references portfolios (id);
alter table shard1.trades add index FKCC663B8F4CAF3620 (stock_id),
  add constraint FKCC663B8F4CAF3620 foreign key (stock_id)
  references stocks (id);
alter table shard1.trades add index FKCC663B8F51C8ADE0 (portfolio_id),
  add constraint FKCC663B8F51C8ADE0 foreign key (portfolio_id)
  references portfolios (id);
alter table shard1.watchlist add index FKEF5075ED4CAF3620 (stock_id),
  add constraint FKEF5075ED4CAF3620 foreign key (stock_id)
  references stocks (id);
alter table shard1.watchlist add index FKEF5075ED51C8ADE0 (portfolio_id),
  add constraint FKEF5075ED51C8ADE0 foreign key (portfolio_id)
  references portfolios (id);

-- constraints on shard 2
alter table shard2.balances add index FK8D456D1751C8ADE0 (portfolio_id),
  add constraint FK8D456D1751C8ADE0 foreign key (portfolio_id)
  references portfolios (id);
alter table shard2.daily_gains add index FK501AF2E51C8ADE0 (portfolio_id),
  add constraint FK501AF2E51C8ADE0 foreign key (portfolio_id)
  references portfolios (id);
alter table shard2.lots add index FK32C742CA551734 (position_id),
  add constraint FK32C742CA551734 foreign key (position_id)
  references positions (id);
alter table shard2.orders add index FKC3DF62E54CAF3620 (stock_id),
  add constraint FKC3DF62E54CAF3620 foreign key (stock_id)
  references stocks (id);
alter table shard2.orders add index FKC3DF62E551C8ADE0 (portfolio_id),
  add constraint FKC3DF62E551C8ADE0 foreign key (portfolio_id)
  references portfolios (id);
alter table shard2.positions add index FK65C08C6A4CAF3620 (stock_id),
  add constraint FK65C08C6A4CAF3620 foreign key (stock_id)
  references stocks (id);
alter table shard2.positions add index FK65C08C6A51C8ADE0 (portfolio_id),
  add constraint FK65C08C6A51C8ADE0 foreign key (portfolio_id)
  references portfolios (id);
alter table shard2.trades add index FKCC663B8F4CAF3620 (stock_id),
  add constraint FKCC663B8F4CAF3620 foreign key (stock_id)
  references stocks (id);
alter table shard2.trades add index FKCC663B8F51C8ADE0 (portfolio_id),
  add constraint FKCC663B8F51C8ADE0 foreign key (portfolio_id)
  references portfolios (id);
alter table shard2.watchlist add index FKEF5075ED4CAF3620 (stock_id),
  add constraint FKEF5075ED4CAF3620 foreign key (stock_id)
  references stocks (id);
alter table shard2.watchlist add index FKEF5075ED51C8ADE0 (portfolio_id),
  add constraint FKEF5075ED51C8ADE0 foreign key (portfolio_id)
  references portfolios (id);

-- constraints on shard 3
alter table shard3.balances add index FK8D456D1751C8ADE0 (portfolio_id),
  add constraint FK8D456D1751C8ADE0 foreign key (portfolio_id)
  references portfolios (id);
alter table shard3.daily_gains add index FK501AF2E51C8ADE0 (portfolio_id),
  add constraint FK501AF2E51C8ADE0 foreign key (portfolio_id)
  references portfolios (id);
alter table shard3.lots add index FK32C742CA551734 (position_id),
  add constraint FK32C742CA551734 foreign key (position_id)
  references positions (id);
alter table shard3.orders add index FKC3DF62E54CAF3620 (stock_id),
  add constraint FKC3DF62E54CAF3620 foreign key (stock_id)
  references stocks (id);
alter table shard3.orders add index FKC3DF62E551C8ADE0 (portfolio_id),
  add constraint FKC3DF62E551C8ADE0 foreign key (portfolio_id)
  references portfolios (id);
alter table shard3.positions add index FK65C08C6A4CAF3620 (stock_id),
  add constraint FK65C08C6A4CAF3620 foreign key (stock_id)
  references stocks (id);
alter table shard3.positions add index FK65C08C6A51C8ADE0 (portfolio_id),
  add constraint FK65C08C6A51C8ADE0 foreign key (portfolio_id)
  references portfolios (id);
alter table shard3.trades add index FKCC663B8F4CAF3620 (stock_id),
  add constraint FKCC663B8F4CAF3620 foreign key (stock_id)
  references stocks (id);
alter table shard3.trades add index FKCC663B8F51C8ADE0 (portfolio_id),
  add constraint FKCC663B8F51C8ADE0 foreign key (portfolio_id)
  references portfolios (id);
alter table shard3.watchlist add index FKEF5075ED4CAF3620 (stock_id),
  add constraint FKEF5075ED4CAF3620 foreign key (stock_id)
  references stocks (id);
alter table shard3.watchlist add index FKEF5075ED51C8ADE0 (portfolio_id),
  add constraint FKEF5075ED51C8ADE0 foreign key (portfolio_id)
  references portfolios (id);

-- constraints on shard 4
alter table shard4.balances add index FK8D456D1751C8ADE0 (portfolio_id),
  add constraint FK8D456D1751C8ADE0 foreign key (portfolio_id)
  references portfolios (id);
alter table shard4.daily_gains add index FK501AF2E51C8ADE0 (portfolio_id),
  add constraint FK501AF2E51C8ADE0 foreign key (portfolio_id)
  references portfolios (id);
alter table shard4.lots add index FK32C742CA551734 (position_id),
  add constraint FK32C742CA551734 foreign key (position_id)
  references positions (id);
alter table shard4.orders add index FKC3DF62E54CAF3620 (stock_id),
  add constraint FKC3DF62E54CAF3620 foreign key (stock_id)
  references stocks (id);
alter table shard4.orders add index FKC3DF62E551C8ADE0 (portfolio_id),
  add constraint FKC3DF62E551C8ADE0 foreign key (portfolio_id)
  references portfolios (id);
alter table shard4.positions add index FK65C08C6A4CAF3620 (stock_id),
  add constraint FK65C08C6A4CAF3620 foreign key (stock_id)
  references stocks (id);
alter table shard4.positions add index FK65C08C6A51C8ADE0 (portfolio_id),
  add constraint FK65C08C6A51C8ADE0 foreign key (portfolio_id)
  references portfolios (id);
alter table shard4.trades add index FKCC663B8F4CAF3620 (stock_id),
  add constraint FKCC663B8F4CAF3620 foreign key (stock_id)
  references stocks (id);
alter table shard4.trades add index FKCC663B8F51C8ADE0 (portfolio_id),
  add constraint FKCC663B8F51C8ADE0 foreign key (portfolio_id)
  references portfolios (id);
alter table shard4.watchlist add index FKEF5075ED4CAF3620 (stock_id),
  add constraint FKEF5075ED4CAF3620 foreign key (stock_id)
  references stocks (id);
alter table shard4.watchlist add index FKEF5075ED51C8ADE0 (portfolio_id),
  add constraint FKEF5075ED51C8ADE0 foreign key (portfolio_id)
  references portfolios (id);

-- constraints on shard 5
alter table shard5.balances add index FK8D456D1751C8ADE0 (portfolio_id),
  add constraint FK8D456D1751C8ADE0 foreign key (portfolio_id)
  references portfolios (id);
alter table shard5.daily_gains add index FK501AF2E51C8ADE0 (portfolio_id),
  add constraint FK501AF2E51C8ADE0 foreign key (portfolio_id)
  references portfolios (id);
alter table shard5.lots add index FK32C742CA551734 (position_id),
  add constraint FK32C742CA551734 foreign key (position_id)
  references positions (id);
alter table shard5.orders add index FKC3DF62E54CAF3620 (stock_id),
  add constraint FKC3DF62E54CAF3620 foreign key (stock_id)
  references stocks (id);
alter table shard5.orders add index FKC3DF62E551C8ADE0 (portfolio_id),
  add constraint FKC3DF62E551C8ADE0 foreign key (portfolio_id)
  references portfolios (id);
alter table shard5.positions add index FK65C08C6A4CAF3620 (stock_id),
  add constraint FK65C08C6A4CAF3620 foreign key (stock_id)
  references stocks (id);
alter table shard5.positions add index FK65C08C6A51C8ADE0 (portfolio_id),
  add constraint FK65C08C6A51C8ADE0 foreign key (portfolio_id)
  references portfolios (id);
alter table shard5.trades add index FKCC663B8F4CAF3620 (stock_id),
  add constraint FKCC663B8F4CAF3620 foreign key (stock_id)
  references stocks (id);
alter table shard5.trades add index FKCC663B8F51C8ADE0 (portfolio_id),
  add constraint FKCC663B8F51C8ADE0 foreign key (portfolio_id)
  references portfolios (id);
alter table shard5.watchlist add index FKEF5075ED4CAF3620 (stock_id),
  add constraint FKEF5075ED4CAF3620 foreign key (stock_id)
  references stocks (id);
alter table shard5.watchlist add index FKEF5075ED51C8ADE0 (portfolio_id),
  add constraint FKEF5075ED51C8ADE0 foreign key (portfolio_id)
  references portfolios (id);

-- constraints on shard 6
alter table shard6.balances add index FK8D456D1751C8ADE0 (portfolio_id),
  add constraint FK8D456D1751C8ADE0 foreign key (portfolio_id)
  references portfolios (id);
alter table shard6.daily_gains add index FK501AF2E51C8ADE0 (portfolio_id),
  add constraint FK501AF2E51C8ADE0 foreign key (portfolio_id)
  references portfolios (id);
alter table shard6.lots add index FK32C742CA551734 (position_id),
  add constraint FK32C742CA551734 foreign key (position_id)
  references positions (id);
alter table shard6.orders add index FKC3DF62E54CAF3620 (stock_id),
  add constraint FKC3DF62E54CAF3620 foreign key (stock_id)
  references stocks (id);
alter table shard6.orders add index FKC3DF62E551C8ADE0 (portfolio_id),
  add constraint FKC3DF62E551C8ADE0 foreign key (portfolio_id)
  references portfolios (id);
alter table shard6.positions add index FK65C08C6A4CAF3620 (stock_id),
  add constraint FK65C08C6A4CAF3620 foreign key (stock_id)
  references stocks (id);
alter table shard6.positions add index FK65C08C6A51C8ADE0 (portfolio_id),
  add constraint FK65C08C6A51C8ADE0 foreign key (portfolio_id)
  references portfolios (id);
alter table shard6.trades add index FKCC663B8F4CAF3620 (stock_id),
  add constraint FKCC663B8F4CAF3620 foreign key (stock_id)
  references stocks (id);
alter table shard6.trades add index FKCC663B8F51C8ADE0 (portfolio_id),
  add constraint FKCC663B8F51C8ADE0 foreign key (portfolio_id)
  references portfolios (id);
alter table shard6.watchlist add index FKEF5075ED4CAF3620 (stock_id),
  add constraint FKEF5075ED4CAF3620 foreign key (stock_id)
  references stocks (id);
alter table shard6.watchlist add index FKEF5075ED51C8ADE0 (portfolio_id),
  add constraint FKEF5075ED51C8ADE0 foreign key (portfolio_id)
  references portfolios (id);

-- constraints on shard 7
alter table shard7.balances add index FK8D456D1751C8ADE0 (portfolio_id),
  add constraint FK8D456D1751C8ADE0 foreign key (portfolio_id)
  references portfolios (id);
alter table shard7.daily_gains add index FK501AF2E51C8ADE0 (portfolio_id),
  add constraint FK501AF2E51C8ADE0 foreign key (portfolio_id)
  references portfolios (id);
alter table shard7.lots add index FK32C742CA551734 (position_id),
  add constraint FK32C742CA551734 foreign key (position_id)
  references positions (id);
alter table shard7.orders add index FKC3DF62E54CAF3620 (stock_id),
  add constraint FKC3DF62E54CAF3620 foreign key (stock_id)
  references stocks (id);
alter table shard7.orders add index FKC3DF62E551C8ADE0 (portfolio_id),
  add constraint FKC3DF62E551C8ADE0 foreign key (portfolio_id)
  references portfolios (id);
alter table shard7.positions add index FK65C08C6A4CAF3620 (stock_id),
  add constraint FK65C08C6A4CAF3620 foreign key (stock_id)
  references stocks (id);
alter table shard7.positions add index FK65C08C6A51C8ADE0 (portfolio_id),
  add constraint FK65C08C6A51C8ADE0 foreign key (portfolio_id)
  references portfolios (id);
alter table shard7.trades add index FKCC663B8F4CAF3620 (stock_id),
  add constraint FKCC663B8F4CAF3620 foreign key (stock_id)
  references stocks (id);
alter table shard7.trades add index FKCC663B8F51C8ADE0 (portfolio_id),
  add constraint FKCC663B8F51C8ADE0 foreign key (portfolio_id)
  references portfolios (id);
alter table shard7.watchlist add index FKEF5075ED4CAF3620 (stock_id),
  add constraint FKEF5075ED4CAF3620 foreign key (stock_id)
  references stocks (id);
alter table shard7.watchlist add index FKEF5075ED51C8ADE0 (portfolio_id),
  add constraint FKEF5075ED51C8ADE0 foreign key (portfolio_id)
  references portfolios (id);

-- constraints on shard 8
alter table shard8.balances add index FK8D456D1751C8ADE0 (portfolio_id),
  add constraint FK8D456D1751C8ADE0 foreign key (portfolio_id)
  references portfolios (id);
alter table shard8.daily_gains add index FK501AF2E51C8ADE0 (portfolio_id),
  add constraint FK501AF2E51C8ADE0 foreign key (portfolio_id)
  references portfolios (id);
alter table shard8.lots add index FK32C742CA551734 (position_id),
  add constraint FK32C742CA551734 foreign key (position_id)
  references positions (id);
alter table shard8.orders add index FKC3DF62E54CAF3620 (stock_id),
  add constraint FKC3DF62E54CAF3620 foreign key (stock_id)
  references stocks (id);
alter table shard8.orders add index FKC3DF62E551C8ADE0 (portfolio_id),
  add constraint FKC3DF62E551C8ADE0 foreign key (portfolio_id)
  references portfolios (id);
alter table shard8.positions add index FK65C08C6A4CAF3620 (stock_id),
  add constraint FK65C08C6A4CAF3620 foreign key (stock_id)
  references stocks (id);
alter table shard8.positions add index FK65C08C6A51C8ADE0 (portfolio_id),
  add constraint FK65C08C6A51C8ADE0 foreign key (portfolio_id)
  references portfolios (id);
alter table shard8.trades add index FKCC663B8F4CAF3620 (stock_id),
  add constraint FKCC663B8F4CAF3620 foreign key (stock_id)
  references stocks (id);
alter table shard8.trades add index FKCC663B8F51C8ADE0 (portfolio_id),
  add constraint FKCC663B8F51C8ADE0 foreign key (portfolio_id)
  references portfolios (id);
alter table shard8.watchlist add index FKEF5075ED4CAF3620 (stock_id),
  add constraint FKEF5075ED4CAF3620 foreign key (stock_id)
  references stocks (id);
alter table shard8.watchlist add index FKEF5075ED51C8ADE0 (portfolio_id),
  add constraint FKEF5075ED51C8ADE0 foreign key (portfolio_id)
  references portfolios (id);

-- constraints on shard 9
alter table shard9.balances add index FK8D456D1751C8ADE0 (portfolio_id),
  add constraint FK8D456D1751C8ADE0 foreign key (portfolio_id)
  references portfolios (id);
alter table shard9.daily_gains add index FK501AF2E51C8ADE0 (portfolio_id),
  add constraint FK501AF2E51C8ADE0 foreign key (portfolio_id)
  references portfolios (id);
alter table shard9.lots add index FK32C742CA551734 (position_id),
  add constraint FK32C742CA551734 foreign key (position_id)
  references positions (id);
alter table shard9.orders add index FKC3DF62E54CAF3620 (stock_id),
  add constraint FKC3DF62E54CAF3620 foreign key (stock_id)
  references stocks (id);
alter table shard9.orders add index FKC3DF62E551C8ADE0 (portfolio_id),
  add constraint FKC3DF62E551C8ADE0 foreign key (portfolio_id)
  references portfolios (id);
alter table shard9.positions add index FK65C08C6A4CAF3620 (stock_id),
  add constraint FK65C08C6A4CAF3620 foreign key (stock_id)
  references stocks (id);
alter table shard9.positions add index FK65C08C6A51C8ADE0 (portfolio_id),
  add constraint FK65C08C6A51C8ADE0 foreign key (portfolio_id)
  references portfolios (id);
alter table shard9.trades add index FKCC663B8F4CAF3620 (stock_id),
  add constraint FKCC663B8F4CAF3620 foreign key (stock_id)
  references stocks (id);
alter table shard9.trades add index FKCC663B8F51C8ADE0 (portfolio_id),
  add constraint FKCC663B8F51C8ADE0 foreign key (portfolio_id)
  references portfolios (id);
alter table shard9.watchlist add index FKEF5075ED4CAF3620 (stock_id),
  add constraint FKEF5075ED4CAF3620 foreign key (stock_id)
  references stocks (id);
alter table shard9.watchlist add index FKEF5075ED51C8ADE0 (portfolio_id),
  add constraint FKEF5075ED51C8ADE0 foreign key (portfolio_id)
  references portfolios (id);
