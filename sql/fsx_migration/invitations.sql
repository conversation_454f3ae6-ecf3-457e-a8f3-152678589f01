DROP DATABASE if exists invitations;

CREATE DATABASE invitations CHARACTER SET 'utf8' COLLATE utf8_general_ci;

create table invitations.referrals_count (
  id bigint not null,
  refer bigint not null,
  refer_toadd bigint not null default 0,
  primary key (id)) engine = InnoDB;

create table invitations.referrals (
  id bigint not null auto_increment,
  referer_id bigint not null,
  referee_id bigint default null,
  date_referred datetime default null,
  date_accepted datetime default null,
  date_paid datetime default null,
  primary key (id),
  unique (referer_id, referee_id))
  engine = InnoDB;

insert ignore into invitations.referrals_count
  (id, refer)
(select
  face_id, refer
from
  hedge.facebook_users);

insert ignore into invitations.referrals
  (referer_id, referee_id, date_referred, date_accepted)
(select
  referer_id, referee_id, date_refered, date_accepted
from
  hedge.facebook_refer);

update invitations.referrals_count set refer_toadd =
  refer - (select count(*) from invitations.referrals r where r.id = referrals_count.id);

insert into invitations.referrals (referer_id)
  select id from invitations.referrals_count where refer_toadd > 0;
update invitations.referrals_count set refer_toadd = refer_toadd - 1;

insert into invitations.referrals (referer_id)
  select id from invitations.referrals_count where refer_toadd > 0;
update invitations.referrals_count set refer_toadd = refer_toadd - 1;

insert into invitations.referrals (referer_id)
  select id from invitations.referrals_count where refer_toadd > 0;
update invitations.referrals_count set refer_toadd = refer_toadd - 1;

insert into invitations.referrals (referer_id)
  select id from invitations.referrals_count where refer_toadd > 0;
update invitations.referrals_count set refer_toadd = refer_toadd - 1;

insert into invitations.referrals (referer_id)
  select id from invitations.referrals_count where refer_toadd > 0;
update invitations.referrals_count set refer_toadd = refer_toadd - 1;

insert into invitations.referrals (referer_id)
  select id from invitations.referrals_count where refer_toadd > 0;
update invitations.referrals_count set refer_toadd = refer_toadd - 1;

insert into invitations.referrals (referer_id)
  select id from invitations.referrals_count where refer_toadd > 0;
update invitations.referrals_count set refer_toadd = refer_toadd - 1;

insert into invitations.referrals (referer_id)
  select id from invitations.referrals_count where refer_toadd > 0;
update invitations.referrals_count set refer_toadd = refer_toadd - 1;

insert into invitations.referrals (referer_id)
  select id from invitations.referrals_count where refer_toadd > 0;
update invitations.referrals_count set refer_toadd = refer_toadd - 1;

insert into invitations.referrals (referer_id)
  select id from invitations.referrals_count where refer_toadd > 0;
update invitations.referrals_count set refer_toadd = refer_toadd - 1;

insert into invitations.referrals (referer_id)
  select id from invitations.referrals_count where refer_toadd > 0;
update invitations.referrals_count set refer_toadd = refer_toadd - 1;

insert into invitations.referrals (referer_id)
  select id from invitations.referrals_count where refer_toadd > 0;
update invitations.referrals_count set refer_toadd = refer_toadd - 1;

insert into invitations.referrals (referer_id)
  select id from invitations.referrals_count where refer_toadd > 0;
update invitations.referrals_count set refer_toadd = refer_toadd - 1;

insert into invitations.referrals (referer_id)
  select id from invitations.referrals_count where refer_toadd > 0;
update invitations.referrals_count set refer_toadd = refer_toadd - 1;

insert into invitations.referrals (referer_id)
  select id from invitations.referrals_count where refer_toadd > 0;
update invitations.referrals_count set refer_toadd = refer_toadd - 1;

insert into invitations.referrals (referer_id)
  select id from invitations.referrals_count where refer_toadd > 0;
update invitations.referrals_count set refer_toadd = refer_toadd - 1;

insert into invitations.referrals (referer_id)
  select id from invitations.referrals_count where refer_toadd > 0;
update invitations.referrals_count set refer_toadd = refer_toadd - 1;

insert into invitations.referrals (referer_id)
  select id from invitations.referrals_count where refer_toadd > 0;
update invitations.referrals_count set refer_toadd = refer_toadd - 1;

insert into invitations.referrals (referer_id)
  select id from invitations.referrals_count where refer_toadd > 0;
update invitations.referrals_count set refer_toadd = refer_toadd - 1;

insert into invitations.referrals (referer_id)
  select id from invitations.referrals_count where refer_toadd > 0;
update invitations.referrals_count set refer_toadd = refer_toadd - 1;

insert into invitations.referrals (referer_id)
  select id from invitations.referrals_count where refer_toadd > 0;
update invitations.referrals_count set refer_toadd = refer_toadd - 1;

insert into invitations.referrals (referer_id)
  select id from invitations.referrals_count where refer_toadd > 0;
update invitations.referrals_count set refer_toadd = refer_toadd - 1;

insert into invitations.referrals (referer_id)
  select id from invitations.referrals_count where refer_toadd > 0;
update invitations.referrals_count set refer_toadd = refer_toadd - 1;

insert into invitations.referrals (referer_id)
  select id from invitations.referrals_count where refer_toadd > 0;
update invitations.referrals_count set refer_toadd = refer_toadd - 1;

insert into invitations.referrals (referer_id)
  select id from invitations.referrals_count where refer_toadd > 0;
update invitations.referrals_count set refer_toadd = refer_toadd - 1;

insert into invitations.referrals (referer_id)
  select id from invitations.referrals_count where refer_toadd > 0;
update invitations.referrals_count set refer_toadd = refer_toadd - 1;

insert into invitations.referrals (referer_id)
  select id from invitations.referrals_count where refer_toadd > 0;
update invitations.referrals_count set refer_toadd = refer_toadd - 1;

insert into invitations.referrals (referer_id)
  select id from invitations.referrals_count where refer_toadd > 0;
update invitations.referrals_count set refer_toadd = refer_toadd - 1;

insert into invitations.referrals (referer_id)
  select id from invitations.referrals_count where refer_toadd > 0;
update invitations.referrals_count set refer_toadd = refer_toadd - 1;

insert into invitations.referrals (referer_id)
  select id from invitations.referrals_count where refer_toadd > 0;
update invitations.referrals_count set refer_toadd = refer_toadd - 1;

insert into invitations.referrals (referer_id)
  select id from invitations.referrals_count where refer_toadd > 0;
update invitations.referrals_count set refer_toadd = refer_toadd - 1;

insert into invitations.referrals (referer_id)
  select id from invitations.referrals_count where refer_toadd > 0;
update invitations.referrals_count set refer_toadd = refer_toadd - 1;

insert into invitations.referrals (referer_id)
  select id from invitations.referrals_count where refer_toadd > 0;
update invitations.referrals_count set refer_toadd = refer_toadd - 1;

insert into invitations.referrals (referer_id)
  select id from invitations.referrals_count where refer_toadd > 0;
update invitations.referrals_count set refer_toadd = refer_toadd - 1;

insert into invitations.referrals (referer_id)
  select id from invitations.referrals_count where refer_toadd > 0;
update invitations.referrals_count set refer_toadd = refer_toadd - 1;

insert into invitations.referrals (referer_id)
  select id from invitations.referrals_count where refer_toadd > 0;
update invitations.referrals_count set refer_toadd = refer_toadd - 1;

insert into invitations.referrals (referer_id)
  select id from invitations.referrals_count where refer_toadd > 0;
update invitations.referrals_count set refer_toadd = refer_toadd - 1;

insert into invitations.referrals (referer_id)
  select id from invitations.referrals_count where refer_toadd > 0;
update invitations.referrals_count set refer_toadd = refer_toadd - 1;

insert into invitations.referrals (referer_id)
  select id from invitations.referrals_count where refer_toadd > 0;
update invitations.referrals_count set refer_toadd = refer_toadd - 1;

insert into invitations.referrals (referer_id)
  select id from invitations.referrals_count where refer_toadd > 0;
update invitations.referrals_count set refer_toadd = refer_toadd - 1;

insert into invitations.referrals (referer_id)
  select id from invitations.referrals_count where refer_toadd > 0;
update invitations.referrals_count set refer_toadd = refer_toadd - 1;

insert into invitations.referrals (referer_id)
  select id from invitations.referrals_count where refer_toadd > 0;
update invitations.referrals_count set refer_toadd = refer_toadd - 1;

insert into invitations.referrals (referer_id)
  select id from invitations.referrals_count where refer_toadd > 0;
update invitations.referrals_count set refer_toadd = refer_toadd - 1;

insert into invitations.referrals (referer_id)
  select id from invitations.referrals_count where refer_toadd > 0;
update invitations.referrals_count set refer_toadd = refer_toadd - 1;

insert into invitations.referrals (referer_id)
  select id from invitations.referrals_count where refer_toadd > 0;
update invitations.referrals_count set refer_toadd = refer_toadd - 1;

insert into invitations.referrals (referer_id)
  select id from invitations.referrals_count where refer_toadd > 0;
update invitations.referrals_count set refer_toadd = refer_toadd - 1;

insert into invitations.referrals (referer_id)
  select id from invitations.referrals_count where refer_toadd > 0;
update invitations.referrals_count set refer_toadd = refer_toadd - 1;

insert into invitations.referrals (referer_id)
  select id from invitations.referrals_count where refer_toadd > 0;
update invitations.referrals_count set refer_toadd = refer_toadd - 1;

insert into invitations.referrals (referer_id)
  select id from invitations.referrals_count where refer_toadd > 0;
update invitations.referrals_count set refer_toadd = refer_toadd - 1;

insert into invitations.referrals (referer_id)
  select id from invitations.referrals_count where refer_toadd > 0;
update invitations.referrals_count set refer_toadd = refer_toadd - 1;

insert into invitations.referrals (referer_id)
  select id from invitations.referrals_count where refer_toadd > 0;
update invitations.referrals_count set refer_toadd = refer_toadd - 1;

insert into invitations.referrals (referer_id)
  select id from invitations.referrals_count where refer_toadd > 0;
update invitations.referrals_count set refer_toadd = refer_toadd - 1;

insert into invitations.referrals (referer_id)
  select id from invitations.referrals_count where refer_toadd > 0;
update invitations.referrals_count set refer_toadd = refer_toadd - 1;

insert into invitations.referrals (referer_id)
  select id from invitations.referrals_count where refer_toadd > 0;
update invitations.referrals_count set refer_toadd = refer_toadd - 1;

insert into invitations.referrals (referer_id)
  select id from invitations.referrals_count where refer_toadd > 0;
update invitations.referrals_count set refer_toadd = refer_toadd - 1;

insert into invitations.referrals (referer_id)
  select id from invitations.referrals_count where refer_toadd > 0;
update invitations.referrals_count set refer_toadd = refer_toadd - 1;

insert into invitations.referrals (referer_id)
  select id from invitations.referrals_count where refer_toadd > 0;
update invitations.referrals_count set refer_toadd = refer_toadd - 1;

UPDATE referrals SET date_paid = date_accepted WHERE date_accepted IS NOT NULL;