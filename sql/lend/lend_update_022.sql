ALTER TABLE applications
ADD COLUMN vesta_loan_id VARCHAR(255) DEFAULT NULL AFTER `state`;

CREATE TABLE version_vesta_loan_id (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  application_id BIGINT(20) NOT NULL,
  version INT(11) NOT NULL,
  created_at DATETIME NOT NULL,
  deleted TINYINT(1) NOT NULL,
  value VARCHAR(255) NOT NULL,
  PRIMARY KEY (id),
  INDEX version_vesta_loan_id_created_at (created_at),
  CONSTRAINT fkey_version_application_id FOREIGN KEY (application_id) REFERENCES applications (id)
)
ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

INSERT INTO schema_log (schema_version, date) VALUES (22, NOW());