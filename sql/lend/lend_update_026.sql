CREATE TABLE mortgage_application_voyager_records (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  application_id BIGINT(20) NOT NULL,
  voyager_record_id BIGINT(20) NOT NULL,
  voyager_type VARCHAR(255) NOT NULL DEFAULT '',
  created_at DATETIME NOT NULL,
  PRIMARY KEY(id),
  CONSTRAINT fkey_mortgage_application_voyager_records_application_id 
  FOREIGN KEY (application_id) REFERENCES applications(id),
  CONSTRAINT fkey_mortgage_application_voyager_records_id 
  FOREIGN KEY (voyager_record_id) REFERENCES voyager_records (id),
  INDEX mavr_applcation_id_voyager_record_id (application_id, voyager_record_id)
) 
ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
INSERT INTO schema_log (schema_version, date) VALUES (26, NOW());