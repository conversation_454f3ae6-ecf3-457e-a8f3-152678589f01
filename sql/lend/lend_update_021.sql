DROP TABLE IF EXISTS version_vesta_webhooks_state;
DROP TABLE IF EXISTS vesta_webhooks;

CREATE TABLE vesta_webhooks (
 id BIGINT(20) NOT NULL AUTO_INCREMENT,
 application_id BIGINT(20) DEFAULT NULL,
 type VARCHAR(255) DEFAULT NULL,
 state VARCHAR(255) NOT NULL,
 payload TEXT NOT NULL,
 created_at DATETIME NOT NULL,
 vesta_timestamp DATETIME DEFAULT NULL,
 queued_vesta_operation_id BIGINT(20) DEFAULT NULL,
 PRIMARY KEY(id),
 CONSTRAINT vesta_webhooks_application_id FOREIGN KEY (application_id) REFERENCES applications(id),
 CONSTRAINT vesta_webhooks_queued_vesta_operation_id FOREIGN KEY (queued_vesta_operation_id) REFERENCES queued_vesta_operations(id)
 )
 ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

 CREATE TABLE version_vesta_webhooks_state (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  vesta_webhook_id BIGINT(20) NOT NULL,
  version INT(11) NOT NULL,
  created_at DATETIME NOT NULL,
  deleted TINYINT(1) NOT NULL,
  value VARCHAR(255) NOT NULL,
  PRIMARY KEY (id),
  INDEX version_vesta_webhooks_state_created_at (created_at),
  CONSTRAINT fkey_version_vesta_webhook_id FOREIGN KEY (vesta_webhook_id) REFERENCES vesta_webhooks (id)
 )
  ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

INSERT INTO schema_log (schema_version, date) VALUES (21, NOW());