create table applications (
    id               BIGINT(20)     NOT NULL auto_increment,
    version          INT(11)        NOT NULL,
    state            VARCHAR(255)   NOT NULL,
    created_at       DATETIME       NOT NULL,
    primary key (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

create table version_application_state (
    id               BIGINT(20)     NOT NULL auto_increment,
    application_id   BIGINT(20)     NOT NULL,
    version          INT(11)        NOT NULL,
    created_at       DATETIME       NOT NULL,
    deleted          TINYINT(1)     NOT NULL,
    value            VARCHAR(255)   NOT NULL,
    primary key (id),
    CONSTRAINT fkey_version_application_state FOREIGN KEY (application_id) REFERENCES applications (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

create table application_users (
    id               BIGINT(20)     NOT NULL auto_increment,
    application_id   BIGINT(20)     NOT NULL,
    userid           BIGINT(20)     NOT NULL,
    state            VARCHAR(255)   NOT NULL,
    type             VARCHAR(255)   NOT NULL,
    created_at       DATETIME       NOT NULL,
    primary key (id),
    constraint foreign key (application_id) references applications (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

create table version_application_user_state (
    id                      BIGINT(20)     NOT NULL auto_increment,
    application_user_id     BIGINT(20)     NOT NULL,
    version                 INT(11)        NOT NULL,
    created_at              DATETIME       NOT NULL,
    deleted                 TINYINT(1)     NOT NULL,
    value                   VARCHAR(255)   NOT NULL,
    primary key (id),
    CONSTRAINT fkey_version_application_users_state FOREIGN KEY (application_user_id) REFERENCES application_users (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

INSERT INTO schema_log (schema_version, date) VALUES (7, NOW());
