CREATE TABLE queued_vesta_applications (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  application_id BIGINT(20) NOT NULL,
  created_at DATETIME NOT NULL,
  polled_time DATETIME DEFAULT NULL,
  sent_time D<PERSON><PERSON>IM<PERSON> DEFAULT NULL,
  ignored_at DATETIME DEFAULT NULL,
  error_flag TINYINT(1) NOT NULL DEFAULT 0,
  priority INT(11) NOT NULL DEFAULT 0,
  PRIMARY KEY(id),
  CONSTRAINT queued_vesta_applications_application_id FOREIGN KEY (application_id) REFERENCES applications (id),
  INDEX queued_application_syncs_application_id (application_id),
  INDEX queued_application_syncs_created_id (created_at),
  INDEX queued_application_syncs_sent_polled_error_id (sent_time, polled_time, error_flag),
  INDEX queued_application_syncs_polled_error_id (polled_time, error_flag),
  INDEX queued_application_syncs_error_id (error_flag)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

INSERT INTO schema_log (schema_version, date) VALUES (9, NOW());
