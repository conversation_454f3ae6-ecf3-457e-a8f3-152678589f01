-- Add proper indexes to voyager_records table for optimal query performance

-- Single column indexes
CREATE INDEX idx_voyager_records_user_id ON voyager_records (user_id);
CREATE INDEX idx_voyager_records_created_at ON voyager_records (created_at);
CREATE INDEX idx_voyager_records_state ON voyager_records (state);
CREATE INDEX idx_voyager_records_voyage_type ON voyager_records (voyage_type);

-- Composite indexes for common query patterns
CREATE INDEX idx_voyager_records_user_created ON voyager_records (user_id, created_at);
CREATE INDEX idx_voyager_records_user_state ON voyager_records (user_id, state);

INSERT INTO schema_log (schema_version, date) VALUES (28, NOW());
