CREATE TABLE queued_vesta_operations (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  application_id BIGINT(20) NOT NULL,
  queued_vesta_application_id BIGINT(20) DEFAULT NULL,
  created_at DATETIME NOT NULL,
  poll_at DATETIME NOT NULL,
  state VARCHAR(255) NOT NULL,
  type VARCHAR(255) NOT NULL,
  PRIMARY KEY(id),
  CONSTRAINT queued_vesta_operations_application_id
      FOREIGN KEY (application_id) REFERENCES applications (id),
  CONSTRAINT queued_vesta_operations_queued_vesta_application_id
      FOREIGN KEY (queued_vesta_application_id) REFERENCES queued_vesta_applications (id),
  INDEX queued_vesta_operations_created_id (created_at),
  INDEX queued_vesta_operations_polled_id (poll_at),
  INDEX queued_vesta_operations_state (state)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE version_queued_vesta_operations_state (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  queued_vesta_operation_id BIGINT(20) NOT NULL,
  version INT(11) NOT NULL,
  created_at DATETIME NOT NULL,
  deleted TINYINT(1) NOT NULL,
  value VARCHAR(255) NOT NULL,
  PRIMARY KEY (id),
  INDEX version_queued_vesta_operations_state_created_at (created_at),
  CONSTRAINT fkey_version_queued_vesta_operations_state FOREIGN KEY (queued_vesta_operation_id) REFERENCES queued_vesta_operations (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

ALTER TABLE application_data_points
ADD COLUMN queued_vesta_operation_id BIGINT(20) DEFAULT NULL AFTER application_id,
ADD CONSTRAINT FOREIGN KEY (queued_vesta_operation_id) references queued_vesta_operations (id);

INSERT INTO schema_log (schema_version, date) VALUES (10, NOW());