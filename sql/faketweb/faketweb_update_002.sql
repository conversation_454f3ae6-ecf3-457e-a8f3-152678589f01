CREATE TABLE `outgoing_fix_messages` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `time` datetime NOT NULL,
  `beginstring` char(8) NOT NULL,
  `sendercompid` varchar(64) NOT NULL,
  `sendersubid` varchar(64) NOT NULL,
  `senderlocid` varchar(64) NOT NULL,
  `targetcompid` varchar(64) NOT NULL,
  `targetsubid` varchar(64) NOT NULL,
  `targetlocid` varchar(64) NOT NULL,
  `session_qualifier` varchar(64) NOT NULL,
  `text` text NOT NULL,
  PRIMARY KEY (`id`),
  KEY `time` (`time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `fix_events` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `time` datetime NOT NULL,
  `beginstring` char(8) NOT NULL,
  `sendercompid` varchar(64) NOT NULL,
  `sendersubid` varchar(64) NOT NULL,
  `senderlocid` varchar(64) NOT NULL,
  `targetcompid` varchar(64) NOT NULL,
  `targetsubid` varchar(64) NOT NULL,
  `targetlocid` varchar(64) NOT NULL,
  `session_qualifier` varchar(64) DEFAULT NULL,
  `text` text NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `incoming_fix_messages` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `time` datetime NOT NULL,
  `beginstring` char(8) NOT NULL,
  `sendercompid` varchar(64) NOT NULL,
  `sendersubid` varchar(64) NOT NULL,
  `senderlocid` varchar(64) NOT NULL,
  `targetcompid` varchar(64) NOT NULL,
  `targetsubid` varchar(64) NOT NULL,
  `targetlocid` varchar(64) NOT NULL,
  `session_qualifier` varchar(64) NOT NULL,
  `text` text NOT NULL,
  PRIMARY KEY (`id`),
  KEY `time` (`time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO schema_log (schema_version, date) VALUES (2, now());

