CREATE TABLE `trade_web_orders` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `version` BIGINT(20) NOT NULL DEFAULT 0,
  `router_orderid` BIGINT(20) NOT NULL,
  `router_order_sequence_number` BIGINT(20) NOT NULL DEFAULT 0,
  `rbc_account_number` VARCHAR(255) NOT NULL,
  `capacity` VARCHAR(63) NOT NULL,
  `instrumentid` BIGINT(20) NOT NULL,
  `action` VARCHAR(255) NOT NULL,
  `order_quantity` DECIMAL(12,4) NOT NULL,
  `target_price` DECIMAL(12,4) DEFAULT NULL,
  `open_state` VARCHAR(255) NOT NULL,
  `fill_state` VARCHAR(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `trade_web_orders_router_orderid` (`router_orderid`),
  KEY `trade_web_orders_rbc_account_number` (`rbc_account_number`),
  <PERSON><PERSON>Y `trade_web_orders_open_state` (`open_state`),
  <PERSON><PERSON><PERSON> `trade_web_orders_router_orderid_router_order_sequence_number` (`router_orderid`,`router_order_sequence_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `trade_web_order_events` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `type` VARCHAR(255) NOT NULL,
  `version` BIGINT(20) NOT NULL DEFAULT 0,
  `sequence_number` INT(11) NOT NULL,
  `trade_web_order_id` BIGINT(20) NOT NULL,
  `event_time` DATETIME NOT NULL,
  `polled_time` DATETIME DEFAULT NULL,
  `sent_time` DATETIME DEFAULT NULL,
  `error_flag` TINYINT(1) NOT NULL DEFAULT 0,
  `exec_id` VARCHAR(255) DEFAULT NULL,
  `fix_transact_time` DATETIME DEFAULT NULL,
  `fix_transact_time_millis` VARCHAR(255) DEFAULT NULL,
  `client_orderid` VARCHAR(255) DEFAULT NULL,
  `fill_price` DECIMAL(12,4) DEFAULT NULL,
  `fill_quantity` DECIMAL(12,4) DEFAULT NULL,
  `accrued_interest_amount` DECIMAL(12,4) DEFAULT NULL,
  `yield_to_maturity` DECIMAL(8,6) DEFAULT NULL,
  `settlement_date` DATE DEFAULT NULL,
  `reason` VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `trade_web_order_id` (`trade_web_order_id`),
  KEY `trade_web_order_events_polled_time_error_flag` (`polled_time`,`error_flag`),
  KEY `trade_web_order_events_sent_time_polled_time_error_flag` (`sent_time`,`polled_time`,`error_flag`),
  KEY `trade_web_order_events_fix_transact_time` (`fix_transact_time`),
  CONSTRAINT `trade_web_order_events_trade_web_order_id` FOREIGN KEY (`trade_web_order_id`) REFERENCES `trade_web_orders` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

INSERT INTO schema_log (schema_version, DATE) VALUES (3, now());
