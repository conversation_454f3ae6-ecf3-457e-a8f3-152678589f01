CREATE TABLE `queued_trade_web_order_to_treasury_bond_trace_messages` (
  `id` BIGINT(20) unsigned NOT NULL AUTO_INCREMENT,
  `order_event_id` BIGINT(20) NOT NULL UNIQUE,
  `created_at` DATETIME NOT NULL,
  `polled_time` <PERSON><PERSON><PERSON>IME DEFAULT NULL,
  `sent_time` <PERSON><PERSON>ETIME DEFAULT NULL,
  `ignored_at` DATETIME DEFAULT NULL,
  `error_flag` TINYINT(1) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT FOREIGN KEY (`order_event_id`) REFERENCES `trade_web_order_events` (`id`),
  UNIQUE (`order_event_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

INSERT INTO schema_log (schema_version, date) VALUES (7, now());