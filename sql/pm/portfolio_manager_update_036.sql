alter table portfolios
    change column  inception_date inception_date datetime not null;

update portfolios set inception_date = creation_date;

create table temp_inception_date (
  portfolio_id bigint(20) not null,
  inception_date datetime not null);

insert into temp_inception_date (portfolio_id, inception_date)
  select t.portfolio_id,max(date) from trades t
  where t.type = 'reset-portfolio' group by t.portfolio_id;

update portfolios p, temp_inception_date i set p.inception_date = i.inception_date
  where p.id = i.portfolio_id;

drop table temp_inception_date;

insert into schema_log (schema_version, date) values (36, now());