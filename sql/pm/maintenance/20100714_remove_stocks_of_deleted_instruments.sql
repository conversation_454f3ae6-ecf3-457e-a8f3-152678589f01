delete orders
    from orders, deleted_instrumentids
    where orders.state = 'expired'
    and orders.instrumentid = deleted_instrumentids.instrumentid;
delete symbols
    from symbols, stocks, deleted_instrumentids
    where symbols.stock_id = stocks.id
    and stocks.instrumentid = deleted_instrumentids.instrumentid;
delete stocks
    from stocks, deleted_instrumentids
    where stocks.instrumentid = deleted_instrumentids.instrumentid;

update stocks set symbol = concat(symbol, 'OLD') where instrumentid in (
    48497,
    65314,
    69584
    );
