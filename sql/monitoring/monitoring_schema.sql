CREATE TABLE `queries` (
  `id` int(11) NOT NULL auto_increment,
  `previous_id` int(11) default NULL,
  `timestamp` datetime NOT NULL,
  `service` varchar(8) NOT NULL,
  `processed` int(11) NOT NULL,
  `slow` int(11) NOT NULL,
  `failed` int(11) NOT NULL,
  PRIMARY KEY  (`id`),
  KEY `service_timestamp` (`service`,`timestamp`)
) ENGINE=InnoDB;

CREATE TABLE stats_int (
  `id` int(11) NOT NULL auto_increment,
  `timestamp` datetime NOT NULL,
  `name` varchar(255) NOT NULL,
  `value` int(11) NOT NULL,
  PRIMARY KEY  (`id`),
  KEY `name_timestamp` (`name`,`timestamp`)
) ENGINE=InnoDB;
