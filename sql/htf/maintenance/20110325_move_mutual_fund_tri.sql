begin;

select concat("FROM ",109224," TO ", 118050);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (109224,114132,118085,102054,104202,102508,110198,128841,110701,118052,109513,86159,107231,116007,117886,104274,106755,105456,115586,115920,112921,112374,100158,112021,108864,107546,117106,100702,101478,103694,114783,115264,100153,114763,115113,106449,104273,102252,109673,117628,108035,99855,100819,114487,116846,111667,128839,117888,118385,101655,116539,111758,110287,116205,106248,105553,118986,118168,102514,101371,107354,106118,114469,117676,100030,101765,103974,105182,107108,100989,102297,118187,103284,115066,102598,110876,85965,104275,101831,112551,101646,109426,108763,115261,116680,109715,116037,107122,116537,86183,106376,101075,109543,86184,111124,105236,100171,86185,107618,112929,102210,101296,101378,108112,103085,110548,107755,85966,101031,106753,115519,113883,110510,105895,86186,108752,110968,109539,109677,115827,117298,106161,109547,110059,117103,101173,111495,116349,107286,101637,108061,107145,116599,102311,103036,109981,114382,103906,113561,117054,99820,108018,114527,101021,116087,116204,100198,105273,102949,115102,99564,101719,117454,108940,112906,117885,113850,110874,111825,106124,112407,107491,99819,118104,102915,99952,113154,111359,116177,105327,117893,107631,115231,102776,117682,117755,100077,109225,107901,112597,112375,103136,113571,106012,102215,113499,109336,107280,102350,100476,86050,117625,113663,102479,113651,118953,106054,100304,102199,118050);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (109224,114132,118085,102054,104202,102508,110198,128841,110701,118052,109513,86159,107231,116007,117886,104274,106755,105456,115586,115920,112921,112374,100158,112021,108864,107546,117106,100702,101478,103694,114783,115264,100153,114763,115113,106449,104273,102252,109673,117628,108035,99855,100819,114487,116846,111667,128839,117888,118385,101655,116539,111758,110287,116205,106248,105553,118986,118168,102514,101371,107354,106118,114469,117676,100030,101765,103974,105182,107108,100989,102297,118187,103284,115066,102598,110876,85965,104275,101831,112551,101646,109426,108763,115261,116680,109715,116037,107122,116537,86183,106376,101075,109543,86184,111124,105236,100171,86185,107618,112929,102210,101296,101378,108112,103085,110548,107755,85966,101031,106753,115519,113883,110510,105895,86186,108752,110968,109539,109677,115827,117298,106161,109547,110059,117103,101173,111495,116349,107286,101637,108061,107145,116599,102311,103036,109981,114382,103906,113561,117054,99820,108018,114527,101021,116087,116204,100198,105273,102949,115102,99564,101719,117454,108940,112906,117885,113850,110874,111825,106124,112407,107491,99819,118104,102915,99952,113154,111359,116177,105327,117893,107631,115231,102776,117682,117755,100077,109225,107901,112597,112375,103136,113571,106012,102215,113499,109336,107280,102350,100476,86050,117625,113663,102479,113651,118953,106054,100304,102199,118050);

delete quotes.* 
from quotes where instrumentid in (109224,114132,118085,102054,104202,102508,110198,128841,110701,118052,109513,86159,107231,116007,117886,104274,106755,105456,115586,115920,112921,112374,100158,112021,108864,107546,117106,100702,101478,103694,114783,115264,100153,114763,115113,106449,104273,102252,109673,117628,108035,99855,100819,114487,116846,111667,128839,117888,118385,101655,116539,111758,110287,116205,106248,105553,118986,118168,102514,101371,107354,106118,114469,117676,100030,101765,103974,105182,107108,100989,102297,118187,103284,115066,102598,110876,85965,104275,101831,112551,101646,109426,108763,115261,116680,109715,116037,107122,116537,86183,106376,101075,109543,86184,111124,105236,100171,86185,107618,112929,102210,101296,101378,108112,103085,110548,107755,85966,101031,106753,115519,113883,110510,105895,86186,108752,110968,109539,109677,115827,117298,106161,109547,110059,117103,101173,111495,116349,107286,101637,108061,107145,116599,102311,103036,109981,114382,103906,113561,117054,99820,108018,114527,101021,116087,116204,100198,105273,102949,115102,99564,101719,117454,108940,112906,117885,113850,110874,111825,106124,112407,107491,99819,118104,102915,99952,113154,111359,116177,105327,117893,107631,115231,102776,117682,117755,100077,109225,107901,112597,112375,103136,113571,106012,102215,113499,109336,107280,102350,100476,86050,117625,113663,102479,113651,118953,106054,100304,102199,118050);

commit;



begin;

select concat("FROM ",109417," TO ", 111968);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (109417,102220,108085,116232,110887,110886,115105,111099,111666,100166,107485,112619,112983,102873,85967,108109,107421,111251,110003,117786,100740,108333,110875,109737,109133,86217,112176,108270,115781,109568,104992,101209,111222,106381,100322,112553,108899,105518,113385,105935,113013,114235,102791,99407,100624,108295,100617,104076,109592,102386,108620,102924,115042,111590,105508,109995,109341,110001,110877,111415,103534,102461,113254,101024,114639,117184,105536,110873,107426,117482,111356,106101,103162,107160,110593,103549,103796,116092,111121,86051,110549,112330,103474,107290,112223,102223,100983,105990,112699,99818,114674,108709,102383,113830,117633,114498,111203,115023,112633,100327,118056,113315,105139,110761,101400,111404,113609,106218,106075,116993,111513,105278,118527,107628,119135,111806,112408,99701,112410,111831,102533,100242,112881,112884,113988,99520,86052,105439,105675,117674,115921,106695,107464,106476,104808,116242,101261,111762,108026,103280,103591,110913,116585,108636,101025,103287,101322,108012,102950,112271,118174,104607,102512,111060,99967,108706,99611,104066,119184,85903,109811,109074,111860,104797,100601,105065,116403,113221,117576,116484,86265,112883,102465,106425,105425,101810,108997,101357,104512,104514,109214,100968,108225,106346,111362,103354,107902,103379,99903,102716,101109,117042,114395,86072,103382,105494,86266,114172,102073,111968);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (109417,102220,108085,116232,110887,110886,115105,111099,111666,100166,107485,112619,112983,102873,85967,108109,107421,111251,110003,117786,100740,108333,110875,109737,109133,86217,112176,108270,115781,109568,104992,101209,111222,106381,100322,112553,108899,105518,113385,105935,113013,114235,102791,99407,100624,108295,100617,104076,109592,102386,108620,102924,115042,111590,105508,109995,109341,110001,110877,111415,103534,102461,113254,101024,114639,117184,105536,110873,107426,117482,111356,106101,103162,107160,110593,103549,103796,116092,111121,86051,110549,112330,103474,107290,112223,102223,100983,105990,112699,99818,114674,108709,102383,113830,117633,114498,111203,115023,112633,100327,118056,113315,105139,110761,101400,111404,113609,106218,106075,116993,111513,105278,118527,107628,119135,111806,112408,99701,112410,111831,102533,100242,112881,112884,113988,99520,86052,105439,105675,117674,115921,106695,107464,106476,104808,116242,101261,111762,108026,103280,103591,110913,116585,108636,101025,103287,101322,108012,102950,112271,118174,104607,102512,111060,99967,108706,99611,104066,119184,85903,109811,109074,111860,104797,100601,105065,116403,113221,117576,116484,86265,112883,102465,106425,105425,101810,108997,101357,104512,104514,109214,100968,108225,106346,111362,103354,107902,103379,99903,102716,101109,117042,114395,86072,103382,105494,86266,114172,102073,111968);

delete quotes.* 
from quotes where instrumentid in (109417,102220,108085,116232,110887,110886,115105,111099,111666,100166,107485,112619,112983,102873,85967,108109,107421,111251,110003,117786,100740,108333,110875,109737,109133,86217,112176,108270,115781,109568,104992,101209,111222,106381,100322,112553,108899,105518,113385,105935,113013,114235,102791,99407,100624,108295,100617,104076,109592,102386,108620,102924,115042,111590,105508,109995,109341,110001,110877,111415,103534,102461,113254,101024,114639,117184,105536,110873,107426,117482,111356,106101,103162,107160,110593,103549,103796,116092,111121,86051,110549,112330,103474,107290,112223,102223,100983,105990,112699,99818,114674,108709,102383,113830,117633,114498,111203,115023,112633,100327,118056,113315,105139,110761,101400,111404,113609,106218,106075,116993,111513,105278,118527,107628,119135,111806,112408,99701,112410,111831,102533,100242,112881,112884,113988,99520,86052,105439,105675,117674,115921,106695,107464,106476,104808,116242,101261,111762,108026,103280,103591,110913,116585,108636,101025,103287,101322,108012,102950,112271,118174,104607,102512,111060,99967,108706,99611,104066,119184,85903,109811,109074,111860,104797,100601,105065,116403,113221,117576,116484,86265,112883,102465,106425,105425,101810,108997,101357,104512,104514,109214,100968,108225,106346,111362,103354,107902,103379,99903,102716,101109,117042,114395,86072,103382,105494,86266,114172,102073,111968);

commit;



begin;

select concat("FROM ",113849," TO ", 102534);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (113849,105995,105814,111326,113530,104624,105535,111010,104031,99942,110743,108492,109821,110677,115875,113238,103698,112504,116091,117693,106959,101370,117316,101436,110683,114392,111910,105342,107096,111522,109061,107383,117070,100901,101946,112270,102984,113225,107951,112846,111224,114754,109403,112206,106347,117301,114360,105836,100331,113642,113644,85943,101642,116367,103396,114755,103393,113741,105837,113746,106351,102096,104214,85993,119271,86073,101345,105123,113753,105130,117201,115325,110752,111856,109899,107991,86291,115635,115412,104215,113048,100435,103575,99633,117358,112170,109526,108923,103811,113917,111607,116546,115234,107113,108463,113257,109567,117502,113607,111400,112222,85994,101372,103643,108907,85904,102355,103100,117200,103950,106263,110870,111853,105566,114861,105559,100518,103134,111057,109488,109892,102301,116110,100446,109557,109670,104775,112820,86292,101307,102685,101469,105046,108048,103318,101668,107940,85905,102214,105335,111857,114264,108922,116231,105609,109226,107378,107738,113261,101572,117583,106308,110682,100608,99947,117835,111510,101337,108001,86293,102539,108519,101298,111315,117836,110069,107347,106307,104346,116093,116724,102882,100786,100220,107057,99717,101605,86314,111428,110807,119428,117233,109129,105424,116512,118274,118642,113155,106299,101156,103315,105842,100616,113147,106863,101206,86315,86106,105520,102534);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (113849,105995,105814,111326,113530,104624,105535,111010,104031,99942,110743,108492,109821,110677,115875,113238,103698,112504,116091,117693,106959,101370,117316,101436,110683,114392,111910,105342,107096,111522,109061,107383,117070,100901,101946,112270,102984,113225,107951,112846,111224,114754,109403,112206,106347,117301,114360,105836,100331,113642,113644,85943,101642,116367,103396,114755,103393,113741,105837,113746,106351,102096,104214,85993,119271,86073,101345,105123,113753,105130,117201,115325,110752,111856,109899,107991,86291,115635,115412,104215,113048,100435,103575,99633,117358,112170,109526,108923,103811,113917,111607,116546,115234,107113,108463,113257,109567,117502,113607,111400,112222,85994,101372,103643,108907,85904,102355,103100,117200,103950,106263,110870,111853,105566,114861,105559,100518,103134,111057,109488,109892,102301,116110,100446,109557,109670,104775,112820,86292,101307,102685,101469,105046,108048,103318,101668,107940,85905,102214,105335,111857,114264,108922,116231,105609,109226,107378,107738,113261,101572,117583,106308,110682,100608,99947,117835,111510,101337,108001,86293,102539,108519,101298,111315,117836,110069,107347,106307,104346,116093,116724,102882,100786,100220,107057,99717,101605,86314,111428,110807,119428,117233,109129,105424,116512,118274,118642,113155,106299,101156,103315,105842,100616,113147,106863,101206,86315,86106,105520,102534);

delete quotes.* 
from quotes where instrumentid in (113849,105995,105814,111326,113530,104624,105535,111010,104031,99942,110743,108492,109821,110677,115875,113238,103698,112504,116091,117693,106959,101370,117316,101436,110683,114392,111910,105342,107096,111522,109061,107383,117070,100901,101946,112270,102984,113225,107951,112846,111224,114754,109403,112206,106347,117301,114360,105836,100331,113642,113644,85943,101642,116367,103396,114755,103393,113741,105837,113746,106351,102096,104214,85993,119271,86073,101345,105123,113753,105130,117201,115325,110752,111856,109899,107991,86291,115635,115412,104215,113048,100435,103575,99633,117358,112170,109526,108923,103811,113917,111607,116546,115234,107113,108463,113257,109567,117502,113607,111400,112222,85994,101372,103643,108907,85904,102355,103100,117200,103950,106263,110870,111853,105566,114861,105559,100518,103134,111057,109488,109892,102301,116110,100446,109557,109670,104775,112820,86292,101307,102685,101469,105046,108048,103318,101668,107940,85905,102214,105335,111857,114264,108922,116231,105609,109226,107378,107738,113261,101572,117583,106308,110682,100608,99947,117835,111510,101337,108001,86293,102539,108519,101298,111315,117836,110069,107347,106307,104346,116093,116724,102882,100786,100220,107057,99717,101605,86314,111428,110807,119428,117233,109129,105424,116512,118274,118642,113155,106299,101156,103315,105842,100616,113147,106863,101206,86315,86106,105520,102534);

commit;



begin;

select concat("FROM ",107058," TO ", 114307);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (107058,112567,112413,110213,116386,111708,111068,101086,112279,105383,110453,106426,100611,109482,107988,115527,115884,116645,107506,110079,99940,107676,111076,110759,111262,109478,103383,110092,116450,104541,118745,110382,102348,101476,114588,107615,100051,106302,117368,115778,86316,115380,105246,113081,111703,116171,115206,114937,116547,104380,102867,105873,113075,104584,102966,111641,118160,113415,114241,116549,86350,103559,104267,113466,106404,103454,104504,102363,102765,101704,109524,113847,108081,108906,110705,116548,107027,111704,108935,110542,111223,106129,113835,85995,112169,107722,100140,118147,111397,112974,115806,117371,107956,109976,107996,113580,110703,107024,102418,107234,86107,106708,105300,108488,117366,101650,108805,99987,111065,110023,104921,109696,102155,102244,107699,114909,100319,103421,112888,110138,108674,116596,103808,103608,111523,109939,99894,105580,99868,100010,106990,114570,105268,113045,101058,116320,105127,110570,116153,102094,105572,116154,110571,106424,100083,117403,103925,102440,110739,104388,109024,100175,110916,113879,115531,109037,100383,103889,102951,109917,103843,105087,105249,115336,115094,104807,105644,116888,109970,110389,113150,105166,106379,116591,114297,105584,108196,110809,86022,109708,106132,114286,100996,106682,103776,102056,103289,115079,103767,106613,107235,115780,109672,115145,100264,112922,106890,101175,107216,114307);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (107058,112567,112413,110213,116386,111708,111068,101086,112279,105383,110453,106426,100611,109482,107988,115527,115884,116645,107506,110079,99940,107676,111076,110759,111262,109478,103383,110092,116450,104541,118745,110382,102348,101476,114588,107615,100051,106302,117368,115778,86316,115380,105246,113081,111703,116171,115206,114937,116547,104380,102867,105873,113075,104584,102966,111641,118160,113415,114241,116549,86350,103559,104267,113466,106404,103454,104504,102363,102765,101704,109524,113847,108081,108906,110705,116548,107027,111704,108935,110542,111223,106129,113835,85995,112169,107722,100140,118147,111397,112974,115806,117371,107956,109976,107996,113580,110703,107024,102418,107234,86107,106708,105300,108488,117366,101650,108805,99987,111065,110023,104921,109696,102155,102244,107699,114909,100319,103421,112888,110138,108674,116596,103808,103608,111523,109939,99894,105580,99868,100010,106990,114570,105268,113045,101058,116320,105127,110570,116153,102094,105572,116154,110571,106424,100083,117403,103925,102440,110739,104388,109024,100175,110916,113879,115531,109037,100383,103889,102951,109917,103843,105087,105249,115336,115094,104807,105644,116888,109970,110389,113150,105166,106379,116591,114297,105584,108196,110809,86022,109708,106132,114286,100996,106682,103776,102056,103289,115079,103767,106613,107235,115780,109672,115145,100264,112922,106890,101175,107216,114307);

delete quotes.* 
from quotes where instrumentid in (107058,112567,112413,110213,116386,111708,111068,101086,112279,105383,110453,106426,100611,109482,107988,115527,115884,116645,107506,110079,99940,107676,111076,110759,111262,109478,103383,110092,116450,104541,118745,110382,102348,101476,114588,107615,100051,106302,117368,115778,86316,115380,105246,113081,111703,116171,115206,114937,116547,104380,102867,105873,113075,104584,102966,111641,118160,113415,114241,116549,86350,103559,104267,113466,106404,103454,104504,102363,102765,101704,109524,113847,108081,108906,110705,116548,107027,111704,108935,110542,111223,106129,113835,85995,112169,107722,100140,118147,111397,112974,115806,117371,107956,109976,107996,113580,110703,107024,102418,107234,86107,106708,105300,108488,117366,101650,108805,99987,111065,110023,104921,109696,102155,102244,107699,114909,100319,103421,112888,110138,108674,116596,103808,103608,111523,109939,99894,105580,99868,100010,106990,114570,105268,113045,101058,116320,105127,110570,116153,102094,105572,116154,110571,106424,100083,117403,103925,102440,110739,104388,109024,100175,110916,113879,115531,109037,100383,103889,102951,109917,103843,105087,105249,115336,115094,104807,105644,116888,109970,110389,113150,105166,106379,116591,114297,105584,108196,110809,86022,109708,106132,114286,100996,106682,103776,102056,103289,115079,103767,106613,107235,115780,109672,115145,100264,112922,106890,101175,107216,114307);

commit;



begin;

select concat("FROM ",109967," TO ", 116551);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (109967,86379,115779,104094,102543,113604,113402,118167,112496,116243,110065,118042,113606,119438,114933,111021,109698,86380,118914,113840,103133,119115,109548,113605,101200,106699,107186,111928,114494,111036,109694,115177,102468,112339,116208,101101,100951,116479,116606,104126,106456,116832,108287,110058,117837,116795,112849,112677,111492,102332,111493,108288,111314,115402,100713,114361,103293,102961,101084,107730,114122,109118,111430,86408,118966,113676,100244,110483,111398,106614,106913,107300,113896,105169,113897,107033,115565,113263,107889,114682,100912,103832,101394,104105,109335,116252,105426,103955,102181,86409,107314,104391,86145,101677,107148,111355,111254,102819,118253,111431,110266,102136,85877,109818,113056,110748,111437,104136,112778,114621,112265,101305,105522,104249,115465,105422,104255,109636,114737,104517,100420,115096,116533,110444,115332,103929,119010,100840,104155,116811,99943,107962,105531,114490,106200,115184,117315,105433,110940,119206,86146,116383,102692,101093,111993,116176,105918,99393,101488,108358,101459,105968,110616,109213,100148,99762,102509,108136,118811,103537,102484,108332,107218,115052,107441,108741,103781,86427,107509,115998,103813,116365,108106,99495,117700,117840,117304,86428,107339,106884,116070,103309,108362,107835,111325,116550,86023,109549,86172,106903,102662,105302,119161,107106,116981,114941,86429,100708,100665,116551);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (109967,86379,115779,104094,102543,113604,113402,118167,112496,116243,110065,118042,113606,119438,114933,111021,109698,86380,118914,113840,103133,119115,109548,113605,101200,106699,107186,111928,114494,111036,109694,115177,102468,112339,116208,101101,100951,116479,116606,104126,106456,116832,108287,110058,117837,116795,112849,112677,111492,102332,111493,108288,111314,115402,100713,114361,103293,102961,101084,107730,114122,109118,111430,86408,118966,113676,100244,110483,111398,106614,106913,107300,113896,105169,113897,107033,115565,113263,107889,114682,100912,103832,101394,104105,109335,116252,105426,103955,102181,86409,107314,104391,86145,101677,107148,111355,111254,102819,118253,111431,110266,102136,85877,109818,113056,110748,111437,104136,112778,114621,112265,101305,105522,104249,115465,105422,104255,109636,114737,104517,100420,115096,116533,110444,115332,103929,119010,100840,104155,116811,99943,107962,105531,114490,106200,115184,117315,105433,110940,119206,86146,116383,102692,101093,111993,116176,105918,99393,101488,108358,101459,105968,110616,109213,100148,99762,102509,108136,118811,103537,102484,108332,107218,115052,107441,108741,103781,86427,107509,115998,103813,116365,108106,99495,117700,117840,117304,86428,107339,106884,116070,103309,108362,107835,111325,116550,86023,109549,86172,106903,102662,105302,119161,107106,116981,114941,86429,100708,100665,116551);

delete quotes.* 
from quotes where instrumentid in (109967,86379,115779,104094,102543,113604,113402,118167,112496,116243,110065,118042,113606,119438,114933,111021,109698,86380,118914,113840,103133,119115,109548,113605,101200,106699,107186,111928,114494,111036,109694,115177,102468,112339,116208,101101,100951,116479,116606,104126,106456,116832,108287,110058,117837,116795,112849,112677,111492,102332,111493,108288,111314,115402,100713,114361,103293,102961,101084,107730,114122,109118,111430,86408,118966,113676,100244,110483,111398,106614,106913,107300,113896,105169,113897,107033,115565,113263,107889,114682,100912,103832,101394,104105,109335,116252,105426,103955,102181,86409,107314,104391,86145,101677,107148,111355,111254,102819,118253,111431,110266,102136,85877,109818,113056,110748,111437,104136,112778,114621,112265,101305,105522,104249,115465,105422,104255,109636,114737,104517,100420,115096,116533,110444,115332,103929,119010,100840,104155,116811,99943,107962,105531,114490,106200,115184,117315,105433,110940,119206,86146,116383,102692,101093,111993,116176,105918,99393,101488,108358,101459,105968,110616,109213,100148,99762,102509,108136,118811,103537,102484,108332,107218,115052,107441,108741,103781,86427,107509,115998,103813,116365,108106,99495,117700,117840,117304,86428,107339,106884,116070,103309,108362,107835,111325,116550,86023,109549,86172,106903,102662,105302,119161,107106,116981,114941,86429,100708,100665,116551);

commit;



begin;

select concat("FROM ",115379," TO ", 102933);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (115379,101443,118178,107855,102325,108302,100926,110204,108982,105613,99991,113712,85906,111126,115676,105701,111412,103361,101618,105417,110126,108655,108489,111127,109706,86430,112470,100092,109971,116902,111960,103279,117218,102257,103678,100491,114717,116103,109940,116480,108191,113916,112950,103150,102814,113343,116573,99937,115870,86173,117268,104095,101992,99414,86431,107999,107483,108433,118702,117239,103705,105017,103331,111229,115246,99637,115309,103299,103539,104709,114454,101600,113885,116372,100028,105716,114372,112259,107533,114708,116581,105637,108939,106799,114134,109875,100747,115777,116488,101971,115534,114346,100529,117203,112880,106697,113226,99867,101442,111399,104421,113608,109975,111263,106077,113463,110376,110145,118105,115626,110085,100567,113228,117212,116849,114355,109972,108831,108363,116903,116111,117019,100953,117638,111589,111970,118216,108530,118805,108737,116414,101766,111274,86452,100791,103208,107961,86453,100071,103888,113842,113639,105821,101678,102005,109924,111539,104604,111268,110608,115550,113612,103609,103860,112411,112876,113222,103139,108020,105384,110290,114781,108309,111143,105090,100829,102338,114250,100717,114010,109340,86206,114994,86056,108702,100121,101205,107944,106766,86207,110698,101573,101519,99518,116217,100377,108784,113219,111956,112875,111957,105635,110585,110211,110586,115785,112549,109591,116622,102933);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (115379,101443,118178,107855,102325,108302,100926,110204,108982,105613,99991,113712,85906,111126,115676,105701,111412,103361,101618,105417,110126,108655,108489,111127,109706,86430,112470,100092,109971,116902,111960,103279,117218,102257,103678,100491,114717,116103,109940,116480,108191,113916,112950,103150,102814,113343,116573,99937,115870,86173,117268,104095,101992,99414,86431,107999,107483,108433,118702,117239,103705,105017,103331,111229,115246,99637,115309,103299,103539,104709,114454,101600,113885,116372,100028,105716,114372,112259,107533,114708,116581,105637,108939,106799,114134,109875,100747,115777,116488,101971,115534,114346,100529,117203,112880,106697,113226,99867,101442,111399,104421,113608,109975,111263,106077,113463,110376,110145,118105,115626,110085,100567,113228,117212,116849,114355,109972,108831,108363,116903,116111,117019,100953,117638,111589,111970,118216,108530,118805,108737,116414,101766,111274,86452,100791,103208,107961,86453,100071,103888,113842,113639,105821,101678,102005,109924,111539,104604,111268,110608,115550,113612,103609,103860,112411,112876,113222,103139,108020,105384,110290,114781,108309,111143,105090,100829,102338,114250,100717,114010,109340,86206,114994,86056,108702,100121,101205,107944,106766,86207,110698,101573,101519,99518,116217,100377,108784,113219,111956,112875,111957,105635,110585,110211,110586,115785,112549,109591,116622,102933);

delete quotes.* 
from quotes where instrumentid in (115379,101443,118178,107855,102325,108302,100926,110204,108982,105613,99991,113712,85906,111126,115676,105701,111412,103361,101618,105417,110126,108655,108489,111127,109706,86430,112470,100092,109971,116902,111960,103279,117218,102257,103678,100491,114717,116103,109940,116480,108191,113916,112950,103150,102814,113343,116573,99937,115870,86173,117268,104095,101992,99414,86431,107999,107483,108433,118702,117239,103705,105017,103331,111229,115246,99637,115309,103299,103539,104709,114454,101600,113885,116372,100028,105716,114372,112259,107533,114708,116581,105637,108939,106799,114134,109875,100747,115777,116488,101971,115534,114346,100529,117203,112880,106697,113226,99867,101442,111399,104421,113608,109975,111263,106077,113463,110376,110145,118105,115626,110085,100567,113228,117212,116849,114355,109972,108831,108363,116903,116111,117019,100953,117638,111589,111970,118216,108530,118805,108737,116414,101766,111274,86452,100791,103208,107961,86453,100071,103888,113842,113639,105821,101678,102005,109924,111539,104604,111268,110608,115550,113612,103609,103860,112411,112876,113222,103139,108020,105384,110290,114781,108309,111143,105090,100829,102338,114250,100717,114010,109340,86206,114994,86056,108702,100121,101205,107944,106766,86207,110698,101573,101519,99518,116217,100377,108784,113219,111956,112875,111957,105635,110585,110211,110586,115785,112549,109591,116622,102933);

commit;



begin;

select concat("FROM ",116647," TO ", 105050);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (116647,100969,114263,103336,113540,86057,105568,101399,103569,101380,117856,119162,106228,116501,110936,107092,116241,105135,86481,104333,113585,103886,103956,117509,117485,105567,107041,106372,86482,104879,86483,110267,102457,85907,110140,119238,102778,105376,116179,105733,106851,105904,111959,111585,116797,111401,105519,86208,86484,104530,102631,117526,102121,118323,113191,108453,107093,118162,100847,111731,104023,102473,106946,115032,109115,99827,104779,100626,86514,99695,109245,102935,109682,115759,86515,109575,107177,110732,117897,100763,105734,112412,101867,102060,86058,102306,37459,105145,102130,113545,102365,101238,86059,107950,111633,104809,101969,106357,109042,99471,99434,99742,116527,113358,116244,110131,113153,110439,106781,104320,102680,117332,113359,117498,103205,104531,111805,100544,107986,111903,110937,108347,105301,113146,108041,107094,100986,106756,112111,111320,106832,113018,108440,106741,111814,109201,113537,107842,109681,105136,105158,110587,109278,116505,111433,110939,113869,114133,117093,111416,105666,111736,99995,118557,116648,104592,109895,105730,37460,117938,100627,86241,103447,100229,116213,108518,103367,116240,100903,117293,99792,105194,105299,101856,111361,105156,116239,114053,102349,113572,113046,114451,100597,85970,107154,112518,86542,109350,105234,86242,116102,99828,102063,117896,102453,117205,106419,100949,101679,105050);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (116647,100969,114263,103336,113540,86057,105568,101399,103569,101380,117856,119162,106228,116501,110936,107092,116241,105135,86481,104333,113585,103886,103956,117509,117485,105567,107041,106372,86482,104879,86483,110267,102457,85907,110140,119238,102778,105376,116179,105733,106851,105904,111959,111585,116797,111401,105519,86208,86484,104530,102631,117526,102121,118323,113191,108453,107093,118162,100847,111731,104023,102473,106946,115032,109115,99827,104779,100626,86514,99695,109245,102935,109682,115759,86515,109575,107177,110732,117897,100763,105734,112412,101867,102060,86058,102306,37459,105145,102130,113545,102365,101238,86059,107950,111633,104809,101969,106357,109042,99471,99434,99742,116527,113358,116244,110131,113153,110439,106781,104320,102680,117332,113359,117498,103205,104531,111805,100544,107986,111903,110937,108347,105301,113146,108041,107094,100986,106756,112111,111320,106832,113018,108440,106741,111814,109201,113537,107842,109681,105136,105158,110587,109278,116505,111433,110939,113869,114133,117093,111416,105666,111736,99995,118557,116648,104592,109895,105730,37460,117938,100627,86241,103447,100229,116213,108518,103367,116240,100903,117293,99792,105194,105299,101856,111361,105156,116239,114053,102349,113572,113046,114451,100597,85970,107154,112518,86542,109350,105234,86242,116102,99828,102063,117896,102453,117205,106419,100949,101679,105050);

delete quotes.* 
from quotes where instrumentid in (116647,100969,114263,103336,113540,86057,105568,101399,103569,101380,117856,119162,106228,116501,110936,107092,116241,105135,86481,104333,113585,103886,103956,117509,117485,105567,107041,106372,86482,104879,86483,110267,102457,85907,110140,119238,102778,105376,116179,105733,106851,105904,111959,111585,116797,111401,105519,86208,86484,104530,102631,117526,102121,118323,113191,108453,107093,118162,100847,111731,104023,102473,106946,115032,109115,99827,104779,100626,86514,99695,109245,102935,109682,115759,86515,109575,107177,110732,117897,100763,105734,112412,101867,102060,86058,102306,37459,105145,102130,113545,102365,101238,86059,107950,111633,104809,101969,106357,109042,99471,99434,99742,116527,113358,116244,110131,113153,110439,106781,104320,102680,117332,113359,117498,103205,104531,111805,100544,107986,111903,110937,108347,105301,113146,108041,107094,100986,106756,112111,111320,106832,113018,108440,106741,111814,109201,113537,107842,109681,105136,105158,110587,109278,116505,111433,110939,113869,114133,117093,111416,105666,111736,99995,118557,116648,104592,109895,105730,37460,117938,100627,86241,103447,100229,116213,108518,103367,116240,100903,117293,99792,105194,105299,101856,111361,105156,116239,114053,102349,113572,113046,114451,100597,85970,107154,112518,86542,109350,105234,86242,116102,99828,102063,117896,102453,117205,106419,100949,101679,105050);

commit;



begin;

select concat("FROM ",107465," TO ", 111032);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (107465,100689,104707,100427,114034,112573,100250,117928,113898,101466,109683,102638,100877,102607,105040,118077,113453,103378,100625,109527,111363,106719,118139,100686,118431,114051,113845,106557,114236,102747,107115,104912,101126,107495,112266,110403,117038,106856,116238,111587,103209,111583,118535,99910,113360,104925,105685,107816,99790,116245,100400,102711,117898,108821,109096,109544,109509,106751,104528,105343,109973,108402,117890,102235,111927,105252,108073,110967,104326,117827,104993,107240,99958,116731,108148,115017,103671,104613,105972,102002,102316,111895,115448,112350,109658,101850,112197,117815,86557,101906,101381,111562,111741,100853,108703,105019,100965,104911,109048,104327,86558,110501,105030,116762,102731,108869,99666,108069,100734,104176,108964,107674,102469,118948,101154,109778,106353,114641,106621,107688,103603,105607,102969,110374,119113,108542,110673,113978,106599,102957,111075,113458,112159,112998,107325,107700,104345,107702,115324,113032,109184,105329,101211,110753,107568,111360,106428,117457,113638,113253,107117,105905,101229,116234,110973,102710,100129,106150,102673,119183,110614,107207,117688,106749,102880,99932,100495,112886,105506,108089,100982,112942,106280,111756,113665,110764,106384,102368,100399,111480,86582,106235,99542,108865,108859,111467,113854,102053,100900,116370,105542,107995,107504,107994,111135,105072,112554,106922,114128,111032);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (107465,100689,104707,100427,114034,112573,100250,117928,113898,101466,109683,102638,100877,102607,105040,118077,113453,103378,100625,109527,111363,106719,118139,100686,118431,114051,113845,106557,114236,102747,107115,104912,101126,107495,112266,110403,117038,106856,116238,111587,103209,111583,118535,99910,113360,104925,105685,107816,99790,116245,100400,102711,117898,108821,109096,109544,109509,106751,104528,105343,109973,108402,117890,102235,111927,105252,108073,110967,104326,117827,104993,107240,99958,116731,108148,115017,103671,104613,105972,102002,102316,111895,115448,112350,109658,101850,112197,117815,86557,101906,101381,111562,111741,100853,108703,105019,100965,104911,109048,104327,86558,110501,105030,116762,102731,108869,99666,108069,100734,104176,108964,107674,102469,118948,101154,109778,106353,114641,106621,107688,103603,105607,102969,110374,119113,108542,110673,113978,106599,102957,111075,113458,112159,112998,107325,107700,104345,107702,115324,113032,109184,105329,101211,110753,107568,111360,106428,117457,113638,113253,107117,105905,101229,116234,110973,102710,100129,106150,102673,119183,110614,107207,117688,106749,102880,99932,100495,112886,105506,108089,100982,112942,106280,111756,113665,110764,106384,102368,100399,111480,86582,106235,99542,108865,108859,111467,113854,102053,100900,116370,105542,107995,107504,107994,111135,105072,112554,106922,114128,111032);

delete quotes.* 
from quotes where instrumentid in (107465,100689,104707,100427,114034,112573,100250,117928,113898,101466,109683,102638,100877,102607,105040,118077,113453,103378,100625,109527,111363,106719,118139,100686,118431,114051,113845,106557,114236,102747,107115,104912,101126,107495,112266,110403,117038,106856,116238,111587,103209,111583,118535,99910,113360,104925,105685,107816,99790,116245,100400,102711,117898,108821,109096,109544,109509,106751,104528,105343,109973,108402,117890,102235,111927,105252,108073,110967,104326,117827,104993,107240,99958,116731,108148,115017,103671,104613,105972,102002,102316,111895,115448,112350,109658,101850,112197,117815,86557,101906,101381,111562,111741,100853,108703,105019,100965,104911,109048,104327,86558,110501,105030,116762,102731,108869,99666,108069,100734,104176,108964,107674,102469,118948,101154,109778,106353,114641,106621,107688,103603,105607,102969,110374,119113,108542,110673,113978,106599,102957,111075,113458,112159,112998,107325,107700,104345,107702,115324,113032,109184,105329,101211,110753,107568,111360,106428,117457,113638,113253,107117,105905,101229,116234,110973,102710,100129,106150,102673,119183,110614,107207,117688,106749,102880,99932,100495,112886,105506,108089,100982,112942,106280,111756,113665,110764,106384,102368,100399,111480,86582,106235,99542,108865,108859,111467,113854,102053,100900,116370,105542,107995,107504,107994,111135,105072,112554,106922,114128,111032);

commit;



begin;

select concat("FROM ",103973," TO ", 110648);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (103973,106754,107101,112895,114414,104850,104240,110902,100633,116983,113939,117550,103241,115315,100402,103269,112398,108447,113165,112627,104780,105129,107744,106783,102448,108250,85880,86583,106841,105028,101790,116048,110576,108016,107443,117323,108248,114193,104605,86584,101290,112181,115446,102502,116915,104944,107452,108513,100724,104513,110084,111348,107453,105258,106448,109921,111471,111499,105889,117108,105253,99363,107905,108917,106883,110762,110209,111515,100753,107272,116140,113884,109040,103535,102516,100842,110687,115828,110573,99826,116612,119356,104747,99474,116288,117900,103835,99811,104093,86260,117305,116098,100458,105024,99899,85971,101988,114169,86602,109332,100827,115739,104908,114347,86603,102356,106204,115377,100500,109961,116099,117128,102019,107556,107813,105448,86604,106146,108293,103902,115169,111350,105313,110903,108145,99723,101428,109060,110749,116371,116187,109094,110613,119227,109594,104786,100614,116233,115204,113866,107860,103376,102524,117156,105016,104673,116631,102048,104864,110156,86640,101857,100547,107298,110391,86074,86641,109593,108868,112357,99686,111237,110882,107992,107474,111207,86642,112559,106234,119081,106705,99559,104139,104812,110689,102403,102732,100409,115677,108237,109337,114880,102283,116569,108628,99728,110377,108244,109045,103254,109991,102097,105305,86285,86643,111742,99371,101866,110495,110648);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (103973,106754,107101,112895,114414,104850,104240,110902,100633,116983,113939,117550,103241,115315,100402,103269,112398,108447,113165,112627,104780,105129,107744,106783,102448,108250,85880,86583,106841,105028,101790,116048,110576,108016,107443,117323,108248,114193,104605,86584,101290,112181,115446,102502,116915,104944,107452,108513,100724,104513,110084,111348,107453,105258,106448,109921,111471,111499,105889,117108,105253,99363,107905,108917,106883,110762,110209,111515,100753,107272,116140,113884,109040,103535,102516,100842,110687,115828,110573,99826,116612,119356,104747,99474,116288,117900,103835,99811,104093,86260,117305,116098,100458,105024,99899,85971,101988,114169,86602,109332,100827,115739,104908,114347,86603,102356,106204,115377,100500,109961,116099,117128,102019,107556,107813,105448,86604,106146,108293,103902,115169,111350,105313,110903,108145,99723,101428,109060,110749,116371,116187,109094,110613,119227,109594,104786,100614,116233,115204,113866,107860,103376,102524,117156,105016,104673,116631,102048,104864,110156,86640,101857,100547,107298,110391,86074,86641,109593,108868,112357,99686,111237,110882,107992,107474,111207,86642,112559,106234,119081,106705,99559,104139,104812,110689,102403,102732,100409,115677,108237,109337,114880,102283,116569,108628,99728,110377,108244,109045,103254,109991,102097,105305,86285,86643,111742,99371,101866,110495,110648);

delete quotes.* 
from quotes where instrumentid in (103973,106754,107101,112895,114414,104850,104240,110902,100633,116983,113939,117550,103241,115315,100402,103269,112398,108447,113165,112627,104780,105129,107744,106783,102448,108250,85880,86583,106841,105028,101790,116048,110576,108016,107443,117323,108248,114193,104605,86584,101290,112181,115446,102502,116915,104944,107452,108513,100724,104513,110084,111348,107453,105258,106448,109921,111471,111499,105889,117108,105253,99363,107905,108917,106883,110762,110209,111515,100753,107272,116140,113884,109040,103535,102516,100842,110687,115828,110573,99826,116612,119356,104747,99474,116288,117900,103835,99811,104093,86260,117305,116098,100458,105024,99899,85971,101988,114169,86602,109332,100827,115739,104908,114347,86603,102356,106204,115377,100500,109961,116099,117128,102019,107556,107813,105448,86604,106146,108293,103902,115169,111350,105313,110903,108145,99723,101428,109060,110749,116371,116187,109094,110613,119227,109594,104786,100614,116233,115204,113866,107860,103376,102524,117156,105016,104673,116631,102048,104864,110156,86640,101857,100547,107298,110391,86074,86641,109593,108868,112357,99686,111237,110882,107992,107474,111207,86642,112559,106234,119081,106705,99559,104139,104812,110689,102403,102732,100409,115677,108237,109337,114880,102283,116569,108628,99728,110377,108244,109045,103254,109991,102097,105305,86285,86643,111742,99371,101866,110495,110648);

commit;



begin;

select concat("FROM ",111196," TO ", 100190);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (111196,117555,105326,106700,109467,111025,110445,111595,86644,117086,117290,109324,101328,111421,104262,103964,104551,104979,118602,105377,107155,116206,108090,115188,116577,117507,109343,118048,111385,108121,107448,119200,102926,108036,101901,106938,117883,113588,100480,101613,108060,104516,104070,116096,113080,104670,106716,105128,101125,113660,105404,110650,111368,108297,115194,107934,104677,104676,111408,105447,103738,103245,104821,108781,115867,108739,113194,115475,100291,108373,115373,104929,116719,108425,107301,99885,114291,105189,109323,107936,113368,111847,119114,109838,112671,114153,113689,102907,112824,106083,107916,115834,102481,106153,86286,107420,86075,110226,86287,117549,106654,116521,100385,86076,102488,100396,114892,106394,114013,116248,100151,86077,101763,116373,109944,102729,110699,115894,111836,113367,108316,108579,108604,100365,114875,100770,112157,113670,110363,106214,113921,110662,104143,105190,108351,116558,119156,102421,113692,102709,111384,116977,107118,106709,111802,111472,103165,114178,115033,86312,99670,100498,109398,111632,109211,116998,102703,108141,104192,115835,101538,115848,100909,107531,116604,104577,100510,113459,106336,112487,108577,115252,105583,109342,105987,115822,99419,109399,108516,109905,86117,99516,107017,102908,103814,109191,118266,115953,104527,86313,106255,101773,118367,117238,103125,103374,109931,110720,113945,100190);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (111196,117555,105326,106700,109467,111025,110445,111595,86644,117086,117290,109324,101328,111421,104262,103964,104551,104979,118602,105377,107155,116206,108090,115188,116577,117507,109343,118048,111385,108121,107448,119200,102926,108036,101901,106938,117883,113588,100480,101613,108060,104516,104070,116096,113080,104670,106716,105128,101125,113660,105404,110650,111368,108297,115194,107934,104677,104676,111408,105447,103738,103245,104821,108781,115867,108739,113194,115475,100291,108373,115373,104929,116719,108425,107301,99885,114291,105189,109323,107936,113368,111847,119114,109838,112671,114153,113689,102907,112824,106083,107916,115834,102481,106153,86286,107420,86075,110226,86287,117549,106654,116521,100385,86076,102488,100396,114892,106394,114013,116248,100151,86077,101763,116373,109944,102729,110699,115894,111836,113367,108316,108579,108604,100365,114875,100770,112157,113670,110363,106214,113921,110662,104143,105190,108351,116558,119156,102421,113692,102709,111384,116977,107118,106709,111802,111472,103165,114178,115033,86312,99670,100498,109398,111632,109211,116998,102703,108141,104192,115835,101538,115848,100909,107531,116604,104577,100510,113459,106336,112487,108577,115252,105583,109342,105987,115822,99419,109399,108516,109905,86117,99516,107017,102908,103814,109191,118266,115953,104527,86313,106255,101773,118367,117238,103125,103374,109931,110720,113945,100190);

delete quotes.* 
from quotes where instrumentid in (111196,117555,105326,106700,109467,111025,110445,111595,86644,117086,117290,109324,101328,111421,104262,103964,104551,104979,118602,105377,107155,116206,108090,115188,116577,117507,109343,118048,111385,108121,107448,119200,102926,108036,101901,106938,117883,113588,100480,101613,108060,104516,104070,116096,113080,104670,106716,105128,101125,113660,105404,110650,111368,108297,115194,107934,104677,104676,111408,105447,103738,103245,104821,108781,115867,108739,113194,115475,100291,108373,115373,104929,116719,108425,107301,99885,114291,105189,109323,107936,113368,111847,119114,109838,112671,114153,113689,102907,112824,106083,107916,115834,102481,106153,86286,107420,86075,110226,86287,117549,106654,116521,100385,86076,102488,100396,114892,106394,114013,116248,100151,86077,101763,116373,109944,102729,110699,115894,111836,113367,108316,108579,108604,100365,114875,100770,112157,113670,110363,106214,113921,110662,104143,105190,108351,116558,119156,102421,113692,102709,111384,116977,107118,106709,111802,111472,103165,114178,115033,86312,99670,100498,109398,111632,109211,116998,102703,108141,104192,115835,101538,115848,100909,107531,116604,104577,100510,113459,106336,112487,108577,115252,105583,109342,105987,115822,99419,109399,108516,109905,86117,99516,107017,102908,103814,109191,118266,115953,104527,86313,106255,101773,118367,117238,103125,103374,109931,110720,113945,100190);

commit;



begin;

select concat("FROM ",101331," TO ", 100809);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (101331,86118,105049,100577,106715,108809,115138,101276,116760,102194,110628,117523,114779,115569,113881,117280,99687,100088,109896,113012,112702,117918,100771,117689,104040,118635,86700,103995,86701,100742,108410,109771,104077,101275,99577,100055,115361,85999,104463,102763,111043,101980,103291,100493,118795,116613,108445,102126,113535,116689,111645,101603,102098,107727,100224,111313,103507,100357,108795,107404,117720,117704,107418,105198,104918,117553,111506,115871,86721,109932,107870,113502,99726,105850,111120,114009,116246,111423,101543,110590,103164,111601,103512,100787,114244,117061,101416,114242,116559,115717,103977,115282,106047,104082,86722,86119,111695,107102,110470,100691,103002,110404,105544,107695,101432,111414,117582,106211,100333,109707,115293,116816,116375,104029,110243,101693,115283,101440,108870,103469,113662,115768,114874,110247,100186,116565,116295,101194,86723,100103,103148,103748,111759,109966,114578,110071,119082,102030,116228,104705,112834,111382,109693,113667,113927,112366,118152,101373,106247,105498,106165,99638,100281,107558,101313,111879,116732,112972,104091,116297,103570,111830,115859,107032,112912,106689,101784,117062,101863,116868,105356,106886,107814,100176,86758,100216,117480,114488,113874,113727,102243,108773,103152,104603,103463,109942,106260,112353,100810,108843,113495,116307,116991,104381,119080,116342,105191,116351,86759,100809);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (101331,86118,105049,100577,106715,108809,115138,101276,116760,102194,110628,117523,114779,115569,113881,117280,99687,100088,109896,113012,112702,117918,100771,117689,104040,118635,86700,103995,86701,100742,108410,109771,104077,101275,99577,100055,115361,85999,104463,102763,111043,101980,103291,100493,118795,116613,108445,102126,113535,116689,111645,101603,102098,107727,100224,111313,103507,100357,108795,107404,117720,117704,107418,105198,104918,117553,111506,115871,86721,109932,107870,113502,99726,105850,111120,114009,116246,111423,101543,110590,103164,111601,103512,100787,114244,117061,101416,114242,116559,115717,103977,115282,106047,104082,86722,86119,111695,107102,110470,100691,103002,110404,105544,107695,101432,111414,117582,106211,100333,109707,115293,116816,116375,104029,110243,101693,115283,101440,108870,103469,113662,115768,114874,110247,100186,116565,116295,101194,86723,100103,103148,103748,111759,109966,114578,110071,119082,102030,116228,104705,112834,111382,109693,113667,113927,112366,118152,101373,106247,105498,106165,99638,100281,107558,101313,111879,116732,112972,104091,116297,103570,111830,115859,107032,112912,106689,101784,117062,101863,116868,105356,106886,107814,100176,86758,100216,117480,114488,113874,113727,102243,108773,103152,104603,103463,109942,106260,112353,100810,108843,113495,116307,116991,104381,119080,116342,105191,116351,86759,100809);

delete quotes.* 
from quotes where instrumentid in (101331,86118,105049,100577,106715,108809,115138,101276,116760,102194,110628,117523,114779,115569,113881,117280,99687,100088,109896,113012,112702,117918,100771,117689,104040,118635,86700,103995,86701,100742,108410,109771,104077,101275,99577,100055,115361,85999,104463,102763,111043,101980,103291,100493,118795,116613,108445,102126,113535,116689,111645,101603,102098,107727,100224,111313,103507,100357,108795,107404,117720,117704,107418,105198,104918,117553,111506,115871,86721,109932,107870,113502,99726,105850,111120,114009,116246,111423,101543,110590,103164,111601,103512,100787,114244,117061,101416,114242,116559,115717,103977,115282,106047,104082,86722,86119,111695,107102,110470,100691,103002,110404,105544,107695,101432,111414,117582,106211,100333,109707,115293,116816,116375,104029,110243,101693,115283,101440,108870,103469,113662,115768,114874,110247,100186,116565,116295,101194,86723,100103,103148,103748,111759,109966,114578,110071,119082,102030,116228,104705,112834,111382,109693,113667,113927,112366,118152,101373,106247,105498,106165,99638,100281,107558,101313,111879,116732,112972,104091,116297,103570,111830,115859,107032,112912,106689,101784,117062,101863,116868,105356,106886,107814,100176,86758,100216,117480,114488,113874,113727,102243,108773,103152,104603,103463,109942,106260,112353,100810,108843,113495,116307,116991,104381,119080,116342,105191,116351,86759,100809);

commit;



begin;

select concat("FROM ",117521," TO ", 110741);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (117521,112976,101102,106579,114507,103936,118063,100736,103953,106610,110622,113536,111364,109449,102033,110799,102154,105967,114217,116068,110899,101208,109974,103949,111082,100210,107215,106232,99939,112973,114210,102580,105954,117552,106595,115934,101291,105464,107091,110740,111752,118701,118556,119226,118220,119274,118467,119179,86786,118913,119089,107948,102881,112819,110378,100373,105586,112821,106720,110433,102895,102586,86787,101202,107327,112863,108934,109902,119160,118303,118430,119151,119127,117000,99630,118667,86788,118648,118647,118415,118714,118494,106148,111813,114738,103483,106309,114290,86789,115842,117224,118433,118808,86120,119284,119128,118417,118666,119207,119343,118700,119201,118432,119270,118347,115912,119163,103088,119357,118779,86790,115583,118807,85882,104895,117619,105969,114873,118713,118239,112439,102823,102541,100222,115178,118016,102979,113672,113102,104855,114474,100397,105764,107794,108874,112089,107537,86000,105088,112441,110694,110455,102387,86375,109196,109510,115418,109599,103904,114131,113859,107943,106718,104626,106706,108311,116570,108537,100623,110693,112945,85953,104591,111455,106240,100421,105192,106830,116301,115754,116304,101458,111081,86150,117436,109203,103637,101246,86805,100681,104141,112185,114150,108876,109204,111071,106824,105736,86806,105223,113703,112946,113666,100206,116782,100325,86807,110303,114011,110741);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (117521,112976,101102,106579,114507,103936,118063,100736,103953,106610,110622,113536,111364,109449,102033,110799,102154,105967,114217,116068,110899,101208,109974,103949,111082,100210,107215,106232,99939,112973,114210,102580,105954,117552,106595,115934,101291,105464,107091,110740,111752,118701,118556,119226,118220,119274,118467,119179,86786,118913,119089,107948,102881,112819,110378,100373,105586,112821,106720,110433,102895,102586,86787,101202,107327,112863,108934,109902,119160,118303,118430,119151,119127,117000,99630,118667,86788,118648,118647,118415,118714,118494,106148,111813,114738,103483,106309,114290,86789,115842,117224,118433,118808,86120,119284,119128,118417,118666,119207,119343,118700,119201,118432,119270,118347,115912,119163,103088,119357,118779,86790,115583,118807,85882,104895,117619,105969,114873,118713,118239,112439,102823,102541,100222,115178,118016,102979,113672,113102,104855,114474,100397,105764,107794,108874,112089,107537,86000,105088,112441,110694,110455,102387,86375,109196,109510,115418,109599,103904,114131,113859,107943,106718,104626,106706,108311,116570,108537,100623,110693,112945,85953,104591,111455,106240,100421,105192,106830,116301,115754,116304,101458,111081,86150,117436,109203,103637,101246,86805,100681,104141,112185,114150,108876,109204,111071,106824,105736,86806,105223,113703,112946,113666,100206,116782,100325,86807,110303,114011,110741);

delete quotes.* 
from quotes where instrumentid in (117521,112976,101102,106579,114507,103936,118063,100736,103953,106610,110622,113536,111364,109449,102033,110799,102154,105967,114217,116068,110899,101208,109974,103949,111082,100210,107215,106232,99939,112973,114210,102580,105954,117552,106595,115934,101291,105464,107091,110740,111752,118701,118556,119226,118220,119274,118467,119179,86786,118913,119089,107948,102881,112819,110378,100373,105586,112821,106720,110433,102895,102586,86787,101202,107327,112863,108934,109902,119160,118303,118430,119151,119127,117000,99630,118667,86788,118648,118647,118415,118714,118494,106148,111813,114738,103483,106309,114290,86789,115842,117224,118433,118808,86120,119284,119128,118417,118666,119207,119343,118700,119201,118432,119270,118347,115912,119163,103088,119357,118779,86790,115583,118807,85882,104895,117619,105969,114873,118713,118239,112439,102823,102541,100222,115178,118016,102979,113672,113102,104855,114474,100397,105764,107794,108874,112089,107537,86000,105088,112441,110694,110455,102387,86375,109196,109510,115418,109599,103904,114131,113859,107943,106718,104626,106706,108311,116570,108537,100623,110693,112945,85953,104591,111455,106240,100421,105192,106830,116301,115754,116304,101458,111081,86150,117436,109203,103637,101246,86805,100681,104141,112185,114150,108876,109204,111071,106824,105736,86806,105223,113703,112946,113666,100206,116782,100325,86807,110303,114011,110741);

commit;



begin;

select concat("FROM ",86808," TO ", 112426);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (86808,110543,114258,109700,114956,113353,104022,118656,85908,107580,100111,100830,107621,113361,114061,100994,86809,112901,105134,117658,106143,102129,102742,108007,86376,114952,112467,101861,101218,110120,109833,106597,113694,112417,86151,102817,114359,103213,99849,104708,114480,113087,100434,103118,111636,117187,86001,106521,106657,100758,112367,114449,111751,103597,108002,100956,100193,104439,112196,112598,101515,114012,86002,116097,115218,109660,105479,111252,100060,100888,115751,110897,111477,117970,111930,117721,110373,113170,118066,115171,86377,103240,104210,119116,104621,100557,105877,105008,102726,101931,105973,112065,106531,115091,100337,110402,108543,105975,105729,103626,104485,109530,109493,112647,114125,118928,116540,104140,100433,112926,119269,112925,117153,99599,86152,108009,115820,111445,108668,109523,116441,115907,105648,112443,106459,86830,111091,104653,104791,110941,115242,101991,104652,111204,108553,113503,103145,116747,101187,113362,86397,108538,114795,112817,118120,100484,101177,108361,111494,107297,107149,100980,114199,104874,102303,109049,112182,101133,109977,101050,117124,99551,108547,99538,86852,103879,115378,101718,112359,102341,108170,102411,116783,117884,86853,100961,100026,115240,112489,108137,101483,111753,112688,113299,114864,100370,100985,107074,113802,100520,99465,86398,105573,86153,103802,103809,104290,107447,112080,112426);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (86808,110543,114258,109700,114956,113353,104022,118656,85908,107580,100111,100830,107621,113361,114061,100994,86809,112901,105134,117658,106143,102129,102742,108007,86376,114952,112467,101861,101218,110120,109833,106597,113694,112417,86151,102817,114359,103213,99849,104708,114480,113087,100434,103118,111636,117187,86001,106521,106657,100758,112367,114449,111751,103597,108002,100956,100193,104439,112196,112598,101515,114012,86002,116097,115218,109660,105479,111252,100060,100888,115751,110897,111477,117970,111930,117721,110373,113170,118066,115171,86377,103240,104210,119116,104621,100557,105877,105008,102726,101931,105973,112065,106531,115091,100337,110402,108543,105975,105729,103626,104485,109530,109493,112647,114125,118928,116540,104140,100433,112926,119269,112925,117153,99599,86152,108009,115820,111445,108668,109523,116441,115907,105648,112443,106459,86830,111091,104653,104791,110941,115242,101991,104652,111204,108553,113503,103145,116747,101187,113362,86397,108538,114795,112817,118120,100484,101177,108361,111494,107297,107149,100980,114199,104874,102303,109049,112182,101133,109977,101050,117124,99551,108547,99538,86852,103879,115378,101718,112359,102341,108170,102411,116783,117884,86853,100961,100026,115240,112489,108137,101483,111753,112688,113299,114864,100370,100985,107074,113802,100520,99465,86398,105573,86153,103802,103809,104290,107447,112080,112426);

delete quotes.* 
from quotes where instrumentid in (86808,110543,114258,109700,114956,113353,104022,118656,85908,107580,100111,100830,107621,113361,114061,100994,86809,112901,105134,117658,106143,102129,102742,108007,86376,114952,112467,101861,101218,110120,109833,106597,113694,112417,86151,102817,114359,103213,99849,104708,114480,113087,100434,103118,111636,117187,86001,106521,106657,100758,112367,114449,111751,103597,108002,100956,100193,104439,112196,112598,101515,114012,86002,116097,115218,109660,105479,111252,100060,100888,115751,110897,111477,117970,111930,117721,110373,113170,118066,115171,86377,103240,104210,119116,104621,100557,105877,105008,102726,101931,105973,112065,106531,115091,100337,110402,108543,105975,105729,103626,104485,109530,109493,112647,114125,118928,116540,104140,100433,112926,119269,112925,117153,99599,86152,108009,115820,111445,108668,109523,116441,115907,105648,112443,106459,86830,111091,104653,104791,110941,115242,101991,104652,111204,108553,113503,103145,116747,101187,113362,86397,108538,114795,112817,118120,100484,101177,108361,111494,107297,107149,100980,114199,104874,102303,109049,112182,101133,109977,101050,117124,99551,108547,99538,86852,103879,115378,101718,112359,102341,108170,102411,116783,117884,86853,100961,100026,115240,112489,108137,101483,111753,112688,113299,114864,100370,100985,107074,113802,100520,99465,86398,105573,86153,103802,103809,104290,107447,112080,112426);

commit;



begin;

select concat("FROM ",108807," TO ", 86180);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (108807,117483,117607,105062,86854,86003,109907,100369,103765,86855,115611,104104,106009,100273,118465,117527,101207,102610,104294,99914,86004,108556,118233,112650,113983,117538,116753,109577,103214,106683,117518,110681,116897,103186,101438,114721,111011,115997,104684,103769,111496,115083,101709,111129,117752,100440,101685,103327,118549,86883,109855,114018,109122,117547,103414,104097,102012,117322,86399,107146,110165,116830,116235,101081,105265,101801,107013,105914,106985,117772,108042,117663,105534,99617,101198,103260,111443,113534,103484,114296,104419,108692,100432,114739,116327,115584,115585,102498,104228,115773,105671,101959,101852,117717,118027,86884,112449,105431,111037,103270,105530,115265,86400,86885,111216,116433,110056,101456,110672,109590,101169,102309,106261,86401,105853,113352,105143,107926,86886,114130,86887,114129,104130,85909,86912,110484,86913,104859,108408,112905,112902,100846,113441,104292,106693,116464,107488,109217,86914,105478,112134,102414,114683,108673,113564,109538,116487,108812,99976,102934,108426,107529,115618,109269,114957,111049,114453,107815,110124,104664,115679,104654,113193,109498,105409,113356,86915,86179,117624,100534,101860,116696,99904,113089,111217,101487,107098,114249,108491,86033,106984,108264,108701,114020,105732,113445,106149,104926,106184,103126,109550,107206,119403,103281,112095,104129,101332,116602,101106,86180);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (108807,117483,117607,105062,86854,86003,109907,100369,103765,86855,115611,104104,106009,100273,118465,117527,101207,102610,104294,99914,86004,108556,118233,112650,113983,117538,116753,109577,103214,106683,117518,110681,116897,103186,101438,114721,111011,115997,104684,103769,111496,115083,101709,111129,117752,100440,101685,103327,118549,86883,109855,114018,109122,117547,103414,104097,102012,117322,86399,107146,110165,116830,116235,101081,105265,101801,107013,105914,106985,117772,108042,117663,105534,99617,101198,103260,111443,113534,103484,114296,104419,108692,100432,114739,116327,115584,115585,102498,104228,115773,105671,101959,101852,117717,118027,86884,112449,105431,111037,103270,105530,115265,86400,86885,111216,116433,110056,101456,110672,109590,101169,102309,106261,86401,105853,113352,105143,107926,86886,114130,86887,114129,104130,85909,86912,110484,86913,104859,108408,112905,112902,100846,113441,104292,106693,116464,107488,109217,86914,105478,112134,102414,114683,108673,113564,109538,116487,108812,99976,102934,108426,107529,115618,109269,114957,111049,114453,107815,110124,104664,115679,104654,113193,109498,105409,113356,86915,86179,117624,100534,101860,116696,99904,113089,111217,101487,107098,114249,108491,86033,106984,108264,108701,114020,105732,113445,106149,104926,106184,103126,109550,107206,119403,103281,112095,104129,101332,116602,101106,86180);

delete quotes.* 
from quotes where instrumentid in (108807,117483,117607,105062,86854,86003,109907,100369,103765,86855,115611,104104,106009,100273,118465,117527,101207,102610,104294,99914,86004,108556,118233,112650,113983,117538,116753,109577,103214,106683,117518,110681,116897,103186,101438,114721,111011,115997,104684,103769,111496,115083,101709,111129,117752,100440,101685,103327,118549,86883,109855,114018,109122,117547,103414,104097,102012,117322,86399,107146,110165,116830,116235,101081,105265,101801,107013,105914,106985,117772,108042,117663,105534,99617,101198,103260,111443,113534,103484,114296,104419,108692,100432,114739,116327,115584,115585,102498,104228,115773,105671,101959,101852,117717,118027,86884,112449,105431,111037,103270,105530,115265,86400,86885,111216,116433,110056,101456,110672,109590,101169,102309,106261,86401,105853,113352,105143,107926,86886,114130,86887,114129,104130,85909,86912,110484,86913,104859,108408,112905,112902,100846,113441,104292,106693,116464,107488,109217,86914,105478,112134,102414,114683,108673,113564,109538,116487,108812,99976,102934,108426,107529,115618,109269,114957,111049,114453,107815,110124,104664,115679,104654,113193,109498,105409,113356,86915,86179,117624,100534,101860,116696,99904,113089,111217,101487,107098,114249,108491,86033,106984,108264,108701,114020,105732,113445,106149,104926,106184,103126,109550,107206,119403,103281,112095,104129,101332,116602,101106,86180);

commit;



begin;

select concat("FROM ",109162," TO ", 112770);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (109162,112217,104935,102366,112183,106523,117998,101913,100886,117308,113699,101932,101602,102148,86916,103342,86917,109136,113756,107042,101494,104609,106905,86181,117063,103807,110563,110390,106733,102682,107437,109433,104547,112446,112748,103938,116151,106094,117801,101577,100532,117784,115640,102947,108265,117565,101652,105813,101383,100776,101470,112447,101970,112781,115362,110314,107756,100777,110299,101472,102705,111868,104425,108497,114716,107076,108745,101464,109415,116681,112719,101182,100555,114532,100203,113565,101056,101977,111937,115537,105499,102460,109914,117459,114959,106203,99455,106734,102940,115552,104739,107063,115137,100120,113860,112306,111015,103395,99919,105371,105399,106604,113095,112315,86422,112138,108789,102738,115630,117788,116005,114167,118060,107742,112303,100978,108755,109416,114592,109163,101939,112636,86034,113702,115352,114166,117440,116520,114967,118613,101871,104930,114168,112324,106678,108342,103225,105937,101176,104682,106269,101757,108762,104206,110122,110653,113216,105940,116778,103895,117450,103946,86449,100954,108219,112438,103169,100861,114298,103124,111581,112289,102702,113422,102922,99732,115909,112442,115149,101141,112087,103893,103172,114155,102485,116716,108065,113424,109730,102885,107178,115805,112486,86969,114022,112892,111689,105475,104306,86970,100660,114069,100690,119264,113828,101546,115059,100342,114387,112770);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (109162,112217,104935,102366,112183,106523,117998,101913,100886,117308,113699,101932,101602,102148,86916,103342,86917,109136,113756,107042,101494,104609,106905,86181,117063,103807,110563,110390,106733,102682,107437,109433,104547,112446,112748,103938,116151,106094,117801,101577,100532,117784,115640,102947,108265,117565,101652,105813,101383,100776,101470,112447,101970,112781,115362,110314,107756,100777,110299,101472,102705,111868,104425,108497,114716,107076,108745,101464,109415,116681,112719,101182,100555,114532,100203,113565,101056,101977,111937,115537,105499,102460,109914,117459,114959,106203,99455,106734,102940,115552,104739,107063,115137,100120,113860,112306,111015,103395,99919,105371,105399,106604,113095,112315,86422,112138,108789,102738,115630,117788,116005,114167,118060,107742,112303,100978,108755,109416,114592,109163,101939,112636,86034,113702,115352,114166,117440,116520,114967,118613,101871,104930,114168,112324,106678,108342,103225,105937,101176,104682,106269,101757,108762,104206,110122,110653,113216,105940,116778,103895,117450,103946,86449,100954,108219,112438,103169,100861,114298,103124,111581,112289,102702,113422,102922,99732,115909,112442,115149,101141,112087,103893,103172,114155,102485,116716,108065,113424,109730,102885,107178,115805,112486,86969,114022,112892,111689,105475,104306,86970,100660,114069,100690,119264,113828,101546,115059,100342,114387,112770);

delete quotes.* 
from quotes where instrumentid in (109162,112217,104935,102366,112183,106523,117998,101913,100886,117308,113699,101932,101602,102148,86916,103342,86917,109136,113756,107042,101494,104609,106905,86181,117063,103807,110563,110390,106733,102682,107437,109433,104547,112446,112748,103938,116151,106094,117801,101577,100532,117784,115640,102947,108265,117565,101652,105813,101383,100776,101470,112447,101970,112781,115362,110314,107756,100777,110299,101472,102705,111868,104425,108497,114716,107076,108745,101464,109415,116681,112719,101182,100555,114532,100203,113565,101056,101977,111937,115537,105499,102460,109914,117459,114959,106203,99455,106734,102940,115552,104739,107063,115137,100120,113860,112306,111015,103395,99919,105371,105399,106604,113095,112315,86422,112138,108789,102738,115630,117788,116005,114167,118060,107742,112303,100978,108755,109416,114592,109163,101939,112636,86034,113702,115352,114166,117440,116520,114967,118613,101871,104930,114168,112324,106678,108342,103225,105937,101176,104682,106269,101757,108762,104206,110122,110653,113216,105940,116778,103895,117450,103946,86449,100954,108219,112438,103169,100861,114298,103124,111581,112289,102702,113422,102922,99732,115909,112442,115149,101141,112087,103893,103172,114155,102485,116716,108065,113424,109730,102885,107178,115805,112486,86969,114022,112892,111689,105475,104306,86970,100660,114069,100690,119264,113828,101546,115059,100342,114387,112770);

commit;



begin;

select concat("FROM ",102299," TO ", 99905);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (102299,103737,112620,109936,106177,109189,110747,112774,114731,111668,114591,86450,102191,116877,119408,103394,109216,114567,109182,103544,110135,111716,112255,110152,113500,102722,107097,118150,105722,104295,109270,99632,109926,118871,107800,113772,114962,105038,112762,113675,119185,110144,109207,86451,106360,106875,107862,109925,111070,118299,114014,109663,86989,86182,112130,107099,101900,85954,100884,116118,108754,108751,104919,101516,103816,100205,117830,101389,99756,109529,100671,103911,105064,104667,113713,111873,112199,111318,114482,108839,111877,115443,116614,113714,117069,104960,114077,101892,109475,104818,111199,102470,101064,109286,102870,111156,104387,109294,117345,103967,117343,110074,116142,101465,115589,102745,105881,108381,102874,99697,103428,114510,100784,117458,99482,114316,102010,103117,100678,99566,107265,110766,105101,112419,119197,100722,105220,110096,102766,108697,87011,103350,114862,112996,112560,101402,103264,117351,109267,117354,103274,111637,117519,86470,113525,86035,106909,114643,109783,100919,110101,115424,104729,118477,103052,106712,111164,99451,103959,115536,104679,108357,110232,101984,108691,111899,115684,113109,110459,106103,113550,114624,87012,99540,116597,105185,103027,106757,103107,110125,116876,116874,113563,111209,110436,115750,117359,106241,111299,109988,111953,114071,105916,104575,113552,105406,106144,102878,106702,99905);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (102299,103737,112620,109936,106177,109189,110747,112774,114731,111668,114591,86450,102191,116877,119408,103394,109216,114567,109182,103544,110135,111716,112255,110152,113500,102722,107097,118150,105722,104295,109270,99632,109926,118871,107800,113772,114962,105038,112762,113675,119185,110144,109207,86451,106360,106875,107862,109925,111070,118299,114014,109663,86989,86182,112130,107099,101900,85954,100884,116118,108754,108751,104919,101516,103816,100205,117830,101389,99756,109529,100671,103911,105064,104667,113713,111873,112199,111318,114482,108839,111877,115443,116614,113714,117069,104960,114077,101892,109475,104818,111199,102470,101064,109286,102870,111156,104387,109294,117345,103967,117343,110074,116142,101465,115589,102745,105881,108381,102874,99697,103428,114510,100784,117458,99482,114316,102010,103117,100678,99566,107265,110766,105101,112419,119197,100722,105220,110096,102766,108697,87011,103350,114862,112996,112560,101402,103264,117351,109267,117354,103274,111637,117519,86470,113525,86035,106909,114643,109783,100919,110101,115424,104729,118477,103052,106712,111164,99451,103959,115536,104679,108357,110232,101984,108691,111899,115684,113109,110459,106103,113550,114624,87012,99540,116597,105185,103027,106757,103107,110125,116876,116874,113563,111209,110436,115750,117359,106241,111299,109988,111953,114071,105916,104575,113552,105406,106144,102878,106702,99905);

delete quotes.* 
from quotes where instrumentid in (102299,103737,112620,109936,106177,109189,110747,112774,114731,111668,114591,86450,102191,116877,119408,103394,109216,114567,109182,103544,110135,111716,112255,110152,113500,102722,107097,118150,105722,104295,109270,99632,109926,118871,107800,113772,114962,105038,112762,113675,119185,110144,109207,86451,106360,106875,107862,109925,111070,118299,114014,109663,86989,86182,112130,107099,101900,85954,100884,116118,108754,108751,104919,101516,103816,100205,117830,101389,99756,109529,100671,103911,105064,104667,113713,111873,112199,111318,114482,108839,111877,115443,116614,113714,117069,104960,114077,101892,109475,104818,111199,102470,101064,109286,102870,111156,104387,109294,117345,103967,117343,110074,116142,101465,115589,102745,105881,108381,102874,99697,103428,114510,100784,117458,99482,114316,102010,103117,100678,99566,107265,110766,105101,112419,119197,100722,105220,110096,102766,108697,87011,103350,114862,112996,112560,101402,103264,117351,109267,117354,103274,111637,117519,86470,113525,86035,106909,114643,109783,100919,110101,115424,104729,118477,103052,106712,111164,99451,103959,115536,104679,108357,110232,101984,108691,111899,115684,113109,110459,106103,113550,114624,87012,99540,116597,105185,103027,106757,103107,110125,116876,116874,113563,111209,110436,115750,117359,106241,111299,109988,111953,114071,105916,104575,113552,105406,106144,102878,106702,99905);

commit;



begin;

select concat("FROM ",105915," TO ", 104691);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (105915,110437,115055,108495,114114,104873,104480,103806,101508,109012,115571,110465,117941,102165,105825,106233,85955,106243,106522,108548,103048,108908,110612,115320,103582,104678,113088,105005,118459,104659,106395,114867,104727,104042,104728,111634,87035,102832,87036,100233,103943,102521,113501,117041,107743,101886,107515,111624,102158,106729,103479,86218,114158,102826,108200,107616,116210,111662,104595,110315,100134,102986,111155,106231,116896,106559,117105,101267,86471,112231,104539,100599,86472,109830,110602,117573,104519,114321,102360,87037,103146,111154,106588,105312,110603,100070,100568,111692,103253,118685,112840,114030,86473,100231,110472,104158,115602,115650,112090,102259,107369,115649,111975,101809,102323,102080,102913,104546,116513,114961,112507,105219,106081,101191,104313,104831,87038,100826,113579,102686,118012,113002,108768,115963,107065,105812,116271,104634,110150,100085,109046,115653,106577,109928,118044,119228,105386,102899,115428,105639,110726,111843,99865,103834,116286,109287,100904,115160,105379,102669,108989,107734,87060,102095,102042,105617,87061,100007,112546,111688,105473,115648,102596,107131,106373,107520,111484,106891,103917,112550,106107,86509,104438,106947,113576,109648,114879,106598,111004,106338,105943,112104,99831,114963,116406,114965,111661,113643,87062,99844,103503,101941,111176,108845,87063,86510,117151,115930,105202,104691);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (105915,110437,115055,108495,114114,104873,104480,103806,101508,109012,115571,110465,117941,102165,105825,106233,85955,106243,106522,108548,103048,108908,110612,115320,103582,104678,113088,105005,118459,104659,106395,114867,104727,104042,104728,111634,87035,102832,87036,100233,103943,102521,113501,117041,107743,101886,107515,111624,102158,106729,103479,86218,114158,102826,108200,107616,116210,111662,104595,110315,100134,102986,111155,106231,116896,106559,117105,101267,86471,112231,104539,100599,86472,109830,110602,117573,104519,114321,102360,87037,103146,111154,106588,105312,110603,100070,100568,111692,103253,118685,112840,114030,86473,100231,110472,104158,115602,115650,112090,102259,107369,115649,111975,101809,102323,102080,102913,104546,116513,114961,112507,105219,106081,101191,104313,104831,87038,100826,113579,102686,118012,113002,108768,115963,107065,105812,116271,104634,110150,100085,109046,115653,106577,109928,118044,119228,105386,102899,115428,105639,110726,111843,99865,103834,116286,109287,100904,115160,105379,102669,108989,107734,87060,102095,102042,105617,87061,100007,112546,111688,105473,115648,102596,107131,106373,107520,111484,106891,103917,112550,106107,86509,104438,106947,113576,109648,114879,106598,111004,106338,105943,112104,99831,114963,116406,114965,111661,113643,87062,99844,103503,101941,111176,108845,87063,86510,117151,115930,105202,104691);

delete quotes.* 
from quotes where instrumentid in (105915,110437,115055,108495,114114,104873,104480,103806,101508,109012,115571,110465,117941,102165,105825,106233,85955,106243,106522,108548,103048,108908,110612,115320,103582,104678,113088,105005,118459,104659,106395,114867,104727,104042,104728,111634,87035,102832,87036,100233,103943,102521,113501,117041,107743,101886,107515,111624,102158,106729,103479,86218,114158,102826,108200,107616,116210,111662,104595,110315,100134,102986,111155,106231,116896,106559,117105,101267,86471,112231,104539,100599,86472,109830,110602,117573,104519,114321,102360,87037,103146,111154,106588,105312,110603,100070,100568,111692,103253,118685,112840,114030,86473,100231,110472,104158,115602,115650,112090,102259,107369,115649,111975,101809,102323,102080,102913,104546,116513,114961,112507,105219,106081,101191,104313,104831,87038,100826,113579,102686,118012,113002,108768,115963,107065,105812,116271,104634,110150,100085,109046,115653,106577,109928,118044,119228,105386,102899,115428,105639,110726,111843,99865,103834,116286,109287,100904,115160,105379,102669,108989,107734,87060,102095,102042,105617,87061,100007,112546,111688,105473,115648,102596,107131,106373,107520,111484,106891,103917,112550,106107,86509,104438,106947,113576,109648,114879,106598,111004,106338,105943,112104,99831,114963,116406,114965,111661,113643,87062,99844,103503,101941,111176,108845,87063,86510,117151,115930,105202,104691);

commit;



begin;

select concat("FROM ",103265," TO ", 105777);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (103265,103819,114964,109293,109210,107608,101417,115637,101266,102578,104010,102594,107636,102811,118719,116024,118230,86219,86511,118450,103388,86220,111981,105784,102246,106186,106189,111978,109583,117089,105806,106594,109582,113974,111842,110859,110844,117310,117477,114764,116691,101327,86512,101178,111977,104953,116003,86036,100410,119150,109664,111905,103642,111255,102868,107454,105941,102567,102894,114116,86513,115964,100558,102428,113693,100704,102858,113568,100746,112805,107718,108568,99841,112786,116803,100455,109282,112347,105616,117921,105476,100257,104110,99631,115816,114438,103522,102550,106300,113172,105541,107578,113680,111855,112286,108339,118038,108062,106525,115748,105318,106134,102859,116010,112002,110410,117653,107681,102262,116293,105378,108618,86541,103634,110078,85910,102520,105819,114817,114816,113900,116810,102728,105051,103271,103386,117597,116801,106658,108006,112245,112088,113982,111310,116015,118857,114154,117236,106660,85977,100059,109996,102064,104014,111141,102744,105235,114852,108167,100230,112202,114115,102751,109257,102491,116089,118065,114120,117198,100034,86244,101044,115455,103262,115318,107582,111189,101401,86245,113922,105862,114233,100150,116871,115588,106244,109004,107586,104789,108567,103409,106998,105743,100703,114851,112799,112365,115454,101040,113691,106178,106681,106402,110038,109255,101511,104369,115999,107532,105777);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (103265,103819,114964,109293,109210,107608,101417,115637,101266,102578,104010,102594,107636,102811,118719,116024,118230,86219,86511,118450,103388,86220,111981,105784,102246,106186,106189,111978,109583,117089,105806,106594,109582,113974,111842,110859,110844,117310,117477,114764,116691,101327,86512,101178,111977,104953,116003,86036,100410,119150,109664,111905,103642,111255,102868,107454,105941,102567,102894,114116,86513,115964,100558,102428,113693,100704,102858,113568,100746,112805,107718,108568,99841,112786,116803,100455,109282,112347,105616,117921,105476,100257,104110,99631,115816,114438,103522,102550,106300,113172,105541,107578,113680,111855,112286,108339,118038,108062,106525,115748,105318,106134,102859,116010,112002,110410,117653,107681,102262,116293,105378,108618,86541,103634,110078,85910,102520,105819,114817,114816,113900,116810,102728,105051,103271,103386,117597,116801,106658,108006,112245,112088,113982,111310,116015,118857,114154,117236,106660,85977,100059,109996,102064,104014,111141,102744,105235,114852,108167,100230,112202,114115,102751,109257,102491,116089,118065,114120,117198,100034,86244,101044,115455,103262,115318,107582,111189,101401,86245,113922,105862,114233,100150,116871,115588,106244,109004,107586,104789,108567,103409,106998,105743,100703,114851,112799,112365,115454,101040,113691,106178,106681,106402,110038,109255,101511,104369,115999,107532,105777);

delete quotes.* 
from quotes where instrumentid in (103265,103819,114964,109293,109210,107608,101417,115637,101266,102578,104010,102594,107636,102811,118719,116024,118230,86219,86511,118450,103388,86220,111981,105784,102246,106186,106189,111978,109583,117089,105806,106594,109582,113974,111842,110859,110844,117310,117477,114764,116691,101327,86512,101178,111977,104953,116003,86036,100410,119150,109664,111905,103642,111255,102868,107454,105941,102567,102894,114116,86513,115964,100558,102428,113693,100704,102858,113568,100746,112805,107718,108568,99841,112786,116803,100455,109282,112347,105616,117921,105476,100257,104110,99631,115816,114438,103522,102550,106300,113172,105541,107578,113680,111855,112286,108339,118038,108062,106525,115748,105318,106134,102859,116010,112002,110410,117653,107681,102262,116293,105378,108618,86541,103634,110078,85910,102520,105819,114817,114816,113900,116810,102728,105051,103271,103386,117597,116801,106658,108006,112245,112088,113982,111310,116015,118857,114154,117236,106660,85977,100059,109996,102064,104014,111141,102744,105235,114852,108167,100230,112202,114115,102751,109257,102491,116089,118065,114120,117198,100034,86244,101044,115455,103262,115318,107582,111189,101401,86245,113922,105862,114233,100150,116871,115588,106244,109004,107586,104789,108567,103409,106998,105743,100703,114851,112799,112365,115454,101040,113691,106178,106681,106402,110038,109255,101511,104369,115999,107532,105777);

commit;



begin;

select concat("FROM ",102990," TO ", 103789);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (102990,99862,100559,107670,108439,109331,103785,112563,114727,115703,114811,114846,86559,110279,112612,103261,100499,110129,103618,86246,112826,113803,117046,114847,102531,110257,106285,109274,115464,104410,112494,101902,112044,111328,106935,115466,110900,103030,86560,115156,102476,107129,112275,104520,112777,115652,107505,100423,105885,116465,104834,105944,104785,109172,86561,85881,105200,114757,113171,107713,105048,114850,105226,108797,101956,112772,106592,99915,107519,101922,103329,100894,104857,105115,86585,86065,111582,116510,108409,105860,118605,115581,108053,104154,116155,103960,112975,110615,114594,100512,100379,117462,111152,118607,104236,105858,101898,118275,117333,101281,102806,105397,109741,112469,113919,104160,114565,110283,105230,103380,109463,105474,114344,102918,113483,99745,101884,101067,106765,104393,86586,111151,115120,115161,118967,99575,112785,112924,110555,86263,104917,102585,105621,107119,110170,104334,107769,115322,102487,112831,115592,103857,102140,106040,117084,100999,111093,86066,108910,113516,105472,103097,107171,103667,101934,105103,111639,115908,103594,116913,115195,117258,112803,107306,105086,86610,116128,86611,111073,114760,106928,105225,86264,105037,105036,103478,102057,106250,116535,99652,114095,104316,114097,112448,114854,110509,111330,100808,99743,113949,110714,115162,111046,116481,103640,118091,103294,103075,118184,103789);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (102990,99862,100559,107670,108439,109331,103785,112563,114727,115703,114811,114846,86559,110279,112612,103261,100499,110129,103618,86246,112826,113803,117046,114847,102531,110257,106285,109274,115464,104410,112494,101902,112044,111328,106935,115466,110900,103030,86560,115156,102476,107129,112275,104520,112777,115652,107505,100423,105885,116465,104834,105944,104785,109172,86561,85881,105200,114757,113171,107713,105048,114850,105226,108797,101956,112772,106592,99915,107519,101922,103329,100894,104857,105115,86585,86065,111582,116510,108409,105860,118605,115581,108053,104154,116155,103960,112975,110615,114594,100512,100379,117462,111152,118607,104236,105858,101898,118275,117333,101281,102806,105397,109741,112469,113919,104160,114565,110283,105230,103380,109463,105474,114344,102918,113483,99745,101884,101067,106765,104393,86586,111151,115120,115161,118967,99575,112785,112924,110555,86263,104917,102585,105621,107119,110170,104334,107769,115322,102487,112831,115592,103857,102140,106040,117084,100999,111093,86066,108910,113516,105472,103097,107171,103667,101934,105103,111639,115908,103594,116913,115195,117258,112803,107306,105086,86610,116128,86611,111073,114760,106928,105225,86264,105037,105036,103478,102057,106250,116535,99652,114095,104316,114097,112448,114854,110509,111330,100808,99743,113949,110714,115162,111046,116481,103640,118091,103294,103075,118184,103789);

delete quotes.* 
from quotes where instrumentid in (102990,99862,100559,107670,108439,109331,103785,112563,114727,115703,114811,114846,86559,110279,112612,103261,100499,110129,103618,86246,112826,113803,117046,114847,102531,110257,106285,109274,115464,104410,112494,101902,112044,111328,106935,115466,110900,103030,86560,115156,102476,107129,112275,104520,112777,115652,107505,100423,105885,116465,104834,105944,104785,109172,86561,85881,105200,114757,113171,107713,105048,114850,105226,108797,101956,112772,106592,99915,107519,101922,103329,100894,104857,105115,86585,86065,111582,116510,108409,105860,118605,115581,108053,104154,116155,103960,112975,110615,114594,100512,100379,117462,111152,118607,104236,105858,101898,118275,117333,101281,102806,105397,109741,112469,113919,104160,114565,110283,105230,103380,109463,105474,114344,102918,113483,99745,101884,101067,106765,104393,86586,111151,115120,115161,118967,99575,112785,112924,110555,86263,104917,102585,105621,107119,110170,104334,107769,115322,102487,112831,115592,103857,102140,106040,117084,100999,111093,86066,108910,113516,105472,103097,107171,103667,101934,105103,111639,115908,103594,116913,115195,117258,112803,107306,105086,86610,116128,86611,111073,114760,106928,105225,86264,105037,105036,103478,102057,106250,116535,99652,114095,104316,114097,112448,114854,110509,111330,100808,99743,113949,110714,115162,111046,116481,103640,118091,103294,103075,118184,103789);

commit;



begin;

select concat("FROM ",100318," TO ", 107920);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (100318,113903,104876,114853,101043,115619,100674,106840,114569,110022,109516,113658,108511,112839,101237,106723,102668,114142,116662,107604,104518,112887,109157,102128,118354,112251,114966,101286,112978,102251,106014,106647,110139,104658,113132,99722,116168,104559,104134,101601,110431,109609,112349,113657,110194,101769,115392,115874,112358,105884,107142,105450,105726,104706,102736,112623,99737,86067,105532,116255,115892,85911,86645,113941,105270,117090,104671,107125,105094,113920,114002,113824,104269,112968,116448,99848,85883,106179,101264,117605,112773,111205,113512,110256,102248,100906,99945,86646,113650,100748,107007,108343,115116,110819,114841,114724,104407,107831,109298,109703,104649,108229,113862,100984,86104,115614,107797,102737,108163,104259,104342,117001,103687,118191,103114,101009,117506,116528,99543,106279,116949,102574,101512,112570,113925,99773,112628,117385,100360,110130,106080,103577,105783,113717,101451,106924,113780,113498,117889,107852,113174,103853,103845,105172,86294,118715,108436,104606,110840,100000,103006,110834,101343,86647,110839,110132,102519,112569,108442,73085,117119,86295,109537,85978,116704,109338,86674,112483,108454,117606,101826,99778,110412,108808,111950,111177,113566,111727,113942,118893,117670,102059,108749,109791,115967,106342,108376,100804,117083,107693,101231,107875,110582,105856,86675,111691,109462,111635,102437,107920);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (100318,113903,104876,114853,101043,115619,100674,106840,114569,110022,109516,113658,108511,112839,101237,106723,102668,114142,116662,107604,104518,112887,109157,102128,118354,112251,114966,101286,112978,102251,106014,106647,110139,104658,113132,99722,116168,104559,104134,101601,110431,109609,112349,113657,110194,101769,115392,115874,112358,105884,107142,105450,105726,104706,102736,112623,99737,86067,105532,116255,115892,85911,86645,113941,105270,117090,104671,107125,105094,113920,114002,113824,104269,112968,116448,99848,85883,106179,101264,117605,112773,111205,113512,110256,102248,100906,99945,86646,113650,100748,107007,108343,115116,110819,114841,114724,104407,107831,109298,109703,104649,108229,113862,100984,86104,115614,107797,102737,108163,104259,104342,117001,103687,118191,103114,101009,117506,116528,99543,106279,116949,102574,101512,112570,113925,99773,112628,117385,100360,110130,106080,103577,105783,113717,101451,106924,113780,113498,117889,107852,113174,103853,103845,105172,86294,118715,108436,104606,110840,100000,103006,110834,101343,86647,110839,110132,102519,112569,108442,73085,117119,86295,109537,85978,116704,109338,86674,112483,108454,117606,101826,99778,110412,108808,111950,111177,113566,111727,113942,118893,117670,102059,108749,109791,115967,106342,108376,100804,117083,107693,101231,107875,110582,105856,86675,111691,109462,111635,102437,107920);

delete quotes.* 
from quotes where instrumentid in (100318,113903,104876,114853,101043,115619,100674,106840,114569,110022,109516,113658,108511,112839,101237,106723,102668,114142,116662,107604,104518,112887,109157,102128,118354,112251,114966,101286,112978,102251,106014,106647,110139,104658,113132,99722,116168,104559,104134,101601,110431,109609,112349,113657,110194,101769,115392,115874,112358,105884,107142,105450,105726,104706,102736,112623,99737,86067,105532,116255,115892,85911,86645,113941,105270,117090,104671,107125,105094,113920,114002,113824,104269,112968,116448,99848,85883,106179,101264,117605,112773,111205,113512,110256,102248,100906,99945,86646,113650,100748,107007,108343,115116,110819,114841,114724,104407,107831,109298,109703,104649,108229,113862,100984,86104,115614,107797,102737,108163,104259,104342,117001,103687,118191,103114,101009,117506,116528,99543,106279,116949,102574,101512,112570,113925,99773,112628,117385,100360,110130,106080,103577,105783,113717,101451,106924,113780,113498,117889,107852,113174,103853,103845,105172,86294,118715,108436,104606,110840,100000,103006,110834,101343,86647,110839,110132,102519,112569,108442,73085,117119,86295,109537,85978,116704,109338,86674,112483,108454,117606,101826,99778,110412,108808,111950,111177,113566,111727,113942,118893,117670,102059,108749,109791,115967,106342,108376,100804,117083,107693,101231,107875,110582,105856,86675,111691,109462,111635,102437,107920);

commit;



begin;

select concat("FROM ",114082," TO ", 108750);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (114082,113968,118729,109847,116268,113748,107283,116277,100530,114017,117085,104470,114484,106102,118410,103210,113886,116116,112049,102743,106004,115152,113161,86676,100822,102997,110688,118278,103868,110142,104197,101527,105082,113659,104299,104207,109735,102581,113472,106064,106960,103741,119008,113426,115442,106286,102100,105923,112721,102517,110252,111157,108165,116485,110153,100944,100586,102087,115966,104324,111863,114055,100600,102250,113527,105897,107043,114004,110298,110911,110707,116673,117979,108871,107424,100418,104548,102505,100336,86693,115825,115425,99663,104844,117269,109525,118655,108933,102568,101449,114832,106044,101499,108573,114405,86694,105389,113029,101391,116211,100308,115538,115904,101277,118888,115799,102381,108142,102727,100364,111648,116292,101794,86319,116444,107107,101082,112262,112343,115596,109605,107512,106341,113784,113076,112045,103285,118276,102854,99793,110242,108364,104938,113346,108867,101007,119299,115580,103160,108748,110080,109219,109536,85912,111658,115163,101894,86105,117671,104318,102893,104637,110891,112509,116447,109674,114195,101893,100436,115085,116164,86320,114139,109546,114140,110838,102195,108419,99771,102254,113649,114023,86724,115590,102713,103774,106549,110277,112827,109327,106007,109166,116324,116600,107923,101295,108510,116703,86725,85878,109297,111007,108771,102089,107851,115965,111511,109169,107141,108750);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (114082,113968,118729,109847,116268,113748,107283,116277,100530,114017,117085,104470,114484,106102,118410,103210,113886,116116,112049,102743,106004,115152,113161,86676,100822,102997,110688,118278,103868,110142,104197,101527,105082,113659,104299,104207,109735,102581,113472,106064,106960,103741,119008,113426,115442,106286,102100,105923,112721,102517,110252,111157,108165,116485,110153,100944,100586,102087,115966,104324,111863,114055,100600,102250,113527,105897,107043,114004,110298,110911,110707,116673,117979,108871,107424,100418,104548,102505,100336,86693,115825,115425,99663,104844,117269,109525,118655,108933,102568,101449,114832,106044,101499,108573,114405,86694,105389,113029,101391,116211,100308,115538,115904,101277,118888,115799,102381,108142,102727,100364,111648,116292,101794,86319,116444,107107,101082,112262,112343,115596,109605,107512,106341,113784,113076,112045,103285,118276,102854,99793,110242,108364,104938,113346,108867,101007,119299,115580,103160,108748,110080,109219,109536,85912,111658,115163,101894,86105,117671,104318,102893,104637,110891,112509,116447,109674,114195,101893,100436,115085,116164,86320,114139,109546,114140,110838,102195,108419,99771,102254,113649,114023,86724,115590,102713,103774,106549,110277,112827,109327,106007,109166,116324,116600,107923,101295,108510,116703,86725,85878,109297,111007,108771,102089,107851,115965,111511,109169,107141,108750);

delete quotes.* 
from quotes where instrumentid in (114082,113968,118729,109847,116268,113748,107283,116277,100530,114017,117085,104470,114484,106102,118410,103210,113886,116116,112049,102743,106004,115152,113161,86676,100822,102997,110688,118278,103868,110142,104197,101527,105082,113659,104299,104207,109735,102581,113472,106064,106960,103741,119008,113426,115442,106286,102100,105923,112721,102517,110252,111157,108165,116485,110153,100944,100586,102087,115966,104324,111863,114055,100600,102250,113527,105897,107043,114004,110298,110911,110707,116673,117979,108871,107424,100418,104548,102505,100336,86693,115825,115425,99663,104844,117269,109525,118655,108933,102568,101449,114832,106044,101499,108573,114405,86694,105389,113029,101391,116211,100308,115538,115904,101277,118888,115799,102381,108142,102727,100364,111648,116292,101794,86319,116444,107107,101082,112262,112343,115596,109605,107512,106341,113784,113076,112045,103285,118276,102854,99793,110242,108364,104938,113346,108867,101007,119299,115580,103160,108748,110080,109219,109536,85912,111658,115163,101894,86105,117671,104318,102893,104637,110891,112509,116447,109674,114195,101893,100436,115085,116164,86320,114139,109546,114140,110838,102195,108419,99771,102254,113649,114023,86724,115590,102713,103774,106549,110277,112827,109327,106007,109166,116324,116600,107923,101295,108510,116703,86725,85878,109297,111007,108771,102089,107851,115965,111511,109169,107141,108750);

commit;



begin;

select concat("FROM ",107613," TO ", 111497);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (107613,114062,113992,103171,85913,111656,103290,114844,114343,86726,86727,112450,104680,100050,113647,86728,101891,103119,116794,116500,99772,103524,106333,106294,114201,118141,114793,112188,109691,117430,106721,86729,110379,100444,117182,111305,114601,112128,112344,112787,116684,86730,112253,114378,108407,111019,113998,102901,105018,104289,111627,114459,105089,104472,115881,86354,86760,109689,114025,108936,118706,114141,86761,114015,105398,116640,106246,109662,108501,101500,115029,115028,106082,111468,111902,107389,109300,110364,103212,100638,103965,109237,113394,102426,104662,100634,113734,100255,105646,116150,115366,106008,106866,108367,117683,112453,102813,110468,110050,107518,100286,105737,101687,101942,99933,105336,99898,100749,116706,101554,101450,116675,113169,104882,103054,99657,115134,112599,113541,104322,115656,99702,108493,113678,118865,115540,107955,119450,101775,117111,119223,106187,105466,113706,118384,109626,110619,114003,103120,101239,119177,114165,112529,119190,107846,106452,111233,86762,113967,102542,107553,108027,111191,86355,109810,110255,110441,116207,112491,110133,118468,113030,118833,115444,100334,100945,106171,108756,110030,110645,102395,101222,106162,106953,117972,107757,86356,110036,100294,112493,114802,101329,101696,106029,109894,100663,119273,117487,101586,115524,116156,112416,86357,106076,110025,111422,102166,111002,111890,111497);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (107613,114062,113992,103171,85913,111656,103290,114844,114343,86726,86727,112450,104680,100050,113647,86728,101891,103119,116794,116500,99772,103524,106333,106294,114201,118141,114793,112188,109691,117430,106721,86729,110379,100444,117182,111305,114601,112128,112344,112787,116684,86730,112253,114378,108407,111019,113998,102901,105018,104289,111627,114459,105089,104472,115881,86354,86760,109689,114025,108936,118706,114141,86761,114015,105398,116640,106246,109662,108501,101500,115029,115028,106082,111468,111902,107389,109300,110364,103212,100638,103965,109237,113394,102426,104662,100634,113734,100255,105646,116150,115366,106008,106866,108367,117683,112453,102813,110468,110050,107518,100286,105737,101687,101942,99933,105336,99898,100749,116706,101554,101450,116675,113169,104882,103054,99657,115134,112599,113541,104322,115656,99702,108493,113678,118865,115540,107955,119450,101775,117111,119223,106187,105466,113706,118384,109626,110619,114003,103120,101239,119177,114165,112529,119190,107846,106452,111233,86762,113967,102542,107553,108027,111191,86355,109810,110255,110441,116207,112491,110133,118468,113030,118833,115444,100334,100945,106171,108756,110030,110645,102395,101222,106162,106953,117972,107757,86356,110036,100294,112493,114802,101329,101696,106029,109894,100663,119273,117487,101586,115524,116156,112416,86357,106076,110025,111422,102166,111002,111890,111497);

delete quotes.* 
from quotes where instrumentid in (107613,114062,113992,103171,85913,111656,103290,114844,114343,86726,86727,112450,104680,100050,113647,86728,101891,103119,116794,116500,99772,103524,106333,106294,114201,118141,114793,112188,109691,117430,106721,86729,110379,100444,117182,111305,114601,112128,112344,112787,116684,86730,112253,114378,108407,111019,113998,102901,105018,104289,111627,114459,105089,104472,115881,86354,86760,109689,114025,108936,118706,114141,86761,114015,105398,116640,106246,109662,108501,101500,115029,115028,106082,111468,111902,107389,109300,110364,103212,100638,103965,109237,113394,102426,104662,100634,113734,100255,105646,116150,115366,106008,106866,108367,117683,112453,102813,110468,110050,107518,100286,105737,101687,101942,99933,105336,99898,100749,116706,101554,101450,116675,113169,104882,103054,99657,115134,112599,113541,104322,115656,99702,108493,113678,118865,115540,107955,119450,101775,117111,119223,106187,105466,113706,118384,109626,110619,114003,103120,101239,119177,114165,112529,119190,107846,106452,111233,86762,113967,102542,107553,108027,111191,86355,109810,110255,110441,116207,112491,110133,118468,113030,118833,115444,100334,100945,106171,108756,110030,110645,102395,101222,106162,106953,117972,107757,86356,110036,100294,112493,114802,101329,101696,106029,109894,100663,119273,117487,101586,115524,116156,112416,86357,106076,110025,111422,102166,111002,111890,111497);

commit;



begin;

select concat("FROM ",102620," TO ", 100003);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (102620,114551,110949,117494,99758,103568,116831,103232,110515,114678,104336,86358,99655,112766,108075,112356,100464,86141,118304,110824,111352,108811,118952,104107,112459,102267,110018,115237,100931,102891,111236,116200,107376,110073,117719,106591,109284,114545,103032,102312,112763,111489,113069,111248,109143,111794,99949,86811,105896,115216,111733,113041,105217,105167,112420,111308,115346,110451,100288,105201,101785,105353,114424,103954,103000,100891,108650,116185,108813,115837,106038,110241,101140,114548,115623,99767,108792,99908,102589,108176,105630,100821,101321,107143,100799,113159,99795,106274,115970,105776,104674,112780,115503,116152,99496,103855,74307,104894,115958,118750,100706,109276,100930,114056,99936,115548,114855,86812,114252,111319,115245,113584,105183,106251,112422,118076,86813,102759,110642,109396,119122,116445,113583,100239,101627,100647,105623,112779,116957,111565,111687,109489,108840,103801,112533,108151,99814,107075,103366,99440,112864,105052,112531,107577,105045,102458,108014,129624,105146,101339,102496,117287,108625,110342,105221,114404,107697,102241,107672,113913,115891,109906,108753,117493,86831,105750,99895,101878,104860,86387,108667,103247,114636,115071,112505,114925,86832,105387,102914,85951,119065,108589,108435,86142,86833,112198,108909,99725,117141,112024,109401,102845,101740,112971,107716,104309,108932,101782,99860,100003);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (102620,114551,110949,117494,99758,103568,116831,103232,110515,114678,104336,86358,99655,112766,108075,112356,100464,86141,118304,110824,111352,108811,118952,104107,112459,102267,110018,115237,100931,102891,111236,116200,107376,110073,117719,106591,109284,114545,103032,102312,112763,111489,113069,111248,109143,111794,99949,86811,105896,115216,111733,113041,105217,105167,112420,111308,115346,110451,100288,105201,101785,105353,114424,103954,103000,100891,108650,116185,108813,115837,106038,110241,101140,114548,115623,99767,108792,99908,102589,108176,105630,100821,101321,107143,100799,113159,99795,106274,115970,105776,104674,112780,115503,116152,99496,103855,74307,104894,115958,118750,100706,109276,100930,114056,99936,115548,114855,86812,114252,111319,115245,113584,105183,106251,112422,118076,86813,102759,110642,109396,119122,116445,113583,100239,101627,100647,105623,112779,116957,111565,111687,109489,108840,103801,112533,108151,99814,107075,103366,99440,112864,105052,112531,107577,105045,102458,108014,129624,105146,101339,102496,117287,108625,110342,105221,114404,107697,102241,107672,113913,115891,109906,108753,117493,86831,105750,99895,101878,104860,86387,108667,103247,114636,115071,112505,114925,86832,105387,102914,85951,119065,108589,108435,86142,86833,112198,108909,99725,117141,112024,109401,102845,101740,112971,107716,104309,108932,101782,99860,100003);

delete quotes.* 
from quotes where instrumentid in (102620,114551,110949,117494,99758,103568,116831,103232,110515,114678,104336,86358,99655,112766,108075,112356,100464,86141,118304,110824,111352,108811,118952,104107,112459,102267,110018,115237,100931,102891,111236,116200,107376,110073,117719,106591,109284,114545,103032,102312,112763,111489,113069,111248,109143,111794,99949,86811,105896,115216,111733,113041,105217,105167,112420,111308,115346,110451,100288,105201,101785,105353,114424,103954,103000,100891,108650,116185,108813,115837,106038,110241,101140,114548,115623,99767,108792,99908,102589,108176,105630,100821,101321,107143,100799,113159,99795,106274,115970,105776,104674,112780,115503,116152,99496,103855,74307,104894,115958,118750,100706,109276,100930,114056,99936,115548,114855,86812,114252,111319,115245,113584,105183,106251,112422,118076,86813,102759,110642,109396,119122,116445,113583,100239,101627,100647,105623,112779,116957,111565,111687,109489,108840,103801,112533,108151,99814,107075,103366,99440,112864,105052,112531,107577,105045,102458,108014,129624,105146,101339,102496,117287,108625,110342,105221,114404,107697,102241,107672,113913,115891,109906,108753,117493,86831,105750,99895,101878,104860,86387,108667,103247,114636,115071,112505,114925,86832,105387,102914,85951,119065,108589,108435,86142,86833,112198,108909,99725,117141,112024,109401,102845,101740,112971,107716,104309,108932,101782,99860,100003);

commit;



begin;

select concat("FROM ",112879," TO ", 101152);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (112879,114830,107313,104681,114791,112757,108246,105177,110767,111380,107308,110072,86834,112025,113839,116812,86835,117875,114572,117464,103968,109292,111245,111175,113656,104869,107610,85884,114573,114279,112838,103179,102486,118051,101219,101662,111517,103377,116616,113682,86836,86388,110551,103647,111643,113870,104034,105197,101707,112631,112771,114278,119124,86412,86856,116805,112935,104314,110948,102375,110659,110841,118579,108149,99640,113043,86857,103090,100517,86413,113590,114927,106316,100947,106722,105394,112400,105315,99994,115290,106761,101890,111535,102335,115004,107124,116917,111149,100889,115128,104230,103771,116956,111246,86165,116990,109808,114913,101960,109428,118075,114915,107788,113293,105117,105262,104899,115595,115582,118277,107444,102340,99479,104820,113409,115445,112495,105069,113009,116739,114784,106324,86858,110584,101535,117656,114680,108465,114882,101468,106882,115257,104020,116170,107566,101259,111720,104043,105829,104565,112173,102376,115262,109266,118090,106848,102044,101236,117599,101235,100902,110034,111479,106019,106973,118267,115310,110869,112836,110210,114472,113559,105147,99553,115959,115634,86880,113861,116314,111247,110893,102062,105321,111182,115632,101644,102930,116270,101661,116542,104373,114057,107939,113229,105346,110892,101793,109141,114628,114292,110951,112807,86881,107844,107320,101649,101580,111323,100920,101152);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (112879,114830,107313,104681,114791,112757,108246,105177,110767,111380,107308,110072,86834,112025,113839,116812,86835,117875,114572,117464,103968,109292,111245,111175,113656,104869,107610,85884,114573,114279,112838,103179,102486,118051,101219,101662,111517,103377,116616,113682,86836,86388,110551,103647,111643,113870,104034,105197,101707,112631,112771,114278,119124,86412,86856,116805,112935,104314,110948,102375,110659,110841,118579,108149,99640,113043,86857,103090,100517,86413,113590,114927,106316,100947,106722,105394,112400,105315,99994,115290,106761,101890,111535,102335,115004,107124,116917,111149,100889,115128,104230,103771,116956,111246,86165,116990,109808,114913,101960,109428,118075,114915,107788,113293,105117,105262,104899,115595,115582,118277,107444,102340,99479,104820,113409,115445,112495,105069,113009,116739,114784,106324,86858,110584,101535,117656,114680,108465,114882,101468,106882,115257,104020,116170,107566,101259,111720,104043,105829,104565,112173,102376,115262,109266,118090,106848,102044,101236,117599,101235,100902,110034,111479,106019,106973,118267,115310,110869,112836,110210,114472,113559,105147,99553,115959,115634,86880,113861,116314,111247,110893,102062,105321,111182,115632,101644,102930,116270,101661,116542,104373,114057,107939,113229,105346,110892,101793,109141,114628,114292,110951,112807,86881,107844,107320,101649,101580,111323,100920,101152);

delete quotes.* 
from quotes where instrumentid in (112879,114830,107313,104681,114791,112757,108246,105177,110767,111380,107308,110072,86834,112025,113839,116812,86835,117875,114572,117464,103968,109292,111245,111175,113656,104869,107610,85884,114573,114279,112838,103179,102486,118051,101219,101662,111517,103377,116616,113682,86836,86388,110551,103647,111643,113870,104034,105197,101707,112631,112771,114278,119124,86412,86856,116805,112935,104314,110948,102375,110659,110841,118579,108149,99640,113043,86857,103090,100517,86413,113590,114927,106316,100947,106722,105394,112400,105315,99994,115290,106761,101890,111535,102335,115004,107124,116917,111149,100889,115128,104230,103771,116956,111246,86165,116990,109808,114913,101960,109428,118075,114915,107788,113293,105117,105262,104899,115595,115582,118277,107444,102340,99479,104820,113409,115445,112495,105069,113009,116739,114784,106324,86858,110584,101535,117656,114680,108465,114882,101468,106882,115257,104020,116170,107566,101259,111720,104043,105829,104565,112173,102376,115262,109266,118090,106848,102044,101236,117599,101235,100902,110034,111479,106019,106973,118267,115310,110869,112836,110210,114472,113559,105147,99553,115959,115634,86880,113861,116314,111247,110893,102062,105321,111182,115632,101644,102930,116270,101661,116542,104373,114057,107939,113229,105346,110892,101793,109141,114628,114292,110951,112807,86881,107844,107320,101649,101580,111323,100920,101152);

commit;



begin;

select concat("FROM ",111148," TO ", 111705);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (111148,101034,113077,103147,103029,113511,102506,114552,109943,100027,105800,113934,108298,115314,112498,105543,99595,103398,107064,109534,114005,110730,107524,86882,101484,113914,116742,106685,103638,104580,113294,108858,106319,106183,100948,102079,109252,115968,99718,104798,114577,111029,110786,101599,113935,101216,115654,115655,116201,116705,105965,113573,113036,101122,108992,107847,114549,102466,116014,110293,105070,99579,103084,99612,114744,110646,107271,109678,110259,112940,103910,103548,115239,104083,114163,86902,113971,103800,104356,107907,118739,106800,100368,113370,101471,111183,101359,104415,104422,107049,105524,106574,115719,106835,110690,107583,117950,108239,105393,118998,100797,112862,114007,104668,99561,112918,111304,86903,111608,99483,108816,86904,112193,111097,86166,101161,106500,111339,118780,113233,118428,107915,111640,111302,114877,112281,112280,115024,106908,110660,110281,109954,109719,108331,101002,107433,117154,118149,113652,105218,103175,102373,115541,115173,104321,109116,112460,112923,99777,116169,111153,110351,112548,113289,105304,117109,114386,109465,110634,111253,118146,112052,105664,116033,114928,115627,106731,106527,110649,105317,116130,103505,86935,103890,104423,101392,106526,111684,111462,113371,114159,86019,100675,99645,114475,102007,103401,110304,86936,110525,114813,116791,103276,100163,99749,100392,110620,86020,115104,111705);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (111148,101034,113077,103147,103029,113511,102506,114552,109943,100027,105800,113934,108298,115314,112498,105543,99595,103398,107064,109534,114005,110730,107524,86882,101484,113914,116742,106685,103638,104580,113294,108858,106319,106183,100948,102079,109252,115968,99718,104798,114577,111029,110786,101599,113935,101216,115654,115655,116201,116705,105965,113573,113036,101122,108992,107847,114549,102466,116014,110293,105070,99579,103084,99612,114744,110646,107271,109678,110259,112940,103910,103548,115239,104083,114163,86902,113971,103800,104356,107907,118739,106800,100368,113370,101471,111183,101359,104415,104422,107049,105524,106574,115719,106835,110690,107583,117950,108239,105393,118998,100797,112862,114007,104668,99561,112918,111304,86903,111608,99483,108816,86904,112193,111097,86166,101161,106500,111339,118780,113233,118428,107915,111640,111302,114877,112281,112280,115024,106908,110660,110281,109954,109719,108331,101002,107433,117154,118149,113652,105218,103175,102373,115541,115173,104321,109116,112460,112923,99777,116169,111153,110351,112548,113289,105304,117109,114386,109465,110634,111253,118146,112052,105664,116033,114928,115627,106731,106527,110649,105317,116130,103505,86935,103890,104423,101392,106526,111684,111462,113371,114159,86019,100675,99645,114475,102007,103401,110304,86936,110525,114813,116791,103276,100163,99749,100392,110620,86020,115104,111705);

delete quotes.* 
from quotes where instrumentid in (111148,101034,113077,103147,103029,113511,102506,114552,109943,100027,105800,113934,108298,115314,112498,105543,99595,103398,107064,109534,114005,110730,107524,86882,101484,113914,116742,106685,103638,104580,113294,108858,106319,106183,100948,102079,109252,115968,99718,104798,114577,111029,110786,101599,113935,101216,115654,115655,116201,116705,105965,113573,113036,101122,108992,107847,114549,102466,116014,110293,105070,99579,103084,99612,114744,110646,107271,109678,110259,112940,103910,103548,115239,104083,114163,86902,113971,103800,104356,107907,118739,106800,100368,113370,101471,111183,101359,104415,104422,107049,105524,106574,115719,106835,110690,107583,117950,108239,105393,118998,100797,112862,114007,104668,99561,112918,111304,86903,111608,99483,108816,86904,112193,111097,86166,101161,106500,111339,118780,113233,118428,107915,111640,111302,114877,112281,112280,115024,106908,110660,110281,109954,109719,108331,101002,107433,117154,118149,113652,105218,103175,102373,115541,115173,104321,109116,112460,112923,99777,116169,111153,110351,112548,113289,105304,117109,114386,109465,110634,111253,118146,112052,105664,116033,114928,115627,106731,106527,110649,105317,116130,103505,86935,103890,104423,101392,106526,111684,111462,113371,114159,86019,100675,99645,114475,102007,103401,110304,86936,110525,114813,116791,103276,100163,99749,100392,110620,86020,115104,111705);

commit;



begin;

select concat("FROM ",111997," TO ", 103321);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (111997,117724,109952,114276,102367,114253,118546,103798,115259,86167,108238,111388,116285,101556,104553,103793,113028,118159,111723,113747,100441,112056,109279,115994,105027,116108,102107,109120,109766,86021,86937,116686,113247,104689,113958,111214,100700,112032,105077,86455,113544,86198,101477,103665,108926,107494,100803,106173,101938,102104,116827,105582,105296,104692,109743,110244,115929,112434,99906,100697,112552,115560,110291,86456,105871,101746,105934,86457,102790,115000,110952,100448,110520,86964,102974,103651,105569,100046,102004,109528,101080,110108,117180,118363,105173,115551,107345,109848,105071,106936,100612,101316,102467,103821,102872,107051,102358,100445,104053,110624,114070,102786,119462,108172,111677,108672,99996,86965,116018,106743,99824,104271,104594,116442,104006,110627,99570,111682,105491,115025,107396,106363,103751,86047,115787,101692,115830,106455,106061,101482,116434,110944,109250,107475,110174,102825,100598,105470,99534,117570,100583,86458,111009,109427,108468,111181,112890,106752,100802,112796,103696,109329,105193,100539,118286,112835,113364,86983,103278,99817,108883,106620,85928,114035,118764,110301,114465,114529,104435,104167,112380,114700,103149,100404,108848,111376,86459,113600,101873,99890,107304,111050,107299,116514,114327,112680,102400,86460,117824,111441,86984,106033,104103,117488,103106,108860,104736,111324,104301,103321);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (111997,117724,109952,114276,102367,114253,118546,103798,115259,86167,108238,111388,116285,101556,104553,103793,113028,118159,111723,113747,100441,112056,109279,115994,105027,116108,102107,109120,109766,86021,86937,116686,113247,104689,113958,111214,100700,112032,105077,86455,113544,86198,101477,103665,108926,107494,100803,106173,101938,102104,116827,105582,105296,104692,109743,110244,115929,112434,99906,100697,112552,115560,110291,86456,105871,101746,105934,86457,102790,115000,110952,100448,110520,86964,102974,103651,105569,100046,102004,109528,101080,110108,117180,118363,105173,115551,107345,109848,105071,106936,100612,101316,102467,103821,102872,107051,102358,100445,104053,110624,114070,102786,119462,108172,111677,108672,99996,86965,116018,106743,99824,104271,104594,116442,104006,110627,99570,111682,105491,115025,107396,106363,103751,86047,115787,101692,115830,106455,106061,101482,116434,110944,109250,107475,110174,102825,100598,105470,99534,117570,100583,86458,111009,109427,108468,111181,112890,106752,100802,112796,103696,109329,105193,100539,118286,112835,113364,86983,103278,99817,108883,106620,85928,114035,118764,110301,114465,114529,104435,104167,112380,114700,103149,100404,108848,111376,86459,113600,101873,99890,107304,111050,107299,116514,114327,112680,102400,86460,117824,111441,86984,106033,104103,117488,103106,108860,104736,111324,104301,103321);

delete quotes.* 
from quotes where instrumentid in (111997,117724,109952,114276,102367,114253,118546,103798,115259,86167,108238,111388,116285,101556,104553,103793,113028,118159,111723,113747,100441,112056,109279,115994,105027,116108,102107,109120,109766,86021,86937,116686,113247,104689,113958,111214,100700,112032,105077,86455,113544,86198,101477,103665,108926,107494,100803,106173,101938,102104,116827,105582,105296,104692,109743,110244,115929,112434,99906,100697,112552,115560,110291,86456,105871,101746,105934,86457,102790,115000,110952,100448,110520,86964,102974,103651,105569,100046,102004,109528,101080,110108,117180,118363,105173,115551,107345,109848,105071,106936,100612,101316,102467,103821,102872,107051,102358,100445,104053,110624,114070,102786,119462,108172,111677,108672,99996,86965,116018,106743,99824,104271,104594,116442,104006,110627,99570,111682,105491,115025,107396,106363,103751,86047,115787,101692,115830,106455,106061,101482,116434,110944,109250,107475,110174,102825,100598,105470,99534,117570,100583,86458,111009,109427,108468,111181,112890,106752,100802,112796,103696,109329,105193,100539,118286,112835,113364,86983,103278,99817,108883,106620,85928,114035,118764,110301,114465,114529,104435,104167,112380,114700,103149,100404,108848,111376,86459,113600,101873,99890,107304,111050,107299,116514,114327,112680,102400,86460,117824,111441,86984,106033,104103,117488,103106,108860,104736,111324,104301,103321);

commit;



begin;

select concat("FROM ",105528," TO ", 108693);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (105528,115014,105484,100812,118142,117629,104366,109679,104153,104762,111974,116745,100688,106627,86985,86461,118034,109554,100459,112381,104347,109579,117496,114798,112502,100932,108296,112490,111629,114845,108074,113310,117362,116257,109913,116974,115746,103510,115260,104477,116119,102240,100033,112162,112795,104554,112378,102124,107139,108582,108688,104282,86999,111094,115699,109712,87000,115604,87001,105168,101124,109941,118169,113300,114790,116362,101340,117967,101966,102370,103982,85952,105385,102396,105942,101393,112368,105887,113655,113999,109277,103515,114439,108019,115233,86493,114803,113513,102123,103180,117033,102099,114886,116073,99473,115570,109092,110343,105311,110350,101674,109026,114054,110745,102328,101864,101263,116817,114778,104537,103854,86494,109930,111061,117903,113825,116989,108744,119126,102616,108030,86199,108124,110837,115910,109158,114161,104620,104054,115826,115612,111332,114839,105847,115597,109422,104426,86200,113578,109232,106487,111865,103756,108695,109978,116620,108680,86495,114517,111861,107841,104287,117756,105797,87027,112394,115064,117662,106056,104297,107703,87028,108632,112390,100824,109935,106607,109561,110429,117892,111713,100780,109431,117598,86496,115579,86201,106811,114323,119411,107748,106550,111948,107918,115338,102239,114425,102667,113538,105979,116516,113777,100610,115371,99760,101963,113705,111174,102077,108693);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (105528,115014,105484,100812,118142,117629,104366,109679,104153,104762,111974,116745,100688,106627,86985,86461,118034,109554,100459,112381,104347,109579,117496,114798,112502,100932,108296,112490,111629,114845,108074,113310,117362,116257,109913,116974,115746,103510,115260,104477,116119,102240,100033,112162,112795,104554,112378,102124,107139,108582,108688,104282,86999,111094,115699,109712,87000,115604,87001,105168,101124,109941,118169,113300,114790,116362,101340,117967,101966,102370,103982,85952,105385,102396,105942,101393,112368,105887,113655,113999,109277,103515,114439,108019,115233,86493,114803,113513,102123,103180,117033,102099,114886,116073,99473,115570,109092,110343,105311,110350,101674,109026,114054,110745,102328,101864,101263,116817,114778,104537,103854,86494,109930,111061,117903,113825,116989,108744,119126,102616,108030,86199,108124,110837,115910,109158,114161,104620,104054,115826,115612,111332,114839,105847,115597,109422,104426,86200,113578,109232,106487,111865,103756,108695,109978,116620,108680,86495,114517,111861,107841,104287,117756,105797,87027,112394,115064,117662,106056,104297,107703,87028,108632,112390,100824,109935,106607,109561,110429,117892,111713,100780,109431,117598,86496,115579,86201,106811,114323,119411,107748,106550,111948,107918,115338,102239,114425,102667,113538,105979,116516,113777,100610,115371,99760,101963,113705,111174,102077,108693);

delete quotes.* 
from quotes where instrumentid in (105528,115014,105484,100812,118142,117629,104366,109679,104153,104762,111974,116745,100688,106627,86985,86461,118034,109554,100459,112381,104347,109579,117496,114798,112502,100932,108296,112490,111629,114845,108074,113310,117362,116257,109913,116974,115746,103510,115260,104477,116119,102240,100033,112162,112795,104554,112378,102124,107139,108582,108688,104282,86999,111094,115699,109712,87000,115604,87001,105168,101124,109941,118169,113300,114790,116362,101340,117967,101966,102370,103982,85952,105385,102396,105942,101393,112368,105887,113655,113999,109277,103515,114439,108019,115233,86493,114803,113513,102123,103180,117033,102099,114886,116073,99473,115570,109092,110343,105311,110350,101674,109026,114054,110745,102328,101864,101263,116817,114778,104537,103854,86494,109930,111061,117903,113825,116989,108744,119126,102616,108030,86199,108124,110837,115910,109158,114161,104620,104054,115826,115612,111332,114839,105847,115597,109422,104426,86200,113578,109232,106487,111865,103756,108695,109978,116620,108680,86495,114517,111861,107841,104287,117756,105797,87027,112394,115064,117662,106056,104297,107703,87028,108632,112390,100824,109935,106607,109561,110429,117892,111713,100780,109431,117598,86496,115579,86201,106811,114323,119411,107748,106550,111948,107918,115338,102239,114425,102667,113538,105979,116516,113777,100610,115371,99760,101963,113705,111174,102077,108693);

commit;



begin;

select concat("FROM ",111166," TO ", 86519);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (111166,99418,116751,101675,113096,105441,102900,111331,115995,111811,99913,114260,114067,109381,112127,115636,106568,103094,100805,104920,110047,105375,107856,111090,118819,100312,112488,107476,112558,118151,112291,86231,102991,100760,102740,114052,114640,109956,105164,102576,115992,109904,102374,117367,116770,104058,105822,114632,109123,106340,116476,110415,114489,110107,102812,116432,101534,104870,107011,112904,101760,108052,118818,110227,104495,105754,108240,86048,87044,116536,114712,101366,110923,117375,109386,109898,108029,116144,117715,102748,113732,101497,119427,113856,114871,109495,115644,111335,118030,100479,106062,104552,113086,110334,107372,107673,110519,101213,101096,115680,101319,106623,102630,113832,106136,87045,118446,101547,115704,105151,102861,100179,86517,100228,109564,106205,86518,112564,109499,112557,110554,114497,117912,110663,112264,103282,119006,103523,100428,99864,106684,111386,102866,111793,100564,109128,100794,110685,104436,85929,101042,101128,109804,86232,87065,110488,112035,104737,99770,117432,108487,118995,112992,99769,115983,106742,113911,110349,86233,117899,111859,110826,116887,103368,104281,99605,117753,102679,117431,106253,85879,116377,105791,106867,109175,116869,118241,112210,105846,111651,101575,102182,109556,112783,106060,106704,103249,101768,114508,100157,116276,111573,116259,109235,103257,109078,99735,86234,104788,86519);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (111166,99418,116751,101675,113096,105441,102900,111331,115995,111811,99913,114260,114067,109381,112127,115636,106568,103094,100805,104920,110047,105375,107856,111090,118819,100312,112488,107476,112558,118151,112291,86231,102991,100760,102740,114052,114640,109956,105164,102576,115992,109904,102374,117367,116770,104058,105822,114632,109123,106340,116476,110415,114489,110107,102812,116432,101534,104870,107011,112904,101760,108052,118818,110227,104495,105754,108240,86048,87044,116536,114712,101366,110923,117375,109386,109898,108029,116144,117715,102748,113732,101497,119427,113856,114871,109495,115644,111335,118030,100479,106062,104552,113086,110334,107372,107673,110519,101213,101096,115680,101319,106623,102630,113832,106136,87045,118446,101547,115704,105151,102861,100179,86517,100228,109564,106205,86518,112564,109499,112557,110554,114497,117912,110663,112264,103282,119006,103523,100428,99864,106684,111386,102866,111793,100564,109128,100794,110685,104436,85929,101042,101128,109804,86232,87065,110488,112035,104737,99770,117432,108487,118995,112992,99769,115983,106742,113911,110349,86233,117899,111859,110826,116887,103368,104281,99605,117753,102679,117431,106253,85879,116377,105791,106867,109175,116869,118241,112210,105846,111651,101575,102182,109556,112783,106060,106704,103249,101768,114508,100157,116276,111573,116259,109235,103257,109078,99735,86234,104788,86519);

delete quotes.* 
from quotes where instrumentid in (111166,99418,116751,101675,113096,105441,102900,111331,115995,111811,99913,114260,114067,109381,112127,115636,106568,103094,100805,104920,110047,105375,107856,111090,118819,100312,112488,107476,112558,118151,112291,86231,102991,100760,102740,114052,114640,109956,105164,102576,115992,109904,102374,117367,116770,104058,105822,114632,109123,106340,116476,110415,114489,110107,102812,116432,101534,104870,107011,112904,101760,108052,118818,110227,104495,105754,108240,86048,87044,116536,114712,101366,110923,117375,109386,109898,108029,116144,117715,102748,113732,101497,119427,113856,114871,109495,115644,111335,118030,100479,106062,104552,113086,110334,107372,107673,110519,101213,101096,115680,101319,106623,102630,113832,106136,87045,118446,101547,115704,105151,102861,100179,86517,100228,109564,106205,86518,112564,109499,112557,110554,114497,117912,110663,112264,103282,119006,103523,100428,99864,106684,111386,102866,111793,100564,109128,100794,110685,104436,85929,101042,101128,109804,86232,87065,110488,112035,104737,99770,117432,108487,118995,112992,99769,115983,106742,113911,110349,86233,117899,111859,110826,116887,103368,104281,99605,117753,102679,117431,106253,85879,116377,105791,106867,109175,116869,118241,112210,105846,111651,101575,102182,109556,112783,106060,106704,103249,101768,114508,100157,116276,111573,116259,109235,103257,109078,99735,86234,104788,86519);

commit;



begin;

select concat("FROM ",114633," TO ", 86571);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (114633,105811,117259,115645,106310,104947,115641,117566,102794,109522,111649,103263,115889,102548,104570,101837,105107,112257,102329,113389,100694,108743,108608,116666,111180,114202,117395,117460,109503,114204,109690,103108,103786,113728,109617,102249,110168,112388,87071,109654,111179,100127,112369,114773,114890,118001,105485,103790,102493,99971,102733,87072,100454,87073,105231,109311,100796,114123,103135,103003,113771,116267,100114,87074,118588,113387,102983,112250,111870,104783,115740,104904,116443,110708,100788,114866,117234,104454,101579,110111,109077,101641,104311,102133,114734,99594,105779,118189,118775,105872,102171,117196,115687,113610,112284,104566,105093,102029,103572,103795,114295,117091,102061,106663,113348,113664,100320,109720,105619,99986,117650,118753,108644,112225,109097,111439,110644,100928,115226,111188,107970,116792,86235,100871,104284,112427,106049,109013,109903,113111,114200,107425,109702,112999,116469,117374,112788,107603,86236,108428,108765,106364,99741,103621,103506,99834,86237,117369,112614,101100,99439,112860,105059,114064,100306,100009,116667,100743,100329,108814,86049,116426,103357,86544,109580,101046,106464,106170,112578,114630,103652,108390,113704,109542,101936,99544,109104,117067,104693,110220,102462,116793,112515,117869,99893,117596,111706,99727,110696,111844,108431,107589,112081,106533,104841,106707,101278,99628,118789,86571);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (114633,105811,117259,115645,106310,104947,115641,117566,102794,109522,111649,103263,115889,102548,104570,101837,105107,112257,102329,113389,100694,108743,108608,116666,111180,114202,117395,117460,109503,114204,109690,103108,103786,113728,109617,102249,110168,112388,87071,109654,111179,100127,112369,114773,114890,118001,105485,103790,102493,99971,102733,87072,100454,87073,105231,109311,100796,114123,103135,103003,113771,116267,100114,87074,118588,113387,102983,112250,111870,104783,115740,104904,116443,110708,100788,114866,117234,104454,101579,110111,109077,101641,104311,102133,114734,99594,105779,118189,118775,105872,102171,117196,115687,113610,112284,104566,105093,102029,103572,103795,114295,117091,102061,106663,113348,113664,100320,109720,105619,99986,117650,118753,108644,112225,109097,111439,110644,100928,115226,111188,107970,116792,86235,100871,104284,112427,106049,109013,109903,113111,114200,107425,109702,112999,116469,117374,112788,107603,86236,108428,108765,106364,99741,103621,103506,99834,86237,117369,112614,101100,99439,112860,105059,114064,100306,100009,116667,100743,100329,108814,86049,116426,103357,86544,109580,101046,106464,106170,112578,114630,103652,108390,113704,109542,101936,99544,109104,117067,104693,110220,102462,116793,112515,117869,99893,117596,111706,99727,110696,111844,108431,107589,112081,106533,104841,106707,101278,99628,118789,86571);

delete quotes.* 
from quotes where instrumentid in (114633,105811,117259,115645,106310,104947,115641,117566,102794,109522,111649,103263,115889,102548,104570,101837,105107,112257,102329,113389,100694,108743,108608,116666,111180,114202,117395,117460,109503,114204,109690,103108,103786,113728,109617,102249,110168,112388,87071,109654,111179,100127,112369,114773,114890,118001,105485,103790,102493,99971,102733,87072,100454,87073,105231,109311,100796,114123,103135,103003,113771,116267,100114,87074,118588,113387,102983,112250,111870,104783,115740,104904,116443,110708,100788,114866,117234,104454,101579,110111,109077,101641,104311,102133,114734,99594,105779,118189,118775,105872,102171,117196,115687,113610,112284,104566,105093,102029,103572,103795,114295,117091,102061,106663,113348,113664,100320,109720,105619,99986,117650,118753,108644,112225,109097,111439,110644,100928,115226,111188,107970,116792,86235,100871,104284,112427,106049,109013,109903,113111,114200,107425,109702,112999,116469,117374,112788,107603,86236,108428,108765,106364,99741,103621,103506,99834,86237,117369,112614,101100,99439,112860,105059,114064,100306,100009,116667,100743,100329,108814,86049,116426,103357,86544,109580,101046,106464,106170,112578,114630,103652,108390,113704,109542,101936,99544,109104,117067,104693,110220,102462,116793,112515,117869,99893,117596,111706,99727,110696,111844,108431,107589,112081,106533,104841,106707,101278,99628,118789,86571);

commit;



begin;

select concat("FROM ",113390," TO ", 108385);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (113390,113865,114843,112816,104804,99931,101929,114211,101020,86572,109334,107144,102298,111621,119204,116256,102156,115535,109223,118011,103302,107497,106536,117318,104545,102522,102092,86573,114789,110836,112382,112042,100113,103072,86253,106837,115232,102399,113726,107004,110106,113873,107721,110275,104300,107293,108469,105078,108855,114544,109137,103076,108850,116227,105396,105391,105458,114642,101542,113173,105925,106273,105152,117776,109868,117575,104408,112622,117068,108669,106679,115844,86070,86594,115763,100832,106838,103660,107088,106151,115615,107549,113792,100475,102753,99897,103204,99918,115153,99930,104698,107114,106256,114317,117185,112589,105091,101344,101576,115577,100183,114246,114319,116006,116100,105055,109560,117016,115256,115683,105068,109854,101729,105277,101397,106602,99598,102718,105390,117231,113007,110918,101643,85974,102327,104099,103674,86595,104549,107579,109695,107169,110917,86596,110416,115092,108366,114208,86597,102570,117461,117968,116197,106942,102981,116382,108734,107867,104343,101927,106301,106314,104420,109253,118154,110223,113392,112694,107678,109127,101363,99676,101419,109408,102261,113755,111469,108120,117295,113641,116212,109836,114031,105628,108712,110522,111470,112391,117024,107056,110818,112050,107824,110383,101686,111379,118165,101282,110060,108138,117365,114580,100323,114261,115809,104264,103173,101940,106670,108385);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (113390,113865,114843,112816,104804,99931,101929,114211,101020,86572,109334,107144,102298,111621,119204,116256,102156,115535,109223,118011,103302,107497,106536,117318,104545,102522,102092,86573,114789,110836,112382,112042,100113,103072,86253,106837,115232,102399,113726,107004,110106,113873,107721,110275,104300,107293,108469,105078,108855,114544,109137,103076,108850,116227,105396,105391,105458,114642,101542,113173,105925,106273,105152,117776,109868,117575,104408,112622,117068,108669,106679,115844,86070,86594,115763,100832,106838,103660,107088,106151,115615,107549,113792,100475,102753,99897,103204,99918,115153,99930,104698,107114,106256,114317,117185,112589,105091,101344,101576,115577,100183,114246,114319,116006,116100,105055,109560,117016,115256,115683,105068,109854,101729,105277,101397,106602,99598,102718,105390,117231,113007,110918,101643,85974,102327,104099,103674,86595,104549,107579,109695,107169,110917,86596,110416,115092,108366,114208,86597,102570,117461,117968,116197,106942,102981,116382,108734,107867,104343,101927,106301,106314,104420,109253,118154,110223,113392,112694,107678,109127,101363,99676,101419,109408,102261,113755,111469,108120,117295,113641,116212,109836,114031,105628,108712,110522,111470,112391,117024,107056,110818,112050,107824,110383,101686,111379,118165,101282,110060,108138,117365,114580,100323,114261,115809,104264,103173,101940,106670,108385);

delete quotes.* 
from quotes where instrumentid in (113390,113865,114843,112816,104804,99931,101929,114211,101020,86572,109334,107144,102298,111621,119204,116256,102156,115535,109223,118011,103302,107497,106536,117318,104545,102522,102092,86573,114789,110836,112382,112042,100113,103072,86253,106837,115232,102399,113726,107004,110106,113873,107721,110275,104300,107293,108469,105078,108855,114544,109137,103076,108850,116227,105396,105391,105458,114642,101542,113173,105925,106273,105152,117776,109868,117575,104408,112622,117068,108669,106679,115844,86070,86594,115763,100832,106838,103660,107088,106151,115615,107549,113792,100475,102753,99897,103204,99918,115153,99930,104698,107114,106256,114317,117185,112589,105091,101344,101576,115577,100183,114246,114319,116006,116100,105055,109560,117016,115256,115683,105068,109854,101729,105277,101397,106602,99598,102718,105390,117231,113007,110918,101643,85974,102327,104099,103674,86595,104549,107579,109695,107169,110917,86596,110416,115092,108366,114208,86597,102570,117461,117968,116197,106942,102981,116382,108734,107867,104343,101927,106301,106314,104420,109253,118154,110223,113392,112694,107678,109127,101363,99676,101419,109408,102261,113755,111469,108120,117295,113641,116212,109836,114031,105628,108712,110522,111470,112391,117024,107056,110818,112050,107824,110383,101686,111379,118165,101282,110060,108138,117365,114580,100323,114261,115809,104264,103173,101940,106670,108385);

commit;



begin;

select concat("FROM ",110195," TO ", 112341);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (110195,103833,105831,104838,109140,99678,99965,101993,105913,118733,100698,116866,86622,119120,110928,86276,101848,112290,115829,114526,113733,105627,101028,111990,107228,108613,116428,102464,110340,100016,110347,106352,112651,103770,102831,99951,118297,109969,108766,117913,101537,107440,118140,115831,106941,114898,86277,86623,100408,105144,106691,115365,103369,112802,99887,117914,105486,100381,107849,99615,103951,100355,117654,116541,101611,86624,105857,115962,104124,102856,102787,115823,107470,110110,111798,104836,105933,109171,103952,103314,105640,99501,102659,102818,86278,101192,104379,99668,110196,116378,111966,118156,114351,108575,103359,86279,103690,112833,112393,111642,112767,85930,108849,116196,102110,104204,106844,117072,113867,112038,104459,109444,113754,107006,113826,115086,109990,104462,117637,111655,107045,86071,102678,105564,100792,99840,106548,86280,116664,101937,86659,115174,99730,101097,110276,111389,103306,114254,102047,112180,101404,110190,101876,114801,110827,113735,112497,108161,107922,111297,86281,86660,102247,116001,106063,107912,110317,101630,106252,116222,102985,104847,100801,101822,117102,117630,110114,106125,101698,99806,117463,112207,109268,109288,119149,102330,112385,102688,99783,104447,105388,117087,118956,86108,86678,104402,109491,104286,117499,104362,112804,110425,110880,100865,110335,117723,117660,100662,113517,112341);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (110195,103833,105831,104838,109140,99678,99965,101993,105913,118733,100698,116866,86622,119120,110928,86276,101848,112290,115829,114526,113733,105627,101028,111990,107228,108613,116428,102464,110340,100016,110347,106352,112651,103770,102831,99951,118297,109969,108766,117913,101537,107440,118140,115831,106941,114898,86277,86623,100408,105144,106691,115365,103369,112802,99887,117914,105486,100381,107849,99615,103951,100355,117654,116541,101611,86624,105857,115962,104124,102856,102787,115823,107470,110110,111798,104836,105933,109171,103952,103314,105640,99501,102659,102818,86278,101192,104379,99668,110196,116378,111966,118156,114351,108575,103359,86279,103690,112833,112393,111642,112767,85930,108849,116196,102110,104204,106844,117072,113867,112038,104459,109444,113754,107006,113826,115086,109990,104462,117637,111655,107045,86071,102678,105564,100792,99840,106548,86280,116664,101937,86659,115174,99730,101097,110276,111389,103306,114254,102047,112180,101404,110190,101876,114801,110827,113735,112497,108161,107922,111297,86281,86660,102247,116001,106063,107912,110317,101630,106252,116222,102985,104847,100801,101822,117102,117630,110114,106125,101698,99806,117463,112207,109268,109288,119149,102330,112385,102688,99783,104447,105388,117087,118956,86108,86678,104402,109491,104286,117499,104362,112804,110425,110880,100865,110335,117723,117660,100662,113517,112341);

delete quotes.* 
from quotes where instrumentid in (110195,103833,105831,104838,109140,99678,99965,101993,105913,118733,100698,116866,86622,119120,110928,86276,101848,112290,115829,114526,113733,105627,101028,111990,107228,108613,116428,102464,110340,100016,110347,106352,112651,103770,102831,99951,118297,109969,108766,117913,101537,107440,118140,115831,106941,114898,86277,86623,100408,105144,106691,115365,103369,112802,99887,117914,105486,100381,107849,99615,103951,100355,117654,116541,101611,86624,105857,115962,104124,102856,102787,115823,107470,110110,111798,104836,105933,109171,103952,103314,105640,99501,102659,102818,86278,101192,104379,99668,110196,116378,111966,118156,114351,108575,103359,86279,103690,112833,112393,111642,112767,85930,108849,116196,102110,104204,106844,117072,113867,112038,104459,109444,113754,107006,113826,115086,109990,104462,117637,111655,107045,86071,102678,105564,100792,99840,106548,86280,116664,101937,86659,115174,99730,101097,110276,111389,103306,114254,102047,112180,101404,110190,101876,114801,110827,113735,112497,108161,107922,111297,86281,86660,102247,116001,106063,107912,110317,101630,106252,116222,102985,104847,100801,101822,117102,117630,110114,106125,101698,99806,117463,112207,109268,109288,119149,102330,112385,102688,99783,104447,105388,117087,118956,86108,86678,104402,109491,104286,117499,104362,112804,110425,110880,100865,110335,117723,117660,100662,113517,112341);

commit;



begin;

select concat("FROM ",116914," TO ", 105966);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (116914,86309,106547,117969,110292,105163,113750,86109,106726,115608,86679,106906,100119,108249,119298,116769,104643,110270,101045,99635,108380,104837,110830,113685,105013,102717,111660,110665,113015,114622,112562,108767,106904,106104,109748,103445,101964,105879,86680,86110,86681,105462,86682,85885,107898,115817,103804,99412,100041,105835,103144,106350,106191,106852,117902,110738,117901,103268,109003,114899,99369,105761,116376,114162,86707,114324,101830,110408,111669,100238,115849,103585,106206,112542,115013,110294,116507,116076,99572,102756,116438,117577,115317,85975,110282,86310,107489,102973,100439,116733,100125,108494,99622,111721,117568,119176,100473,116120,117026,103358,109502,108670,110233,108747,112395,85886,100515,100098,108461,116493,113158,118427,109929,100422,110671,100048,105752,111963,116958,104298,105978,118580,107538,102708,116202,103683,115578,112715,104856,111996,86708,101129,102125,111311,99856,112048,102772,111351,86709,118002,113966,111799,118843,118512,100438,109581,118438,101676,107770,112764,86710,118752,116948,108285,100123,109304,100043,118214,118207,112101,114771,115564,117915,104064,115341,86111,100451,116199,101385,116584,117120,86337,108411,99653,108638,109272,111192,101502,86738,112897,101260,116517,115629,116780,103334,104363,100211,107548,99926,116504,103250,108446,104655,102851,113184,100714,106257,114060,108318,105966);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (116914,86309,106547,117969,110292,105163,113750,86109,106726,115608,86679,106906,100119,108249,119298,116769,104643,110270,101045,99635,108380,104837,110830,113685,105013,102717,111660,110665,113015,114622,112562,108767,106904,106104,109748,103445,101964,105879,86680,86110,86681,105462,86682,85885,107898,115817,103804,99412,100041,105835,103144,106350,106191,106852,117902,110738,117901,103268,109003,114899,99369,105761,116376,114162,86707,114324,101830,110408,111669,100238,115849,103585,106206,112542,115013,110294,116507,116076,99572,102756,116438,117577,115317,85975,110282,86310,107489,102973,100439,116733,100125,108494,99622,111721,117568,119176,100473,116120,117026,103358,109502,108670,110233,108747,112395,85886,100515,100098,108461,116493,113158,118427,109929,100422,110671,100048,105752,111963,116958,104298,105978,118580,107538,102708,116202,103683,115578,112715,104856,111996,86708,101129,102125,111311,99856,112048,102772,111351,86709,118002,113966,111799,118843,118512,100438,109581,118438,101676,107770,112764,86710,118752,116948,108285,100123,109304,100043,118214,118207,112101,114771,115564,117915,104064,115341,86111,100451,116199,101385,116584,117120,86337,108411,99653,108638,109272,111192,101502,86738,112897,101260,116517,115629,116780,103334,104363,100211,107548,99926,116504,103250,108446,104655,102851,113184,100714,106257,114060,108318,105966);

delete quotes.* 
from quotes where instrumentid in (116914,86309,106547,117969,110292,105163,113750,86109,106726,115608,86679,106906,100119,108249,119298,116769,104643,110270,101045,99635,108380,104837,110830,113685,105013,102717,111660,110665,113015,114622,112562,108767,106904,106104,109748,103445,101964,105879,86680,86110,86681,105462,86682,85885,107898,115817,103804,99412,100041,105835,103144,106350,106191,106852,117902,110738,117901,103268,109003,114899,99369,105761,116376,114162,86707,114324,101830,110408,111669,100238,115849,103585,106206,112542,115013,110294,116507,116076,99572,102756,116438,117577,115317,85975,110282,86310,107489,102973,100439,116733,100125,108494,99622,111721,117568,119176,100473,116120,117026,103358,109502,108670,110233,108747,112395,85886,100515,100098,108461,116493,113158,118427,109929,100422,110671,100048,105752,111963,116958,104298,105978,118580,107538,102708,116202,103683,115578,112715,104856,111996,86708,101129,102125,111311,99856,112048,102772,111351,86709,118002,113966,111799,118843,118512,100438,109581,118438,101676,107770,112764,86710,118752,116948,108285,100123,109304,100043,118214,118207,112101,114771,115564,117915,104064,115341,86111,100451,116199,101385,116584,117120,86337,108411,99653,108638,109272,111192,101502,86738,112897,101260,116517,115629,116780,103334,104363,100211,107548,99926,116504,103250,108446,104655,102851,113184,100714,106257,114060,108318,105966);

commit;



begin;

select concat("FROM ",113440," TO ", 116676);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (113440,104597,102798,100778,118127,102011,114792,86739,85976,106779,112194,109117,111150,112461,100580,112033,106298,117122,107544,111985,110481,108122,86338,114448,110148,106387,108785,113128,105165,111420,86339,113276,103450,105142,114709,110384,86740,86340,110572,115563,104690,102041,112738,114820,116926,103896,99720,101225,105328,100014,100899,113990,114460,113628,114181,114443,112574,110702,101616,102888,117484,119404,113439,99644,105845,100217,99706,117143,113287,86341,107499,115417,109858,86342,108054,103593,107431,116075,105174,110407,118559,107451,114349,116555,118951,113119,100481,116841,113557,100867,104033,116708,106565,100651,117469,118083,100755,100656,86765,110009,102082,105196,111875,106051,105626,117567,110737,116885,113946,112063,117870,114520,109454,110684,119049,112335,101666,117444,99429,108601,103734,106940,104191,115890,102409,101280,99406,108375,111030,115008,100709,106775,86343,86766,111184,105956,105746,86767,110946,110318,105820,102658,111185,86344,111378,106264,99759,99467,112029,105176,111405,107248,86768,106092,102020,117581,106475,85887,110066,112678,114516,107523,115836,114576,112479,101018,106596,110254,107597,86147,107254,108013,104839,116837,102343,107188,100921,116838,104799,107492,101783,103898,102492,108562,107904,108388,102093,104203,117397,106398,100018,112106,114668,105983,106339,107790,102887,111722,86371,116676);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (113440,104597,102798,100778,118127,102011,114792,86739,85976,106779,112194,109117,111150,112461,100580,112033,106298,117122,107544,111985,110481,108122,86338,114448,110148,106387,108785,113128,105165,111420,86339,113276,103450,105142,114709,110384,86740,86340,110572,115563,104690,102041,112738,114820,116926,103896,99720,101225,105328,100014,100899,113990,114460,113628,114181,114443,112574,110702,101616,102888,117484,119404,113439,99644,105845,100217,99706,117143,113287,86341,107499,115417,109858,86342,108054,103593,107431,116075,105174,110407,118559,107451,114349,116555,118951,113119,100481,116841,113557,100867,104033,116708,106565,100651,117469,118083,100755,100656,86765,110009,102082,105196,111875,106051,105626,117567,110737,116885,113946,112063,117870,114520,109454,110684,119049,112335,101666,117444,99429,108601,103734,106940,104191,115890,102409,101280,99406,108375,111030,115008,100709,106775,86343,86766,111184,105956,105746,86767,110946,110318,105820,102658,111185,86344,111378,106264,99759,99467,112029,105176,111405,107248,86768,106092,102020,117581,106475,85887,110066,112678,114516,107523,115836,114576,112479,101018,106596,110254,107597,86147,107254,108013,104839,116837,102343,107188,100921,116838,104799,107492,101783,103898,102492,108562,107904,108388,102093,104203,117397,106398,100018,112106,114668,105983,106339,107790,102887,111722,86371,116676);

delete quotes.* 
from quotes where instrumentid in (113440,104597,102798,100778,118127,102011,114792,86739,85976,106779,112194,109117,111150,112461,100580,112033,106298,117122,107544,111985,110481,108122,86338,114448,110148,106387,108785,113128,105165,111420,86339,113276,103450,105142,114709,110384,86740,86340,110572,115563,104690,102041,112738,114820,116926,103896,99720,101225,105328,100014,100899,113990,114460,113628,114181,114443,112574,110702,101616,102888,117484,119404,113439,99644,105845,100217,99706,117143,113287,86341,107499,115417,109858,86342,108054,103593,107431,116075,105174,110407,118559,107451,114349,116555,118951,113119,100481,116841,113557,100867,104033,116708,106565,100651,117469,118083,100755,100656,86765,110009,102082,105196,111875,106051,105626,117567,110737,116885,113946,112063,117870,114520,109454,110684,119049,112335,101666,117444,99429,108601,103734,106940,104191,115890,102409,101280,99406,108375,111030,115008,100709,106775,86343,86766,111184,105956,105746,86767,110946,110318,105820,102658,111185,86344,111378,106264,99759,99467,112029,105176,111405,107248,86768,106092,102020,117581,106475,85887,110066,112678,114516,107523,115836,114576,112479,101018,106596,110254,107597,86147,107254,108013,104839,116837,102343,107188,100921,116838,104799,107492,101783,103898,102492,108562,107904,108388,102093,104203,117397,106398,100018,112106,114668,105983,106339,107790,102887,111722,86371,116676);

commit;



begin;

select concat("FROM ",109985," TO ", 103975);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (109985,86793,107802,115764,111410,111198,104702,100913,111079,115485,86372,101407,117895,116722,110611,106954,110454,108623,99984,108635,102876,114230,112815,109410,110801,116553,101410,111955,101455,106664,108643,108912,101445,116554,104768,112742,104767,103645,116029,108742,102192,109440,110711,109849,114901,104277,113858,100790,105237,118055,107250,103081,103540,102532,117098,110081,115698,106123,101258,107575,104813,86148,101597,100430,86819,105161,115969,86373,106295,100730,107361,99768,115914,117012,106414,86374,108087,103141,109476,117013,113136,101247,86820,100745,117930,103704,110260,104330,117419,103689,86821,116385,104784,112321,106403,117398,86822,85956,105170,116195,86823,116834,104927,116938,107863,112323,105199,99372,101354,104854,109927,113991,101880,113008,112425,104562,112810,116532,107513,105340,115330,107151,103659,106686,109744,115568,106931,103766,114315,103047,99478,101107,115701,112034,105834,103109,100641,108098,108005,100117,101665,104268,110914,101123,116425,115814,115813,107069,102972,104018,111014,110945,106601,114829,86839,118202,106041,112457,105453,111349,99402,85957,107029,103104,102613,112751,86840,111867,99747,102427,101587,110768,106000,106725,115447,104683,107935,101781,115147,114001,109197,116004,86841,113548,114188,117877,115747,112126,114836,103655,111599,111336,107536,110258,110273,103920,112208,114634,105936,103975);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (109985,86793,107802,115764,111410,111198,104702,100913,111079,115485,86372,101407,117895,116722,110611,106954,110454,108623,99984,108635,102876,114230,112815,109410,110801,116553,101410,111955,101455,106664,108643,108912,101445,116554,104768,112742,104767,103645,116029,108742,102192,109440,110711,109849,114901,104277,113858,100790,105237,118055,107250,103081,103540,102532,117098,110081,115698,106123,101258,107575,104813,86148,101597,100430,86819,105161,115969,86373,106295,100730,107361,99768,115914,117012,106414,86374,108087,103141,109476,117013,113136,101247,86820,100745,117930,103704,110260,104330,117419,103689,86821,116385,104784,112321,106403,117398,86822,85956,105170,116195,86823,116834,104927,116938,107863,112323,105199,99372,101354,104854,109927,113991,101880,113008,112425,104562,112810,116532,107513,105340,115330,107151,103659,106686,109744,115568,106931,103766,114315,103047,99478,101107,115701,112034,105834,103109,100641,108098,108005,100117,101665,104268,110914,101123,116425,115814,115813,107069,102972,104018,111014,110945,106601,114829,86839,118202,106041,112457,105453,111349,99402,85957,107029,103104,102613,112751,86840,111867,99747,102427,101587,110768,106000,106725,115447,104683,107935,101781,115147,114001,109197,116004,86841,113548,114188,117877,115747,112126,114836,103655,111599,111336,107536,110258,110273,103920,112208,114634,105936,103975);

delete quotes.* 
from quotes where instrumentid in (109985,86793,107802,115764,111410,111198,104702,100913,111079,115485,86372,101407,117895,116722,110611,106954,110454,108623,99984,108635,102876,114230,112815,109410,110801,116553,101410,111955,101455,106664,108643,108912,101445,116554,104768,112742,104767,103645,116029,108742,102192,109440,110711,109849,114901,104277,113858,100790,105237,118055,107250,103081,103540,102532,117098,110081,115698,106123,101258,107575,104813,86148,101597,100430,86819,105161,115969,86373,106295,100730,107361,99768,115914,117012,106414,86374,108087,103141,109476,117013,113136,101247,86820,100745,117930,103704,110260,104330,117419,103689,86821,116385,104784,112321,106403,117398,86822,85956,105170,116195,86823,116834,104927,116938,107863,112323,105199,99372,101354,104854,109927,113991,101880,113008,112425,104562,112810,116532,107513,105340,115330,107151,103659,106686,109744,115568,106931,103766,114315,103047,99478,101107,115701,112034,105834,103109,100641,108098,108005,100117,101665,104268,110914,101123,116425,115814,115813,107069,102972,104018,111014,110945,106601,114829,86839,118202,106041,112457,105453,111349,99402,85957,107029,103104,102613,112751,86840,111867,99747,102427,101587,110768,106000,106725,115447,104683,107935,101781,115147,114001,109197,116004,86841,113548,114188,117877,115747,112126,114836,103655,111599,111336,107536,110258,110273,103920,112208,114634,105936,103975);

commit;



begin;

select concat("FROM ",110746," TO ", 103488);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (110746,112168,114367,106878,105786,108913,100195,116829,118003,108941,86390,107274,113256,113611,112464,109322,118876,112775,113120,105092,112220,115628,113342,111680,108467,86391,114078,86149,114027,105415,108097,86006,107077,86392,103256,119456,108322,100754,99556,102345,105986,104522,115148,110601,112436,112287,86007,102051,100226,86842,115818,103761,99665,112900,105539,112171,100589,106193,110583,100084,117132,107762,102890,102614,105009,86866,102413,107574,117434,86867,103221,117202,104331,116932,102229,102127,113831,106361,113758,112784,99432,108096,100873,101270,106359,100868,112841,112565,100424,108443,113262,86393,112011,108614,102739,112769,108505,99911,103924,109439,104302,103516,99387,105053,108424,111569,108872,105392,105461,104560,112566,110019,86868,101514,101978,107212,110915,111249,109086,113297,100519,100987,99477,110895,113138,115368,106907,112934,112556,102671,110655,114835,103897,108569,99975,86869,101119,86870,109922,109053,103012,108565,114822,107353,116840,101063,111077,119426,113232,109050,113547,102022,102081,106939,111144,99902,102415,111344,86892,100170,113635,118949,99403,99548,110686,108740,117031,110742,116833,102510,86893,113351,105033,114837,106408,104231,99812,116312,109473,86417,104642,117626,103198,100073,116056,106774,99415,118393,105254,102566,111797,117032,86171,109879,106816,118262,108046,110234,105982,103488);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (110746,112168,114367,106878,105786,108913,100195,116829,118003,108941,86390,107274,113256,113611,112464,109322,118876,112775,113120,105092,112220,115628,113342,111680,108467,86391,114078,86149,114027,105415,108097,86006,107077,86392,103256,119456,108322,100754,99556,102345,105986,104522,115148,110601,112436,112287,86007,102051,100226,86842,115818,103761,99665,112900,105539,112171,100589,106193,110583,100084,117132,107762,102890,102614,105009,86866,102413,107574,117434,86867,103221,117202,104331,116932,102229,102127,113831,106361,113758,112784,99432,108096,100873,101270,106359,100868,112841,112565,100424,108443,113262,86393,112011,108614,102739,112769,108505,99911,103924,109439,104302,103516,99387,105053,108424,111569,108872,105392,105461,104560,112566,110019,86868,101514,101978,107212,110915,111249,109086,113297,100519,100987,99477,110895,113138,115368,106907,112934,112556,102671,110655,114835,103897,108569,99975,86869,101119,86870,109922,109053,103012,108565,114822,107353,116840,101063,111077,119426,113232,109050,113547,102022,102081,106939,111144,99902,102415,111344,86892,100170,113635,118949,99403,99548,110686,108740,117031,110742,116833,102510,86893,113351,105033,114837,106408,104231,99812,116312,109473,86417,104642,117626,103198,100073,116056,106774,99415,118393,105254,102566,111797,117032,86171,109879,106816,118262,108046,110234,105982,103488);

delete quotes.* 
from quotes where instrumentid in (110746,112168,114367,106878,105786,108913,100195,116829,118003,108941,86390,107274,113256,113611,112464,109322,118876,112775,113120,105092,112220,115628,113342,111680,108467,86391,114078,86149,114027,105415,108097,86006,107077,86392,103256,119456,108322,100754,99556,102345,105986,104522,115148,110601,112436,112287,86007,102051,100226,86842,115818,103761,99665,112900,105539,112171,100589,106193,110583,100084,117132,107762,102890,102614,105009,86866,102413,107574,117434,86867,103221,117202,104331,116932,102229,102127,113831,106361,113758,112784,99432,108096,100873,101270,106359,100868,112841,112565,100424,108443,113262,86393,112011,108614,102739,112769,108505,99911,103924,109439,104302,103516,99387,105053,108424,111569,108872,105392,105461,104560,112566,110019,86868,101514,101978,107212,110915,111249,109086,113297,100519,100987,99477,110895,113138,115368,106907,112934,112556,102671,110655,114835,103897,108569,99975,86869,101119,86870,109922,109053,103012,108565,114822,107353,116840,101063,111077,119426,113232,109050,113547,102022,102081,106939,111144,99902,102415,111344,86892,100170,113635,118949,99403,99548,110686,108740,117031,110742,116833,102510,86893,113351,105033,114837,106408,104231,99812,116312,109473,86417,104642,117626,103198,100073,116056,106774,99415,118393,105254,102566,111797,117032,86171,109879,106816,118262,108046,110234,105982,103488);

commit;



begin;

select concat("FROM ",103856," TO ", 115227);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (103856,86894,109181,103865,114823,107003,114485,108242,110466,110541,111130,109198,107796,114834,109362,102045,106385,116529,103745,106278,116143,118193,108990,101186,107001,102674,108423,117244,110320,110666,106458,113259,99892,100489,106666,108647,104616,100785,106199,104171,111718,101387,116002,103604,111300,109047,118122,114991,112801,106926,101509,110263,106996,115012,100313,106587,115993,118440,110654,102307,108853,103753,107005,109721,101491,109501,86008,100416,109113,107386,110763,101008,102065,109742,106628,114593,106795,104525,105907,117138,112203,113905,115334,106575,117891,107501,116845,112555,108356,106028,112465,116665,113457,119333,117219,116030,103752,112204,115620,113242,111301,103170,111008,105552,111773,103176,111707,86922,117229,104354,112214,100793,111016,101306,100622,112058,116712,110695,99405,102302,104258,116126,102752,101549,102749,104412,112059,111568,105413,105259,103720,116670,111033,119121,113240,115335,99874,111023,102391,103916,113117,100752,114225,118683,86418,108080,104359,99691,107427,111567,105010,116416,104092,99392,105728,115026,104980,105782,112001,108746,105817,116615,107592,104749,112219,111683,107726,101130,106784,102269,106744,101395,102237,113546,109355,111190,100047,115505,102197,107251,112768,105186,101029,109339,114669,103323,115027,108146,116174,102347,115509,108536,103703,103701,117390,107906,108259,86939,114557,115227);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (103856,86894,109181,103865,114823,107003,114485,108242,110466,110541,111130,109198,107796,114834,109362,102045,106385,116529,103745,106278,116143,118193,108990,101186,107001,102674,108423,117244,110320,110666,106458,113259,99892,100489,106666,108647,104616,100785,106199,104171,111718,101387,116002,103604,111300,109047,118122,114991,112801,106926,101509,110263,106996,115012,100313,106587,115993,118440,110654,102307,108853,103753,107005,109721,101491,109501,86008,100416,109113,107386,110763,101008,102065,109742,106628,114593,106795,104525,105907,117138,112203,113905,115334,106575,117891,107501,116845,112555,108356,106028,112465,116665,113457,119333,117219,116030,103752,112204,115620,113242,111301,103170,111008,105552,111773,103176,111707,86922,117229,104354,112214,100793,111016,101306,100622,112058,116712,110695,99405,102302,104258,116126,102752,101549,102749,104412,112059,111568,105413,105259,103720,116670,111033,119121,113240,115335,99874,111023,102391,103916,113117,100752,114225,118683,86418,108080,104359,99691,107427,111567,105010,116416,104092,99392,105728,115026,104980,105782,112001,108746,105817,116615,107592,104749,112219,111683,107726,101130,106784,102269,106744,101395,102237,113546,109355,111190,100047,115505,102197,107251,112768,105186,101029,109339,114669,103323,115027,108146,116174,102347,115509,108536,103703,103701,117390,107906,108259,86939,114557,115227);

delete quotes.* 
from quotes where instrumentid in (103856,86894,109181,103865,114823,107003,114485,108242,110466,110541,111130,109198,107796,114834,109362,102045,106385,116529,103745,106278,116143,118193,108990,101186,107001,102674,108423,117244,110320,110666,106458,113259,99892,100489,106666,108647,104616,100785,106199,104171,111718,101387,116002,103604,111300,109047,118122,114991,112801,106926,101509,110263,106996,115012,100313,106587,115993,118440,110654,102307,108853,103753,107005,109721,101491,109501,86008,100416,109113,107386,110763,101008,102065,109742,106628,114593,106795,104525,105907,117138,112203,113905,115334,106575,117891,107501,116845,112555,108356,106028,112465,116665,113457,119333,117219,116030,103752,112204,115620,113242,111301,103170,111008,105552,111773,103176,111707,86922,117229,104354,112214,100793,111016,101306,100622,112058,116712,110695,99405,102302,104258,116126,102752,101549,102749,104412,112059,111568,105413,105259,103720,116670,111033,119121,113240,115335,99874,111023,102391,103916,113117,100752,114225,118683,86418,108080,104359,99691,107427,111567,105010,116416,104092,99392,105728,115026,104980,105782,112001,108746,105817,116615,107592,104749,112219,111683,107726,101130,106784,102269,106744,101395,102237,113546,109355,111190,100047,115505,102197,107251,112768,105186,101029,109339,114669,103323,115027,108146,116174,102347,115509,108536,103703,103701,117390,107906,108259,86939,114557,115227);

commit;



begin;

select concat("FROM ",107974," TO ", 113930);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (107974,111821,107990,104770,111003,114283,101092,103157,99683,102948,112739,86940,112736,109058,101005,114555,115513,105251,104651,109752,102380,105181,110678,101348,107729,111212,108586,105357,116058,100640,114556,109545,86446,104503,113072,86941,103026,114637,105770,102967,106763,103131,116467,116173,106089,102627,105407,115413,107779,103461,99693,111213,109740,110007,117117,110010,113175,107279,118196,114068,101732,103792,110623,108600,110413,105344,115063,104288,103791,99500,117675,99788,108593,103864,102018,99786,102624,103838,106546,117685,114336,105149,114562,109562,103402,106995,114560,110920,114558,103132,111371,101731,105308,110921,110924,111373,106747,109563,108606,108863,107593,101227,113130,100415,109666,111652,103028,117501,86447,103627,99729,112798,114647,110639,109320,115512,109486,103762,105669,117508,112741,105025,86448,108634,111588,102035,108459,117497,106992,111474,108732,101524,85914,110866,108486,108460,103206,108901,110791,105437,100345,104743,114436,114447,86993,101413,114334,106971,110578,104276,112683,109733,104884,102547,100194,112903,114655,103222,116083,100604,111227,106590,111250,106099,99744,106880,111715,115566,111841,100124,112944,102929,115506,100274,108329,113653,113648,102691,103267,105423,106100,115364,113434,100571,100501,102789,106481,113654,111425,111714,111702,102684,112947,105855,110049,111602,116656,99523,103110,113930);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (107974,111821,107990,104770,111003,114283,101092,103157,99683,102948,112739,86940,112736,109058,101005,114555,115513,105251,104651,109752,102380,105181,110678,101348,107729,111212,108586,105357,116058,100640,114556,109545,86446,104503,113072,86941,103026,114637,105770,102967,106763,103131,116467,116173,106089,102627,105407,115413,107779,103461,99693,111213,109740,110007,117117,110010,113175,107279,118196,114068,101732,103792,110623,108600,110413,105344,115063,104288,103791,99500,117675,99788,108593,103864,102018,99786,102624,103838,106546,117685,114336,105149,114562,109562,103402,106995,114560,110920,114558,103132,111371,101731,105308,110921,110924,111373,106747,109563,108606,108863,107593,101227,113130,100415,109666,111652,103028,117501,86447,103627,99729,112798,114647,110639,109320,115512,109486,103762,105669,117508,112741,105025,86448,108634,111588,102035,108459,117497,106992,111474,108732,101524,85914,110866,108486,108460,103206,108901,110791,105437,100345,104743,114436,114447,86993,101413,114334,106971,110578,104276,112683,109733,104884,102547,100194,112903,114655,103222,116083,100604,111227,106590,111250,106099,99744,106880,111715,115566,111841,100124,112944,102929,115506,100274,108329,113653,113648,102691,103267,105423,106100,115364,113434,100571,100501,102789,106481,113654,111425,111714,111702,102684,112947,105855,110049,111602,116656,99523,103110,113930);

delete quotes.* 
from quotes where instrumentid in (107974,111821,107990,104770,111003,114283,101092,103157,99683,102948,112739,86940,112736,109058,101005,114555,115513,105251,104651,109752,102380,105181,110678,101348,107729,111212,108586,105357,116058,100640,114556,109545,86446,104503,113072,86941,103026,114637,105770,102967,106763,103131,116467,116173,106089,102627,105407,115413,107779,103461,99693,111213,109740,110007,117117,110010,113175,107279,118196,114068,101732,103792,110623,108600,110413,105344,115063,104288,103791,99500,117675,99788,108593,103864,102018,99786,102624,103838,106546,117685,114336,105149,114562,109562,103402,106995,114560,110920,114558,103132,111371,101731,105308,110921,110924,111373,106747,109563,108606,108863,107593,101227,113130,100415,109666,111652,103028,117501,86447,103627,99729,112798,114647,110639,109320,115512,109486,103762,105669,117508,112741,105025,86448,108634,111588,102035,108459,117497,106992,111474,108732,101524,85914,110866,108486,108460,103206,108901,110791,105437,100345,104743,114436,114447,86993,101413,114334,106971,110578,104276,112683,109733,104884,102547,100194,112903,114655,103222,116083,100604,111227,106590,111250,106099,99744,106880,111715,115566,111841,100124,112944,102929,115506,100274,108329,113653,113648,102691,103267,105423,106100,115364,113434,100571,100501,102789,106481,113654,111425,111714,111702,102684,112947,105855,110049,111602,116656,99523,103110,113930);

commit;



begin;

select concat("FROM ",113924," TO ", 87034);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (113924,105641,86994,115917,116657,107412,113844,117414,109468,109464,105992,116468,111611,107890,106635,102442,116149,109082,107963,106842,107820,104733,99526,113938,110225,100922,110865,113933,106474,107473,102390,111220,110871,110868,111109,118092,110597,110598,106011,103675,102850,106490,104019,112615,100065,105608,110471,103942,103007,117785,108588,107517,107157,113168,108263,100470,103389,100977,103475,118183,113779,116852,87019,115606,100395,114574,112616,104032,111035,86469,101147,102369,116754,104479,116875,107617,116588,116589,107714,85936,103231,111463,111375,117383,103588,105579,111883,106259,118192,99982,116921,115453,106776,114679,87020,85958,107388,114502,87021,99739,109803,99822,111760,105269,116621,107387,111034,99873,107600,116329,106563,110709,103292,109859,100296,104265,109057,104266,103587,116405,111366,111872,100566,101032,103552,99866,103961,103016,110797,110017,108023,100256,110251,109633,103892,112184,106001,103744,107978,117413,103714,114119,114113,114440,113616,107759,111671,113197,102138,100005,109806,111596,109817,99507,105886,109807,115947,111598,115625,112030,86502,101761,86205,103023,106978,109960,115616,104614,109020,101758,111800,112908,115931,112693,110789,87033,108365,105429,104972,112687,116575,100565,111133,100513,111681,115559,114878,102635,103554,112909,107211,115591,110113,106847,100093,108028,104544,107028,115897,87034);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (113924,105641,86994,115917,116657,107412,113844,117414,109468,109464,105992,116468,111611,107890,106635,102442,116149,109082,107963,106842,107820,104733,99526,113938,110225,100922,110865,113933,106474,107473,102390,111220,110871,110868,111109,118092,110597,110598,106011,103675,102850,106490,104019,112615,100065,105608,110471,103942,103007,117785,108588,107517,107157,113168,108263,100470,103389,100977,103475,118183,113779,116852,87019,115606,100395,114574,112616,104032,111035,86469,101147,102369,116754,104479,116875,107617,116588,116589,107714,85936,103231,111463,111375,117383,103588,105579,111883,106259,118192,99982,116921,115453,106776,114679,87020,85958,107388,114502,87021,99739,109803,99822,111760,105269,116621,107387,111034,99873,107600,116329,106563,110709,103292,109859,100296,104265,109057,104266,103587,116405,111366,111872,100566,101032,103552,99866,103961,103016,110797,110017,108023,100256,110251,109633,103892,112184,106001,103744,107978,117413,103714,114119,114113,114440,113616,107759,111671,113197,102138,100005,109806,111596,109817,99507,105886,109807,115947,111598,115625,112030,86502,101761,86205,103023,106978,109960,115616,104614,109020,101758,111800,112908,115931,112693,110789,87033,108365,105429,104972,112687,116575,100565,111133,100513,111681,115559,114878,102635,103554,112909,107211,115591,110113,106847,100093,108028,104544,107028,115897,87034);

delete quotes.* 
from quotes where instrumentid in (113924,105641,86994,115917,116657,107412,113844,117414,109468,109464,105992,116468,111611,107890,106635,102442,116149,109082,107963,106842,107820,104733,99526,113938,110225,100922,110865,113933,106474,107473,102390,111220,110871,110868,111109,118092,110597,110598,106011,103675,102850,106490,104019,112615,100065,105608,110471,103942,103007,117785,108588,107517,107157,113168,108263,100470,103389,100977,103475,118183,113779,116852,87019,115606,100395,114574,112616,104032,111035,86469,101147,102369,116754,104479,116875,107617,116588,116589,107714,85936,103231,111463,111375,117383,103588,105579,111883,106259,118192,99982,116921,115453,106776,114679,87020,85958,107388,114502,87021,99739,109803,99822,111760,105269,116621,107387,111034,99873,107600,116329,106563,110709,103292,109859,100296,104265,109057,104266,103587,116405,111366,111872,100566,101032,103552,99866,103961,103016,110797,110017,108023,100256,110251,109633,103892,112184,106001,103744,107978,117413,103714,114119,114113,114440,113616,107759,111671,113197,102138,100005,109806,111596,109817,99507,105886,109807,115947,111598,115625,112030,86502,101761,86205,103023,106978,109960,115616,104614,109020,101758,111800,112908,115931,112693,110789,87033,108365,105429,104972,112687,116575,100565,111133,100513,111681,115559,114878,102635,103554,112909,107211,115591,110113,106847,100093,108028,104544,107028,115897,87034);

commit;



begin;

select concat("FROM ",99969," TO ", 106843);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (99969,116779,116345,100349,102724,116333,111275,107487,102681,114076,103455,114117,109469,109460,110529,104571,105989,114118,113395,112227,109684,114124,102665,107054,102987,109680,108498,110530,108500,108506,117578,114492,112224,101078,114491,100504,108991,101903,100572,116346,113022,107232,115775,111647,101869,109947,99619,105119,115087,101256,115948,117603,109884,109883,101953,106735,86503,87056,100044,106097,86504,102256,86031,111476,115316,110647,101995,100101,105676,117651,118069,103827,115183,101727,110535,110331,114691,109769,104778,118170,108776,87057,105449,104511,110330,109085,111606,99681,105598,114234,111987,103923,105481,106937,114243,110240,114442,102921,113907,103677,100442,87058,110729,100260,100836,87059,99606,117384,102844,107264,115093,114688,108718,108760,100221,108509,112852,103283,87066,103646,107103,86524,105859,113673,111519,114751,100556,114065,107241,100639,107048,114774,114016,113061,102377,109948,112008,109059,114689,101210,86525,103526,105083,111828,117876,109793,108713,104924,114722,114446,86238,107899,110319,100875,116854,110872,102615,109705,115900,104596,86526,111824,116482,108521,115081,112267,118426,116598,100253,108503,102224,87067,100115,117261,108920,108482,109512,104937,115090,111578,102435,111059,100619,115238,106713,103657,111074,104163,113203,109963,104450,104466,109496,114213,104893,111276,86527,114214,118155,106843);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (99969,116779,116345,100349,102724,116333,111275,107487,102681,114076,103455,114117,109469,109460,110529,104571,105989,114118,113395,112227,109684,114124,102665,107054,102987,109680,108498,110530,108500,108506,117578,114492,112224,101078,114491,100504,108991,101903,100572,116346,113022,107232,115775,111647,101869,109947,99619,105119,115087,101256,115948,117603,109884,109883,101953,106735,86503,87056,100044,106097,86504,102256,86031,111476,115316,110647,101995,100101,105676,117651,118069,103827,115183,101727,110535,110331,114691,109769,104778,118170,108776,87057,105449,104511,110330,109085,111606,99681,105598,114234,111987,103923,105481,106937,114243,110240,114442,102921,113907,103677,100442,87058,110729,100260,100836,87059,99606,117384,102844,107264,115093,114688,108718,108760,100221,108509,112852,103283,87066,103646,107103,86524,105859,113673,111519,114751,100556,114065,107241,100639,107048,114774,114016,113061,102377,109948,112008,109059,114689,101210,86525,103526,105083,111828,117876,109793,108713,104924,114722,114446,86238,107899,110319,100875,116854,110872,102615,109705,115900,104596,86526,111824,116482,108521,115081,112267,118426,116598,100253,108503,102224,87067,100115,117261,108920,108482,109512,104937,115090,111578,102435,111059,100619,115238,106713,103657,111074,104163,113203,109963,104450,104466,109496,114213,104893,111276,86527,114214,118155,106843);

delete quotes.* 
from quotes where instrumentid in (99969,116779,116345,100349,102724,116333,111275,107487,102681,114076,103455,114117,109469,109460,110529,104571,105989,114118,113395,112227,109684,114124,102665,107054,102987,109680,108498,110530,108500,108506,117578,114492,112224,101078,114491,100504,108991,101903,100572,116346,113022,107232,115775,111647,101869,109947,99619,105119,115087,101256,115948,117603,109884,109883,101953,106735,86503,87056,100044,106097,86504,102256,86031,111476,115316,110647,101995,100101,105676,117651,118069,103827,115183,101727,110535,110331,114691,109769,104778,118170,108776,87057,105449,104511,110330,109085,111606,99681,105598,114234,111987,103923,105481,106937,114243,110240,114442,102921,113907,103677,100442,87058,110729,100260,100836,87059,99606,117384,102844,107264,115093,114688,108718,108760,100221,108509,112852,103283,87066,103646,107103,86524,105859,113673,111519,114751,100556,114065,107241,100639,107048,114774,114016,113061,102377,109948,112008,109059,114689,101210,86525,103526,105083,111828,117876,109793,108713,104924,114722,114446,86238,107899,110319,100875,116854,110872,102615,109705,115900,104596,86526,111824,116482,108521,115081,112267,118426,116598,100253,108503,102224,87067,100115,117261,108920,108482,109512,104937,115090,111578,102435,111059,100619,115238,106713,103657,111074,104163,113203,109963,104450,104466,109496,114213,104893,111276,86527,114214,118155,106843);

commit;



begin;

select concat("FROM ",114747," TO ", 117207);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (114747,118020,104563,104149,107317,107316,108715,116999,105645,108507,112053,108499,110964,108722,102629,107375,117335,103623,102021,102281,117842,109038,108474,105011,110540,112758,85915,117411,109425,111801,109418,113969,111260,106593,109424,108247,111270,111466,99815,115899,110845,114445,104833,114256,114677,103217,106870,115136,114269,108532,110963,112927,107704,87075,114885,105279,114681,103218,105280,109090,108817,117377,117378,114312,109033,107259,103332,109413,109375,87076,103013,106328,117379,109354,103501,106327,109412,87077,106750,112492,114568,112837,110333,109519,113433,114566,100818,113736,114564,111475,111845,111072,86528,113668,113669,110479,105827,87078,112273,87079,109724,110536,116135,116138,110537,110851,109725,109994,107707,110852,107886,109983,101411,106976,111626,110500,115132,110675,106703,106580,101701,101806,103266,107128,110272,101755,107482,110268,103779,112236,105903,114389,108277,99960,116192,104845,107342,111983,112237,101079,117077,108820,106778,105657,106371,119117,86032,117078,86550,117079,105805,99993,102159,87080,99765,106967,101268,100765,110769,100425,108245,111729,113926,114275,110704,107547,101663,100628,117035,105454,111904,100715,102324,86239,100366,112708,101310,107998,112814,106785,114205,100169,108215,109451,106917,111809,112133,107309,113953,115601,114906,115774,114895,106585,109497,115600,100036,104340,109953,117207);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (114747,118020,104563,104149,107317,107316,108715,116999,105645,108507,112053,108499,110964,108722,102629,107375,117335,103623,102021,102281,117842,109038,108474,105011,110540,112758,85915,117411,109425,111801,109418,113969,111260,106593,109424,108247,111270,111466,99815,115899,110845,114445,104833,114256,114677,103217,106870,115136,114269,108532,110963,112927,107704,87075,114885,105279,114681,103218,105280,109090,108817,117377,117378,114312,109033,107259,103332,109413,109375,87076,103013,106328,117379,109354,103501,106327,109412,87077,106750,112492,114568,112837,110333,109519,113433,114566,100818,113736,114564,111475,111845,111072,86528,113668,113669,110479,105827,87078,112273,87079,109724,110536,116135,116138,110537,110851,109725,109994,107707,110852,107886,109983,101411,106976,111626,110500,115132,110675,106703,106580,101701,101806,103266,107128,110272,101755,107482,110268,103779,112236,105903,114389,108277,99960,116192,104845,107342,111983,112237,101079,117077,108820,106778,105657,106371,119117,86032,117078,86550,117079,105805,99993,102159,87080,99765,106967,101268,100765,110769,100425,108245,111729,113926,114275,110704,107547,101663,100628,117035,105454,111904,100715,102324,86239,100366,112708,101310,107998,112814,106785,114205,100169,108215,109451,106917,111809,112133,107309,113953,115601,114906,115774,114895,106585,109497,115600,100036,104340,109953,117207);

delete quotes.* 
from quotes where instrumentid in (114747,118020,104563,104149,107317,107316,108715,116999,105645,108507,112053,108499,110964,108722,102629,107375,117335,103623,102021,102281,117842,109038,108474,105011,110540,112758,85915,117411,109425,111801,109418,113969,111260,106593,109424,108247,111270,111466,99815,115899,110845,114445,104833,114256,114677,103217,106870,115136,114269,108532,110963,112927,107704,87075,114885,105279,114681,103218,105280,109090,108817,117377,117378,114312,109033,107259,103332,109413,109375,87076,103013,106328,117379,109354,103501,106327,109412,87077,106750,112492,114568,112837,110333,109519,113433,114566,100818,113736,114564,111475,111845,111072,86528,113668,113669,110479,105827,87078,112273,87079,109724,110536,116135,116138,110537,110851,109725,109994,107707,110852,107886,109983,101411,106976,111626,110500,115132,110675,106703,106580,101701,101806,103266,107128,110272,101755,107482,110268,103779,112236,105903,114389,108277,99960,116192,104845,107342,111983,112237,101079,117077,108820,106778,105657,106371,119117,86032,117078,86550,117079,105805,99993,102159,87080,99765,106967,101268,100765,110769,100425,108245,111729,113926,114275,110704,107547,101663,100628,117035,105454,111904,100715,102324,86239,100366,112708,101310,107998,112814,106785,114205,100169,108215,109451,106917,111809,112133,107309,113953,115601,114906,115774,114895,106585,109497,115600,100036,104340,109953,117207);

commit;



begin;

select concat("FROM ",100677," TO ", 102760);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (100677,111459,111954,87081,115598,100122,100892,113954,104429,104135,111208,105102,100563,116538,108031,101244,106869,103216,114294,85959,114266,106126,107053,118650,107147,110409,118242,103340,115482,110250,110411,106676,118466,111131,115758,114271,119303,104071,102287,99685,113267,117139,101539,116360,107773,103815,116085,112229,107692,107798,101723,116857,116867,111907,86551,115756,110814,110067,101724,110854,101301,106948,119212,116861,118131,103025,101741,113019,103303,103566,118119,118123,107294,105633,111579,102112,104012,101068,101702,99527,100735,111542,104805,109432,86568,102278,86569,106478,103092,107868,101138,110888,108833,101664,111230,110167,104376,111240,101444,114973,104881,111239,113248,104883,106451,102436,109461,114876,111625,101928,100118,107528,113363,106964,112782,110253,86570,115880,106759,114627,104341,115808,115886,114924,100649,106085,105171,111322,112734,101224,102783,102339,111321,115738,116997,116633,113957,112733,104059,109984,116642,108649,108382,112610,111834,110850,108630,102535,111604,109044,117387,110803,116641,113787,109777,86251,118004,106226,115510,104866,108377,103871,104500,101571,113227,99639,117758,102371,117134,116918,117513,105058,112765,104256,110896,115705,111187,110980,113268,107882,105061,102897,111507,102334,114259,103661,115555,105663,101249,116439,99964,108640,108624,107526,115621,115593,111951,113618,109650,102760);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (100677,111459,111954,87081,115598,100122,100892,113954,104429,104135,111208,105102,100563,116538,108031,101244,106869,103216,114294,85959,114266,106126,107053,118650,107147,110409,118242,103340,115482,110250,110411,106676,118466,111131,115758,114271,119303,104071,102287,99685,113267,117139,101539,116360,107773,103815,116085,112229,107692,107798,101723,116857,116867,111907,86551,115756,110814,110067,101724,110854,101301,106948,119212,116861,118131,103025,101741,113019,103303,103566,118119,118123,107294,105633,111579,102112,104012,101068,101702,99527,100735,111542,104805,109432,86568,102278,86569,106478,103092,107868,101138,110888,108833,101664,111230,110167,104376,111240,101444,114973,104881,111239,113248,104883,106451,102436,109461,114876,111625,101928,100118,107528,113363,106964,112782,110253,86570,115880,106759,114627,104341,115808,115886,114924,100649,106085,105171,111322,112734,101224,102783,102339,111321,115738,116997,116633,113957,112733,104059,109984,116642,108649,108382,112610,111834,110850,108630,102535,111604,109044,117387,110803,116641,113787,109777,86251,118004,106226,115510,104866,108377,103871,104500,101571,113227,99639,117758,102371,117134,116918,117513,105058,112765,104256,110896,115705,111187,110980,113268,107882,105061,102897,111507,102334,114259,103661,115555,105663,101249,116439,99964,108640,108624,107526,115621,115593,111951,113618,109650,102760);

delete quotes.* 
from quotes where instrumentid in (100677,111459,111954,87081,115598,100122,100892,113954,104429,104135,111208,105102,100563,116538,108031,101244,106869,103216,114294,85959,114266,106126,107053,118650,107147,110409,118242,103340,115482,110250,110411,106676,118466,111131,115758,114271,119303,104071,102287,99685,113267,117139,101539,116360,107773,103815,116085,112229,107692,107798,101723,116857,116867,111907,86551,115756,110814,110067,101724,110854,101301,106948,119212,116861,118131,103025,101741,113019,103303,103566,118119,118123,107294,105633,111579,102112,104012,101068,101702,99527,100735,111542,104805,109432,86568,102278,86569,106478,103092,107868,101138,110888,108833,101664,111230,110167,104376,111240,101444,114973,104881,111239,113248,104883,106451,102436,109461,114876,111625,101928,100118,107528,113363,106964,112782,110253,86570,115880,106759,114627,104341,115808,115886,114924,100649,106085,105171,111322,112734,101224,102783,102339,111321,115738,116997,116633,113957,112733,104059,109984,116642,108649,108382,112610,111834,110850,108630,102535,111604,109044,117387,110803,116641,113787,109777,86251,118004,106226,115510,104866,108377,103871,104500,101571,113227,99639,117758,102371,117134,116918,117513,105058,112765,104256,110896,115705,111187,110980,113268,107882,105061,102897,111507,102334,114259,103661,115555,105663,101249,116439,99964,108640,108624,107526,115621,115593,111951,113618,109650,102760);

commit;



begin;

select concat("FROM ",103492," TO ", 100929);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (103492,107705,113482,104990,112342,86252,86591,85982,109434,116214,86592,115507,115937,99877,85937,86593,110927,106669,105150,116074,107360,107646,110641,116692,99591,105264,110371,108152,116139,105440,113947,100584,99600,101353,111123,117710,117703,111107,116078,104996,107869,106229,108587,100490,116636,112503,106489,101059,110943,104725,114496,102260,113677,110491,116634,111848,107958,114495,117850,99700,110460,117204,112868,112870,113212,112865,110464,104726,116879,113816,111628,105772,117150,117531,110700,102417,105020,113857,118145,114607,117495,101087,86612,86271,109492,86063,110825,106230,111235,115757,115041,103347,86613,117148,114156,111871,109421,104618,110261,111650,101006,105755,109822,111967,105673,105672,99499,86272,103984,109901,86614,102834,115856,115857,115855,105148,109900,116400,101957,118195,109170,111367,100993,103166,106486,113940,110883,116395,109305,104967,110779,103167,101999,114900,113514,102822,117361,101920,116637,113309,106636,113308,106505,100284,102622,102993,101014,110008,110450,118015,101159,100143,104486,110105,109745,110013,110011,103011,107581,102208,116839,117199,107937,100292,110057,110919,102416,100035,109676,110302,113443,115006,103069,106355,101027,100405,110820,86648,86273,104830,105106,104885,109647,114990,107367,105516,114605,106583,100469,107527,104815,99724,113786,100390,114989,101551,107804,105408,100849,100929);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (103492,107705,113482,104990,112342,86252,86591,85982,109434,116214,86592,115507,115937,99877,85937,86593,110927,106669,105150,116074,107360,107646,110641,116692,99591,105264,110371,108152,116139,105440,113947,100584,99600,101353,111123,117710,117703,111107,116078,104996,107869,106229,108587,100490,116636,112503,106489,101059,110943,104725,114496,102260,113677,110491,116634,111848,107958,114495,117850,99700,110460,117204,112868,112870,113212,112865,110464,104726,116879,113816,111628,105772,117150,117531,110700,102417,105020,113857,118145,114607,117495,101087,86612,86271,109492,86063,110825,106230,111235,115757,115041,103347,86613,117148,114156,111871,109421,104618,110261,111650,101006,105755,109822,111967,105673,105672,99499,86272,103984,109901,86614,102834,115856,115857,115855,105148,109900,116400,101957,118195,109170,111367,100993,103166,106486,113940,110883,116395,109305,104967,110779,103167,101999,114900,113514,102822,117361,101920,116637,113309,106636,113308,106505,100284,102622,102993,101014,110008,110450,118015,101159,100143,104486,110105,109745,110013,110011,103011,107581,102208,116839,117199,107937,100292,110057,110919,102416,100035,109676,110302,113443,115006,103069,106355,101027,100405,110820,86648,86273,104830,105106,104885,109647,114990,107367,105516,114605,106583,100469,107527,104815,99724,113786,100390,114989,101551,107804,105408,100849,100929);

delete quotes.* 
from quotes where instrumentid in (103492,107705,113482,104990,112342,86252,86591,85982,109434,116214,86592,115507,115937,99877,85937,86593,110927,106669,105150,116074,107360,107646,110641,116692,99591,105264,110371,108152,116139,105440,113947,100584,99600,101353,111123,117710,117703,111107,116078,104996,107869,106229,108587,100490,116636,112503,106489,101059,110943,104725,114496,102260,113677,110491,116634,111848,107958,114495,117850,99700,110460,117204,112868,112870,113212,112865,110464,104726,116879,113816,111628,105772,117150,117531,110700,102417,105020,113857,118145,114607,117495,101087,86612,86271,109492,86063,110825,106230,111235,115757,115041,103347,86613,117148,114156,111871,109421,104618,110261,111650,101006,105755,109822,111967,105673,105672,99499,86272,103984,109901,86614,102834,115856,115857,115855,105148,109900,116400,101957,118195,109170,111367,100993,103166,106486,113940,110883,116395,109305,104967,110779,103167,101999,114900,113514,102822,117361,101920,116637,113309,106636,113308,106505,100284,102622,102993,101014,110008,110450,118015,101159,100143,104486,110105,109745,110013,110011,103011,107581,102208,116839,117199,107937,100292,110057,110919,102416,100035,109676,110302,113443,115006,103069,106355,101027,100405,110820,86648,86273,104830,105106,104885,109647,114990,107367,105516,114605,106583,100469,107527,104815,99724,113786,100390,114989,101551,107804,105408,100849,100929);

commit;



begin;

select concat("FROM ",110931," TO ", 102139);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (110931,110116,99383,99456,99546,108451,105154,106262,117757,105955,99609,100309,110933,101895,112871,113214,112869,113215,86064,86649,86274,102177,118188,115553,118158,115561,112851,108711,104942,113195,110932,111055,101544,108197,115502,108184,108480,117353,103805,101917,110097,107710,101897,107880,109555,110760,115522,101167,109409,116253,111816,115241,86305,100548,110432,110878,102398,99475,116737,99857,111690,108059,101921,106914,100549,112402,107055,114493,99929,106916,99721,111464,101762,106477,101350,101039,101435,101610,118005,99954,103001,114662,105395,112100,112458,112102,99708,99398,104057,106242,114371,103763,101110,107908,111481,113827,102898,111145,112735,114512,107070,106406,100685,100483,109541,104556,105155,115060,116669,111876,105625,110926,110109,110417,112040,112039,117363,99750,104238,103931,106128,112914,112915,106306,108037,108581,105525,114559,101001,100155,111911,107776,103861,111969,103988,115003,101858,111846,115350,112730,107725,110879,86083,103859,102628,110771,106746,86695,86306,86696,110325,100833,118093,113064,116117,112131,104118,116188,112132,116394,116619,104418,115236,99444,86307,116725,113312,113311,106792,99876,110523,110521,115926,117764,107652,86697,100314,85983,103586,99970,86084,116440,104819,100295,106834,103051,108144,101288,104385,113033,107795,102228,101743,112842,105330,118157,101346,85938,108597,102139);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (110931,110116,99383,99456,99546,108451,105154,106262,117757,105955,99609,100309,110933,101895,112871,113214,112869,113215,86064,86649,86274,102177,118188,115553,118158,115561,112851,108711,104942,113195,110932,111055,101544,108197,115502,108184,108480,117353,103805,101917,110097,107710,101897,107880,109555,110760,115522,101167,109409,116253,111816,115241,86305,100548,110432,110878,102398,99475,116737,99857,111690,108059,101921,106914,100549,112402,107055,114493,99929,106916,99721,111464,101762,106477,101350,101039,101435,101610,118005,99954,103001,114662,105395,112100,112458,112102,99708,99398,104057,106242,114371,103763,101110,107908,111481,113827,102898,111145,112735,114512,107070,106406,100685,100483,109541,104556,105155,115060,116669,111876,105625,110926,110109,110417,112040,112039,117363,99750,104238,103931,106128,112914,112915,106306,108037,108581,105525,114559,101001,100155,111911,107776,103861,111969,103988,115003,101858,111846,115350,112730,107725,110879,86083,103859,102628,110771,106746,86695,86306,86696,110325,100833,118093,113064,116117,112131,104118,116188,112132,116394,116619,104418,115236,99444,86307,116725,113312,113311,106792,99876,110523,110521,115926,117764,107652,86697,100314,85983,103586,99970,86084,116440,104819,100295,106834,103051,108144,101288,104385,113033,107795,102228,101743,112842,105330,118157,101346,85938,108597,102139);

delete quotes.* 
from quotes where instrumentid in (110931,110116,99383,99456,99546,108451,105154,106262,117757,105955,99609,100309,110933,101895,112871,113214,112869,113215,86064,86649,86274,102177,118188,115553,118158,115561,112851,108711,104942,113195,110932,111055,101544,108197,115502,108184,108480,117353,103805,101917,110097,107710,101897,107880,109555,110760,115522,101167,109409,116253,111816,115241,86305,100548,110432,110878,102398,99475,116737,99857,111690,108059,101921,106914,100549,112402,107055,114493,99929,106916,99721,111464,101762,106477,101350,101039,101435,101610,118005,99954,103001,114662,105395,112100,112458,112102,99708,99398,104057,106242,114371,103763,101110,107908,111481,113827,102898,111145,112735,114512,107070,106406,100685,100483,109541,104556,105155,115060,116669,111876,105625,110926,110109,110417,112040,112039,117363,99750,104238,103931,106128,112914,112915,106306,108037,108581,105525,114559,101001,100155,111911,107776,103861,111969,103988,115003,101858,111846,115350,112730,107725,110879,86083,103859,102628,110771,106746,86695,86306,86696,110325,100833,118093,113064,116117,112131,104118,116188,112132,116394,116619,104418,115236,99444,86307,116725,113312,113311,106792,99876,110523,110521,115926,117764,107652,86697,100314,85983,103586,99970,86084,116440,104819,100295,106834,103051,108144,101288,104385,113033,107795,102228,101743,112842,105330,118157,101346,85938,108597,102139);

commit;



begin;

select concat("FROM ",117681," TO ", 105659);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (117681,102784,86698,118185,109754,111521,114554,112338,108187,106401,115807,103312,103223,106321,102700,107965,103706,101531,106504,113302,109073,109789,86717,86328,104464,114329,114194,115599,86718,85939,105210,86329,113674,112512,114019,117049,101036,111644,112513,106109,99408,112186,102258,100851,113192,108398,100711,104428,111949,101158,110075,108607,116220,105632,111580,105775,100712,113952,113960,108847,108846,106687,112215,108829,106717,86085,110337,112626,109031,103326,107787,108642,108983,110434,102592,113488,106396,85916,110128,112673,112674,101879,101041,106175,107609,106453,86330,86719,117657,104137,108379,102078,99567,110418,100507,109179,100080,109815,100603,102134,111195,112370,116269,99574,115333,111488,104588,100652,100174,104950,111193,110517,110833,110831,117094,117092,117095,103373,100307,101734,107543,116020,109125,112213,105981,111884,86331,86756,86086,111881,111058,106072,106667,106534,101254,113721,106532,105435,111807,100605,111427,114918,114920,102782,115274,117266,117604,117267,115401,112911,106304,112910,106303,105274,111045,99672,85940,111137,111592,105636,86087,104948,101172,106245,85984,104469,86332,107323,107892,101658,106668,115074,115073,115075,108021,110651,113507,102038,100453,115755,103337,113720,102597,107449,103243,102923,109816,115311,103467,102265,105767,105766,104740,109823,115331,111541,106127,86088,86757,105659);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (117681,102784,86698,118185,109754,111521,114554,112338,108187,106401,115807,103312,103223,106321,102700,107965,103706,101531,106504,113302,109073,109789,86717,86328,104464,114329,114194,115599,86718,85939,105210,86329,113674,112512,114019,117049,101036,111644,112513,106109,99408,112186,102258,100851,113192,108398,100711,104428,111949,101158,110075,108607,116220,105632,111580,105775,100712,113952,113960,108847,108846,106687,112215,108829,106717,86085,110337,112626,109031,103326,107787,108642,108983,110434,102592,113488,106396,85916,110128,112673,112674,101879,101041,106175,107609,106453,86330,86719,117657,104137,108379,102078,99567,110418,100507,109179,100080,109815,100603,102134,111195,112370,116269,99574,115333,111488,104588,100652,100174,104950,111193,110517,110833,110831,117094,117092,117095,103373,100307,101734,107543,116020,109125,112213,105981,111884,86331,86756,86086,111881,111058,106072,106667,106534,101254,113721,106532,105435,111807,100605,111427,114918,114920,102782,115274,117266,117604,117267,115401,112911,106304,112910,106303,105274,111045,99672,85940,111137,111592,105636,86087,104948,101172,106245,85984,104469,86332,107323,107892,101658,106668,115074,115073,115075,108021,110651,113507,102038,100453,115755,103337,113720,102597,107449,103243,102923,109816,115311,103467,102265,105767,105766,104740,109823,115331,111541,106127,86088,86757,105659);

delete quotes.* 
from quotes where instrumentid in (117681,102784,86698,118185,109754,111521,114554,112338,108187,106401,115807,103312,103223,106321,102700,107965,103706,101531,106504,113302,109073,109789,86717,86328,104464,114329,114194,115599,86718,85939,105210,86329,113674,112512,114019,117049,101036,111644,112513,106109,99408,112186,102258,100851,113192,108398,100711,104428,111949,101158,110075,108607,116220,105632,111580,105775,100712,113952,113960,108847,108846,106687,112215,108829,106717,86085,110337,112626,109031,103326,107787,108642,108983,110434,102592,113488,106396,85916,110128,112673,112674,101879,101041,106175,107609,106453,86330,86719,117657,104137,108379,102078,99567,110418,100507,109179,100080,109815,100603,102134,111195,112370,116269,99574,115333,111488,104588,100652,100174,104950,111193,110517,110833,110831,117094,117092,117095,103373,100307,101734,107543,116020,109125,112213,105981,111884,86331,86756,86086,111881,111058,106072,106667,106534,101254,113721,106532,105435,111807,100605,111427,114918,114920,102782,115274,117266,117604,117267,115401,112911,106304,112910,106303,105274,111045,99672,85940,111137,111592,105636,86087,104948,101172,106245,85984,104469,86332,107323,107892,101658,106668,115074,115073,115075,108021,110651,113507,102038,100453,115755,103337,113720,102597,107449,103243,102923,109816,115311,103467,102265,105767,105766,104740,109823,115331,111541,106127,86088,86757,105659);

commit;



begin;

select concat("FROM ",104550," TO ", 112811);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (104550,109531,101853,101854,100684,99547,99938,100223,99522,100279,107522,115518,117838,101756,117004,111674,111676,112027,111672,111673,117213,117208,116417,108353,114381,114380,112686,101112,106615,100958,114587,109036,110042,109035,102636,114352,105680,109246,100482,115622,107525,103743,102135,109812,102027,109813,117435,85960,103234,112907,99420,112028,103017,99682,111973,112371,114684,107262,118686,110589,105195,105428,107152,85947,117726,110787,115468,111685,112037,112007,86783,86015,86784,111136,111947,117861,86785,105681,111678,109110,108368,115610,102848,100997,111679,112026,99505,103759,100471,111064,113981,107789,104125,100550,113950,113542,103040,110925,86134,101851,86361,111232,110829,111528,112634,110478,104986,106965,104394,104004,107819,111234,111159,113210,101949,109511,107884,107340,106895,115731,109366,117186,110629,110625,115730,86362,104048,107857,99810,105023,104383,110632,104970,116592,107627,111646,105762,106450,117713,115732,109070,100629,107659,100191,115114,107364,105137,86016,115613,105211,107521,102202,103881,117849,108413,107200,101744,109521,86802,108969,104180,112681,111999,112012,102275,102754,100095,99871,107191,117211,109105,111840,100756,101823,86363,104521,109065,86803,104344,113236,111306,113888,86364,104389,105780,86804,109802,101636,102603,112009,102236,102608,106485,113177,108818,114887,112752,107295,114690,112811);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (104550,109531,101853,101854,100684,99547,99938,100223,99522,100279,107522,115518,117838,101756,117004,111674,111676,112027,111672,111673,117213,117208,116417,108353,114381,114380,112686,101112,106615,100958,114587,109036,110042,109035,102636,114352,105680,109246,100482,115622,107525,103743,102135,109812,102027,109813,117435,85960,103234,112907,99420,112028,103017,99682,111973,112371,114684,107262,118686,110589,105195,105428,107152,85947,117726,110787,115468,111685,112037,112007,86783,86015,86784,111136,111947,117861,86785,105681,111678,109110,108368,115610,102848,100997,111679,112026,99505,103759,100471,111064,113981,107789,104125,100550,113950,113542,103040,110925,86134,101851,86361,111232,110829,111528,112634,110478,104986,106965,104394,104004,107819,111234,111159,113210,101949,109511,107884,107340,106895,115731,109366,117186,110629,110625,115730,86362,104048,107857,99810,105023,104383,110632,104970,116592,107627,111646,105762,106450,117713,115732,109070,100629,107659,100191,115114,107364,105137,86016,115613,105211,107521,102202,103881,117849,108413,107200,101744,109521,86802,108969,104180,112681,111999,112012,102275,102754,100095,99871,107191,117211,109105,111840,100756,101823,86363,104521,109065,86803,104344,113236,111306,113888,86364,104389,105780,86804,109802,101636,102603,112009,102236,102608,106485,113177,108818,114887,112752,107295,114690,112811);

delete quotes.* 
from quotes where instrumentid in (104550,109531,101853,101854,100684,99547,99938,100223,99522,100279,107522,115518,117838,101756,117004,111674,111676,112027,111672,111673,117213,117208,116417,108353,114381,114380,112686,101112,106615,100958,114587,109036,110042,109035,102636,114352,105680,109246,100482,115622,107525,103743,102135,109812,102027,109813,117435,85960,103234,112907,99420,112028,103017,99682,111973,112371,114684,107262,118686,110589,105195,105428,107152,85947,117726,110787,115468,111685,112037,112007,86783,86015,86784,111136,111947,117861,86785,105681,111678,109110,108368,115610,102848,100997,111679,112026,99505,103759,100471,111064,113981,107789,104125,100550,113950,113542,103040,110925,86134,101851,86361,111232,110829,111528,112634,110478,104986,106965,104394,104004,107819,111234,111159,113210,101949,109511,107884,107340,106895,115731,109366,117186,110629,110625,115730,86362,104048,107857,99810,105023,104383,110632,104970,116592,107627,111646,105762,106450,117713,115732,109070,100629,107659,100191,115114,107364,105137,86016,115613,105211,107521,102202,103881,117849,108413,107200,101744,109521,86802,108969,104180,112681,111999,112012,102275,102754,100095,99871,107191,117211,109105,111840,100756,101823,86363,104521,109065,86803,104344,113236,111306,113888,86364,104389,105780,86804,109802,101636,102603,112009,102236,102608,106485,113177,108818,114887,112752,107295,114690,112811);

commit;



begin;

select concat("FROM ",104973," TO ", 99592);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (104973,116084,116359,115852,103824,102998,112263,116709,103648,109087,111808,102530,113497,108915,114729,109027,104384,109028,111693,106853,113876,105809,117066,114974,113437,115504,115421,107640,111547,111548,117210,117216,101507,113995,113646,114000,102721,118082,105209,112866,103580,107594,113719,114455,114756,100705,111988,101887,109290,100905,86386,109273,109285,86827,108159,109291,104319,110591,104913,109289,114341,101595,103998,116458,104315,104914,110592,103444,103460,107793,104117,109017,109016,117297,107596,106013,116842,116843,115077,103244,107287,106619,114190,86828,107442,114912,108759,108504,108514,115973,101406,115796,115795,112018,107112,112017,107530,104390,111665,116398,116397,116757,103399,114366,116495,112977,107570,111885,110538,111815,105370,117303,111880,110387,105893,112051,107664,116145,106803,113444,105695,102849,107555,110881,104151,105892,109039,103948,99438,108166,118153,107445,86135,86848,107318,114919,116424,116494,105919,101293,110134,111047,103764,117572,108496,100775,102009,114533,102122,86849,116758,100707,108043,111267,108022,117023,86136,102896,103787,116759,105768,107227,117263,86850,114736,111452,114542,101072,109649,111934,100897,105652,111931,108654,102003,114626,106116,113049,113687,112091,113577,103482,111892,109088,106407,106925,114513,114515,108319,106813,108278,113815,114590,117799,108269,107785,99804,108261,99541,99592);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (104973,116084,116359,115852,103824,102998,112263,116709,103648,109087,111808,102530,113497,108915,114729,109027,104384,109028,111693,106853,113876,105809,117066,114974,113437,115504,115421,107640,111547,111548,117210,117216,101507,113995,113646,114000,102721,118082,105209,112866,103580,107594,113719,114455,114756,100705,111988,101887,109290,100905,86386,109273,109285,86827,108159,109291,104319,110591,104913,109289,114341,101595,103998,116458,104315,104914,110592,103444,103460,107793,104117,109017,109016,117297,107596,106013,116842,116843,115077,103244,107287,106619,114190,86828,107442,114912,108759,108504,108514,115973,101406,115796,115795,112018,107112,112017,107530,104390,111665,116398,116397,116757,103399,114366,116495,112977,107570,111885,110538,111815,105370,117303,111880,110387,105893,112051,107664,116145,106803,113444,105695,102849,107555,110881,104151,105892,109039,103948,99438,108166,118153,107445,86135,86848,107318,114919,116424,116494,105919,101293,110134,111047,103764,117572,108496,100775,102009,114533,102122,86849,116758,100707,108043,111267,108022,117023,86136,102896,103787,116759,105768,107227,117263,86850,114736,111452,114542,101072,109649,111934,100897,105652,111931,108654,102003,114626,106116,113049,113687,112091,113577,103482,111892,109088,106407,106925,114513,114515,108319,106813,108278,113815,114590,117799,108269,107785,99804,108261,99541,99592);

delete quotes.* 
from quotes where instrumentid in (104973,116084,116359,115852,103824,102998,112263,116709,103648,109087,111808,102530,113497,108915,114729,109027,104384,109028,111693,106853,113876,105809,117066,114974,113437,115504,115421,107640,111547,111548,117210,117216,101507,113995,113646,114000,102721,118082,105209,112866,103580,107594,113719,114455,114756,100705,111988,101887,109290,100905,86386,109273,109285,86827,108159,109291,104319,110591,104913,109289,114341,101595,103998,116458,104315,104914,110592,103444,103460,107793,104117,109017,109016,117297,107596,106013,116842,116843,115077,103244,107287,106619,114190,86828,107442,114912,108759,108504,108514,115973,101406,115796,115795,112018,107112,112017,107530,104390,111665,116398,116397,116757,103399,114366,116495,112977,107570,111885,110538,111815,105370,117303,111880,110387,105893,112051,107664,116145,106803,113444,105695,102849,107555,110881,104151,105892,109039,103948,99438,108166,118153,107445,86135,86848,107318,114919,116424,116494,105919,101293,110134,111047,103764,117572,108496,100775,102009,114533,102122,86849,116758,100707,108043,111267,108022,117023,86136,102896,103787,116759,105768,107227,117263,86850,114736,111452,114542,101072,109649,111934,100897,105652,111931,108654,102003,114626,106116,113049,113687,112091,113577,103482,111892,109088,106407,106925,114513,114515,108319,106813,108278,113815,114590,117799,108269,107785,99804,108261,99541,99592);

commit;



begin;

select concat("FROM ",113813," TO ", 108629);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (113813,104237,101302,113814,113805,106817,115858,115734,115121,109578,115391,112603,102828,106399,112336,106147,110356,112010,100407,111451,100004,106462,105596,111461,117481,104188,108986,109395,113337,108183,117107,116215,106507,106918,106561,86874,101054,108605,106518,113818,113686,114174,106649,112212,112201,114499,86875,102455,114501,114500,106919,111819,109686,107022,105222,106197,102429,113083,100648,111750,105540,116688,114765,112430,112082,102838,105126,107587,117433,99629,116976,100180,116194,107601,86876,86410,111100,104193,110031,102397,103680,100398,107751,117157,115043,106793,112167,106467,101506,108350,101338,117569,112898,112899,103345,115016,103664,99842,118164,104961,105307,104958,103412,107060,101091,112706,100087,110727,105374,100837,109062,108545,107534,86160,102015,86411,86895,112299,111971,107858,99698,86161,103742,115422,112023,110630,104170,112675,100108,86162,106822,103614,107370,110588,115982,115667,115933,104612,103517,115423,117391,111312,102337,105533,111387,114827,116032,107649,101621,112190,112192,115230,113619,110610,109477,100761,104372,116912,99596,107089,103277,107090,108535,117386,102206,108231,111374,100067,113759,100332,113760,102410,105624,111874,115533,106472,112440,112086,113335,113338,116356,116080,102802,113149,102277,105803,111935,102730,102643,114348,106157,112405,103466,115981,110366,106042,103564,115363,110528,108629);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (113813,104237,101302,113814,113805,106817,115858,115734,115121,109578,115391,112603,102828,106399,112336,106147,110356,112010,100407,111451,100004,106462,105596,111461,117481,104188,108986,109395,113337,108183,117107,116215,106507,106918,106561,86874,101054,108605,106518,113818,113686,114174,106649,112212,112201,114499,86875,102455,114501,114500,106919,111819,109686,107022,105222,106197,102429,113083,100648,111750,105540,116688,114765,112430,112082,102838,105126,107587,117433,99629,116976,100180,116194,107601,86876,86410,111100,104193,110031,102397,103680,100398,107751,117157,115043,106793,112167,106467,101506,108350,101338,117569,112898,112899,103345,115016,103664,99842,118164,104961,105307,104958,103412,107060,101091,112706,100087,110727,105374,100837,109062,108545,107534,86160,102015,86411,86895,112299,111971,107858,99698,86161,103742,115422,112023,110630,104170,112675,100108,86162,106822,103614,107370,110588,115982,115667,115933,104612,103517,115423,117391,111312,102337,105533,111387,114827,116032,107649,101621,112190,112192,115230,113619,110610,109477,100761,104372,116912,99596,107089,103277,107090,108535,117386,102206,108231,111374,100067,113759,100332,113760,102410,105624,111874,115533,106472,112440,112086,113335,113338,116356,116080,102802,113149,102277,105803,111935,102730,102643,114348,106157,112405,103466,115981,110366,106042,103564,115363,110528,108629);

delete quotes.* 
from quotes where instrumentid in (113813,104237,101302,113814,113805,106817,115858,115734,115121,109578,115391,112603,102828,106399,112336,106147,110356,112010,100407,111451,100004,106462,105596,111461,117481,104188,108986,109395,113337,108183,117107,116215,106507,106918,106561,86874,101054,108605,106518,113818,113686,114174,106649,112212,112201,114499,86875,102455,114501,114500,106919,111819,109686,107022,105222,106197,102429,113083,100648,111750,105540,116688,114765,112430,112082,102838,105126,107587,117433,99629,116976,100180,116194,107601,86876,86410,111100,104193,110031,102397,103680,100398,107751,117157,115043,106793,112167,106467,101506,108350,101338,117569,112898,112899,103345,115016,103664,99842,118164,104961,105307,104958,103412,107060,101091,112706,100087,110727,105374,100837,109062,108545,107534,86160,102015,86411,86895,112299,111971,107858,99698,86161,103742,115422,112023,110630,104170,112675,100108,86162,106822,103614,107370,110588,115982,115667,115933,104612,103517,115423,117391,111312,102337,105533,111387,114827,116032,107649,101621,112190,112192,115230,113619,110610,109477,100761,104372,116912,99596,107089,103277,107090,108535,117386,102206,108231,111374,100067,113759,100332,113760,102410,105624,111874,115533,106472,112440,112086,113335,113338,116356,116080,102802,113149,102277,105803,111935,102730,102643,114348,106157,112405,103466,115981,110366,106042,103564,115363,110528,108629);

commit;



begin;

select concat("FROM ",103997," TO ", 99684);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (103997,115415,112248,115927,117767,108188,109863,115420,117679,86432,86923,108637,104017,113328,113331,104085,106517,113060,105591,115247,103423,86433,106608,114377,103397,116583,110032,115974,116496,111453,107486,112635,112326,106236,111450,114815,106573,86434,107966,108256,86924,112705,114370,114365,86925,109148,86926,104179,108958,116027,114145,112654,106571,113567,104800,115617,108572,116316,86435,105281,105382,100667,108957,110970,105283,107731,116355,109111,108837,113558,106566,116907,113320,111619,105709,106641,113321,118102,112789,108890,113871,100015,86436,113235,112889,111381,109520,104046,118161,117800,107803,110984,86017,86927,105550,108616,113319,106511,115821,106078,85961,86942,116815,100263,111546,116552,106855,106857,116544,116969,110095,103236,114697,107466,104720,108727,114648,101764,105861,105162,86943,86437,114036,106782,116209,115020,116178,102932,106790,109173,99503,117437,112309,100650,113334,103481,103970,116773,86944,111028,109946,102701,105022,106057,106469,101961,106479,113037,114180,100382,113817,116609,101947,86438,110115,86018,113701,106439,102805,113491,111520,103536,104590,107222,114897,117288,115001,115609,108602,108590,117260,110323,113912,115576,106932,111552,109325,109992,110961,108045,99530,112867,105869,110550,116994,103912,86945,112506,110719,105204,105205,116226,102971,108598,116796,116436,116408,100573,103195,99684);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (103997,115415,112248,115927,117767,108188,109863,115420,117679,86432,86923,108637,104017,113328,113331,104085,106517,113060,105591,115247,103423,86433,106608,114377,103397,116583,110032,115974,116496,111453,107486,112635,112326,106236,111450,114815,106573,86434,107966,108256,86924,112705,114370,114365,86925,109148,86926,104179,108958,116027,114145,112654,106571,113567,104800,115617,108572,116316,86435,105281,105382,100667,108957,110970,105283,107731,116355,109111,108837,113558,106566,116907,113320,111619,105709,106641,113321,118102,112789,108890,113871,100015,86436,113235,112889,111381,109520,104046,118161,117800,107803,110984,86017,86927,105550,108616,113319,106511,115821,106078,85961,86942,116815,100263,111546,116552,106855,106857,116544,116969,110095,103236,114697,107466,104720,108727,114648,101764,105861,105162,86943,86437,114036,106782,116209,115020,116178,102932,106790,109173,99503,117437,112309,100650,113334,103481,103970,116773,86944,111028,109946,102701,105022,106057,106469,101961,106479,113037,114180,100382,113817,116609,101947,86438,110115,86018,113701,106439,102805,113491,111520,103536,104590,107222,114897,117288,115001,115609,108602,108590,117260,110323,113912,115576,106932,111552,109325,109992,110961,108045,99530,112867,105869,110550,116994,103912,86945,112506,110719,105204,105205,116226,102971,108598,116796,116436,116408,100573,103195,99684);

delete quotes.* 
from quotes where instrumentid in (103997,115415,112248,115927,117767,108188,109863,115420,117679,86432,86923,108637,104017,113328,113331,104085,106517,113060,105591,115247,103423,86433,106608,114377,103397,116583,110032,115974,116496,111453,107486,112635,112326,106236,111450,114815,106573,86434,107966,108256,86924,112705,114370,114365,86925,109148,86926,104179,108958,116027,114145,112654,106571,113567,104800,115617,108572,116316,86435,105281,105382,100667,108957,110970,105283,107731,116355,109111,108837,113558,106566,116907,113320,111619,105709,106641,113321,118102,112789,108890,113871,100015,86436,113235,112889,111381,109520,104046,118161,117800,107803,110984,86017,86927,105550,108616,113319,106511,115821,106078,85961,86942,116815,100263,111546,116552,106855,106857,116544,116969,110095,103236,114697,107466,104720,108727,114648,101764,105861,105162,86943,86437,114036,106782,116209,115020,116178,102932,106790,109173,99503,117437,112309,100650,113334,103481,103970,116773,86944,111028,109946,102701,105022,106057,106469,101961,106479,113037,114180,100382,113817,116609,101947,86438,110115,86018,113701,106439,102805,113491,111520,103536,104590,107222,114897,117288,115001,115609,108602,108590,117260,110323,113912,115576,106932,111552,109325,109992,110961,108045,99530,112867,105869,110550,116994,103912,86945,112506,110719,105204,105205,116226,102971,108598,116796,116436,116408,100573,103195,99684);

commit;



begin;

select concat("FROM ",111557," TO ", 102279);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (111557,111559,111293,86042,86971,110329,110062,101910,110063,110328,110354,105526,109406,102137,105674,112073,105306,112322,86972,86043,113044,106254,100774,104278,105430,111438,112376,85962,107292,86454,117446,117370,108967,108578,101911,99696,107257,113681,107344,115832,103527,112561,105939,110160,113943,100049,111788,105560,114742,114740,103188,108631,108621,101888,103650,102113,106732,105592,111454,105946,113996,111796,99669,109000,111549,105600,102683,117680,104936,101197,100570,110856,110068,114675,105138,85948,108609,106831,114182,114187,103043,109351,110041,109364,101590,107557,113430,100838,110733,112014,108873,117904,117027,107635,105793,111995,105720,101948,111712,112791,112094,112790,106180,103101,106543,100196,99779,114384,106292,106617,106873,86175,109144,117376,114571,108180,100449,117894,102451,111952,102931,103098,115088,115072,100132,113708,116274,116115,106366,113383,109853,104303,116806,107928,110300,99805,103772,108591,113700,99677,113325,116127,86990,86176,105932,115633,107312,110817,107439,101215,116404,114999,114997,101745,108595,108592,107650,107654,115494,129011,102964,111116,110815,86991,111238,101561,116474,117342,111231,108448,109569,99458,99391,116901,102553,109316,116767,109029,118840,118765,108551,102102,100531,116129,112625,86992,99959,100328,110636,110953,101474,109828,104305,106473,114589,112135,102507,104619,105801,102279);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (111557,111559,111293,86042,86971,110329,110062,101910,110063,110328,110354,105526,109406,102137,105674,112073,105306,112322,86972,86043,113044,106254,100774,104278,105430,111438,112376,85962,107292,86454,117446,117370,108967,108578,101911,99696,107257,113681,107344,115832,103527,112561,105939,110160,113943,100049,111788,105560,114742,114740,103188,108631,108621,101888,103650,102113,106732,105592,111454,105946,113996,111796,99669,109000,111549,105600,102683,117680,104936,101197,100570,110856,110068,114675,105138,85948,108609,106831,114182,114187,103043,109351,110041,109364,101590,107557,113430,100838,110733,112014,108873,117904,117027,107635,105793,111995,105720,101948,111712,112791,112094,112790,106180,103101,106543,100196,99779,114384,106292,106617,106873,86175,109144,117376,114571,108180,100449,117894,102451,111952,102931,103098,115088,115072,100132,113708,116274,116115,106366,113383,109853,104303,116806,107928,110300,99805,103772,108591,113700,99677,113325,116127,86990,86176,105932,115633,107312,110817,107439,101215,116404,114999,114997,101745,108595,108592,107650,107654,115494,129011,102964,111116,110815,86991,111238,101561,116474,117342,111231,108448,109569,99458,99391,116901,102553,109316,116767,109029,118840,118765,108551,102102,100531,116129,112625,86992,99959,100328,110636,110953,101474,109828,104305,106473,114589,112135,102507,104619,105801,102279);

delete quotes.* 
from quotes where instrumentid in (111557,111559,111293,86042,86971,110329,110062,101910,110063,110328,110354,105526,109406,102137,105674,112073,105306,112322,86972,86043,113044,106254,100774,104278,105430,111438,112376,85962,107292,86454,117446,117370,108967,108578,101911,99696,107257,113681,107344,115832,103527,112561,105939,110160,113943,100049,111788,105560,114742,114740,103188,108631,108621,101888,103650,102113,106732,105592,111454,105946,113996,111796,99669,109000,111549,105600,102683,117680,104936,101197,100570,110856,110068,114675,105138,85948,108609,106831,114182,114187,103043,109351,110041,109364,101590,107557,113430,100838,110733,112014,108873,117904,117027,107635,105793,111995,105720,101948,111712,112791,112094,112790,106180,103101,106543,100196,99779,114384,106292,106617,106873,86175,109144,117376,114571,108180,100449,117894,102451,111952,102931,103098,115088,115072,100132,113708,116274,116115,106366,113383,109853,104303,116806,107928,110300,99805,103772,108591,113700,99677,113325,116127,86990,86176,105932,115633,107312,110817,107439,101215,116404,114999,114997,101745,108595,108592,107650,107654,115494,129011,102964,111116,110815,86991,111238,101561,116474,117342,111231,108448,109569,99458,99391,116901,102553,109316,116767,109029,118840,118765,108551,102102,100531,116129,112625,86992,99959,100328,110636,110953,101474,109828,104305,106473,114589,112135,102507,104619,105801,102279);

commit;



begin;

select concat("FROM ",100723," TO ", 103619);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (100723,102276,102785,106091,106090,106087,108155,107507,107508,108827,117973,114910,114089,114415,114101,113492,110086,108340,104712,110091,99373,103669,105788,99763,86044,101320,86474,114461,103487,102459,102503,87005,117445,115843,117381,108203,102075,115123,104121,107701,107653,108415,103411,117448,104050,117447,108235,111377,105310,101730,117328,108416,109457,115126,117910,107808,100301,86475,87006,85949,87007,115873,114103,114110,114099,86476,116678,106988,87008,86477,114098,114421,112149,110136,105906,87009,108966,87010,86177,115938,111031,104351,108916,101055,106736,104088,112639,105160,117834,113707,101715,103255,86478,106945,86178,87029,110461,104576,116396,113508,114896,110778,105463,103115,110051,101088,111654,103826,112691,111456,99925,106690,86479,103073,101896,87030,86480,115702,113133,113562,102664,106237,110954,100506,112744,114904,102941,114431,101066,110346,100835,106167,109919,104952,108412,110626,105841,105593,117438,117439,103757,117172,99921,99388,109631,109877,113556,101073,114741,87031,102255,108661,112663,87032,101142,117812,103933,116357,107341,108891,117925,103161,113976,101184,104122,104481,108903,116798,106581,109507,111435,111285,108458,111774,109458,100431,118074,101204,118533,113626,106444,107047,114289,101998,118342,108217,101982,117722,108214,101872,106225,87046,86045,111972,110656,105562,113793,111442,105060,103619);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (100723,102276,102785,106091,106090,106087,108155,107507,107508,108827,117973,114910,114089,114415,114101,113492,110086,108340,104712,110091,99373,103669,105788,99763,86044,101320,86474,114461,103487,102459,102503,87005,117445,115843,117381,108203,102075,115123,104121,107701,107653,108415,103411,117448,104050,117447,108235,111377,105310,101730,117328,108416,109457,115126,117910,107808,100301,86475,87006,85949,87007,115873,114103,114110,114099,86476,116678,106988,87008,86477,114098,114421,112149,110136,105906,87009,108966,87010,86177,115938,111031,104351,108916,101055,106736,104088,112639,105160,117834,113707,101715,103255,86478,106945,86178,87029,110461,104576,116396,113508,114896,110778,105463,103115,110051,101088,111654,103826,112691,111456,99925,106690,86479,103073,101896,87030,86480,115702,113133,113562,102664,106237,110954,100506,112744,114904,102941,114431,101066,110346,100835,106167,109919,104952,108412,110626,105841,105593,117438,117439,103757,117172,99921,99388,109631,109877,113556,101073,114741,87031,102255,108661,112663,87032,101142,117812,103933,116357,107341,108891,117925,103161,113976,101184,104122,104481,108903,116798,106581,109507,111435,111285,108458,111774,109458,100431,118074,101204,118533,113626,106444,107047,114289,101998,118342,108217,101982,117722,108214,101872,106225,87046,86045,111972,110656,105562,113793,111442,105060,103619);

delete quotes.* 
from quotes where instrumentid in (100723,102276,102785,106091,106090,106087,108155,107507,107508,108827,117973,114910,114089,114415,114101,113492,110086,108340,104712,110091,99373,103669,105788,99763,86044,101320,86474,114461,103487,102459,102503,87005,117445,115843,117381,108203,102075,115123,104121,107701,107653,108415,103411,117448,104050,117447,108235,111377,105310,101730,117328,108416,109457,115126,117910,107808,100301,86475,87006,85949,87007,115873,114103,114110,114099,86476,116678,106988,87008,86477,114098,114421,112149,110136,105906,87009,108966,87010,86177,115938,111031,104351,108916,101055,106736,104088,112639,105160,117834,113707,101715,103255,86478,106945,86178,87029,110461,104576,116396,113508,114896,110778,105463,103115,110051,101088,111654,103826,112691,111456,99925,106690,86479,103073,101896,87030,86480,115702,113133,113562,102664,106237,110954,100506,112744,114904,102941,114431,101066,110346,100835,106167,109919,104952,108412,110626,105841,105593,117438,117439,103757,117172,99921,99388,109631,109877,113556,101073,114741,87031,102255,108661,112663,87032,101142,117812,103933,116357,107341,108891,117925,103161,113976,101184,104122,104481,108903,116798,106581,109507,111435,111285,108458,111774,109458,100431,118074,101204,118533,113626,106444,107047,114289,101998,118342,108217,101982,117722,108214,101872,106225,87046,86045,111972,110656,105562,113793,111442,105060,103619);

commit;



begin;

select concat("FROM ",107666," TO ", 106433);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (107666,115877,101626,99803,103462,100890,119225,118634,119265,118221,118434,87047,118955,101624,119123,118420,118704,118877,118227,119148,118234,119422,118923,104473,104467,99990,87048,114980,100602,117476,103983,112710,86507,101492,86209,110784,105497,101799,106638,106034,99647,87049,86210,107203,108645,109147,111887,109893,111286,129635,102480,114247,115263,103468,111436,108594,117162,112258,107059,110957,110966,116623,116595,103528,116590,86508,116650,119125,87050,109271,111929,103238,104283,99981,105345,103317,101654,99872,115939,99428,102037,114745,105888,113393,115370,103557,99593,107213,103295,115369,114111,115887,114433,103625,107671,103189,115678,107355,100348,103470,116266,104711,99436,114112,116264,112118,112122,105748,87064,105745,112107,108955,112112,105741,108896,105742,115287,115292,115258,113723,106710,107732,109306,110498,105902,111737,115403,107210,101680,101271,106431,111281,111772,119288,101838,117714,101403,99597,99950,106176,106174,107680,115474,116038,99584,112313,102640,86211,115736,115735,107859,111140,116893,117282,114422,110442,110638,112712,104886,114585,115850,115556,103504,101722,112936,112937,105414,114215,100021,107633,115460,115459,111171,102433,100353,100789,99833,104688,100112,104862,99636,100655,101591,115045,106535,113724,115076,86212,110975,115508,114024,106268,116343,107018,106514,104025,111553,107662,115868,106433);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (107666,115877,101626,99803,103462,100890,119225,118634,119265,118221,118434,87047,118955,101624,119123,118420,118704,118877,118227,119148,118234,119422,118923,104473,104467,99990,87048,114980,100602,117476,103983,112710,86507,101492,86209,110784,105497,101799,106638,106034,99647,87049,86210,107203,108645,109147,111887,109893,111286,129635,102480,114247,115263,103468,111436,108594,117162,112258,107059,110957,110966,116623,116595,103528,116590,86508,116650,119125,87050,109271,111929,103238,104283,99981,105345,103317,101654,99872,115939,99428,102037,114745,105888,113393,115370,103557,99593,107213,103295,115369,114111,115887,114433,103625,107671,103189,115678,107355,100348,103470,116266,104711,99436,114112,116264,112118,112122,105748,87064,105745,112107,108955,112112,105741,108896,105742,115287,115292,115258,113723,106710,107732,109306,110498,105902,111737,115403,107210,101680,101271,106431,111281,111772,119288,101838,117714,101403,99597,99950,106176,106174,107680,115474,116038,99584,112313,102640,86211,115736,115735,107859,111140,116893,117282,114422,110442,110638,112712,104886,114585,115850,115556,103504,101722,112936,112937,105414,114215,100021,107633,115460,115459,111171,102433,100353,100789,99833,104688,100112,104862,99636,100655,101591,115045,106535,113724,115076,86212,110975,115508,114024,106268,116343,107018,106514,104025,111553,107662,115868,106433);

delete quotes.* 
from quotes where instrumentid in (107666,115877,101626,99803,103462,100890,119225,118634,119265,118221,118434,87047,118955,101624,119123,118420,118704,118877,118227,119148,118234,119422,118923,104473,104467,99990,87048,114980,100602,117476,103983,112710,86507,101492,86209,110784,105497,101799,106638,106034,99647,87049,86210,107203,108645,109147,111887,109893,111286,129635,102480,114247,115263,103468,111436,108594,117162,112258,107059,110957,110966,116623,116595,103528,116590,86508,116650,119125,87050,109271,111929,103238,104283,99981,105345,103317,101654,99872,115939,99428,102037,114745,105888,113393,115370,103557,99593,107213,103295,115369,114111,115887,114433,103625,107671,103189,115678,107355,100348,103470,116266,104711,99436,114112,116264,112118,112122,105748,87064,105745,112107,108955,112112,105741,108896,105742,115287,115292,115258,113723,106710,107732,109306,110498,105902,111737,115403,107210,101680,101271,106431,111281,111772,119288,101838,117714,101403,99597,99950,106176,106174,107680,115474,116038,99584,112313,102640,86211,115736,115735,107859,111140,116893,117282,114422,110442,110638,112712,104886,114585,115850,115556,103504,101722,112936,112937,105414,114215,100021,107633,115460,115459,111171,102433,100353,100789,99833,104688,100112,104862,99636,100655,101591,115045,106535,113724,115076,86212,110975,115508,114024,106268,116343,107018,106514,104025,111553,107662,115868,106433);

commit;



begin;

select concat("FROM ",108115," TO ", 117511);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (108115,108472,116975,108471,107535,117449,117451,101117,105434,103387,100468,111561,117270,100488,105756,99658,114687,101190,100632,117608,110922,110502,111575,111169,111178,112754,112755,103099,113695,101151,87068,111761,103050,114584,108174,117319,111790,111787,107828,103851,102109,111926,112054,108838,87069,115488,113838,102362,103252,115170,87070,99423,103662,108201,109394,103346,100594,112191,112539,102937,116632,114929,117847,110499,107201,100212,86213,106238,114206,110216,102657,117329,110112,114511,102954,105035,86533,105031,115462,107634,101617,115463,116873,115473,86534,109638,103199,101814,109613,113532,118175,85963,107411,103739,117326,108182,116137,116141,109374,102955,117705,104173,110516,117034,112547,116503,116766,104242,110033,104026,108633,118424,118361,85950,109318,114032,115167,86046,101647,104746,117473,85964,118134,116610,104005,113391,104007,104766,108241,100569,107684,114176,107885,108962,103207,107456,116079,116594,102843,116401,116781,109484,113993,115932,106209,85972,99422,108438,107021,110137,102746,117635,111979,101868,104622,116134,102438,107260,105638,116847,109622,113113,86240,104189,116850,110264,107161,116050,107158,115040,107987,109644,109570,109572,109571,112878,109317,111603,101248,111591,102439,105643,111280,111279,111545,111277,115501,116132,100137,116131,107881,111600,100142,86552,107230,112713,105826,99923,99469,117511);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (108115,108472,116975,108471,107535,117449,117451,101117,105434,103387,100468,111561,117270,100488,105756,99658,114687,101190,100632,117608,110922,110502,111575,111169,111178,112754,112755,103099,113695,101151,87068,111761,103050,114584,108174,117319,111790,111787,107828,103851,102109,111926,112054,108838,87069,115488,113838,102362,103252,115170,87070,99423,103662,108201,109394,103346,100594,112191,112539,102937,116632,114929,117847,110499,107201,100212,86213,106238,114206,110216,102657,117329,110112,114511,102954,105035,86533,105031,115462,107634,101617,115463,116873,115473,86534,109638,103199,101814,109613,113532,118175,85963,107411,103739,117326,108182,116137,116141,109374,102955,117705,104173,110516,117034,112547,116503,116766,104242,110033,104026,108633,118424,118361,85950,109318,114032,115167,86046,101647,104746,117473,85964,118134,116610,104005,113391,104007,104766,108241,100569,107684,114176,107885,108962,103207,107456,116079,116594,102843,116401,116781,109484,113993,115932,106209,85972,99422,108438,107021,110137,102746,117635,111979,101868,104622,116134,102438,107260,105638,116847,109622,113113,86240,104189,116850,110264,107161,116050,107158,115040,107987,109644,109570,109572,109571,112878,109317,111603,101248,111591,102439,105643,111280,111279,111545,111277,115501,116132,100137,116131,107881,111600,100142,86552,107230,112713,105826,99923,99469,117511);

delete quotes.* 
from quotes where instrumentid in (108115,108472,116975,108471,107535,117449,117451,101117,105434,103387,100468,111561,117270,100488,105756,99658,114687,101190,100632,117608,110922,110502,111575,111169,111178,112754,112755,103099,113695,101151,87068,111761,103050,114584,108174,117319,111790,111787,107828,103851,102109,111926,112054,108838,87069,115488,113838,102362,103252,115170,87070,99423,103662,108201,109394,103346,100594,112191,112539,102937,116632,114929,117847,110499,107201,100212,86213,106238,114206,110216,102657,117329,110112,114511,102954,105035,86533,105031,115462,107634,101617,115463,116873,115473,86534,109638,103199,101814,109613,113532,118175,85963,107411,103739,117326,108182,116137,116141,109374,102955,117705,104173,110516,117034,112547,116503,116766,104242,110033,104026,108633,118424,118361,85950,109318,114032,115167,86046,101647,104746,117473,85964,118134,116610,104005,113391,104007,104766,108241,100569,107684,114176,107885,108962,103207,107456,116079,116594,102843,116401,116781,109484,113993,115932,106209,85972,99422,108438,107021,110137,102746,117635,111979,101868,104622,116134,102438,107260,105638,116847,109622,113113,86240,104189,116850,110264,107161,116050,107158,115040,107987,109644,109570,109572,109571,112878,109317,111603,101248,111591,102439,105643,111280,111279,111545,111277,115501,116132,100137,116131,107881,111600,100142,86552,107230,112713,105826,99923,99469,117511);

commit;



begin;

select concat("FROM ",100139," TO ", 106223);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (100139,100462,114530,108360,109397,115607,106652,112709,99879,105503,103633,105991,106665,109798,108205,104617,107656,107657,115499,99688,104003,110631,113176,104055,104962,112594,104279,104697,110358,103754,104178,111426,111105,111114,102295,115476,107663,106434,105804,99816,112243,103989,101659,100032,116720,116148,116146,107712,104280,102076,102091,86574,107315,114914,107336,110070,109600,109597,109345,114136,114135,109598,105670,108334,111962,107249,115810,110388,115266,114164,115587,117245,117857,86575,105124,118031,110564,102663,102579,106043,105316,115792,110365,111298,106802,106584,129037,106445,113480,128911,103272,104661,129021,113620,113270,128892,104666,102084,129167,102292,108024,129039,114413,113493,101672,103681,107832,113622,113271,113621,104665,99569,103794,100403,111103,108914,103137,107123,114926,110102,100918,104717,110099,115054,106466,113052,113053,115296,108952,112728,112731,112724,107450,106084,112711,105844,112071,112421,112423,108414,106558,99966,100582,113470,114638,107559,100494,113345,112078,112083,86254,86598,86068,100031,118064,109333,117601,117373,117372,111909,101252,86599,102321,111503,110514,112462,86255,99962,111829,108110,86256,116728,106966,100925,86257,100116,105771,114979,113116,107973,108646,86600,115106,114732,107356,101299,107619,102846,113427,112607,113455,103348,107490,116036,108260,103039,113182,117226,102781,106223);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (100139,100462,114530,108360,109397,115607,106652,112709,99879,105503,103633,105991,106665,109798,108205,104617,107656,107657,115499,99688,104003,110631,113176,104055,104962,112594,104279,104697,110358,103754,104178,111426,111105,111114,102295,115476,107663,106434,105804,99816,112243,103989,101659,100032,116720,116148,116146,107712,104280,102076,102091,86574,107315,114914,107336,110070,109600,109597,109345,114136,114135,109598,105670,108334,111962,107249,115810,110388,115266,114164,115587,117245,117857,86575,105124,118031,110564,102663,102579,106043,105316,115792,110365,111298,106802,106584,129037,106445,113480,128911,103272,104661,129021,113620,113270,128892,104666,102084,129167,102292,108024,129039,114413,113493,101672,103681,107832,113622,113271,113621,104665,99569,103794,100403,111103,108914,103137,107123,114926,110102,100918,104717,110099,115054,106466,113052,113053,115296,108952,112728,112731,112724,107450,106084,112711,105844,112071,112421,112423,108414,106558,99966,100582,113470,114638,107559,100494,113345,112078,112083,86254,86598,86068,100031,118064,109333,117601,117373,117372,111909,101252,86599,102321,111503,110514,112462,86255,99962,111829,108110,86256,116728,106966,100925,86257,100116,105771,114979,113116,107973,108646,86600,115106,114732,107356,101299,107619,102846,113427,112607,113455,103348,107490,116036,108260,103039,113182,117226,102781,106223);

delete quotes.* 
from quotes where instrumentid in (100139,100462,114530,108360,109397,115607,106652,112709,99879,105503,103633,105991,106665,109798,108205,104617,107656,107657,115499,99688,104003,110631,113176,104055,104962,112594,104279,104697,110358,103754,104178,111426,111105,111114,102295,115476,107663,106434,105804,99816,112243,103989,101659,100032,116720,116148,116146,107712,104280,102076,102091,86574,107315,114914,107336,110070,109600,109597,109345,114136,114135,109598,105670,108334,111962,107249,115810,110388,115266,114164,115587,117245,117857,86575,105124,118031,110564,102663,102579,106043,105316,115792,110365,111298,106802,106584,129037,106445,113480,128911,103272,104661,129021,113620,113270,128892,104666,102084,129167,102292,108024,129039,114413,113493,101672,103681,107832,113622,113271,113621,104665,99569,103794,100403,111103,108914,103137,107123,114926,110102,100918,104717,110099,115054,106466,113052,113053,115296,108952,112728,112731,112724,107450,106084,112711,105844,112071,112421,112423,108414,106558,99966,100582,113470,114638,107559,100494,113345,112078,112083,86254,86598,86068,100031,118064,109333,117601,117373,117372,111909,101252,86599,102321,111503,110514,112462,86255,99962,111829,108110,86256,116728,106966,100925,86257,100116,105771,114979,113116,107973,108646,86600,115106,114732,107356,101299,107619,102846,113427,112607,113455,103348,107490,116036,108260,103039,113182,117226,102781,106223);

commit;



begin;

select concat("FROM ",115270," TO ", 100375);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (115270,115426,103999,101013,109505,106318,109455,112221,112233,109308,100907,109653,111089,109307,101606,104307,104323,117627,117641,104138,106031,114946,108660,114534,102593,99555,100991,103596,112621,101682,100452,107288,101255,115080,113696,86619,85979,107801,86258,110422,115199,107019,107023,115189,107009,116587,103324,103592,110513,110828,108723,86620,101726,104715,110088,104713,100038,117631,111570,107111,105843,101358,100214,86621,86069,107066,107334,116181,100187,103581,100523,109506,105538,104427,111395,107020,115435,107025,107564,101533,108449,117474,109573,109319,106714,101877,107498,104631,109393,99916,107658,115860,100668,100450,107894,108321,100522,113124,99985,100192,100682,117349,107270,103866,109114,108841,101116,102204,101747,117813,107163,117922,117923,105792,111994,115211,100006,111849,105611,106030,112993,109615,115117,107366,117760,114416,114417,100019,107269,99443,105565,102198,115811,101517,112153,100942,105926,112139,111296,107455,115203,115209,109551,105922,105753,99692,117963,103723,108279,115049,100670,114992,114614,114430,114429,115192,114106,114428,114618,115210,115208,118007,111740,113127,103519,115225,108403,103415,108222,105178,100960,108232,115396,106806,115166,114858,111779,105594,117467,108772,110353,106010,115922,117466,99751,113785,113783,103930,117423,106673,111922,99830,103251,117465,104056,107496,108226,112592,108617,100375);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (115270,115426,103999,101013,109505,106318,109455,112221,112233,109308,100907,109653,111089,109307,101606,104307,104323,117627,117641,104138,106031,114946,108660,114534,102593,99555,100991,103596,112621,101682,100452,107288,101255,115080,113696,86619,85979,107801,86258,110422,115199,107019,107023,115189,107009,116587,103324,103592,110513,110828,108723,86620,101726,104715,110088,104713,100038,117631,111570,107111,105843,101358,100214,86621,86069,107066,107334,116181,100187,103581,100523,109506,105538,104427,111395,107020,115435,107025,107564,101533,108449,117474,109573,109319,106714,101877,107498,104631,109393,99916,107658,115860,100668,100450,107894,108321,100522,113124,99985,100192,100682,117349,107270,103866,109114,108841,101116,102204,101747,117813,107163,117922,117923,105792,111994,115211,100006,111849,105611,106030,112993,109615,115117,107366,117760,114416,114417,100019,107269,99443,105565,102198,115811,101517,112153,100942,105926,112139,111296,107455,115203,115209,109551,105922,105753,99692,117963,103723,108279,115049,100670,114992,114614,114430,114429,115192,114106,114428,114618,115210,115208,118007,111740,113127,103519,115225,108403,103415,108222,105178,100960,108232,115396,106806,115166,114858,111779,105594,117467,108772,110353,106010,115922,117466,99751,113785,113783,103930,117423,106673,111922,99830,103251,117465,104056,107496,108226,112592,108617,100375);

delete quotes.* 
from quotes where instrumentid in (115270,115426,103999,101013,109505,106318,109455,112221,112233,109308,100907,109653,111089,109307,101606,104307,104323,117627,117641,104138,106031,114946,108660,114534,102593,99555,100991,103596,112621,101682,100452,107288,101255,115080,113696,86619,85979,107801,86258,110422,115199,107019,107023,115189,107009,116587,103324,103592,110513,110828,108723,86620,101726,104715,110088,104713,100038,117631,111570,107111,105843,101358,100214,86621,86069,107066,107334,116181,100187,103581,100523,109506,105538,104427,111395,107020,115435,107025,107564,101533,108449,117474,109573,109319,106714,101877,107498,104631,109393,99916,107658,115860,100668,100450,107894,108321,100522,113124,99985,100192,100682,117349,107270,103866,109114,108841,101116,102204,101747,117813,107163,117922,117923,105792,111994,115211,100006,111849,105611,106030,112993,109615,115117,107366,117760,114416,114417,100019,107269,99443,105565,102198,115811,101517,112153,100942,105926,112139,111296,107455,115203,115209,109551,105922,105753,99692,117963,103723,108279,115049,100670,114992,114614,114430,114429,115192,114106,114428,114618,115210,115208,118007,111740,113127,103519,115225,108403,103415,108222,105178,100960,108232,115396,106806,115166,114858,111779,105594,117467,108772,110353,106010,115922,117466,99751,113785,113783,103930,117423,106673,111922,99830,103251,117465,104056,107496,108226,112592,108617,100375);

commit;



begin;

select concat("FROM ",113039," TO ", 116330);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (113039,101136,104867,109124,103404,108851,100467,106986,105364,99888,110238,106798,115737,116582,117337,116973,108338,108720,112142,112143,112145,105927,108337,116262,115885,116265,103417,115200,100764,112147,116250,109552,108004,108171,101030,109559,118028,116249,103599,105824,86101,103339,116094,86677,85980,116715,112331,107771,113283,113282,115950,113634,113281,113280,113632,103773,100879,109671,118043,118022,104599,108179,104325,117646,103563,100104,115397,107761,85973,105556,111786,109729,109732,110098,101634,107690,100463,116865,99713,103418,100107,100940,103617,109657,103372,105558,105557,111817,116303,101899,102699,102456,99878,109438,104457,109437,111783,111785,111789,103828,110565,110561,102353,103822,100321,109788,103446,118061,112661,108921,86275,111078,86102,107724,110697,104954,112590,113872,99437,86689,86103,100844,102305,115036,105352,114653,115432,109882,116326,103498,106564,107767,104069,108782,113092,111835,105446,103127,101952,102175,108125,109878,99460,112893,111791,113245,107407,103994,100350,86308,105561,102968,109132,100841,110929,85981,117798,99365,115284,108898,102538,102264,108173,115594,110930,106058,103937,99448,113465,102293,109795,105698,102549,102552,116323,101955,116332,115944,101933,104291,107645,108626,101697,101604,106032,104350,102587,116042,109794,109202,111700,111699,109302,108429,113419,117600,113284,116322,103829,116330);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (113039,101136,104867,109124,103404,108851,100467,106986,105364,99888,110238,106798,115737,116582,117337,116973,108338,108720,112142,112143,112145,105927,108337,116262,115885,116265,103417,115200,100764,112147,116250,109552,108004,108171,101030,109559,118028,116249,103599,105824,86101,103339,116094,86677,85980,116715,112331,107771,113283,113282,115950,113634,113281,113280,113632,103773,100879,109671,118043,118022,104599,108179,104325,117646,103563,100104,115397,107761,85973,105556,111786,109729,109732,110098,101634,107690,100463,116865,99713,103418,100107,100940,103617,109657,103372,105558,105557,111817,116303,101899,102699,102456,99878,109438,104457,109437,111783,111785,111789,103828,110565,110561,102353,103822,100321,109788,103446,118061,112661,108921,86275,111078,86102,107724,110697,104954,112590,113872,99437,86689,86103,100844,102305,115036,105352,114653,115432,109882,116326,103498,106564,107767,104069,108782,113092,111835,105446,103127,101952,102175,108125,109878,99460,112893,111791,113245,107407,103994,100350,86308,105561,102968,109132,100841,110929,85981,117798,99365,115284,108898,102538,102264,108173,115594,110930,106058,103937,99448,113465,102293,109795,105698,102549,102552,116323,101955,116332,115944,101933,104291,107645,108626,101697,101604,106032,104350,102587,116042,109794,109202,111700,111699,109302,108429,113419,117600,113284,116322,103829,116330);

delete quotes.* 
from quotes where instrumentid in (113039,101136,104867,109124,103404,108851,100467,106986,105364,99888,110238,106798,115737,116582,117337,116973,108338,108720,112142,112143,112145,105927,108337,116262,115885,116265,103417,115200,100764,112147,116250,109552,108004,108171,101030,109559,118028,116249,103599,105824,86101,103339,116094,86677,85980,116715,112331,107771,113283,113282,115950,113634,113281,113280,113632,103773,100879,109671,118043,118022,104599,108179,104325,117646,103563,100104,115397,107761,85973,105556,111786,109729,109732,110098,101634,107690,100463,116865,99713,103418,100107,100940,103617,109657,103372,105558,105557,111817,116303,101899,102699,102456,99878,109438,104457,109437,111783,111785,111789,103828,110565,110561,102353,103822,100321,109788,103446,118061,112661,108921,86275,111078,86102,107724,110697,104954,112590,113872,99437,86689,86103,100844,102305,115036,105352,114653,115432,109882,116326,103498,106564,107767,104069,108782,113092,111835,105446,103127,101952,102175,108125,109878,99460,112893,111791,113245,107407,103994,100350,86308,105561,102968,109132,100841,110929,85981,117798,99365,115284,108898,102538,102264,108173,115594,110930,106058,103937,99448,113465,102293,109795,105698,102549,102552,116323,101955,116332,115944,101933,104291,107645,108626,101697,101604,106032,104350,102587,116042,109794,109202,111700,111699,109302,108429,113419,117600,113284,116322,103829,116330);

commit;



begin;

select concat("FROM ",108108," TO ", 118443);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (108108,116966,113151,116962,116325,116339,106145,113636,106182,116820,115431,86720,116095,101085,112282,102513,102956,102953,107720,116160,116163,109711,101635,109692,102719,106572,110794,116369,99699,110717,109423,100075,99488,99694,107258,114676,110958,110438,110127,109295,109299,100133,116291,103632,100813,105488,111444,115414,117014,103915,104148,99435,118026,113031,102836,100245,108603,114121,114787,113937,110795,102996,113761,112727,100505,105957,110507,112152,103616,100023,107642,107660,115866,112274,113986,102965,111711,104758,110447,111309,117321,113112,112019,100303,112241,117929,114804,102982,113217,112872,113011,116415,113213,102582,108025,116661,117060,104984,104801,113231,113230,112885,118035,108694,108696,106222,113526,113529,117908,114661,116658,106088,99386,103708,100727,109064,104445,109067,109063,102612,117530,106552,106108,117050,103768,112652,102120,109165,116683,106791,115407,99374,108799,105057,110974,107960,101520,109443,109448,109885,116336,111558,86751,113189,117002,108842,113398,106570,112294,86137,115214,113084,113082,106198,109837,103044,100521,115039,112641,102180,106821,103351,109886,113187,104793,99417,109801,102769,106059,104177,106554,109887,104796,109890,104795,115945,116334,103497,106724,103887,103553,86138,86752,86325,109716,115286,115291,86326,117558,101481,104931,111630,105320,110239,104615,105647,112665,108707,119077,118443);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (108108,116966,113151,116962,116325,116339,106145,113636,106182,116820,115431,86720,116095,101085,112282,102513,102956,102953,107720,116160,116163,109711,101635,109692,102719,106572,110794,116369,99699,110717,109423,100075,99488,99694,107258,114676,110958,110438,110127,109295,109299,100133,116291,103632,100813,105488,111444,115414,117014,103915,104148,99435,118026,113031,102836,100245,108603,114121,114787,113937,110795,102996,113761,112727,100505,105957,110507,112152,103616,100023,107642,107660,115866,112274,113986,102965,111711,104758,110447,111309,117321,113112,112019,100303,112241,117929,114804,102982,113217,112872,113011,116415,113213,102582,108025,116661,117060,104984,104801,113231,113230,112885,118035,108694,108696,106222,113526,113529,117908,114661,116658,106088,99386,103708,100727,109064,104445,109067,109063,102612,117530,106552,106108,117050,103768,112652,102120,109165,116683,106791,115407,99374,108799,105057,110974,107960,101520,109443,109448,109885,116336,111558,86751,113189,117002,108842,113398,106570,112294,86137,115214,113084,113082,106198,109837,103044,100521,115039,112641,102180,106821,103351,109886,113187,104793,99417,109801,102769,106059,104177,106554,109887,104796,109890,104795,115945,116334,103497,106724,103887,103553,86138,86752,86325,109716,115286,115291,86326,117558,101481,104931,111630,105320,110239,104615,105647,112665,108707,119077,118443);

delete quotes.* 
from quotes where instrumentid in (108108,116966,113151,116962,116325,116339,106145,113636,106182,116820,115431,86720,116095,101085,112282,102513,102956,102953,107720,116160,116163,109711,101635,109692,102719,106572,110794,116369,99699,110717,109423,100075,99488,99694,107258,114676,110958,110438,110127,109295,109299,100133,116291,103632,100813,105488,111444,115414,117014,103915,104148,99435,118026,113031,102836,100245,108603,114121,114787,113937,110795,102996,113761,112727,100505,105957,110507,112152,103616,100023,107642,107660,115866,112274,113986,102965,111711,104758,110447,111309,117321,113112,112019,100303,112241,117929,114804,102982,113217,112872,113011,116415,113213,102582,108025,116661,117060,104984,104801,113231,113230,112885,118035,108694,108696,106222,113526,113529,117908,114661,116658,106088,99386,103708,100727,109064,104445,109067,109063,102612,117530,106552,106108,117050,103768,112652,102120,109165,116683,106791,115407,99374,108799,105057,110974,107960,101520,109443,109448,109885,116336,111558,86751,113189,117002,108842,113398,106570,112294,86137,115214,113084,113082,106198,109837,103044,100521,115039,112641,102180,106821,103351,109886,113187,104793,99417,109801,102769,106059,104177,106554,109887,104796,109890,104795,115945,116334,103497,106724,103887,103553,86138,86752,86325,109716,115286,115291,86326,117558,101481,104931,111630,105320,110239,104615,105647,112665,108707,119077,118443);

commit;



begin;

select concat("FROM ",104896," TO ", 102712);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (104896,101098,113778,113114,106405,100995,105120,104916,100185,104898,102978,109796,115110,116860,106412,100344,114522,107968,109934,103112,113452,110559,113506,107545,114087,114084,115294,114085,107332,112808,116824,103775,116822,86012,86778,86327,116651,99671,105840,100910,102163,101930,86779,99948,101771,101772,117081,111738,117296,109119,105239,112519,105504,105285,111465,109071,101212,104732,110487,110477,113918,106944,114310,108230,104002,104001,115404,113399,106046,108852,114810,109121,102494,102189,116904,113627,113275,101815,106017,112617,112618,116461,116077,116352,104073,118095,101566,112956,105682,112714,113770,111564,100394,111525,111670,111292,105749,108679,114859,113067,111062,113987,115865,115202,104499,112140,117408,103197,110443,109612,105043,114093,116290,110965,107708,110569,107976,117082,103691,99955,112238,104011,86797,103045,111550,111555,111554,111284,103636,108050,86798,104823,108202,110345,112809,108994,101725,108993,101985,107373,109641,104572,110245,110248,106410,113123,114478,108988,102734,112463,99581,101369,107408,113944,101226,113129,86799,86139,99823,86359,112383,113450,114710,116899,103631,116272,113623,103663,106114,100817,101214,107429,107303,103610,108441,103957,115876,108032,107669,108162,108178,103823,100378,100162,106484,113442,99843,112959,100326,112960,105961,112962,105958,102694,101557,102151,108134,101700,112481,102712);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (104896,101098,113778,113114,106405,100995,105120,104916,100185,104898,102978,109796,115110,116860,106412,100344,114522,107968,109934,103112,113452,110559,113506,107545,114087,114084,115294,114085,107332,112808,116824,103775,116822,86012,86778,86327,116651,99671,105840,100910,102163,101930,86779,99948,101771,101772,117081,111738,117296,109119,105239,112519,105504,105285,111465,109071,101212,104732,110487,110477,113918,106944,114310,108230,104002,104001,115404,113399,106046,108852,114810,109121,102494,102189,116904,113627,113275,101815,106017,112617,112618,116461,116077,116352,104073,118095,101566,112956,105682,112714,113770,111564,100394,111525,111670,111292,105749,108679,114859,113067,111062,113987,115865,115202,104499,112140,117408,103197,110443,109612,105043,114093,116290,110965,107708,110569,107976,117082,103691,99955,112238,104011,86797,103045,111550,111555,111554,111284,103636,108050,86798,104823,108202,110345,112809,108994,101725,108993,101985,107373,109641,104572,110245,110248,106410,113123,114478,108988,102734,112463,99581,101369,107408,113944,101226,113129,86799,86139,99823,86359,112383,113450,114710,116899,103631,116272,113623,103663,106114,100817,101214,107429,107303,103610,108441,103957,115876,108032,107669,108162,108178,103823,100378,100162,106484,113442,99843,112959,100326,112960,105961,112962,105958,102694,101557,102151,108134,101700,112481,102712);

delete quotes.* 
from quotes where instrumentid in (104896,101098,113778,113114,106405,100995,105120,104916,100185,104898,102978,109796,115110,116860,106412,100344,114522,107968,109934,103112,113452,110559,113506,107545,114087,114084,115294,114085,107332,112808,116824,103775,116822,86012,86778,86327,116651,99671,105840,100910,102163,101930,86779,99948,101771,101772,117081,111738,117296,109119,105239,112519,105504,105285,111465,109071,101212,104732,110487,110477,113918,106944,114310,108230,104002,104001,115404,113399,106046,108852,114810,109121,102494,102189,116904,113627,113275,101815,106017,112617,112618,116461,116077,116352,104073,118095,101566,112956,105682,112714,113770,111564,100394,111525,111670,111292,105749,108679,114859,113067,111062,113987,115865,115202,104499,112140,117408,103197,110443,109612,105043,114093,116290,110965,107708,110569,107976,117082,103691,99955,112238,104011,86797,103045,111550,111555,111554,111284,103636,108050,86798,104823,108202,110345,112809,108994,101725,108993,101985,107373,109641,104572,110245,110248,106410,113123,114478,108988,102734,112463,99581,101369,107408,113944,101226,113129,86799,86139,99823,86359,112383,113450,114710,116899,103631,116272,113623,103663,106114,100817,101214,107429,107303,103610,108441,103957,115876,108032,107669,108162,108178,103823,100378,100162,106484,113442,99843,112959,100326,112960,105961,112962,105958,102694,101557,102151,108134,101700,112481,102712);

commit;



begin;

select concat("FROM ",112484," TO ", 106786);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (112484,115984,115671,117707,115986,115669,100029,104957,105366,107307,112537,101791,99850,100241,111369,101336,106457,115416,117814,99946,112746,107977,104471,104337,109912,112161,112379,101717,100367,113079,116368,107205,114775,118190,99881,99794,108160,115271,115355,100823,115198,112283,109659,104982,114860,113878,116158,116859,99733,103435,99585,99459,99934,118132,115297,99808,108570,101671,108950,115115,101300,115125,114748,107359,107371,117424,108268,103716,103939,103725,117797,99461,105703,112645,102554,114357,112644,105702,111020,117528,100637,99411,86360,86824,113631,107919,108879,104971,86140,111478,111901,101789,109661,117639,115168,115172,114863,114865,103491,116311,113279,116310,113277,113586,102291,116366,117825,116090,116364,102036,99716,116880,109456,109466,101689,101559,115408,101690,107229,103558,117911,103485,102758,109260,102563,101805,113273,101242,110005,104317,103159,116035,116677,109979,109714,103639,109710,109348,114314,106962,109347,114313,116700,108066,109731,110004,116702,86845,114191,115853,86381,115851,106825,100082,115082,115190,107012,115196,115191,114274,105500,105282,110969,86846,86013,86847,86382,111593,99944,102024,118368,118950,118954,119414,119441,119440,118355,119386,118751,102114,102085,107095,103437,117170,115919,99922,107606,107605,115657,107243,115801,115793,107244,115659,117905,108525,114332,110037,115030,106786);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (112484,115984,115671,117707,115986,115669,100029,104957,105366,107307,112537,101791,99850,100241,111369,101336,106457,115416,117814,99946,112746,107977,104471,104337,109912,112161,112379,101717,100367,113079,116368,107205,114775,118190,99881,99794,108160,115271,115355,100823,115198,112283,109659,104982,114860,113878,116158,116859,99733,103435,99585,99459,99934,118132,115297,99808,108570,101671,108950,115115,101300,115125,114748,107359,107371,117424,108268,103716,103939,103725,117797,99461,105703,112645,102554,114357,112644,105702,111020,117528,100637,99411,86360,86824,113631,107919,108879,104971,86140,111478,111901,101789,109661,117639,115168,115172,114863,114865,103491,116311,113279,116310,113277,113586,102291,116366,117825,116090,116364,102036,99716,116880,109456,109466,101689,101559,115408,101690,107229,103558,117911,103485,102758,109260,102563,101805,113273,101242,110005,104317,103159,116035,116677,109979,109714,103639,109710,109348,114314,106962,109347,114313,116700,108066,109731,110004,116702,86845,114191,115853,86381,115851,106825,100082,115082,115190,107012,115196,115191,114274,105500,105282,110969,86846,86013,86847,86382,111593,99944,102024,118368,118950,118954,119414,119441,119440,118355,119386,118751,102114,102085,107095,103437,117170,115919,99922,107606,107605,115657,107243,115801,115793,107244,115659,117905,108525,114332,110037,115030,106786);

delete quotes.* 
from quotes where instrumentid in (112484,115984,115671,117707,115986,115669,100029,104957,105366,107307,112537,101791,99850,100241,111369,101336,106457,115416,117814,99946,112746,107977,104471,104337,109912,112161,112379,101717,100367,113079,116368,107205,114775,118190,99881,99794,108160,115271,115355,100823,115198,112283,109659,104982,114860,113878,116158,116859,99733,103435,99585,99459,99934,118132,115297,99808,108570,101671,108950,115115,101300,115125,114748,107359,107371,117424,108268,103716,103939,103725,117797,99461,105703,112645,102554,114357,112644,105702,111020,117528,100637,99411,86360,86824,113631,107919,108879,104971,86140,111478,111901,101789,109661,117639,115168,115172,114863,114865,103491,116311,113279,116310,113277,113586,102291,116366,117825,116090,116364,102036,99716,116880,109456,109466,101689,101559,115408,101690,107229,103558,117911,103485,102758,109260,102563,101805,113273,101242,110005,104317,103159,116035,116677,109979,109714,103639,109710,109348,114314,106962,109347,114313,116700,108066,109731,110004,116702,86845,114191,115853,86381,115851,106825,100082,115082,115190,107012,115196,115191,114274,105500,105282,110969,86846,86013,86847,86382,111593,99944,102024,118368,118950,118954,119414,119441,119440,118355,119386,118751,102114,102085,107095,103437,117170,115919,99922,107606,107605,115657,107243,115801,115793,107244,115659,117905,108525,114332,110037,115030,106786);

commit;



begin;

select concat("FROM ",108143," TO ", 115753);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (108143,114318,101115,103830,101132,109310,116165,109517,111394,111392,107322,111393,105319,101493,107446,107319,112397,114911,114921,114828,99519,86862,86383,86863,108255,115918,104239,110229,104647,105294,105286,113094,102970,118133,115301,107462,113209,110197,106190,100880,114273,101792,116387,108651,113423,113388,107861,108177,101796,103192,106370,116851,107377,114227,110908,108854,103356,114788,86384,110175,106468,104923,116772,100798,111022,102842,86385,110076,105998,117489,99764,86864,102816,99624,116750,111329,113464,114306,113595,116509,113836,103364,104696,115665,102273,108280,101077,107981,99924,106138,117966,104589,116864,117243,117580,117197,104223,116930,116931,86865,114869,110823,102693,115176,109652,109656,110959,109655,105496,112954,112151,100053,112953,112952,117179,100990,104365,116456,108918,111989,110728,105368,100720,105789,115007,113078,100354,110574,110575,105131,105029,105026,110637,103615,106432,102803,106168,110512,100843,104951,109805,108968,111992,105790,106442,106443,110452,110143,112387,112386,111744,108961,113718,100591,112377,114333,99801,116883,103432,116072,110934,100347,106210,107434,114331,108485,112372,102768,117993,117747,104452,105868,106272,105118,102865,113252,86402,113589,113601,115101,86403,107687,107685,115749,116296,106142,113074,110606,102810,86889,105314,106957,106194,106874,110352,86890,107086,104646,105601,115753);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (108143,114318,101115,103830,101132,109310,116165,109517,111394,111392,107322,111393,105319,101493,107446,107319,112397,114911,114921,114828,99519,86862,86383,86863,108255,115918,104239,110229,104647,105294,105286,113094,102970,118133,115301,107462,113209,110197,106190,100880,114273,101792,116387,108651,113423,113388,107861,108177,101796,103192,106370,116851,107377,114227,110908,108854,103356,114788,86384,110175,106468,104923,116772,100798,111022,102842,86385,110076,105998,117489,99764,86864,102816,99624,116750,111329,113464,114306,113595,116509,113836,103364,104696,115665,102273,108280,101077,107981,99924,106138,117966,104589,116864,117243,117580,117197,104223,116930,116931,86865,114869,110823,102693,115176,109652,109656,110959,109655,105496,112954,112151,100053,112953,112952,117179,100990,104365,116456,108918,111989,110728,105368,100720,105789,115007,113078,100354,110574,110575,105131,105029,105026,110637,103615,106432,102803,106168,110512,100843,104951,109805,108968,111992,105790,106442,106443,110452,110143,112387,112386,111744,108961,113718,100591,112377,114333,99801,116883,103432,116072,110934,100347,106210,107434,114331,108485,112372,102768,117993,117747,104452,105868,106272,105118,102865,113252,86402,113589,113601,115101,86403,107687,107685,115749,116296,106142,113074,110606,102810,86889,105314,106957,106194,106874,110352,86890,107086,104646,105601,115753);

delete quotes.* 
from quotes where instrumentid in (108143,114318,101115,103830,101132,109310,116165,109517,111394,111392,107322,111393,105319,101493,107446,107319,112397,114911,114921,114828,99519,86862,86383,86863,108255,115918,104239,110229,104647,105294,105286,113094,102970,118133,115301,107462,113209,110197,106190,100880,114273,101792,116387,108651,113423,113388,107861,108177,101796,103192,106370,116851,107377,114227,110908,108854,103356,114788,86384,110175,106468,104923,116772,100798,111022,102842,86385,110076,105998,117489,99764,86864,102816,99624,116750,111329,113464,114306,113595,116509,113836,103364,104696,115665,102273,108280,101077,107981,99924,106138,117966,104589,116864,117243,117580,117197,104223,116930,116931,86865,114869,110823,102693,115176,109652,109656,110959,109655,105496,112954,112151,100053,112953,112952,117179,100990,104365,116456,108918,111989,110728,105368,100720,105789,115007,113078,100354,110574,110575,105131,105029,105026,110637,103615,106432,102803,106168,110512,100843,104951,109805,108968,111992,105790,106442,106443,110452,110143,112387,112386,111744,108961,113718,100591,112377,114333,99801,116883,103432,116072,110934,100347,106210,107434,114331,108485,112372,102768,117993,117747,104452,105868,106272,105118,102865,113252,86402,113589,113601,115101,86403,107687,107685,115749,116296,106142,113074,110606,102810,86889,105314,106957,106194,106874,110352,86890,107086,104646,105601,115753);

commit;



begin;

select concat("FROM ",86404," TO ", 114821);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (86404,103275,86014,114612,103062,101166,102943,107179,100387,112596,105999,99608,86405,86905,86163,114086,114409,113474,113473,106437,110265,100744,110262,86906,102474,116106,109827,115175,116107,114868,100933,109243,103059,113769,114619,112740,99807,109688,105735,116124,117524,117544,105980,110269,105738,111745,106172,86907,101099,117732,111746,86908,117661,107829,117468,109843,102008,110176,112896,116016,111920,105334,111923,116635,112276,108733,117364,114749,113553,86909,100282,108788,118087,115269,111921,102017,101909,111096,109402,86910,99515,116229,110776,102217,86406,103113,99784,103996,86407,86911,86005,111850,111719,102141,86164,113237,113243,111460,113528,113244,113592,113591,115303,114504,118136,102354,111782,109309,114220,117136,106849,113868,113962,116380,116104,107871,108220,116710,86187,86932,101386,103441,117217,117940,107865,116298,107187,105295,115603,113963,112854,109959,105765,112337,104395,112749,105719,111710,101162,114968,107180,114604,107430,114889,99651,108736,105938,100096,115605,115419,113065,103370,106632,103273,86188,108456,114995,108735,113640,117003,112877,104229,106633,100811,108490,113989,117382,113223,110024,113301,105482,102274,104062,110898,108878,118144,108094,86933,86189,86934,86419,111069,105984,100618,117690,115741,115742,115743,107682,106921,101240,103391,113984,106609,103298,103713,116628,115021,107000,114821);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (86404,103275,86014,114612,103062,101166,102943,107179,100387,112596,105999,99608,86405,86905,86163,114086,114409,113474,113473,106437,110265,100744,110262,86906,102474,116106,109827,115175,116107,114868,100933,109243,103059,113769,114619,112740,99807,109688,105735,116124,117524,117544,105980,110269,105738,111745,106172,86907,101099,117732,111746,86908,117661,107829,117468,109843,102008,110176,112896,116016,111920,105334,111923,116635,112276,108733,117364,114749,113553,86909,100282,108788,118087,115269,111921,102017,101909,111096,109402,86910,99515,116229,110776,102217,86406,103113,99784,103996,86407,86911,86005,111850,111719,102141,86164,113237,113243,111460,113528,113244,113592,113591,115303,114504,118136,102354,111782,109309,114220,117136,106849,113868,113962,116380,116104,107871,108220,116710,86187,86932,101386,103441,117217,117940,107865,116298,107187,105295,115603,113963,112854,109959,105765,112337,104395,112749,105719,111710,101162,114968,107180,114604,107430,114889,99651,108736,105938,100096,115605,115419,113065,103370,106632,103273,86188,108456,114995,108735,113640,117003,112877,104229,106633,100811,108490,113989,117382,113223,110024,113301,105482,102274,104062,110898,108878,118144,108094,86933,86189,86934,86419,111069,105984,100618,117690,115741,115742,115743,107682,106921,101240,103391,113984,106609,103298,103713,116628,115021,107000,114821);

delete quotes.* 
from quotes where instrumentid in (86404,103275,86014,114612,103062,101166,102943,107179,100387,112596,105999,99608,86405,86905,86163,114086,114409,113474,113473,106437,110265,100744,110262,86906,102474,116106,109827,115175,116107,114868,100933,109243,103059,113769,114619,112740,99807,109688,105735,116124,117524,117544,105980,110269,105738,111745,106172,86907,101099,117732,111746,86908,117661,107829,117468,109843,102008,110176,112896,116016,111920,105334,111923,116635,112276,108733,117364,114749,113553,86909,100282,108788,118087,115269,111921,102017,101909,111096,109402,86910,99515,116229,110776,102217,86406,103113,99784,103996,86407,86911,86005,111850,111719,102141,86164,113237,113243,111460,113528,113244,113592,113591,115303,114504,118136,102354,111782,109309,114220,117136,106849,113868,113962,116380,116104,107871,108220,116710,86187,86932,101386,103441,117217,117940,107865,116298,107187,105295,115603,113963,112854,109959,105765,112337,104395,112749,105719,111710,101162,114968,107180,114604,107430,114889,99651,108736,105938,100096,115605,115419,113065,103370,106632,103273,86188,108456,114995,108735,113640,117003,112877,104229,106633,100811,108490,113989,117382,113223,110024,113301,105482,102274,104062,110898,108878,118144,108094,86933,86189,86934,86419,111069,105984,100618,117690,115741,115742,115743,107682,106921,101240,103391,113984,106609,103298,103713,116628,115021,107000,114821);

commit;



begin;

select concat("FROM ",103410," TO ", 110962);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (103410,113186,115050,115798,107223,109483,105014,110609,117984,112958,105960,117859,86420,86958,100188,114184,100657,103847,108571,109069,99832,103598,101832,109391,107560,113428,114218,107590,114196,109130,107030,106672,114029,115728,102187,107772,86421,86959,112043,106854,111743,113875,86190,116586,115281,109809,109897,103607,113961,102795,101846,111012,105256,86960,86037,86961,109212,117028,108186,108386,100834,104028,86962,102920,114623,99782,112812,106212,112743,111613,108943,110271,104863,99646,105206,110722,110724,113014,86191,112800,103758,117215,104156,113994,112571,112568,108949,109800,106920,86963,86027,86980,102419,101648,103384,99571,104695,104694,102106,104360,99421,108206,104096,105175,110906,116109,100934,101721,100636,101004,106393,113821,100178,116519,86038,102342,105815,86981,102382,111748,105910,111747,101753,102617,99514,116082,104977,116358,104636,110208,116607,109920,116593,103328,107760,112332,101581,113521,101131,106220,109091,106093,112756,108844,103349,86982,115515,111128,113822,102946,86192,100767,107349,103755,112538,106195,103416,106196,107723,115905,102606,115544,112148,111792,105563,102357,105585,108919,117191,116924,117190,101797,116771,115525,102384,116508,105266,106860,108057,112005,106567,102757,112004,107352,105773,106506,113313,100893,114397,117805,106789,115035,105848,116618,115143,101574,116506,117644,113965,110962);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (103410,113186,115050,115798,107223,109483,105014,110609,117984,112958,105960,117859,86420,86958,100188,114184,100657,103847,108571,109069,99832,103598,101832,109391,107560,113428,114218,107590,114196,109130,107030,106672,114029,115728,102187,107772,86421,86959,112043,106854,111743,113875,86190,116586,115281,109809,109897,103607,113961,102795,101846,111012,105256,86960,86037,86961,109212,117028,108186,108386,100834,104028,86962,102920,114623,99782,112812,106212,112743,111613,108943,110271,104863,99646,105206,110722,110724,113014,86191,112800,103758,117215,104156,113994,112571,112568,108949,109800,106920,86963,86027,86980,102419,101648,103384,99571,104695,104694,102106,104360,99421,108206,104096,105175,110906,116109,100934,101721,100636,101004,106393,113821,100178,116519,86038,102342,105815,86981,102382,111748,105910,111747,101753,102617,99514,116082,104977,116358,104636,110208,116607,109920,116593,103328,107760,112332,101581,113521,101131,106220,109091,106093,112756,108844,103349,86982,115515,111128,113822,102946,86192,100767,107349,103755,112538,106195,103416,106196,107723,115905,102606,115544,112148,111792,105563,102357,105585,108919,117191,116924,117190,101797,116771,115525,102384,116508,105266,106860,108057,112005,106567,102757,112004,107352,105773,106506,113313,100893,114397,117805,106789,115035,105848,116618,115143,101574,116506,117644,113965,110962);

delete quotes.* 
from quotes where instrumentid in (103410,113186,115050,115798,107223,109483,105014,110609,117984,112958,105960,117859,86420,86958,100188,114184,100657,103847,108571,109069,99832,103598,101832,109391,107560,113428,114218,107590,114196,109130,107030,106672,114029,115728,102187,107772,86421,86959,112043,106854,111743,113875,86190,116586,115281,109809,109897,103607,113961,102795,101846,111012,105256,86960,86037,86961,109212,117028,108186,108386,100834,104028,86962,102920,114623,99782,112812,106212,112743,111613,108943,110271,104863,99646,105206,110722,110724,113014,86191,112800,103758,117215,104156,113994,112571,112568,108949,109800,106920,86963,86027,86980,102419,101648,103384,99571,104695,104694,102106,104360,99421,108206,104096,105175,110906,116109,100934,101721,100636,101004,106393,113821,100178,116519,86038,102342,105815,86981,102382,111748,105910,111747,101753,102617,99514,116082,104977,116358,104636,110208,116607,109920,116593,103328,107760,112332,101581,113521,101131,106220,109091,106093,112756,108844,103349,86982,115515,111128,113822,102946,86192,100767,107349,103755,112538,106195,103416,106196,107723,115905,102606,115544,112148,111792,105563,102357,105585,108919,117191,116924,117190,101797,116771,115525,102384,116508,105266,106860,108057,112005,106567,102757,112004,107352,105773,106506,113313,100893,114397,117805,106789,115035,105848,116618,115143,101574,116506,117644,113965,110962);

commit;



begin;

select concat("FROM ",110960," TO ", 108476);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (110960,100908,114272,103477,106367,101223,106098,105455,102755,112928,115955,107949,110289,104627,104635,104630,104822,100878,110307,110306,100081,110288,107775,102385,116459,107735,111024,104364,103448,108683,111440,101457,113260,113258,113255,113603,115276,108894,105631,111576,106556,105362,111158,103420,108475,100177,108724,107192,99464,117761,108266,100099,100429,108251,116318,100917,108726,102211,103918,103700,113105,113518,109867,86444,109846,117831,105363,102799,116784,104377,105948,104181,86997,86039,107953,117809,108877,107827,111501,108881,104835,109835,104955,86445,86998,108911,104744,112528,106115,112527,112913,106305,87022,105929,115207,112144,112146,108641,104030,100741,112328,109167,109159,109160,110341,104745,87023,106427,87024,106045,102871,99738,115940,116968,116967,106422,109471,109829,109470,102441,111605,104409,109174,107233,118135,103850,114786,103403,113524,114806,115664,111991,115979,87025,86466,87026,86040,100152,115348,108585,103874,109283,115005,99854,104502,99513,111200,102253,107016,111370,108576,99601,103749,103407,99719,114476,109301,113066,113070,104386,116608,118054,108971,112226,112230,100302,100876,104339,103227,113021,112399,116460,116466,112154,114870,115273,106086,106745,114553,118186,112373,109303,101827,107273,110118,110524,113485,106441,113481,102807,112178,111039,100057,100538,87039,101640,114646,111961,115053,108476);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (110960,100908,114272,103477,106367,101223,106098,105455,102755,112928,115955,107949,110289,104627,104635,104630,104822,100878,110307,110306,100081,110288,107775,102385,116459,107735,111024,104364,103448,108683,111440,101457,113260,113258,113255,113603,115276,108894,105631,111576,106556,105362,111158,103420,108475,100177,108724,107192,99464,117761,108266,100099,100429,108251,116318,100917,108726,102211,103918,103700,113105,113518,109867,86444,109846,117831,105363,102799,116784,104377,105948,104181,86997,86039,107953,117809,108877,107827,111501,108881,104835,109835,104955,86445,86998,108911,104744,112528,106115,112527,112913,106305,87022,105929,115207,112144,112146,108641,104030,100741,112328,109167,109159,109160,110341,104745,87023,106427,87024,106045,102871,99738,115940,116968,116967,106422,109471,109829,109470,102441,111605,104409,109174,107233,118135,103850,114786,103403,113524,114806,115664,111991,115979,87025,86466,87026,86040,100152,115348,108585,103874,109283,115005,99854,104502,99513,111200,102253,107016,111370,108576,99601,103749,103407,99719,114476,109301,113066,113070,104386,116608,118054,108971,112226,112230,100302,100876,104339,103227,113021,112399,116460,116466,112154,114870,115273,106086,106745,114553,118186,112373,109303,101827,107273,110118,110524,113485,106441,113481,102807,112178,111039,100057,100538,87039,101640,114646,111961,115053,108476);

delete quotes.* 
from quotes where instrumentid in (110960,100908,114272,103477,106367,101223,106098,105455,102755,112928,115955,107949,110289,104627,104635,104630,104822,100878,110307,110306,100081,110288,107775,102385,116459,107735,111024,104364,103448,108683,111440,101457,113260,113258,113255,113603,115276,108894,105631,111576,106556,105362,111158,103420,108475,100177,108724,107192,99464,117761,108266,100099,100429,108251,116318,100917,108726,102211,103918,103700,113105,113518,109867,86444,109846,117831,105363,102799,116784,104377,105948,104181,86997,86039,107953,117809,108877,107827,111501,108881,104835,109835,104955,86445,86998,108911,104744,112528,106115,112527,112913,106305,87022,105929,115207,112144,112146,108641,104030,100741,112328,109167,109159,109160,110341,104745,87023,106427,87024,106045,102871,99738,115940,116968,116967,106422,109471,109829,109470,102441,111605,104409,109174,107233,118135,103850,114786,103403,113524,114806,115664,111991,115979,87025,86466,87026,86040,100152,115348,108585,103874,109283,115005,99854,104502,99513,111200,102253,107016,111370,108576,99601,103749,103407,99719,114476,109301,113066,113070,104386,116608,118054,108971,112226,112230,100302,100876,104339,103227,113021,112399,116460,116466,112154,114870,115273,106086,106745,114553,118186,112373,109303,101827,107273,110118,110524,113485,106441,113481,102807,112178,111039,100057,100538,87039,101640,114646,111961,115053,108476);

commit;



begin;

select concat("FROM ",101127," TO ", 111005);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (101127,114092,104510,110077,103459,110414,118014,105727,113504,112792,115878,109321,113587,86041,114888,102238,101979,104639,109015,105760,113880,107031,113679,117421,104633,104489,105928,113460,106893,102266,115109,102179,112355,114939,106163,107346,106892,106671,106537,100896,102650,102559,112657,107945,108406,114944,110029,115666,105799,109869,111038,105271,109852,104849,108432,117074,103837,115681,112848,113909,103078,117486,109589,100537,103963,101975,104220,109233,105931,86028,113486,86467,87040,86214,87041,100759,104460,111449,87042,86468,109139,108291,87043,107836,108392,103590,106977,100317,111609,115954,87051,108552,113570,117380,117022,99545,101384,103919,111161,114547,105687,112637,112310,112311,102644,102475,99928,100973,102408,101751,109713,86497,115375,109021,104964,86498,87052,86029,107706,107883,116136,108072,87053,101905,114342,104270,109687,109685,114983,114104,114426,114427,114105,114420,114096,100683,100731,109447,104461,102049,115971,115972,107758,115658,105796,113560,110214,102145,100371,103381,104244,110774,109014,115343,87054,86499,106696,113386,115400,102147,116858,114714,112916,116057,108047,107623,115833,116934,116937,102707,107741,102625,104045,112966,105963,102696,117741,114923,119410,105964,112847,106135,103562,110419,87055,86215,112930,100654,107062,109987,86500,104756,101485,115057,103219,101446,118524,86216,106312,111005);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (101127,114092,104510,110077,103459,110414,118014,105727,113504,112792,115878,109321,113587,86041,114888,102238,101979,104639,109015,105760,113880,107031,113679,117421,104633,104489,105928,113460,106893,102266,115109,102179,112355,114939,106163,107346,106892,106671,106537,100896,102650,102559,112657,107945,108406,114944,110029,115666,105799,109869,111038,105271,109852,104849,108432,117074,103837,115681,112848,113909,103078,117486,109589,100537,103963,101975,104220,109233,105931,86028,113486,86467,87040,86214,87041,100759,104460,111449,87042,86468,109139,108291,87043,107836,108392,103590,106977,100317,111609,115954,87051,108552,113570,117380,117022,99545,101384,103919,111161,114547,105687,112637,112310,112311,102644,102475,99928,100973,102408,101751,109713,86497,115375,109021,104964,86498,87052,86029,107706,107883,116136,108072,87053,101905,114342,104270,109687,109685,114983,114104,114426,114427,114105,114420,114096,100683,100731,109447,104461,102049,115971,115972,107758,115658,105796,113560,110214,102145,100371,103381,104244,110774,109014,115343,87054,86499,106696,113386,115400,102147,116858,114714,112916,116057,108047,107623,115833,116934,116937,102707,107741,102625,104045,112966,105963,102696,117741,114923,119410,105964,112847,106135,103562,110419,87055,86215,112930,100654,107062,109987,86500,104756,101485,115057,103219,101446,118524,86216,106312,111005);

delete quotes.* 
from quotes where instrumentid in (101127,114092,104510,110077,103459,110414,118014,105727,113504,112792,115878,109321,113587,86041,114888,102238,101979,104639,109015,105760,113880,107031,113679,117421,104633,104489,105928,113460,106893,102266,115109,102179,112355,114939,106163,107346,106892,106671,106537,100896,102650,102559,112657,107945,108406,114944,110029,115666,105799,109869,111038,105271,109852,104849,108432,117074,103837,115681,112848,113909,103078,117486,109589,100537,103963,101975,104220,109233,105931,86028,113486,86467,87040,86214,87041,100759,104460,111449,87042,86468,109139,108291,87043,107836,108392,103590,106977,100317,111609,115954,87051,108552,113570,117380,117022,99545,101384,103919,111161,114547,105687,112637,112310,112311,102644,102475,99928,100973,102408,101751,109713,86497,115375,109021,104964,86498,87052,86029,107706,107883,116136,108072,87053,101905,114342,104270,109687,109685,114983,114104,114426,114427,114105,114420,114096,100683,100731,109447,104461,102049,115971,115972,107758,115658,105796,113560,110214,102145,100371,103381,104244,110774,109014,115343,87054,86499,106696,113386,115400,102147,116858,114714,112916,116057,108047,107623,115833,116934,116937,102707,107741,102625,104045,112966,105963,102696,117741,114923,119410,105964,112847,106135,103562,110419,87055,86215,112930,100654,107062,109987,86500,104756,101485,115057,103219,101446,118524,86216,106312,111005);

commit;



begin;

select concat("FROM ",118295," TO ", 104378);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (118295,110956,117357,117522,102304,107717,113405,86030,114720,118049,100782,102364,103288,105575,113522,113523,102980,113520,113519,114330,109023,109022,109734,110000,109999,117718,107150,100970,111869,103934,110979,103727,114620,99480,115078,86523,100252,112141,105758,106889,114303,106888,101245,104119,104120,106661,108942,105757,115219,114302,103226,106887,113745,105188,108959,114503,107061,104174,116279,116282,103629,116278,113143,113142,113139,100508,100056,101398,113448,113768,117749,112579,117750,102689,102111,118137,107333,115295,108040,101631,108038,108039,108102,106415,106413,113131,100278,109750,103914,104769,112581,112967,117740,117985,117990,114506,111170,118121,118117,103848,103679,104449,100864,113110,117989,114207,115034,109372,104757,106787,103111,115031,117005,117561,109749,109751,117146,104501,108560,113126,108323,102792,113122,108320,106929,116942,105739,111168,102432,105228,111172,102434,100543,116933,114518,106930,114519,115277,115280,107067,114982,101149,114981,102463,107226,104250,108275,112235,113106,113104,106217,100457,106993,114396,103408,100474,114805,106994,101118,102824,105994,107010,100254,115221,115220,101501,116819,107026,104396,103797,109146,104169,100588,108948,104146,86545,114654,117806,103870,106771,101598,117807,100644,116713,118097,99667,116049,118098,86060,113085,117340,100280,109950,110141,99550,108395,105501,102835,104988,104378);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (118295,110956,117357,117522,102304,107717,113405,86030,114720,118049,100782,102364,103288,105575,113522,113523,102980,113520,113519,114330,109023,109022,109734,110000,109999,117718,107150,100970,111869,103934,110979,103727,114620,99480,115078,86523,100252,112141,105758,106889,114303,106888,101245,104119,104120,106661,108942,105757,115219,114302,103226,106887,113745,105188,108959,114503,107061,104174,116279,116282,103629,116278,113143,113142,113139,100508,100056,101398,113448,113768,117749,112579,117750,102689,102111,118137,107333,115295,108040,101631,108038,108039,108102,106415,106413,113131,100278,109750,103914,104769,112581,112967,117740,117985,117990,114506,111170,118121,118117,103848,103679,104449,100864,113110,117989,114207,115034,109372,104757,106787,103111,115031,117005,117561,109749,109751,117146,104501,108560,113126,108323,102792,113122,108320,106929,116942,105739,111168,102432,105228,111172,102434,100543,116933,114518,106930,114519,115277,115280,107067,114982,101149,114981,102463,107226,104250,108275,112235,113106,113104,106217,100457,106993,114396,103408,100474,114805,106994,101118,102824,105994,107010,100254,115221,115220,101501,116819,107026,104396,103797,109146,104169,100588,108948,104146,86545,114654,117806,103870,106771,101598,117807,100644,116713,118097,99667,116049,118098,86060,113085,117340,100280,109950,110141,99550,108395,105501,102835,104988,104378);

delete quotes.* 
from quotes where instrumentid in (118295,110956,117357,117522,102304,107717,113405,86030,114720,118049,100782,102364,103288,105575,113522,113523,102980,113520,113519,114330,109023,109022,109734,110000,109999,117718,107150,100970,111869,103934,110979,103727,114620,99480,115078,86523,100252,112141,105758,106889,114303,106888,101245,104119,104120,106661,108942,105757,115219,114302,103226,106887,113745,105188,108959,114503,107061,104174,116279,116282,103629,116278,113143,113142,113139,100508,100056,101398,113448,113768,117749,112579,117750,102689,102111,118137,107333,115295,108040,101631,108038,108039,108102,106415,106413,113131,100278,109750,103914,104769,112581,112967,117740,117985,117990,114506,111170,118121,118117,103848,103679,104449,100864,113110,117989,114207,115034,109372,104757,106787,103111,115031,117005,117561,109749,109751,117146,104501,108560,113126,108323,102792,113122,108320,106929,116942,105739,111168,102432,105228,111172,102434,100543,116933,114518,106930,114519,115277,115280,107067,114982,101149,114981,102463,107226,104250,108275,112235,113106,113104,106217,100457,106993,114396,103408,100474,114805,106994,101118,102824,105994,107010,100254,115221,115220,101501,116819,107026,104396,103797,109146,104169,100588,108948,104146,86545,114654,117806,103870,106771,101598,117807,100644,116713,118097,99667,116049,118098,86060,113085,117340,100280,109950,110141,99550,108395,105501,102835,104988,104378);

commit;



begin;

select concat("FROM ",105911," TO ", 119405);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (105911,103746,101035,107224,110448,129636,107929,103518,115222,116823,86546,86243,102294,111832,116611,114916,104100,99623,105816,113490,107080,112239,101467,104998,115142,100545,107196,101168,105688,106674,116752,111919,102218,112585,100324,112583,112582,112917,106131,101908,86562,112919,118088,113058,113059,106471,86563,106140,86564,113936,111307,112409,112216,86259,101695,101144,106417,116952,116950,86061,116961,115788,104123,117227,117225,101523,106586,116499,86565,115215,108114,101148,117490,101774,117045,106130,104115,113767,107595,108189,106202,114408,107343,104133,115398,109275,105108,116818,107563,107931,115223,86566,86567,117739,115692,99539,113853,102808,117563,104293,100825,104903,117309,109553,105365,106680,100426,101588,109234,105373,129637,115275,129602,103885,108561,103876,117008,103812,102852,110635,104910,110581,105133,110579,104909,110580,105132,105121,104902,104897,110558,110560,105122,110557,118033,109558,104152,100272,110566,101521,118032,116422,107909,110200,112689,102959,112701,100607,114733,106999,107351,117953,114819,108273,106221,86062,115976,103958,115662,117265,99852,105056,110205,107015,114391,101463,103644,101638,116761,119362,117336,119381,116497,116498,118023,118864,99537,118232,107925,116813,112813,103555,108698,118041,109982,109717,100952,100646,86587,119390,115197,100478,115661,100299,103187,113549,100590,104182,115539,119405);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (105911,103746,101035,107224,110448,129636,107929,103518,115222,116823,86546,86243,102294,111832,116611,114916,104100,99623,105816,113490,107080,112239,101467,104998,115142,100545,107196,101168,105688,106674,116752,111919,102218,112585,100324,112583,112582,112917,106131,101908,86562,112919,118088,113058,113059,106471,86563,106140,86564,113936,111307,112409,112216,86259,101695,101144,106417,116952,116950,86061,116961,115788,104123,117227,117225,101523,106586,116499,86565,115215,108114,101148,117490,101774,117045,106130,104115,113767,107595,108189,106202,114408,107343,104133,115398,109275,105108,116818,107563,107931,115223,86566,86567,117739,115692,99539,113853,102808,117563,104293,100825,104903,117309,109553,105365,106680,100426,101588,109234,105373,129637,115275,129602,103885,108561,103876,117008,103812,102852,110635,104910,110581,105133,110579,104909,110580,105132,105121,104902,104897,110558,110560,105122,110557,118033,109558,104152,100272,110566,101521,118032,116422,107909,110200,112689,102959,112701,100607,114733,106999,107351,117953,114819,108273,106221,86062,115976,103958,115662,117265,99852,105056,110205,107015,114391,101463,103644,101638,116761,119362,117336,119381,116497,116498,118023,118864,99537,118232,107925,116813,112813,103555,108698,118041,109982,109717,100952,100646,86587,119390,115197,100478,115661,100299,103187,113549,100590,104182,115539,119405);

delete quotes.* 
from quotes where instrumentid in (105911,103746,101035,107224,110448,129636,107929,103518,115222,116823,86546,86243,102294,111832,116611,114916,104100,99623,105816,113490,107080,112239,101467,104998,115142,100545,107196,101168,105688,106674,116752,111919,102218,112585,100324,112583,112582,112917,106131,101908,86562,112919,118088,113058,113059,106471,86563,106140,86564,113936,111307,112409,112216,86259,101695,101144,106417,116952,116950,86061,116961,115788,104123,117227,117225,101523,106586,116499,86565,115215,108114,101148,117490,101774,117045,106130,104115,113767,107595,108189,106202,114408,107343,104133,115398,109275,105108,116818,107563,107931,115223,86566,86567,117739,115692,99539,113853,102808,117563,104293,100825,104903,117309,109553,105365,106680,100426,101588,109234,105373,129637,115275,129602,103885,108561,103876,117008,103812,102852,110635,104910,110581,105133,110579,104909,110580,105132,105121,104902,104897,110558,110560,105122,110557,118033,109558,104152,100272,110566,101521,118032,116422,107909,110200,112689,102959,112701,100607,114733,106999,107351,117953,114819,108273,106221,86062,115976,103958,115662,117265,99852,105056,110205,107015,114391,101463,103644,101638,116761,119362,117336,119381,116497,116498,118023,118864,99537,118232,107925,116813,112813,103555,108698,118041,109982,109717,100952,100646,86587,119390,115197,100478,115661,100299,103187,113549,100590,104182,115539,119405);

commit;



begin;

select concat("FROM ",111295," TO ", 99576);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (111295,115288,105854,105865,99836,118248,105347,118099,113581,108539,108397,104041,101452,99562,115521,117845,117571,99896,110832,110518,118611,119058,108113,116972,115217,115201,86605,86282,102153,118445,110203,118376,108897,118880,106969,118245,105912,112113,111041,111040,103343,112013,114088,106446,86606,86078,86607,86283,117559,117846,117738,117737,112932,112933,118633,105851,86608,86089,113698,106524,115985,118201,117479,110359,101233,115353,116659,102855,117811,117535,112445,106137,106313,99401,116331,108430,105930,112931,115975,102723,86090,111447,111448,100002,105587,109134,117992,86609,107326,113332,113330,110617,107927,110336,110527,113185,101052,112006,113190,106553,111820,105297,109315,109314,111353,103695,108761,105442,100091,116502,116765,116763,118421,118330,119043,119138,116646,116363,116088,102286,104536,100585,99909,99875,117130,113744,111620,107879,105668,104555,107363,115129,101585,111964,117020,103803,112543,102142,86630,114610,86631,86284,117773,117770,102150,101612,99649,115879,104168,117291,102170,116923,100147,103913,86632,86079,86633,118224,109989,116707,109149,109891,103978,129639,99452,112794,110821,112064,111709,118832,104561,118013,113118,115278,110440,104308,118959,102032,109445,118907,101914,117955,110083,100487,102205,103020,117311,116446,101150,116472,109639,102296,110792,115996,100935,107913,114281,114376,111042,99576);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (111295,115288,105854,105865,99836,118248,105347,118099,113581,108539,108397,104041,101452,99562,115521,117845,117571,99896,110832,110518,118611,119058,108113,116972,115217,115201,86605,86282,102153,118445,110203,118376,108897,118880,106969,118245,105912,112113,111041,111040,103343,112013,114088,106446,86606,86078,86607,86283,117559,117846,117738,117737,112932,112933,118633,105851,86608,86089,113698,106524,115985,118201,117479,110359,101233,115353,116659,102855,117811,117535,112445,106137,106313,99401,116331,108430,105930,112931,115975,102723,86090,111447,111448,100002,105587,109134,117992,86609,107326,113332,113330,110617,107927,110336,110527,113185,101052,112006,113190,106553,111820,105297,109315,109314,111353,103695,108761,105442,100091,116502,116765,116763,118421,118330,119043,119138,116646,116363,116088,102286,104536,100585,99909,99875,117130,113744,111620,107879,105668,104555,107363,115129,101585,111964,117020,103803,112543,102142,86630,114610,86631,86284,117773,117770,102150,101612,99649,115879,104168,117291,102170,116923,100147,103913,86632,86079,86633,118224,109989,116707,109149,109891,103978,129639,99452,112794,110821,112064,111709,118832,104561,118013,113118,115278,110440,104308,118959,102032,109445,118907,101914,117955,110083,100487,102205,103020,117311,116446,101150,116472,109639,102296,110792,115996,100935,107913,114281,114376,111042,99576);

delete quotes.* 
from quotes where instrumentid in (111295,115288,105854,105865,99836,118248,105347,118099,113581,108539,108397,104041,101452,99562,115521,117845,117571,99896,110832,110518,118611,119058,108113,116972,115217,115201,86605,86282,102153,118445,110203,118376,108897,118880,106969,118245,105912,112113,111041,111040,103343,112013,114088,106446,86606,86078,86607,86283,117559,117846,117738,117737,112932,112933,118633,105851,86608,86089,113698,106524,115985,118201,117479,110359,101233,115353,116659,102855,117811,117535,112445,106137,106313,99401,116331,108430,105930,112931,115975,102723,86090,111447,111448,100002,105587,109134,117992,86609,107326,113332,113330,110617,107927,110336,110527,113185,101052,112006,113190,106553,111820,105297,109315,109314,111353,103695,108761,105442,100091,116502,116765,116763,118421,118330,119043,119138,116646,116363,116088,102286,104536,100585,99909,99875,117130,113744,111620,107879,105668,104555,107363,115129,101585,111964,117020,103803,112543,102142,86630,114610,86631,86284,117773,117770,102150,101612,99649,115879,104168,117291,102170,116923,100147,103913,86632,86079,86633,118224,109989,116707,109149,109891,103978,129639,99452,112794,110821,112064,111709,118832,104561,118013,113118,115278,110440,104308,118959,102032,109445,118907,101914,117955,110083,100487,102205,103020,117311,116446,101150,116472,109639,102296,110792,115996,100935,107913,114281,114376,111042,99576);

commit;



begin;

select concat("FROM ",99362," TO ", 110907);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (99362,99445,99361,86634,102452,113220,118570,104558,112874,105665,111201,101859,106508,115095,106637,86635,101356,102393,116423,116429,100472,105662,100854,112340,101183,102875,86298,113602,108566,114872,128999,113479,101232,106438,113478,113475,113476,117771,111726,115319,108116,114066,110221,106764,86665,109155,100437,101490,109156,116026,104400,118791,112041,110467,108056,115337,100856,108555,117546,117551,86666,102975,86667,104086,108657,112312,116802,110064,101660,86080,103121,108164,104226,99891,99490,100173,116727,117192,99839,118128,101496,116384,117080,86299,86668,86081,86669,86300,114609,115729,118124,116927,112873,114410,100511,99957,86670,86091,99524,116721,101770,103551,86671,86301,116955,86092,103080,114373,101749,117073,104338,109239,119412,119395,106677,104205,86672,116963,86673,86302,102884,86093,102176,101103,111341,101185,106692,100102,86685,102225,111500,111889,102207,109220,105661,117188,108564,107109,115305,115307,99973,115084,106769,107749,111424,86094,102515,86303,116407,106814,114824,117441,108070,102280,100860,115663,100862,112016,117313,100541,106219,105810,99430,112256,111933,105653,102446,112327,102925,115523,103452,110469,114280,119213,118507,114026,100207,100863,116221,101017,86686,86082,115429,114644,110249,117666,112850,100172,100182,111088,105110,103102,106497,113286,106496,112114,112124,112125,114703,110907);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (99362,99445,99361,86634,102452,113220,118570,104558,112874,105665,111201,101859,106508,115095,106637,86635,101356,102393,116423,116429,100472,105662,100854,112340,101183,102875,86298,113602,108566,114872,128999,113479,101232,106438,113478,113475,113476,117771,111726,115319,108116,114066,110221,106764,86665,109155,100437,101490,109156,116026,104400,118791,112041,110467,108056,115337,100856,108555,117546,117551,86666,102975,86667,104086,108657,112312,116802,110064,101660,86080,103121,108164,104226,99891,99490,100173,116727,117192,99839,118128,101496,116384,117080,86299,86668,86081,86669,86300,114609,115729,118124,116927,112873,114410,100511,99957,86670,86091,99524,116721,101770,103551,86671,86301,116955,86092,103080,114373,101749,117073,104338,109239,119412,119395,106677,104205,86672,116963,86673,86302,102884,86093,102176,101103,111341,101185,106692,100102,86685,102225,111500,111889,102207,109220,105661,117188,108564,107109,115305,115307,99973,115084,106769,107749,111424,86094,102515,86303,116407,106814,114824,117441,108070,102280,100860,115663,100862,112016,117313,100541,106219,105810,99430,112256,111933,105653,102446,112327,102925,115523,103452,110469,114280,119213,118507,114026,100207,100863,116221,101017,86686,86082,115429,114644,110249,117666,112850,100172,100182,111088,105110,103102,106497,113286,106496,112114,112124,112125,114703,110907);

delete quotes.* 
from quotes where instrumentid in (99362,99445,99361,86634,102452,113220,118570,104558,112874,105665,111201,101859,106508,115095,106637,86635,101356,102393,116423,116429,100472,105662,100854,112340,101183,102875,86298,113602,108566,114872,128999,113479,101232,106438,113478,113475,113476,117771,111726,115319,108116,114066,110221,106764,86665,109155,100437,101490,109156,116026,104400,118791,112041,110467,108056,115337,100856,108555,117546,117551,86666,102975,86667,104086,108657,112312,116802,110064,101660,86080,103121,108164,104226,99891,99490,100173,116727,117192,99839,118128,101496,116384,117080,86299,86668,86081,86669,86300,114609,115729,118124,116927,112873,114410,100511,99957,86670,86091,99524,116721,101770,103551,86671,86301,116955,86092,103080,114373,101749,117073,104338,109239,119412,119395,106677,104205,86672,116963,86673,86302,102884,86093,102176,101103,111341,101185,106692,100102,86685,102225,111500,111889,102207,109220,105661,117188,108564,107109,115305,115307,99973,115084,106769,107749,111424,86094,102515,86303,116407,106814,114824,117441,108070,102280,100860,115663,100862,112016,117313,100541,106219,105810,99430,112256,111933,105653,102446,112327,102925,115523,103452,110469,114280,119213,118507,114026,100207,100863,116221,101017,86686,86082,115429,114644,110249,117666,112850,100172,100182,111088,105110,103102,106497,113286,106496,112114,112124,112125,114703,110907);

commit;



begin;

select concat("FROM ",110910," TO ", 106958);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (110910,100642,129450,107197,114984,115977,113180,117906,113178,117241,109637,86687,86304,106499,104329,103486,115107,100661,105063,115923,115803,107984,110026,86688,108947,117262,107910,101563,108779,106213,117942,100443,107822,103676,107821,102026,117548,113569,117537,106354,110935,111502,116711,118101,106770,115342,101424,117540,108687,118024,110526,110843,107745,116353,106423,105610,119050,111354,107358,117874,110668,104598,114893,105945,104741,112572,105833,112690,117961,108928,106133,117155,115529,104638,118057,110718,115660,103494,116321,117948,117947,101564,111418,101094,111827,107461,100737,102310,111524,86711,101379,109699,104701,114894,100828,101188,107284,107261,107435,102412,116996,86712,86321,104852,103163,105208,103740,112057,99558,113855,99847,100858,100276,109075,129642,86322,107833,116679,103682,119353,118441,118553,109371,116828,99618,116886,108064,119095,86323,108473,117338,117346,86324,108946,86112,103130,105250,108823,109095,115639,101272,86732,114546,105795,112003,103685,107840,102886,103686,100679,116335,102837,113449,113038,113795,105597,111458,105595,114186,112679,101779,114322,86733,86121,99620,113071,118851,112672,86734,115267,111051,117951,102877,99450,114750,104803,100339,114575,112606,112604,113846,102392,109263,103015,86735,100496,101155,109264,105467,113798,112745,111631,103533,110950,118834,104430,115956,116884,109566,106958);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (110910,100642,129450,107197,114984,115977,113180,117906,113178,117241,109637,86687,86304,106499,104329,103486,115107,100661,105063,115923,115803,107984,110026,86688,108947,117262,107910,101563,108779,106213,117942,100443,107822,103676,107821,102026,117548,113569,117537,106354,110935,111502,116711,118101,106770,115342,101424,117540,108687,118024,110526,110843,107745,116353,106423,105610,119050,111354,107358,117874,110668,104598,114893,105945,104741,112572,105833,112690,117961,108928,106133,117155,115529,104638,118057,110718,115660,103494,116321,117948,117947,101564,111418,101094,111827,107461,100737,102310,111524,86711,101379,109699,104701,114894,100828,101188,107284,107261,107435,102412,116996,86712,86321,104852,103163,105208,103740,112057,99558,113855,99847,100858,100276,109075,129642,86322,107833,116679,103682,119353,118441,118553,109371,116828,99618,116886,108064,119095,86323,108473,117338,117346,86324,108946,86112,103130,105250,108823,109095,115639,101272,86732,114546,105795,112003,103685,107840,102886,103686,100679,116335,102837,113449,113038,113795,105597,111458,105595,114186,112679,101779,114322,86733,86121,99620,113071,118851,112672,86734,115267,111051,117951,102877,99450,114750,104803,100339,114575,112606,112604,113846,102392,109263,103015,86735,100496,101155,109264,105467,113798,112745,111631,103533,110950,118834,104430,115956,116884,109566,106958);

delete quotes.* 
from quotes where instrumentid in (110910,100642,129450,107197,114984,115977,113180,117906,113178,117241,109637,86687,86304,106499,104329,103486,115107,100661,105063,115923,115803,107984,110026,86688,108947,117262,107910,101563,108779,106213,117942,100443,107822,103676,107821,102026,117548,113569,117537,106354,110935,111502,116711,118101,106770,115342,101424,117540,108687,118024,110526,110843,107745,116353,106423,105610,119050,111354,107358,117874,110668,104598,114893,105945,104741,112572,105833,112690,117961,108928,106133,117155,115529,104638,118057,110718,115660,103494,116321,117948,117947,101564,111418,101094,111827,107461,100737,102310,111524,86711,101379,109699,104701,114894,100828,101188,107284,107261,107435,102412,116996,86712,86321,104852,103163,105208,103740,112057,99558,113855,99847,100858,100276,109075,129642,86322,107833,116679,103682,119353,118441,118553,109371,116828,99618,116886,108064,119095,86323,108473,117338,117346,86324,108946,86112,103130,105250,108823,109095,115639,101272,86732,114546,105795,112003,103685,107840,102886,103686,100679,116335,102837,113449,113038,113795,105597,111458,105595,114186,112679,101779,114322,86733,86121,99620,113071,118851,112672,86734,115267,111051,117951,102877,99450,114750,104803,100339,114575,112606,112604,113846,102392,109263,103015,86735,100496,101155,109264,105467,113798,112745,111631,103533,110950,118834,104430,115956,116884,109566,106958);

commit;



begin;

select concat("FROM ",111210," TO ", 116454);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (111210,111728,111734,86122,99853,106297,119004,111338,119394,102820,106002,117194,110857,102372,113782,86113,99380,117659,102055,86123,86736,101475,100562,86114,86737,108483,108731,102318,114645,117832,102762,113505,108384,104035,104949,101849,110179,100384,110178,104564,104027,86351,119251,105763,113218,104735,112591,104161,109949,103362,86124,112244,110667,86763,86115,118684,118661,119337,116491,116755,116492,116122,116121,106317,103220,112938,106877,112941,109955,104934,100664,109911,102167,109472,104102,108685,105067,101181,106810,103194,100941,112150,100419,109614,102741,104216,112020,115961,104444,105891,116756,101691,101526,86764,103927,110090,101919,110093,110089,104716,110094,103707,107812,100848,117320,113027,86125,114903,106653,114891,109093,107516,101104,112384,114907,107219,112179,105997,113456,102558,102770,114033,110344,109508,108951,100363,118126,99974,101461,103782,109871,109453,110812,115718,112022,119053,103550,86780,86126,86781,86352,86782,105000,103140,102775,101262,101802,109247,111115,104995,105001,104999,102209,114883,105599,113975,113959,107302,109514,117076,117780,113964,105917,105480,109909,115776,104802,109908,117075,100465,103836,113531,104406,104404,104405,100866,113533,111833,99829,102942,117242,114350,86116,116455,99512,101453,104431,104432,113690,105452,105066,101105,112120,100262,111888,107924,115646,113956,112119,116454);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (111210,111728,111734,86122,99853,106297,119004,111338,119394,102820,106002,117194,110857,102372,113782,86113,99380,117659,102055,86123,86736,101475,100562,86114,86737,108483,108731,102318,114645,117832,102762,113505,108384,104035,104949,101849,110179,100384,110178,104564,104027,86351,119251,105763,113218,104735,112591,104161,109949,103362,86124,112244,110667,86763,86115,118684,118661,119337,116491,116755,116492,116122,116121,106317,103220,112938,106877,112941,109955,104934,100664,109911,102167,109472,104102,108685,105067,101181,106810,103194,100941,112150,100419,109614,102741,104216,112020,115961,104444,105891,116756,101691,101526,86764,103927,110090,101919,110093,110089,104716,110094,103707,107812,100848,117320,113027,86125,114903,106653,114891,109093,107516,101104,112384,114907,107219,112179,105997,113456,102558,102770,114033,110344,109508,108951,100363,118126,99974,101461,103782,109871,109453,110812,115718,112022,119053,103550,86780,86126,86781,86352,86782,105000,103140,102775,101262,101802,109247,111115,104995,105001,104999,102209,114883,105599,113975,113959,107302,109514,117076,117780,113964,105917,105480,109909,115776,104802,109908,117075,100465,103836,113531,104406,104404,104405,100866,113533,111833,99829,102942,117242,114350,86116,116455,99512,101453,104431,104432,113690,105452,105066,101105,112120,100262,111888,107924,115646,113956,112119,116454);

delete quotes.* 
from quotes where instrumentid in (111210,111728,111734,86122,99853,106297,119004,111338,119394,102820,106002,117194,110857,102372,113782,86113,99380,117659,102055,86123,86736,101475,100562,86114,86737,108483,108731,102318,114645,117832,102762,113505,108384,104035,104949,101849,110179,100384,110178,104564,104027,86351,119251,105763,113218,104735,112591,104161,109949,103362,86124,112244,110667,86763,86115,118684,118661,119337,116491,116755,116492,116122,116121,106317,103220,112938,106877,112941,109955,104934,100664,109911,102167,109472,104102,108685,105067,101181,106810,103194,100941,112150,100419,109614,102741,104216,112020,115961,104444,105891,116756,101691,101526,86764,103927,110090,101919,110093,110089,104716,110094,103707,107812,100848,117320,113027,86125,114903,106653,114891,109093,107516,101104,112384,114907,107219,112179,105997,113456,102558,102770,114033,110344,109508,108951,100363,118126,99974,101461,103782,109871,109453,110812,115718,112022,119053,103550,86780,86126,86781,86352,86782,105000,103140,102775,101262,101802,109247,111115,104995,105001,104999,102209,114883,105599,113975,113959,107302,109514,117076,117780,113964,105917,105480,109909,115776,104802,109908,117075,100465,103836,113531,104406,104404,104405,100866,113533,111833,99829,102942,117242,114350,86116,116455,99512,101453,104431,104432,113690,105452,105066,101105,112120,100262,111888,107924,115646,113956,112119,116454);

commit;



begin;

select concat("FROM ",111837," TO ", 116486);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (111837,99413,86127,106650,110361,101950,107130,111417,107246,99517,86128,115960,86353,129813,106074,115456,117230,106454,118776,106885,117665,110423,115782,117655,104159,108378,117669,113384,117189,103899,99711,100141,113955,106589,116381,115760,129363,119143,118681,100554,108455,119145,119147,118652,119118,86154,102526,102670,109643,109616,86800,129795,86378,129897,119463,118194,102590,108931,104734,118497,104199,118842,118841,110835,102767,112165,103371,118730,119119,119375,118199,118731,119214,119392,119391,118264,118390,119464,117048,112666,112246,115409,100869,115367,103465,114707,86801,115056,110357,110505,105697,117943,108190,112061,108819,86155,118209,118593,119036,104084,108354,100184,100181,129371,110504,110149,101311,105959,103154,104201,102977,112415,105962,112062,101312,112965,112414,112963,113420,113400,104200,117167,103307,100310,113509,107238,100277,115642,112957,112700,100966,106975,105255,101882,102050,99712,112060,102695,116254,113291,112964,113775,116889,102864,116925,119248,118732,118796,115062,101374,100979,100204,100225,101767,118130,118125,115302,101738,113316,100524,113317,106139,106315,113288,86825,103601,115457,107632,103602,101375,115354,107432,119032,119453,105629,110912,101323,113740,114224,102201,110802,117533,112314,116900,102588,101608,104036,101548,108034,100696,105576,112175,110489,101342,111117,105002,106955,103168,101137,116486);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (111837,99413,86127,106650,110361,101950,107130,111417,107246,99517,86128,115960,86353,129813,106074,115456,117230,106454,118776,106885,117665,110423,115782,117655,104159,108378,117669,113384,117189,103899,99711,100141,113955,106589,116381,115760,129363,119143,118681,100554,108455,119145,119147,118652,119118,86154,102526,102670,109643,109616,86800,129795,86378,129897,119463,118194,102590,108931,104734,118497,104199,118842,118841,110835,102767,112165,103371,118730,119119,119375,118199,118731,119214,119392,119391,118264,118390,119464,117048,112666,112246,115409,100869,115367,103465,114707,86801,115056,110357,110505,105697,117943,108190,112061,108819,86155,118209,118593,119036,104084,108354,100184,100181,129371,110504,110149,101311,105959,103154,104201,102977,112415,105962,112062,101312,112965,112414,112963,113420,113400,104200,117167,103307,100310,113509,107238,100277,115642,112957,112700,100966,106975,105255,101882,102050,99712,112060,102695,116254,113291,112964,113775,116889,102864,116925,119248,118732,118796,115062,101374,100979,100204,100225,101767,118130,118125,115302,101738,113316,100524,113317,106139,106315,113288,86825,103601,115457,107632,103602,101375,115354,107432,119032,119453,105629,110912,101323,113740,114224,102201,110802,117533,112314,116900,102588,101608,104036,101548,108034,100696,105576,112175,110489,101342,111117,105002,106955,103168,101137,116486);

delete quotes.* 
from quotes where instrumentid in (111837,99413,86127,106650,110361,101950,107130,111417,107246,99517,86128,115960,86353,129813,106074,115456,117230,106454,118776,106885,117665,110423,115782,117655,104159,108378,117669,113384,117189,103899,99711,100141,113955,106589,116381,115760,129363,119143,118681,100554,108455,119145,119147,118652,119118,86154,102526,102670,109643,109616,86800,129795,86378,129897,119463,118194,102590,108931,104734,118497,104199,118842,118841,110835,102767,112165,103371,118730,119119,119375,118199,118731,119214,119392,119391,118264,118390,119464,117048,112666,112246,115409,100869,115367,103465,114707,86801,115056,110357,110505,105697,117943,108190,112061,108819,86155,118209,118593,119036,104084,108354,100184,100181,129371,110504,110149,101311,105959,103154,104201,102977,112415,105962,112062,101312,112965,112414,112963,113420,113400,104200,117167,103307,100310,113509,107238,100277,115642,112957,112700,100966,106975,105255,101882,102050,99712,112060,102695,116254,113291,112964,113775,116889,102864,116925,119248,118732,118796,115062,101374,100979,100204,100225,101767,118130,118125,115302,101738,113316,100524,113317,106139,106315,113288,86825,103601,115457,107632,103602,101375,115354,107432,119032,119453,105629,110912,101323,113740,114224,102201,110802,117533,112314,116900,102588,101608,104036,101548,108034,100696,105576,112175,110489,101342,111117,105002,106955,103168,101137,116486);

commit;



begin;

select concat("FROM ",108806," TO ", 115557);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (108806,101570,111357,105410,118096,104075,102962,105839,116437,106767,109618,116315,117491,118182,109312,111826,102653,99491,109587,86826,86143,103979,86156,101019,114905,100290,110691,106216,114908,106296,110324,101874,101787,106640,104455,99927,99846,105260,116511,117492,108457,99431,104645,110511,101201,108157,108383,113323,102006,113408,117410,115047,106050,113285,106626,109697,112920,105179,113295,99529,99746,103365,114595,104602,111504,110706,100959,100581,117099,109675,103730,118362,118983,118258,110810,86157,105243,117782,112247,110399,110370,113162,116748,110400,107153,104243,104704,106498,106625,112939,113290,86843,115140,101095,108185,112232,86394,86844,112072,117056,117543,107877,102646,117168,118626,112750,115299,115300,100072,102827,102173,112600,107079,100943,105577,115390,115389,115925,115387,115393,103560,111936,102447,107133,102869,118963,86144,116928,101377,101754,115794,101739,118181,109411,108583,118794,118296,118889,129010,117987,117742,113749,113743,102604,102602,108682,109870,101883,117924,101935,109153,106642,113324,117017,103087,119415,118335,118604,114693,107267,107266,106513,101645,117097,99486,114248,99791,112066,101351,117745,117746,117991,101163,119069,117995,86859,86395,102821,119044,119452,129353,129009,109188,104529,128812,100701,105372,129352,129002,115791,86860,104162,103071,116402,110885,114649,102862,107311,112697,115557);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (108806,101570,111357,105410,118096,104075,102962,105839,116437,106767,109618,116315,117491,118182,109312,111826,102653,99491,109587,86826,86143,103979,86156,101019,114905,100290,110691,106216,114908,106296,110324,101874,101787,106640,104455,99927,99846,105260,116511,117492,108457,99431,104645,110511,101201,108157,108383,113323,102006,113408,117410,115047,106050,113285,106626,109697,112920,105179,113295,99529,99746,103365,114595,104602,111504,110706,100959,100581,117099,109675,103730,118362,118983,118258,110810,86157,105243,117782,112247,110399,110370,113162,116748,110400,107153,104243,104704,106498,106625,112939,113290,86843,115140,101095,108185,112232,86394,86844,112072,117056,117543,107877,102646,117168,118626,112750,115299,115300,100072,102827,102173,112600,107079,100943,105577,115390,115389,115925,115387,115393,103560,111936,102447,107133,102869,118963,86144,116928,101377,101754,115794,101739,118181,109411,108583,118794,118296,118889,129010,117987,117742,113749,113743,102604,102602,108682,109870,101883,117924,101935,109153,106642,113324,117017,103087,119415,118335,118604,114693,107267,107266,106513,101645,117097,99486,114248,99791,112066,101351,117745,117746,117991,101163,119069,117995,86859,86395,102821,119044,119452,129353,129009,109188,104529,128812,100701,105372,129352,129002,115791,86860,104162,103071,116402,110885,114649,102862,107311,112697,115557);

delete quotes.* 
from quotes where instrumentid in (108806,101570,111357,105410,118096,104075,102962,105839,116437,106767,109618,116315,117491,118182,109312,111826,102653,99491,109587,86826,86143,103979,86156,101019,114905,100290,110691,106216,114908,106296,110324,101874,101787,106640,104455,99927,99846,105260,116511,117492,108457,99431,104645,110511,101201,108157,108383,113323,102006,113408,117410,115047,106050,113285,106626,109697,112920,105179,113295,99529,99746,103365,114595,104602,111504,110706,100959,100581,117099,109675,103730,118362,118983,118258,110810,86157,105243,117782,112247,110399,110370,113162,116748,110400,107153,104243,104704,106498,106625,112939,113290,86843,115140,101095,108185,112232,86394,86844,112072,117056,117543,107877,102646,117168,118626,112750,115299,115300,100072,102827,102173,112600,107079,100943,105577,115390,115389,115925,115387,115393,103560,111936,102447,107133,102869,118963,86144,116928,101377,101754,115794,101739,118181,109411,108583,118794,118296,118889,129010,117987,117742,113749,113743,102604,102602,108682,109870,101883,117924,101935,109153,106642,113324,117017,103087,119415,118335,118604,114693,107267,107266,106513,101645,117097,99486,114248,99791,112066,101351,117745,117746,117991,101163,119069,117995,86859,86395,102821,119044,119452,129353,129009,109188,104529,128812,100701,105372,129352,129002,115791,86860,104162,103071,116402,110885,114649,102862,107311,112697,115557);

commit;



begin;

select concat("FROM ",100156," TO ", 102988);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (100156,118261,99426,86158,107268,101250,114696,114695,114694,111340,102346,106631,105180,106501,116625,111343,109152,109429,117766,117768,107983,117422,109154,102572,116308,99390,108904,112389,105899,117634,109251,118435,86396,86861,100971,109841,116946,100237,86168,118499,113207,100227,117292,107185,103628,99381,113447,103542,102529,110497,86174,104648,102132,105412,102994,102618,101003,101735,114777,114479,116475,117871,110607,108294,102995,100287,102108,107335,115298,107460,109248,112361,104227,116922,115672,104959,117926,101728,117668,117667,116624,104332,112428,112070,112429,112076,103894,99659,101433,101294,115987,104063,117927,110736,112305,112307,112304,102184,110327,110326,110355,100695,105691,99375,99761,104367,110456,101495,105993,116263,99710,103183,107305,105490,109101,111822,115130,116430,86169,86888,86414,102537,118835,119142,119072,105443,118436,112268,118884,103235,117158,117159,116890,103735,100086,111407,102500,111402,101360,100213,99453,99376,119005,119030,110503,118008,118009,110756,114698,115313,114699,116453,106644,111139,115312,105212,99370,102555,99634,105694,118774,108257,100560,117774,101653,114147,117872,117880,119436,119253,119437,99787,115720,99485,105581,117327,100719,117010,119239,101881,100672,104943,118958,119088,119087,100401,118746,119290,118270,119385,119172,86898,108563,119249,101842,119396,105949,99870,113436,102988);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (100156,118261,99426,86158,107268,101250,114696,114695,114694,111340,102346,106631,105180,106501,116625,111343,109152,109429,117766,117768,107983,117422,109154,102572,116308,99390,108904,112389,105899,117634,109251,118435,86396,86861,100971,109841,116946,100237,86168,118499,113207,100227,117292,107185,103628,99381,113447,103542,102529,110497,86174,104648,102132,105412,102994,102618,101003,101735,114777,114479,116475,117871,110607,108294,102995,100287,102108,107335,115298,107460,109248,112361,104227,116922,115672,104959,117926,101728,117668,117667,116624,104332,112428,112070,112429,112076,103894,99659,101433,101294,115987,104063,117927,110736,112305,112307,112304,102184,110327,110326,110355,100695,105691,99375,99761,104367,110456,101495,105993,116263,99710,103183,107305,105490,109101,111822,115130,116430,86169,86888,86414,102537,118835,119142,119072,105443,118436,112268,118884,103235,117158,117159,116890,103735,100086,111407,102500,111402,101360,100213,99453,99376,119005,119030,110503,118008,118009,110756,114698,115313,114699,116453,106644,111139,115312,105212,99370,102555,99634,105694,118774,108257,100560,117774,101653,114147,117872,117880,119436,119253,119437,99787,115720,99485,105581,117327,100719,117010,119239,101881,100672,104943,118958,119088,119087,100401,118746,119290,118270,119385,119172,86898,108563,119249,101842,119396,105949,99870,113436,102988);

delete quotes.* 
from quotes where instrumentid in (100156,118261,99426,86158,107268,101250,114696,114695,114694,111340,102346,106631,105180,106501,116625,111343,109152,109429,117766,117768,107983,117422,109154,102572,116308,99390,108904,112389,105899,117634,109251,118435,86396,86861,100971,109841,116946,100237,86168,118499,113207,100227,117292,107185,103628,99381,113447,103542,102529,110497,86174,104648,102132,105412,102994,102618,101003,101735,114777,114479,116475,117871,110607,108294,102995,100287,102108,107335,115298,107460,109248,112361,104227,116922,115672,104959,117926,101728,117668,117667,116624,104332,112428,112070,112429,112076,103894,99659,101433,101294,115987,104063,117927,110736,112305,112307,112304,102184,110327,110326,110355,100695,105691,99375,99761,104367,110456,101495,105993,116263,99710,103183,107305,105490,109101,111822,115130,116430,86169,86888,86414,102537,118835,119142,119072,105443,118436,112268,118884,103235,117158,117159,116890,103735,100086,111407,102500,111402,101360,100213,99453,99376,119005,119030,110503,118008,118009,110756,114698,115313,114699,116453,106644,111139,115312,105212,99370,102555,99634,105694,118774,108257,100560,117774,101653,114147,117872,117880,119436,119253,119437,99787,115720,99485,105581,117327,100719,117010,119239,101881,100672,104943,118958,119088,119087,100401,118746,119290,118270,119385,119172,86898,108563,119249,101842,119396,105949,99870,113436,102988);

commit;



begin;

select concat("FROM ",100285," TO ", 118677);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (100285,113431,102619,108905,106701,119325,86415,114706,107275,107277,103239,104392,109138,109135,103839,86899,117021,86900,86416,116392,107172,116393,113063,118397,119283,118325,118198,118514,118846,118721,105444,103096,86901,112166,117195,100275,111391,111701,108352,112580,117996,112584,117751,112079,112077,118558,101786,110785,102001,100201,106541,116236,86930,108131,108780,117944,100411,112662,102909,101060,115526,114223,101431,116775,118687,118768,118769,118786,111221,110544,110546,115733,110862,119013,118924,119295,118402,118587,118996,100025,116287,108044,109076,99613,102043,109623,101875,110545,110861,117852,110813,103156,111228,106987,111225,119000,116289,111755,119365,111913,101974,109452,100729,107236,113407,108198,86931,100076,108976,104644,104608,119157,101069,101529,112853,108757,109083,109435,108450,110322,104933,112432,112433,112075,117512,117873,108774,117478,107637,111118,118053,118348,110046,101834,110045,104578,116978,114240,100199,103077,109957,101840,110321,107468,115304,107469,113344,112074,106325,119018,119327,118346,101317,113354,99789,106326,100215,101365,109178,100693,110599,108599,106345,103258,116449,109665,99752,104065,104941,117932,115800,113421,101870,117934,101309,118287,119447,118569,119098,118929,107085,118643,86170,102704,106331,106332,113347,104397,107826,107823,109150,116653,116654,104994,116655,109965,119451,118251,100938,118677);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (100285,113431,102619,108905,106701,119325,86415,114706,107275,107277,103239,104392,109138,109135,103839,86899,117021,86900,86416,116392,107172,116393,113063,118397,119283,118325,118198,118514,118846,118721,105444,103096,86901,112166,117195,100275,111391,111701,108352,112580,117996,112584,117751,112079,112077,118558,101786,110785,102001,100201,106541,116236,86930,108131,108780,117944,100411,112662,102909,101060,115526,114223,101431,116775,118687,118768,118769,118786,111221,110544,110546,115733,110862,119013,118924,119295,118402,118587,118996,100025,116287,108044,109076,99613,102043,109623,101875,110545,110861,117852,110813,103156,111228,106987,111225,119000,116289,111755,119365,111913,101974,109452,100729,107236,113407,108198,86931,100076,108976,104644,104608,119157,101069,101529,112853,108757,109083,109435,108450,110322,104933,112432,112433,112075,117512,117873,108774,117478,107637,111118,118053,118348,110046,101834,110045,104578,116978,114240,100199,103077,109957,101840,110321,107468,115304,107469,113344,112074,106325,119018,119327,118346,101317,113354,99789,106326,100215,101365,109178,100693,110599,108599,106345,103258,116449,109665,99752,104065,104941,117932,115800,113421,101870,117934,101309,118287,119447,118569,119098,118929,107085,118643,86170,102704,106331,106332,113347,104397,107826,107823,109150,116653,116654,104994,116655,109965,119451,118251,100938,118677);

delete quotes.* 
from quotes where instrumentid in (100285,113431,102619,108905,106701,119325,86415,114706,107275,107277,103239,104392,109138,109135,103839,86899,117021,86900,86416,116392,107172,116393,113063,118397,119283,118325,118198,118514,118846,118721,105444,103096,86901,112166,117195,100275,111391,111701,108352,112580,117996,112584,117751,112079,112077,118558,101786,110785,102001,100201,106541,116236,86930,108131,108780,117944,100411,112662,102909,101060,115526,114223,101431,116775,118687,118768,118769,118786,111221,110544,110546,115733,110862,119013,118924,119295,118402,118587,118996,100025,116287,108044,109076,99613,102043,109623,101875,110545,110861,117852,110813,103156,111228,106987,111225,119000,116289,111755,119365,111913,101974,109452,100729,107236,113407,108198,86931,100076,108976,104644,104608,119157,101069,101529,112853,108757,109083,109435,108450,110322,104933,112432,112433,112075,117512,117873,108774,117478,107637,111118,118053,118348,110046,101834,110045,104578,116978,114240,100199,103077,109957,101840,110321,107468,115304,107469,113344,112074,106325,119018,119327,118346,101317,113354,99789,106326,100215,101365,109178,100693,110599,108599,106345,103258,116449,109665,99752,104065,104941,117932,115800,113421,101870,117934,101309,118287,119447,118569,119098,118929,107085,118643,86170,102704,106331,106332,113347,104397,107826,107823,109150,116653,116654,104994,116655,109965,119451,118251,100938,118677);

commit;



begin;

select concat("FROM ",106155," TO ", 108978);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (106155,102511,116982,108675,105044,101541,102750,110782,119188,116729,104969,110781,118328,118389,118388,119170,119364,118738,119217,86953,102771,86954,113722,86955,86443,105578,118548,86973,86462,113357,102706,100270,113852,86974,111226,119062,118269,118285,86193,116374,105950,86975,119280,119308,104106,101633,118520,109667,86463,86976,86202,113645,109313,118892,115957,86977,110621,111018,118525,101273,119086,118847,119423,115532,118486,117865,106980,100911,114375,118036,102118,108975,110185,110184,104184,105111,117471,107806,114602,104251,109151,104398,104399,112189,118763,100266,103849,103852,118118,107338,107337,117334,101352,117525,116457,113819,118416,105952,107137,105951,112084,101318,112435,119139,119107,86978,86203,119046,119045,118566,118567,100008,111878,118583,119413,118944,119031,102359,109176,117455,113196,101447,111044,118589,108341,111663,109540,86995,86464,86996,119340,105261,118311,118560,118940,104985,116800,116809,119027,118788,108355,115643,102879,105054,107514,118378,119311,107607,119435,109962,110547,104044,112587,117787,110765,99941,107368,116891,105244,102912,86465,99582,115381,117409,118372,118515,116451,100352,108944,114715,104357,101145,113757,115427,102847,100972,107561,100936,111346,111342,111347,111345,100937,108209,110188,110189,108981,104890,102117,107941,107964,100998,116848,116979,113425,104892,117711,104185,110191,108978);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (106155,102511,116982,108675,105044,101541,102750,110782,119188,116729,104969,110781,118328,118389,118388,119170,119364,118738,119217,86953,102771,86954,113722,86955,86443,105578,118548,86973,86462,113357,102706,100270,113852,86974,111226,119062,118269,118285,86193,116374,105950,86975,119280,119308,104106,101633,118520,109667,86463,86976,86202,113645,109313,118892,115957,86977,110621,111018,118525,101273,119086,118847,119423,115532,118486,117865,106980,100911,114375,118036,102118,108975,110185,110184,104184,105111,117471,107806,114602,104251,109151,104398,104399,112189,118763,100266,103849,103852,118118,107338,107337,117334,101352,117525,116457,113819,118416,105952,107137,105951,112084,101318,112435,119139,119107,86978,86203,119046,119045,118566,118567,100008,111878,118583,119413,118944,119031,102359,109176,117455,113196,101447,111044,118589,108341,111663,109540,86995,86464,86996,119340,105261,118311,118560,118940,104985,116800,116809,119027,118788,108355,115643,102879,105054,107514,118378,119311,107607,119435,109962,110547,104044,112587,117787,110765,99941,107368,116891,105244,102912,86465,99582,115381,117409,118372,118515,116451,100352,108944,114715,104357,101145,113757,115427,102847,100972,107561,100936,111346,111342,111347,111345,100937,108209,110188,110189,108981,104890,102117,107941,107964,100998,116848,116979,113425,104892,117711,104185,110191,108978);

delete quotes.* 
from quotes where instrumentid in (106155,102511,116982,108675,105044,101541,102750,110782,119188,116729,104969,110781,118328,118389,118388,119170,119364,118738,119217,86953,102771,86954,113722,86955,86443,105578,118548,86973,86462,113357,102706,100270,113852,86974,111226,119062,118269,118285,86193,116374,105950,86975,119280,119308,104106,101633,118520,109667,86463,86976,86202,113645,109313,118892,115957,86977,110621,111018,118525,101273,119086,118847,119423,115532,118486,117865,106980,100911,114375,118036,102118,108975,110185,110184,104184,105111,117471,107806,114602,104251,109151,104398,104399,112189,118763,100266,103849,103852,118118,107338,107337,117334,101352,117525,116457,113819,118416,105952,107137,105951,112084,101318,112435,119139,119107,86978,86203,119046,119045,118566,118567,100008,111878,118583,119413,118944,119031,102359,109176,117455,113196,101447,111044,118589,108341,111663,109540,86995,86464,86996,119340,105261,118311,118560,118940,104985,116800,116809,119027,118788,108355,115643,102879,105054,107514,118378,119311,107607,119435,109962,110547,104044,112587,117787,110765,99941,107368,116891,105244,102912,86465,99582,115381,117409,118372,118515,116451,100352,108944,114715,104357,101145,113757,115427,102847,100972,107561,100936,111346,111342,111347,111345,100937,108209,110188,110189,108981,104890,102117,107941,107964,100998,116848,116979,113425,104892,117711,104185,110191,108978);

commit;



begin;

select concat("FROM ",105114," TO ", 119097);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (105114,105416,111084,111085,111413,110618,111083,102188,103363,115122,101448,103060,109860,108681,100739,108666,104842,119068,114157,110246,119146,118651,118653,118547,115118,115928,105654,110146,103390,118828,118891,104600,116237,110393,86204,119331,118802,87013,119425,87014,86489,110457,105602,118645,118644,117118,118308,118331,105381,108147,106963,108401,115141,107942,110193,110192,108204,118773,119152,118291,118577,115205,119254,100208,118693,119178,101016,100362,118505,119377,87015,86194,87016,116764,119233,119240,119257,118461,118699,103993,109864,115181,115179,117618,117617,115180,109668,110858,113298,109669,110863,116988,100988,103697,103451,115530,99883,107084,105322,101422,118947,101195,111810,119105,118391,119307,119066,118688,86490,129802,118748,86195,87017,86491,87018,110119,112818,112468,110121,112477,112474,112823,112472,112822,112473,117881,117882,103972,117514,119291,115647,109951,104932,104523,108710,86196,104687,119108,103726,117823,108529,110061,110780,117534,101829,113910,106447,105900,100536,117962,119421,115745,119296,105692,112316,112949,112951,114288,86492,86197,114287,105445,104593,113593,108622,112319,118412,100631,118138,115186,103079,105489,117515,100673,105818,111396,105420,111419,86516,116644,108290,114146,111383,118462,106698,104978,104928,118936,129020,119354,119355,118680,108627,119064,118918,118800,118630,118631,118316,119097);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (105114,105416,111084,111085,111413,110618,111083,102188,103363,115122,101448,103060,109860,108681,100739,108666,104842,119068,114157,110246,119146,118651,118653,118547,115118,115928,105654,110146,103390,118828,118891,104600,116237,110393,86204,119331,118802,87013,119425,87014,86489,110457,105602,118645,118644,117118,118308,118331,105381,108147,106963,108401,115141,107942,110193,110192,108204,118773,119152,118291,118577,115205,119254,100208,118693,119178,101016,100362,118505,119377,87015,86194,87016,116764,119233,119240,119257,118461,118699,103993,109864,115181,115179,117618,117617,115180,109668,110858,113298,109669,110863,116988,100988,103697,103451,115530,99883,107084,105322,101422,118947,101195,111810,119105,118391,119307,119066,118688,86490,129802,118748,86195,87017,86491,87018,110119,112818,112468,110121,112477,112474,112823,112472,112822,112473,117881,117882,103972,117514,119291,115647,109951,104932,104523,108710,86196,104687,119108,103726,117823,108529,110061,110780,117534,101829,113910,106447,105900,100536,117962,119421,115745,119296,105692,112316,112949,112951,114288,86492,86197,114287,105445,104593,113593,108622,112319,118412,100631,118138,115186,103079,105489,117515,100673,105818,111396,105420,111419,86516,116644,108290,114146,111383,118462,106698,104978,104928,118936,129020,119354,119355,118680,108627,119064,118918,118800,118630,118631,118316,119097);

delete quotes.* 
from quotes where instrumentid in (105114,105416,111084,111085,111413,110618,111083,102188,103363,115122,101448,103060,109860,108681,100739,108666,104842,119068,114157,110246,119146,118651,118653,118547,115118,115928,105654,110146,103390,118828,118891,104600,116237,110393,86204,119331,118802,87013,119425,87014,86489,110457,105602,118645,118644,117118,118308,118331,105381,108147,106963,108401,115141,107942,110193,110192,108204,118773,119152,118291,118577,115205,119254,100208,118693,119178,101016,100362,118505,119377,87015,86194,87016,116764,119233,119240,119257,118461,118699,103993,109864,115181,115179,117618,117617,115180,109668,110858,113298,109669,110863,116988,100988,103697,103451,115530,99883,107084,105322,101422,118947,101195,111810,119105,118391,119307,119066,118688,86490,129802,118748,86195,87017,86491,87018,110119,112818,112468,110121,112477,112474,112823,112472,112822,112473,117881,117882,103972,117514,119291,115647,109951,104932,104523,108710,86196,104687,119108,103726,117823,108529,110061,110780,117534,101829,113910,106447,105900,100536,117962,119421,115745,119296,105692,112316,112949,112951,114288,86492,86197,114287,105445,104593,113593,108622,112319,118412,100631,118138,115186,103079,105489,117515,100673,105818,111396,105420,111419,86516,116644,108290,114146,111383,118462,106698,104978,104928,118936,129020,119354,119355,118680,108627,119064,118918,118800,118630,118631,118316,119097);

commit;



begin;

select concat("FROM ",118934," TO ", 86553);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (118934,118806,118321,118785,108708,105656,101503,100992,107110,100967,104209,113737,86221,100546,113017,112894,113432,107620,113250,108194,108972,117307,117694,114772,116777,113596,108866,117579,110860,113055,102840,106463,113050,113051,105971,112980,112981,112979,117620,105970,107818,103649,107467,107121,112269,112406,115306,118492,117174,107181,117149,112254,100692,118673,119104,112478,112475,112476,119259,115151,101615,101217,99466,101243,111898,110884,102774,112401,112667,112320,86227,113804,86222,114598,114599,103702,117006,103055,108546,110842,86223,119134,113365,112825,112480,119245,119349,118817,118247,118771,118797,119310,118249,118483,117181,119218,117716,100218,101408,118326,100502,106068,106070,111862,117147,100376,101759,118516,105707,109872,104368,102976,116348,102074,106461,106582,105427,111434,116280,103630,116281,116275,118801,105402,111958,86535,86228,86536,106016,102833,100145,103325,99510,113366,118272,119144,118672,86537,86229,86538,86224,114842,119234,119235,106334,106335,115069,103008,107584,104166,113198,113372,113373,113369,117727,117974,112454,112456,100845,105883,113241,100259,102697,112969,112970,117325,111372,105529,86225,86539,86230,103185,86226,86540,107630,113837,105073,99992,104843,118927,119282,106380,102906,102443,111612,112753,106618,116421,116420,118429,107120,109072,106569,99680,112096,105355,111906,114659,86249,86553);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (118934,118806,118321,118785,108708,105656,101503,100992,107110,100967,104209,113737,86221,100546,113017,112894,113432,107620,113250,108194,108972,117307,117694,114772,116777,113596,108866,117579,110860,113055,102840,106463,113050,113051,105971,112980,112981,112979,117620,105970,107818,103649,107467,107121,112269,112406,115306,118492,117174,107181,117149,112254,100692,118673,119104,112478,112475,112476,119259,115151,101615,101217,99466,101243,111898,110884,102774,112401,112667,112320,86227,113804,86222,114598,114599,103702,117006,103055,108546,110842,86223,119134,113365,112825,112480,119245,119349,118817,118247,118771,118797,119310,118249,118483,117181,119218,117716,100218,101408,118326,100502,106068,106070,111862,117147,100376,101759,118516,105707,109872,104368,102976,116348,102074,106461,106582,105427,111434,116280,103630,116281,116275,118801,105402,111958,86535,86228,86536,106016,102833,100145,103325,99510,113366,118272,119144,118672,86537,86229,86538,86224,114842,119234,119235,106334,106335,115069,103008,107584,104166,113198,113372,113373,113369,117727,117974,112454,112456,100845,105883,113241,100259,102697,112969,112970,117325,111372,105529,86225,86539,86230,103185,86226,86540,107630,113837,105073,99992,104843,118927,119282,106380,102906,102443,111612,112753,106618,116421,116420,118429,107120,109072,106569,99680,112096,105355,111906,114659,86249,86553);

delete quotes.* 
from quotes where instrumentid in (118934,118806,118321,118785,108708,105656,101503,100992,107110,100967,104209,113737,86221,100546,113017,112894,113432,107620,113250,108194,108972,117307,117694,114772,116777,113596,108866,117579,110860,113055,102840,106463,113050,113051,105971,112980,112981,112979,117620,105970,107818,103649,107467,107121,112269,112406,115306,118492,117174,107181,117149,112254,100692,118673,119104,112478,112475,112476,119259,115151,101615,101217,99466,101243,111898,110884,102774,112401,112667,112320,86227,113804,86222,114598,114599,103702,117006,103055,108546,110842,86223,119134,113365,112825,112480,119245,119349,118817,118247,118771,118797,119310,118249,118483,117181,119218,117716,100218,101408,118326,100502,106068,106070,111862,117147,100376,101759,118516,105707,109872,104368,102976,116348,102074,106461,106582,105427,111434,116280,103630,116281,116275,118801,105402,111958,86535,86228,86536,106016,102833,100145,103325,99510,113366,118272,119144,118672,86537,86229,86538,86224,114842,119234,119235,106334,106335,115069,103008,107584,104166,113198,113372,113373,113369,117727,117974,112454,112456,100845,105883,113241,100259,102697,112969,112970,117325,111372,105529,86225,86539,86230,103185,86226,86540,107630,113837,105073,99992,104843,118927,119282,106380,102906,102443,111612,112753,106618,116421,116420,118429,107120,109072,106569,99680,112096,105355,111906,114659,86249,86553);

commit;



begin;

select concat("FROM ",86247," TO ", 110808);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (86247,112861,113208,100305,111146,109054,105215,109256,110021,104569,103464,116855,116856,105875,109831,110664,114304,99425,114305,114300,114301,114299,109032,109034,106823,100815,108387,109187,102495,99449,105487,107971,106488,113446,118025,116251,114582,113834,109862,99554,104846,116892,116836,117853,109051,103840,108212,99734,118537,112624,102186,110426,116561,108307,106492,115154,112643,104224,107825,102185,107082,114726,105015,109479,109480,109481,100732,102839,119252,113810,103719,86554,118237,118978,111241,86555,102103,86556,100374,117104,86248,109535,106117,103709,106141,111138,111584,100887,100136,99656,105731,111134,111735,105901,111732,107896,116166,116159,107893,107897,111482,105112,115980,107087,111900,102454,86576,118982,111017,104851,105715,108662,100042,101833,110177,86250,111013,103035,104465,103778,86577,106560,105263,118375,115978,104352,116905,117171,100816,109753,115411,108015,113451,101421,100343,103825,113167,101022,113904,114672,107256,103230,114673,116844,102623,115545,102611,117401,103009,101510,109851,101916,114986,115212,110219,108725,108126,117388,117762,105248,118243,119401,118829,118933,118870,118585,119363,118919,118473,118905,118475,118957,118781,118624,118902,118977,110533,110532,110848,99590,117133,117115,105006,106362,86261,112067,110822,103620,112418,112068,113751,107350,114356,109161,114652,99660,111095,106378,102290,110808);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (86247,112861,113208,100305,111146,109054,105215,109256,110021,104569,103464,116855,116856,105875,109831,110664,114304,99425,114305,114300,114301,114299,109032,109034,106823,100815,108387,109187,102495,99449,105487,107971,106488,113446,118025,116251,114582,113834,109862,99554,104846,116892,116836,117853,109051,103840,108212,99734,118537,112624,102186,110426,116561,108307,106492,115154,112643,104224,107825,102185,107082,114726,105015,109479,109480,109481,100732,102839,119252,113810,103719,86554,118237,118978,111241,86555,102103,86556,100374,117104,86248,109535,106117,103709,106141,111138,111584,100887,100136,99656,105731,111134,111735,105901,111732,107896,116166,116159,107893,107897,111482,105112,115980,107087,111900,102454,86576,118982,111017,104851,105715,108662,100042,101833,110177,86250,111013,103035,104465,103778,86577,106560,105263,118375,115978,104352,116905,117171,100816,109753,115411,108015,113451,101421,100343,103825,113167,101022,113904,114672,107256,103230,114673,116844,102623,115545,102611,117401,103009,101510,109851,101916,114986,115212,110219,108725,108126,117388,117762,105248,118243,119401,118829,118933,118870,118585,119363,118919,118473,118905,118475,118957,118781,118624,118902,118977,110533,110532,110848,99590,117133,117115,105006,106362,86261,112067,110822,103620,112418,112068,113751,107350,114356,109161,114652,99660,111095,106378,102290,110808);

delete quotes.* 
from quotes where instrumentid in (86247,112861,113208,100305,111146,109054,105215,109256,110021,104569,103464,116855,116856,105875,109831,110664,114304,99425,114305,114300,114301,114299,109032,109034,106823,100815,108387,109187,102495,99449,105487,107971,106488,113446,118025,116251,114582,113834,109862,99554,104846,116892,116836,117853,109051,103840,108212,99734,118537,112624,102186,110426,116561,108307,106492,115154,112643,104224,107825,102185,107082,114726,105015,109479,109480,109481,100732,102839,119252,113810,103719,86554,118237,118978,111241,86555,102103,86556,100374,117104,86248,109535,106117,103709,106141,111138,111584,100887,100136,99656,105731,111134,111735,105901,111732,107896,116166,116159,107893,107897,111482,105112,115980,107087,111900,102454,86576,118982,111017,104851,105715,108662,100042,101833,110177,86250,111013,103035,104465,103778,86577,106560,105263,118375,115978,104352,116905,117171,100816,109753,115411,108015,113451,101421,100343,103825,113167,101022,113904,114672,107256,103230,114673,116844,102623,115545,102611,117401,103009,101510,109851,101916,114986,115212,110219,108725,108126,117388,117762,105248,118243,119401,118829,118933,118870,118585,119363,118919,118473,118905,118475,118957,118781,118624,118902,118977,110533,110532,110848,99590,117133,117115,105006,106362,86261,112067,110822,103620,112418,112068,113751,107350,114356,109161,114652,99660,111095,106378,102290,110808);

commit;



begin;

select concat("FROM ",118312," TO ", 110889);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (118312,102609,102599,108292,108312,115193,101528,108719,101839,100831,107348,114728,109861,110362,118257,100699,117096,108313,116579,108317,116580,117744,117986,117743,117988,108092,108093,106643,106512,113322,114692,115155,113199,118555,113200,104165,109958,108716,101720,116489,107792,116490,109574,105104,111725,111724,112093,103867,113752,112092,106348,102761,106349,113742,101382,114650,119037,99709,106768,100283,108101,106420,119132,116951,114423,110680,99521,116617,110679,116515,102317,109799,108310,116564,118809,116477,118787,117088,117500,108462,101651,109747,108559,107747,111804,118598,100630,107957,111803,102450,115744,104052,108418,101283,114311,101065,113931,106950,102388,102407,119033,105620,111866,106951,106970,118457,106956,116717,114285,106881,111757,105953,112943,108554,117065,102626,86267,103578,106780,112329,86268,104451,103863,86615,108596,86616,112856,113201,100079,103962,112855,108717,117475,101562,117935,117232,113202,113376,106337,113379,113374,113375,106915,114075,111432,106912,113543,114192,106968,114328,103174,99776,113097,106215,102525,113098,105451,102999,103589,119346,107887,107711,107888,114160,110421,109490,107972,117011,86617,86262,113100,86269,113204,114840,86618,86270,103093,117055,107138,114953,115483,105323,110360,117396,101203,116906,117173,114972,114686,86650,118869,119393,113378,103495,107765,108107,117729,117976,117975,110889);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (118312,102609,102599,108292,108312,115193,101528,108719,101839,100831,107348,114728,109861,110362,118257,100699,117096,108313,116579,108317,116580,117744,117986,117743,117988,108092,108093,106643,106512,113322,114692,115155,113199,118555,113200,104165,109958,108716,101720,116489,107792,116490,109574,105104,111725,111724,112093,103867,113752,112092,106348,102761,106349,113742,101382,114650,119037,99709,106768,100283,108101,106420,119132,116951,114423,110680,99521,116617,110679,116515,102317,109799,108310,116564,118809,116477,118787,117088,117500,108462,101651,109747,108559,107747,111804,118598,100630,107957,111803,102450,115744,104052,108418,101283,114311,101065,113931,106950,102388,102407,119033,105620,111866,106951,106970,118457,106956,116717,114285,106881,111757,105953,112943,108554,117065,102626,86267,103578,106780,112329,86268,104451,103863,86615,108596,86616,112856,113201,100079,103962,112855,108717,117475,101562,117935,117232,113202,113376,106337,113379,113374,113375,106915,114075,111432,106912,113543,114192,106968,114328,103174,99776,113097,106215,102525,113098,105451,102999,103589,119346,107887,107711,107888,114160,110421,109490,107972,117011,86617,86262,113100,86269,113204,114840,86618,86270,103093,117055,107138,114953,115483,105323,110360,117396,101203,116906,117173,114972,114686,86650,118869,119393,113378,103495,107765,108107,117729,117976,117975,110889);

delete quotes.* 
from quotes where instrumentid in (118312,102609,102599,108292,108312,115193,101528,108719,101839,100831,107348,114728,109861,110362,118257,100699,117096,108313,116579,108317,116580,117744,117986,117743,117988,108092,108093,106643,106512,113322,114692,115155,113199,118555,113200,104165,109958,108716,101720,116489,107792,116490,109574,105104,111725,111724,112093,103867,113752,112092,106348,102761,106349,113742,101382,114650,119037,99709,106768,100283,108101,106420,119132,116951,114423,110680,99521,116617,110679,116515,102317,109799,108310,116564,118809,116477,118787,117088,117500,108462,101651,109747,108559,107747,111804,118598,100630,107957,111803,102450,115744,104052,108418,101283,114311,101065,113931,106950,102388,102407,119033,105620,111866,106951,106970,118457,106956,116717,114285,106881,111757,105953,112943,108554,117065,102626,86267,103578,106780,112329,86268,104451,103863,86615,108596,86616,112856,113201,100079,103962,112855,108717,117475,101562,117935,117232,113202,113376,106337,113379,113374,113375,106915,114075,111432,106912,113543,114192,106968,114328,103174,99776,113097,106215,102525,113098,105451,102999,103589,119346,107887,107711,107888,114160,110421,109490,107972,117011,86617,86262,113100,86269,113204,114840,86618,86270,103093,117055,107138,114953,115483,105323,110360,117396,101203,116906,117173,114972,114686,86650,118869,119393,113378,103495,107765,108107,117729,117976,117975,110889);

commit;



begin;

select concat("FROM ",110890," TO ", 103990);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (110890,111243,114685,107263,116147,117730,117131,117728,103873,112099,104868,117114,103846,86288,118675,102497,107104,103901,102352,111771,111770,111242,99525,111893,117116,102227,86651,86296,86652,86289,86653,86297,104456,113318,110222,119250,102200,118896,114922,118573,119358,119323,118460,118534,118837,118523,111838,118452,119380,86290,111337,119347,107864,105551,100037,118298,115752,117796,107686,107866,111446,105589,99889,118608,119330,108985,105870,102634,118039,100265,101522,101958,106323,103543,108359,86654,113336,108705,114505,128899,129014,128900,117731,103869,117978,117977,108105,101113,114385,107911,110947,117510,104109,113510,103142,105699,117169,117858,104089,102645,113820,111125,113851,108017,116643,100209,104068,100466,117945,117946,117949,113093,103056,107199,114611,103066,101164,110999,111001,100962,116776,107629,100351,107736,111000,115951,108427,108470,117470,101165,116929,109227,101361,101364,99963,102193,101560,102569,112987,101412,102660,113003,106416,113137,108325,113135,116954,102797,118456,114955,108998,106470,104370,101540,99838,107989,100154,116225,102230,113731,101015,117193,113251,116224,99835,103453,107552,104557,114245,99689,115272,110048,109296,101742,118148,112499,114477,118453,109019,104771,106192,117015,109459,104468,118831,108233,108228,117672,104009,100300,119007,105523,111365,115554,119262,100596,118881,118067,118353,103990);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (110890,111243,114685,107263,116147,117730,117131,117728,103873,112099,104868,117114,103846,86288,118675,102497,107104,103901,102352,111771,111770,111242,99525,111893,117116,102227,86651,86296,86652,86289,86653,86297,104456,113318,110222,119250,102200,118896,114922,118573,119358,119323,118460,118534,118837,118523,111838,118452,119380,86290,111337,119347,107864,105551,100037,118298,115752,117796,107686,107866,111446,105589,99889,118608,119330,108985,105870,102634,118039,100265,101522,101958,106323,103543,108359,86654,113336,108705,114505,128899,129014,128900,117731,103869,117978,117977,108105,101113,114385,107911,110947,117510,104109,113510,103142,105699,117169,117858,104089,102645,113820,111125,113851,108017,116643,100209,104068,100466,117945,117946,117949,113093,103056,107199,114611,103066,101164,110999,111001,100962,116776,107629,100351,107736,111000,115951,108427,108470,117470,101165,116929,109227,101361,101364,99963,102193,101560,102569,112987,101412,102660,113003,106416,113137,108325,113135,116954,102797,118456,114955,108998,106470,104370,101540,99838,107989,100154,116225,102230,113731,101015,117193,113251,116224,99835,103453,107552,104557,114245,99689,115272,110048,109296,101742,118148,112499,114477,118453,109019,104771,106192,117015,109459,104468,118831,108233,108228,117672,104009,100300,119007,105523,111365,115554,119262,100596,118881,118067,118353,103990);

delete quotes.* 
from quotes where instrumentid in (110890,111243,114685,107263,116147,117730,117131,117728,103873,112099,104868,117114,103846,86288,118675,102497,107104,103901,102352,111771,111770,111242,99525,111893,117116,102227,86651,86296,86652,86289,86653,86297,104456,113318,110222,119250,102200,118896,114922,118573,119358,119323,118460,118534,118837,118523,111838,118452,119380,86290,111337,119347,107864,105551,100037,118298,115752,117796,107686,107866,111446,105589,99889,118608,119330,108985,105870,102634,118039,100265,101522,101958,106323,103543,108359,86654,113336,108705,114505,128899,129014,128900,117731,103869,117978,117977,108105,101113,114385,107911,110947,117510,104109,113510,103142,105699,117169,117858,104089,102645,113820,111125,113851,108017,116643,100209,104068,100466,117945,117946,117949,113093,103056,107199,114611,103066,101164,110999,111001,100962,116776,107629,100351,107736,111000,115951,108427,108470,117470,101165,116929,109227,101361,101364,99963,102193,101560,102569,112987,101412,102660,113003,106416,113137,108325,113135,116954,102797,118456,114955,108998,106470,104370,101540,99838,107989,100154,116225,102230,113731,101015,117193,113251,116224,99835,103453,107552,104557,114245,99689,115272,110048,109296,101742,118148,112499,114477,118453,109019,104771,106192,117015,109459,104468,118831,108233,108228,117672,104009,100300,119007,105523,111365,115554,119262,100596,118881,118067,118353,103990);

commit;



begin;

select concat("FROM ",118068," TO ", 107959);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (118068,116701,119455,116626,118040,104817,104145,110297,116814,110296,100781,104657,104656,99568,100779,114509,102083,109084,111610,108815,118726,118240,119241,119210,118231,118649,118909,118578,103259,118300,112640,118396,108659,86699,111882,102958,111886,108168,104825,109826,113418,108987,110864,110744,110905,108684,114938,112858,110309,107817,107853,104824,110312,110311,100128,100316,106879,101143,113047,115911,102764,113057,113054,103991,108223,104826,110313,100881,110310,116808,101965,106322,101315,100927,118586,114466,116294,116630,117025,104946,117916,113001,112986,104060,110757,99796,99978,86713,86317,115430,107562,117851,116960,119133,113145,119379,101228,105612,113599,113239,113249,113598,113246,113614,113264,103976,105615,101026,102601,115528,86311,115670,86714,101053,110972,105502,104542,116273,107914,112000,103286,116964,107463,105272,102404,103878,112797,116853,118803,118979,118804,119001,111749,107014,108207,113157,103019,103529,110508,105007,114175,108033,111244,105284,110971,114435,106833,105866,108063,113417,100456,113697,99625,118010,102725,117917,100676,100389,112085,86715,86318,117684,86716,102556,105802,111891,116768,102031,100503,103138,105233,112830,112761,105759,117862,101038,118657,108924,104222,103063,114977,101437,107193,115089,103693,103673,107854,102046,110734,103203,102212,110806,116660,119140,115493,114988,114766,86333,86744,107959);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (118068,116701,119455,116626,118040,104817,104145,110297,116814,110296,100781,104657,104656,99568,100779,114509,102083,109084,111610,108815,118726,118240,119241,119210,118231,118649,118909,118578,103259,118300,112640,118396,108659,86699,111882,102958,111886,108168,104825,109826,113418,108987,110864,110744,110905,108684,114938,112858,110309,107817,107853,104824,110312,110311,100128,100316,106879,101143,113047,115911,102764,113057,113054,103991,108223,104826,110313,100881,110310,116808,101965,106322,101315,100927,118586,114466,116294,116630,117025,104946,117916,113001,112986,104060,110757,99796,99978,86713,86317,115430,107562,117851,116960,119133,113145,119379,101228,105612,113599,113239,113249,113598,113246,113614,113264,103976,105615,101026,102601,115528,86311,115670,86714,101053,110972,105502,104542,116273,107914,112000,103286,116964,107463,105272,102404,103878,112797,116853,118803,118979,118804,119001,111749,107014,108207,113157,103019,103529,110508,105007,114175,108033,111244,105284,110971,114435,106833,105866,108063,113417,100456,113697,99625,118010,102725,117917,100676,100389,112085,86715,86318,117684,86716,102556,105802,111891,116768,102031,100503,103138,105233,112830,112761,105759,117862,101038,118657,108924,104222,103063,114977,101437,107193,115089,103693,103673,107854,102046,110734,103203,102212,110806,116660,119140,115493,114988,114766,86333,86744,107959);

delete quotes.* 
from quotes where instrumentid in (118068,116701,119455,116626,118040,104817,104145,110297,116814,110296,100781,104657,104656,99568,100779,114509,102083,109084,111610,108815,118726,118240,119241,119210,118231,118649,118909,118578,103259,118300,112640,118396,108659,86699,111882,102958,111886,108168,104825,109826,113418,108987,110864,110744,110905,108684,114938,112858,110309,107817,107853,104824,110312,110311,100128,100316,106879,101143,113047,115911,102764,113057,113054,103991,108223,104826,110313,100881,110310,116808,101965,106322,101315,100927,118586,114466,116294,116630,117025,104946,117916,113001,112986,104060,110757,99796,99978,86713,86317,115430,107562,117851,116960,119133,113145,119379,101228,105612,113599,113239,113249,113598,113246,113614,113264,103976,105615,101026,102601,115528,86311,115670,86714,101053,110972,105502,104542,116273,107914,112000,103286,116964,107463,105272,102404,103878,112797,116853,118803,118979,118804,119001,111749,107014,108207,113157,103019,103529,110508,105007,114175,108033,111244,105284,110971,114435,106833,105866,108063,113417,100456,113697,99625,118010,102725,117917,100676,100389,112085,86715,86318,117684,86716,102556,105802,111891,116768,102031,100503,103138,105233,112830,112761,105759,117862,101038,118657,108924,104222,103063,114977,101437,107193,115089,103693,103673,107854,102046,110734,103203,102212,110806,116660,119140,115493,114988,114766,86333,86744,107959);

commit;



begin;

select concat("FROM ",86745," TO ", 113042);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (86745,115783,118770,104827,101669,100492,113774,101326,113773,107072,114514,115279,110661,107068,103352,105240,116473,107419,107753,110375,105700,113404,100388,114617,115187,114232,103476,119402,107500,111142,108527,114782,86746,99368,112793,102333,101111,109208,106383,86345,86747,86334,119182,118917,118333,118987,86748,118654,119171,118568,86749,119443,86750,119263,118597,86346,118894,119131,117664,86347,118551,113090,119444,117647,119255,110202,110201,100236,110199,116601,116184,102595,118814,100814,105469,102172,109910,99385,99457,117909,114935,115119,116319,114137,105769,106164,114951,116603,103439,115631,101362,104218,118463,109218,117064,100576,86773,86348,111571,111303,119446,111594,118403,118984,119419,118484,111027,100064,86335,86774,118503,118205,117160,102591,116941,119205,118720,116936,86349,118398,116943,113125,116935,116986,100161,113762,119338,104685,106389,111818,128912,103458,111108,111067,100680,109205,109199,102632,104945,117163,129035,117018,129026,105380,129030,103511,128845,106065,119175,107321,112396,114917,113435,86336,118334,114954,118676,101325,102034,109845,114239,102271,110755,100855,113889,105634,106392,114237,105614,112174,106071,103622,102883,102472,114521,118419,118381,118518,118856,118778,119281,118682,119129,118974,118975,119420,118824,102216,118744,118742,105787,117300,113891,104147,106862,113892,101304,100974,100635,113042);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (86745,115783,118770,104827,101669,100492,113774,101326,113773,107072,114514,115279,110661,107068,103352,105240,116473,107419,107753,110375,105700,113404,100388,114617,115187,114232,103476,119402,107500,111142,108527,114782,86746,99368,112793,102333,101111,109208,106383,86345,86747,86334,119182,118917,118333,118987,86748,118654,119171,118568,86749,119443,86750,119263,118597,86346,118894,119131,117664,86347,118551,113090,119444,117647,119255,110202,110201,100236,110199,116601,116184,102595,118814,100814,105469,102172,109910,99385,99457,117909,114935,115119,116319,114137,105769,106164,114951,116603,103439,115631,101362,104218,118463,109218,117064,100576,86773,86348,111571,111303,119446,111594,118403,118984,119419,118484,111027,100064,86335,86774,118503,118205,117160,102591,116941,119205,118720,116936,86349,118398,116943,113125,116935,116986,100161,113762,119338,104685,106389,111818,128912,103458,111108,111067,100680,109205,109199,102632,104945,117163,129035,117018,129026,105380,129030,103511,128845,106065,119175,107321,112396,114917,113435,86336,118334,114954,118676,101325,102034,109845,114239,102271,110755,100855,113889,105634,106392,114237,105614,112174,106071,103622,102883,102472,114521,118419,118381,118518,118856,118778,119281,118682,119129,118974,118975,119420,118824,102216,118744,118742,105787,117300,113891,104147,106862,113892,101304,100974,100635,113042);

delete quotes.* 
from quotes where instrumentid in (86745,115783,118770,104827,101669,100492,113774,101326,113773,107072,114514,115279,110661,107068,103352,105240,116473,107419,107753,110375,105700,113404,100388,114617,115187,114232,103476,119402,107500,111142,108527,114782,86746,99368,112793,102333,101111,109208,106383,86345,86747,86334,119182,118917,118333,118987,86748,118654,119171,118568,86749,119443,86750,119263,118597,86346,118894,119131,117664,86347,118551,113090,119444,117647,119255,110202,110201,100236,110199,116601,116184,102595,118814,100814,105469,102172,109910,99385,99457,117909,114935,115119,116319,114137,105769,106164,114951,116603,103439,115631,101362,104218,118463,109218,117064,100576,86773,86348,111571,111303,119446,111594,118403,118984,119419,118484,111027,100064,86335,86774,118503,118205,117160,102591,116941,119205,118720,116936,86349,118398,116943,113125,116935,116986,100161,113762,119338,104685,106389,111818,128912,103458,111108,111067,100680,109205,109199,102632,104945,117163,129035,117018,129026,105380,129030,103511,128845,106065,119175,107321,112396,114917,113435,86336,118334,114954,118676,101325,102034,109845,114239,102271,110755,100855,113889,105634,106392,114237,105614,112174,106071,103622,102883,102472,114521,118419,118381,118518,118856,118778,119281,118682,119129,118974,118975,119420,118824,102216,118744,118742,105787,117300,113891,104147,106862,113892,101304,100974,100635,113042);

commit;



begin;

select concat("FROM ",100857," TO ", 108787);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (100857,112532,101750,104144,86794,86365,104991,102086,117164,129630,86366,104296,106923,119074,114265,103122,114149,115567,118722,119236,118724,119260,118723,114960,114958,119371,118540,109441,112242,100097,111839,118502,118319,119418,119334,116895,118882,116894,113906,118572,119406,101285,112648,102648,108664,108049,116299,103635,109081,109242,99935,116300,108051,116302,112520,115165,106281,101593,110372,114267,102430,111160,116305,119169,113789,99642,109839,105483,101368,104241,108770,109619,109621,117920,109620,104061,109873,111778,111777,111780,113796,103341,117775,117765,117960,116970,100106,113272,113624,116041,107400,116452,112451,101265,111186,116695,109164,129468,114718,103449,129500,103718,106801,114583,118045,129501,108663,102028,86814,129370,105224,111162,86369,86815,86367,86816,86370,111784,111781,99703,105554,111754,118408,101396,113899,111940,116021,104907,118743,104335,106311,111980,105785,110754,112136,105924,110123,104840,107799,114719,112961,105867,119360,110338,110446,118964,119237,112234,115558,118920,102405,100298,119166,119431,118341,118313,102401,105125,110562,106972,114335,119341,99662,101355,103541,118863,119449,86368,119409,119407,118872,118532,119454,119316,102557,109842,116175,103579,129612,103502,113730,111858,118925,119092,117165,100497,113791,106808,113551,119063,118782,102263,119141,103182,115158,108544,115838,107811,107783,108787);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (100857,112532,101750,104144,86794,86365,104991,102086,117164,129630,86366,104296,106923,119074,114265,103122,114149,115567,118722,119236,118724,119260,118723,114960,114958,119371,118540,109441,112242,100097,111839,118502,118319,119418,119334,116895,118882,116894,113906,118572,119406,101285,112648,102648,108664,108049,116299,103635,109081,109242,99935,116300,108051,116302,112520,115165,106281,101593,110372,114267,102430,111160,116305,119169,113789,99642,109839,105483,101368,104241,108770,109619,109621,117920,109620,104061,109873,111778,111777,111780,113796,103341,117775,117765,117960,116970,100106,113272,113624,116041,107400,116452,112451,101265,111186,116695,109164,129468,114718,103449,129500,103718,106801,114583,118045,129501,108663,102028,86814,129370,105224,111162,86369,86815,86367,86816,86370,111784,111781,99703,105554,111754,118408,101396,113899,111940,116021,104907,118743,104335,106311,111980,105785,110754,112136,105924,110123,104840,107799,114719,112961,105867,119360,110338,110446,118964,119237,112234,115558,118920,102405,100298,119166,119431,118341,118313,102401,105125,110562,106972,114335,119341,99662,101355,103541,118863,119449,86368,119409,119407,118872,118532,119454,119316,102557,109842,116175,103579,129612,103502,113730,111858,118925,119092,117165,100497,113791,106808,113551,119063,118782,102263,119141,103182,115158,108544,115838,107811,107783,108787);

delete quotes.* 
from quotes where instrumentid in (100857,112532,101750,104144,86794,86365,104991,102086,117164,129630,86366,104296,106923,119074,114265,103122,114149,115567,118722,119236,118724,119260,118723,114960,114958,119371,118540,109441,112242,100097,111839,118502,118319,119418,119334,116895,118882,116894,113906,118572,119406,101285,112648,102648,108664,108049,116299,103635,109081,109242,99935,116300,108051,116302,112520,115165,106281,101593,110372,114267,102430,111160,116305,119169,113789,99642,109839,105483,101368,104241,108770,109619,109621,117920,109620,104061,109873,111778,111777,111780,113796,103341,117775,117765,117960,116970,100106,113272,113624,116041,107400,116452,112451,101265,111186,116695,109164,129468,114718,103449,129500,103718,106801,114583,118045,129501,108663,102028,86814,129370,105224,111162,86369,86815,86367,86816,86370,111784,111781,99703,105554,111754,118408,101396,113899,111940,116021,104907,118743,104335,106311,111980,105785,110754,112136,105924,110123,104840,107799,114719,112961,105867,119360,110338,110446,118964,119237,112234,115558,118920,102405,100298,119166,119431,118341,118313,102401,105125,110562,106972,114335,119341,99662,101355,103541,118863,119449,86368,119409,119407,118872,118532,119454,119316,102557,109842,116175,103579,129612,103502,113730,111858,118925,119092,117165,100497,113791,106808,113551,119063,118782,102263,119141,103182,115158,108544,115838,107811,107783,108787);

commit;



begin;

select concat("FROM ",118086," TO ", 118992);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (118086,118910,102652,99489,103184,115157,107174,118418,118674,113729,119433,118472,118939,119439,99851,100197,113763,113764,100963,105778,110751,118302,111823,115329,114849,114848,99404,114746,103496,102666,117959,107784,115942,103311,118638,118636,119024,118614,111739,118997,119029,118615,119026,119028,119025,118637,118727,119242,118320,119417,119300,118822,118736,118259,118886,118887,119020,118885,119168,119244,118728,119084,118448,119243,118382,119293,119159,118571,118392,119351,118437,119057,118628,118993,118510,119198,118862,118861,119448,118860,118962,118584,118413,118228,119061,118244,118211,118530,119047,118617,118639,118618,105909,118616,119042,117222,118364,118709,111163,111167,112103,114464,111056,114463,102402,117843,103880,102902,100738,109857,107081,113101,113091,112249,106201,105706,108678,114723,104355,113108,112261,100232,105808,106208,112260,106622,107542,116019,114807,115228,115229,115546,109844,106227,102105,110633,117677,102157,103018,99886,100658,103406,86389,118451,113265,114127,114126,113615,111173,100135,108892,104453,99988,104448,111577,108893,105229,109244,103780,114711,117937,117936,118143,104752,104382,118694,108100,103810,117971,113164,118356,111508,117135,118960,119040,119400,118606,99821,86878,118324,117545,107459,110235,109834,114326,119180,104358,115139,119373,112508,116649,118641,118640,118620,118621,118619,118509,119048,119055,118992);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (118086,118910,102652,99489,103184,115157,107174,118418,118674,113729,119433,118472,118939,119439,99851,100197,113763,113764,100963,105778,110751,118302,111823,115329,114849,114848,99404,114746,103496,102666,117959,107784,115942,103311,118638,118636,119024,118614,111739,118997,119029,118615,119026,119028,119025,118637,118727,119242,118320,119417,119300,118822,118736,118259,118886,118887,119020,118885,119168,119244,118728,119084,118448,119243,118382,119293,119159,118571,118392,119351,118437,119057,118628,118993,118510,119198,118862,118861,119448,118860,118962,118584,118413,118228,119061,118244,118211,118530,119047,118617,118639,118618,105909,118616,119042,117222,118364,118709,111163,111167,112103,114464,111056,114463,102402,117843,103880,102902,100738,109857,107081,113101,113091,112249,106201,105706,108678,114723,104355,113108,112261,100232,105808,106208,112260,106622,107542,116019,114807,115228,115229,115546,109844,106227,102105,110633,117677,102157,103018,99886,100658,103406,86389,118451,113265,114127,114126,113615,111173,100135,108892,104453,99988,104448,111577,108893,105229,109244,103780,114711,117937,117936,118143,104752,104382,118694,108100,103810,117971,113164,118356,111508,117135,118960,119040,119400,118606,99821,86878,118324,117545,107459,110235,109834,114326,119180,104358,115139,119373,112508,116649,118641,118640,118620,118621,118619,118509,119048,119055,118992);

delete quotes.* 
from quotes where instrumentid in (118086,118910,102652,99489,103184,115157,107174,118418,118674,113729,119433,118472,118939,119439,99851,100197,113763,113764,100963,105778,110751,118302,111823,115329,114849,114848,99404,114746,103496,102666,117959,107784,115942,103311,118638,118636,119024,118614,111739,118997,119029,118615,119026,119028,119025,118637,118727,119242,118320,119417,119300,118822,118736,118259,118886,118887,119020,118885,119168,119244,118728,119084,118448,119243,118382,119293,119159,118571,118392,119351,118437,119057,118628,118993,118510,119198,118862,118861,119448,118860,118962,118584,118413,118228,119061,118244,118211,118530,119047,118617,118639,118618,105909,118616,119042,117222,118364,118709,111163,111167,112103,114464,111056,114463,102402,117843,103880,102902,100738,109857,107081,113101,113091,112249,106201,105706,108678,114723,104355,113108,112261,100232,105808,106208,112260,106622,107542,116019,114807,115228,115229,115546,109844,106227,102105,110633,117677,102157,103018,99886,100658,103406,86389,118451,113265,114127,114126,113615,111173,100135,108892,104453,99988,104448,111577,108893,105229,109244,103780,114711,117937,117936,118143,104752,104382,118694,108100,103810,117971,113164,118356,111508,117135,118960,119040,119400,118606,99821,86878,118324,117545,107459,110235,109834,114326,119180,104358,115139,119373,112508,116649,118641,118640,118620,118621,118619,118509,119048,119055,118992);

commit;



begin;

select concat("FROM ",118627," TO ", 99953);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (118627,102952,119056,118821,118575,119361,99797,104585,105823,100234,111053,105747,112115,105744,105693,102551,112318,108704,119130,118252,86879,109565,111574,119009,119321,106989,118850,109856,118965,108889,103987,118590,119424,119060,111409,102561,108135,113278,113554,118543,128893,105622,119256,112668,105717,106329,118365,118737,105798,116992,119279,107638,119287,118172,114143,114144,104871,110278,103907,108996,103033,116478,106807,118740,118697,113305,119111,99476,118981,118858,119278,118973,114021,118383,118976,106740,119101,119322,112437,104411,105689,116821,114268,112278,103237,102242,119023,119328,118622,118596,119022,118394,118256,119329,106185,104233,118387,101951,114450,119220,118972,118595,118798,119106,119070,118222,118591,118265,118377,118926,118562,119154,118371,119261,119232,118755,114796,118754,119215,118849,118337,119335,119370,118554,118921,118545,118544,119277,118906,118854,118825,118490,103733,118345,119276,118758,118690,119301,119224,118289,118236,103546,118820,86897,108111,101230,118816,118716,118281,119285,119266,119267,119268,119286,118215,100964,118344,118263,106119,115112,119372,100733,118488,108283,129611,119272,115839,119345,119103,111615,101390,111854,107780,118711,118283,119109,118670,111864,119312,114467,111066,105034,111054,105041,107830,111614,105649,105880,115991,110804,109595,118471,118282,118310,117778,116945,113141,116947,102796,99953);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (118627,102952,119056,118821,118575,119361,99797,104585,105823,100234,111053,105747,112115,105744,105693,102551,112318,108704,119130,118252,86879,109565,111574,119009,119321,106989,118850,109856,118965,108889,103987,118590,119424,119060,111409,102561,108135,113278,113554,118543,128893,105622,119256,112668,105717,106329,118365,118737,105798,116992,119279,107638,119287,118172,114143,114144,104871,110278,103907,108996,103033,116478,106807,118740,118697,113305,119111,99476,118981,118858,119278,118973,114021,118383,118976,106740,119101,119322,112437,104411,105689,116821,114268,112278,103237,102242,119023,119328,118622,118596,119022,118394,118256,119329,106185,104233,118387,101951,114450,119220,118972,118595,118798,119106,119070,118222,118591,118265,118377,118926,118562,119154,118371,119261,119232,118755,114796,118754,119215,118849,118337,119335,119370,118554,118921,118545,118544,119277,118906,118854,118825,118490,103733,118345,119276,118758,118690,119301,119224,118289,118236,103546,118820,86897,108111,101230,118816,118716,118281,119285,119266,119267,119268,119286,118215,100964,118344,118263,106119,115112,119372,100733,118488,108283,129611,119272,115839,119345,119103,111615,101390,111854,107780,118711,118283,119109,118670,111864,119312,114467,111066,105034,111054,105041,107830,111614,105649,105880,115991,110804,109595,118471,118282,118310,117778,116945,113141,116947,102796,99953);

delete quotes.* 
from quotes where instrumentid in (118627,102952,119056,118821,118575,119361,99797,104585,105823,100234,111053,105747,112115,105744,105693,102551,112318,108704,119130,118252,86879,109565,111574,119009,119321,106989,118850,109856,118965,108889,103987,118590,119424,119060,111409,102561,108135,113278,113554,118543,128893,105622,119256,112668,105717,106329,118365,118737,105798,116992,119279,107638,119287,118172,114143,114144,104871,110278,103907,108996,103033,116478,106807,118740,118697,113305,119111,99476,118981,118858,119278,118973,114021,118383,118976,106740,119101,119322,112437,104411,105689,116821,114268,112278,103237,102242,119023,119328,118622,118596,119022,118394,118256,119329,106185,104233,118387,101951,114450,119220,118972,118595,118798,119106,119070,118222,118591,118265,118377,118926,118562,119154,118371,119261,119232,118755,114796,118754,119215,118849,118337,119335,119370,118554,118921,118545,118544,119277,118906,118854,118825,118490,103733,118345,119276,118758,118690,119301,119224,118289,118236,103546,118820,86897,108111,101230,118816,118716,118281,119285,119266,119267,119268,119286,118215,100964,118344,118263,106119,115112,119372,100733,118488,108283,129611,119272,115839,119345,119103,111615,101390,111854,107780,118711,118283,119109,118670,111864,119312,114467,111066,105034,111054,105041,107830,111614,105649,105880,115991,110804,109595,118471,118282,118310,117778,116945,113141,116947,102796,99953);

commit;



begin;

select concat("FROM ",106368," TO ", 129515);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (106368,102444,119297,119219,118761,118219,118349,118692,118799,118710,118332,119304,119112,118478,119389,119305,118646,118922,105678,112403,106166,111316,111317,107691,111697,111698,112047,115766,107694,107874,99498,100240,118592,103360,112240,118411,118712,111403,105740,111165,109601,109346,109603,100012,116939,116940,86929,113972,113973,106160,103375,106159,105495,105275,105140,117152,111119,107379,115903,116261,115481,108612,104016,108611,113034,104582,116605,99416,108793,118089,86946,86439,86947,86423,86948,86440,86949,86424,86950,106630,102344,106629,107930,115433,115434,102490,102779,100807,100762,115135,101303,86425,86951,86441,112720,109602,114403,100850,112444,102164,108639,114603,103531,109098,104853,86442,86952,86426,118665,118322,102431,118370,118481,114221,118449,118084,119430,118878,103980,115913,118536,118019,118669,112540,106606,112703,106748,114561,104476,129046,118400,118401,102423,100885,118203,118969,118970,118810,119376,108328,105704,118717,119041,108677,118315,119216,118358,118379,118539,118756,119231,118268,118718,119314,119374,119091,118904,118414,118853,118908,119012,102499,119459,119155,101120,118932,118968,118309,118217,115351,118271,107162,102190,107403,107791,118895,118898,118897,118352,119388,119137,119083,119359,118601,118708,118255,99869,119187,118759,118991,118469,114364,118658,114743,101297,129470,129514,118200,129471,129515);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (106368,102444,119297,119219,118761,118219,118349,118692,118799,118710,118332,119304,119112,118478,119389,119305,118646,118922,105678,112403,106166,111316,111317,107691,111697,111698,112047,115766,107694,107874,99498,100240,118592,103360,112240,118411,118712,111403,105740,111165,109601,109346,109603,100012,116939,116940,86929,113972,113973,106160,103375,106159,105495,105275,105140,117152,111119,107379,115903,116261,115481,108612,104016,108611,113034,104582,116605,99416,108793,118089,86946,86439,86947,86423,86948,86440,86949,86424,86950,106630,102344,106629,107930,115433,115434,102490,102779,100807,100762,115135,101303,86425,86951,86441,112720,109602,114403,100850,112444,102164,108639,114603,103531,109098,104853,86442,86952,86426,118665,118322,102431,118370,118481,114221,118449,118084,119430,118878,103980,115913,118536,118019,118669,112540,106606,112703,106748,114561,104476,129046,118400,118401,102423,100885,118203,118969,118970,118810,119376,108328,105704,118717,119041,108677,118315,119216,118358,118379,118539,118756,119231,118268,118718,119314,119374,119091,118904,118414,118853,118908,119012,102499,119459,119155,101120,118932,118968,118309,118217,115351,118271,107162,102190,107403,107791,118895,118898,118897,118352,119388,119137,119083,119359,118601,118708,118255,99869,119187,118759,118991,118469,114364,118658,114743,101297,129470,129514,118200,129471,129515);

delete quotes.* 
from quotes where instrumentid in (106368,102444,119297,119219,118761,118219,118349,118692,118799,118710,118332,119304,119112,118478,119389,119305,118646,118922,105678,112403,106166,111316,111317,107691,111697,111698,112047,115766,107694,107874,99498,100240,118592,103360,112240,118411,118712,111403,105740,111165,109601,109346,109603,100012,116939,116940,86929,113972,113973,106160,103375,106159,105495,105275,105140,117152,111119,107379,115903,116261,115481,108612,104016,108611,113034,104582,116605,99416,108793,118089,86946,86439,86947,86423,86948,86440,86949,86424,86950,106630,102344,106629,107930,115433,115434,102490,102779,100807,100762,115135,101303,86425,86951,86441,112720,109602,114403,100850,112444,102164,108639,114603,103531,109098,104853,86442,86952,86426,118665,118322,102431,118370,118481,114221,118449,118084,119430,118878,103980,115913,118536,118019,118669,112540,106606,112703,106748,114561,104476,129046,118400,118401,102423,100885,118203,118969,118970,118810,119376,108328,105704,118717,119041,108677,118315,119216,118358,118379,118539,118756,119231,118268,118718,119314,119374,119091,118904,118414,118853,118908,119012,102499,119459,119155,101120,118932,118968,118309,118217,115351,118271,107162,102190,107403,107791,118895,118898,118897,118352,119388,119137,119083,119359,118601,118708,118255,99869,119187,118759,118991,118469,114364,118658,114743,101297,129470,129514,118200,129471,129515);

commit;



begin;

select concat("FROM ",119209," TO ", 112576);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (119209,114600,118454,118254,118703,101996,110028,103493,118660,118235,118662,118625,118873,118911,118930,118931,118366,119399,118749,118439,119320,104974,119017,119015,105590,118399,119016,118760,119302,100621,103190,102860,104328,117542,108541,109596,109344,104905,104675,99654,104828,119211,118511,119021,118225,119352,118329,119102,103556,119306,99560,118695,114374,119192,99492,99614,119442,119398,119416,118705,110280,119315,118538,119382,118838,119369,118767,118561,118238,118294,99989,101712,100268,108344,108099,119078,99679,109916,110215,105042,118823,118464,117791,115819,119193,117302,119247,107591,118208,118357,118757,119384,118513,119275,114444,114441,108394,105650,115133,118815,118479,118476,118623,115841,118762,118351,118848,129614,118741,119378,118508,118826,119397,118504,118422,118941,103525,102268,111473,106293,108330,119387,118498,118500,119336,118812,118874,118519,118517,119434,108421,119039,119014,118813,119085,119383,118369,103091,104440,104443,115516,105214,102425,86987,118576,118581,119458,118689,118915,86988,118971,118839,116774,99626,119194,106612,119035,119460,119038,118632,119079,119196,109442,101367,108676,105705,108286,113496,110332,101251,109193,109767,109770,109768,118339,102070,119181,118671,113776,118470,119173,106864,118937,118792,119350,118610,87002,86485,87003,106494,117794,112609,100372,118943,110164,118565,115327,109011,119324,112576);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (119209,114600,118454,118254,118703,101996,110028,103493,118660,118235,118662,118625,118873,118911,118930,118931,118366,119399,118749,118439,119320,104974,119017,119015,105590,118399,119016,118760,119302,100621,103190,102860,104328,117542,108541,109596,109344,104905,104675,99654,104828,119211,118511,119021,118225,119352,118329,119102,103556,119306,99560,118695,114374,119192,99492,99614,119442,119398,119416,118705,110280,119315,118538,119382,118838,119369,118767,118561,118238,118294,99989,101712,100268,108344,108099,119078,99679,109916,110215,105042,118823,118464,117791,115819,119193,117302,119247,107591,118208,118357,118757,119384,118513,119275,114444,114441,108394,105650,115133,118815,118479,118476,118623,115841,118762,118351,118848,129614,118741,119378,118508,118826,119397,118504,118422,118941,103525,102268,111473,106293,108330,119387,118498,118500,119336,118812,118874,118519,118517,119434,108421,119039,119014,118813,119085,119383,118369,103091,104440,104443,115516,105214,102425,86987,118576,118581,119458,118689,118915,86988,118971,118839,116774,99626,119194,106612,119035,119460,119038,118632,119079,119196,109442,101367,108676,105705,108286,113496,110332,101251,109193,109767,109770,109768,118339,102070,119181,118671,113776,118470,119173,106864,118937,118792,119350,118610,87002,86485,87003,106494,117794,112609,100372,118943,110164,118565,115327,109011,119324,112576);

delete quotes.* 
from quotes where instrumentid in (119209,114600,118454,118254,118703,101996,110028,103493,118660,118235,118662,118625,118873,118911,118930,118931,118366,119399,118749,118439,119320,104974,119017,119015,105590,118399,119016,118760,119302,100621,103190,102860,104328,117542,108541,109596,109344,104905,104675,99654,104828,119211,118511,119021,118225,119352,118329,119102,103556,119306,99560,118695,114374,119192,99492,99614,119442,119398,119416,118705,110280,119315,118538,119382,118838,119369,118767,118561,118238,118294,99989,101712,100268,108344,108099,119078,99679,109916,110215,105042,118823,118464,117791,115819,119193,117302,119247,107591,118208,118357,118757,119384,118513,119275,114444,114441,108394,105650,115133,118815,118479,118476,118623,115841,118762,118351,118848,129614,118741,119378,118508,118826,119397,118504,118422,118941,103525,102268,111473,106293,108330,119387,118498,118500,119336,118812,118874,118519,118517,119434,108421,119039,119014,118813,119085,119383,118369,103091,104440,104443,115516,105214,102425,86987,118576,118581,119458,118689,118915,86988,118971,118839,116774,99626,119194,106612,119035,119460,119038,118632,119079,119196,109442,101367,108676,105705,108286,113496,110332,101251,109193,109767,109770,109768,118339,102070,119181,118671,113776,118470,119173,106864,118937,118792,119350,118610,87002,86485,87003,106494,117794,112609,100372,118943,110164,118565,115327,109011,119324,112576);

commit;



begin;

select concat("FROM ",119348," TO ", 101473);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (119348,118766,112015,104114,118541,118542,118900,118552,105985,106611,106616,106605,114362,114363,114735,119318,118903,119292,113179,107683,103181,106982,100219,86486,118827,113144,100509,116965,108615,113711,119258,110435,118279,114902,107438,118999,114761,118989,114759,119339,118284,107416,101713,118528,118916,118526,118373,104829,118305,119075,118447,105298,118707,118317,87004,86487,118868,118867,118360,108960,103499,109880,102219,103151,111211,114400,113915,113574,114293,103224,107997,110368,102149,110230,107938,102633,101737,108945,116413,117206,101504,117030,102793,116944,106538,108524,117933,101944,110231,116409,118790,115840,115321,101505,117029,110847,110531,107585,117129,101420,103872,112485,116985,86488,108324,106418,108953,104172,107511,118491,102483,117161,118777,111851,101748,107380,104939,118226,118301,118406,118407,118600,118935,118444,118288,118725,119167,104672,119051,118218,119191,118280,100872,110217,109915,110218,109918,104641,110012,86505,107602,118563,110274,118564,112696,104806,115824,110640,107754,113381,86501,86506,107763,102654,119342,118942,105947,102698,104361,117854,112649,100806,102477,102478,116431,118480,118783,118698,118229,118336,118852,118409,118506,118489,118883,118582,115804,117777,119090,119319,118612,99583,108284,104458,101292,112334,119186,118866,117748,112352,118423,118734,113829,104442,100725,104611,108956,108954,117220,101473);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (119348,118766,112015,104114,118541,118542,118900,118552,105985,106611,106616,106605,114362,114363,114735,119318,118903,119292,113179,107683,103181,106982,100219,86486,118827,113144,100509,116965,108615,113711,119258,110435,118279,114902,107438,118999,114761,118989,114759,119339,118284,107416,101713,118528,118916,118526,118373,104829,118305,119075,118447,105298,118707,118317,87004,86487,118868,118867,118360,108960,103499,109880,102219,103151,111211,114400,113915,113574,114293,103224,107997,110368,102149,110230,107938,102633,101737,108945,116413,117206,101504,117030,102793,116944,106538,108524,117933,101944,110231,116409,118790,115840,115321,101505,117029,110847,110531,107585,117129,101420,103872,112485,116985,86488,108324,106418,108953,104172,107511,118491,102483,117161,118777,111851,101748,107380,104939,118226,118301,118406,118407,118600,118935,118444,118288,118725,119167,104672,119051,118218,119191,118280,100872,110217,109915,110218,109918,104641,110012,86505,107602,118563,110274,118564,112696,104806,115824,110640,107754,113381,86501,86506,107763,102654,119342,118942,105947,102698,104361,117854,112649,100806,102477,102478,116431,118480,118783,118698,118229,118336,118852,118409,118506,118489,118883,118582,115804,117777,119090,119319,118612,99583,108284,104458,101292,112334,119186,118866,117748,112352,118423,118734,113829,104442,100725,104611,108956,108954,117220,101473);

delete quotes.* 
from quotes where instrumentid in (119348,118766,112015,104114,118541,118542,118900,118552,105985,106611,106616,106605,114362,114363,114735,119318,118903,119292,113179,107683,103181,106982,100219,86486,118827,113144,100509,116965,108615,113711,119258,110435,118279,114902,107438,118999,114761,118989,114759,119339,118284,107416,101713,118528,118916,118526,118373,104829,118305,119075,118447,105298,118707,118317,87004,86487,118868,118867,118360,108960,103499,109880,102219,103151,111211,114400,113915,113574,114293,103224,107997,110368,102149,110230,107938,102633,101737,108945,116413,117206,101504,117030,102793,116944,106538,108524,117933,101944,110231,116409,118790,115840,115321,101505,117029,110847,110531,107585,117129,101420,103872,112485,116985,86488,108324,106418,108953,104172,107511,118491,102483,117161,118777,111851,101748,107380,104939,118226,118301,118406,118407,118600,118935,118444,118288,118725,119167,104672,119051,118218,119191,118280,100872,110217,109915,110218,109918,104641,110012,86505,107602,118563,110274,118564,112696,104806,115824,110640,107754,113381,86501,86506,107763,102654,119342,118942,105947,102698,104361,117854,112649,100806,102477,102478,116431,118480,118783,118698,118229,118336,118852,118409,118506,118489,118883,118582,115804,117777,119090,119319,118612,99583,108284,104458,101292,112334,119186,118866,117748,112352,118423,118734,113829,104442,100725,104611,108956,108954,117220,101473);

commit;



begin;

select concat("FROM ",104640," TO ", 116578);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (104640,118946,118945,118664,113788,107428,112577,105003,100160,118879,102025,112158,108071,109604,118374,119100,119067,86529,119289,118293,86520,118899,118985,119054,118273,119011,105605,118603,109066,102144,112105,104790,109874,105075,105076,102801,106421,113148,108335,100013,118599,118212,118405,129201,128918,128850,128919,100461,112698,105838,100486,116427,103021,102815,114383,100247,129364,129408,129380,129409,104047,103710,117399,113908,101171,117389,118830,118836,112424,119189,118501,119429,108466,119034,118292,112108,112109,105465,113877,100514,118021,104900,86521,86530,112605,112602,110212,100609,86522,86531,108522,86532,99880,108930,102289,101716,105713,112659,103191,108834,118197,118204,99382,101011,114255,102917,114257,106400,118177,110171,110169,105898,108370,117640,108326,108327,108103,104446,117166,116898,112466,111912,105618,119246,101777,112685,100766,103538,112707,119294,118735,118747,117057,117058,117059,105492,110154,105095,115761,110716,105074,86547,86543,86548,116980,116668,116462,113843,103908,110901,119153,116698,118350,119230,114780,119203,119202,119229,118696,119195,114369,118327,118306,118404,118395,106943,118246,99468,104212,107777,99714,116984,111333,86549,109933,109937,110082,100915,101865,100914,110556,104922,111984,117299,108515,111763,112575,103783,116730,114730,119344,108088,117110,109611,109645,103489,101807,99802,101135,116578);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (104640,118946,118945,118664,113788,107428,112577,105003,100160,118879,102025,112158,108071,109604,118374,119100,119067,86529,119289,118293,86520,118899,118985,119054,118273,119011,105605,118603,109066,102144,112105,104790,109874,105075,105076,102801,106421,113148,108335,100013,118599,118212,118405,129201,128918,128850,128919,100461,112698,105838,100486,116427,103021,102815,114383,100247,129364,129408,129380,129409,104047,103710,117399,113908,101171,117389,118830,118836,112424,119189,118501,119429,108466,119034,118292,112108,112109,105465,113877,100514,118021,104900,86521,86530,112605,112602,110212,100609,86522,86531,108522,86532,99880,108930,102289,101716,105713,112659,103191,108834,118197,118204,99382,101011,114255,102917,114257,106400,118177,110171,110169,105898,108370,117640,108326,108327,108103,104446,117166,116898,112466,111912,105618,119246,101777,112685,100766,103538,112707,119294,118735,118747,117057,117058,117059,105492,110154,105095,115761,110716,105074,86547,86543,86548,116980,116668,116462,113843,103908,110901,119153,116698,118350,119230,114780,119203,119202,119229,118696,119195,114369,118327,118306,118404,118395,106943,118246,99468,104212,107777,99714,116984,111333,86549,109933,109937,110082,100915,101865,100914,110556,104922,111984,117299,108515,111763,112575,103783,116730,114730,119344,108088,117110,109611,109645,103489,101807,99802,101135,116578);

delete quotes.* 
from quotes where instrumentid in (104640,118946,118945,118664,113788,107428,112577,105003,100160,118879,102025,112158,108071,109604,118374,119100,119067,86529,119289,118293,86520,118899,118985,119054,118273,119011,105605,118603,109066,102144,112105,104790,109874,105075,105076,102801,106421,113148,108335,100013,118599,118212,118405,129201,128918,128850,128919,100461,112698,105838,100486,116427,103021,102815,114383,100247,129364,129408,129380,129409,104047,103710,117399,113908,101171,117389,118830,118836,112424,119189,118501,119429,108466,119034,118292,112108,112109,105465,113877,100514,118021,104900,86521,86530,112605,112602,110212,100609,86522,86531,108522,86532,99880,108930,102289,101716,105713,112659,103191,108834,118197,118204,99382,101011,114255,102917,114257,106400,118177,110171,110169,105898,108370,117640,108326,108327,108103,104446,117166,116898,112466,111912,105618,119246,101777,112685,100766,103538,112707,119294,118735,118747,117057,117058,117059,105492,110154,105095,115761,110716,105074,86547,86543,86548,116980,116668,116462,113843,103908,110901,119153,116698,118350,119230,114780,119203,119202,119229,118696,119195,114369,118327,118306,118404,118395,106943,118246,99468,104212,107777,99714,116984,111333,86549,109933,109937,110082,100915,101865,100914,110556,104922,111984,117299,108515,111763,112575,103783,116730,114730,119344,108088,117110,109611,109645,103489,101807,99802,101135,116578);

commit;



begin;

select concat("FROM ",101628," TO ", 106271);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (101628,116574,108091,108314,116576,115765,107696,107873,115767,107872,115762,116785,101376,118314,116522,111485,115682,114468,101684,115872,115479,109180,113355,101462,100525,104195,104194,109404,103670,101196,100728,118171,117939,113462,106429,104538,101694,109772,108213,114185,109041,99800,112110,104792,101596,117123,113163,105781,116744,100773,109436,99531,117725,129615,129777,129622,102989,117687,117686,101733,118980,119136,107565,119002,118691,101589,115437,100110,103688,118845,109109,118844,119059,108835,119093,119094,108836,119317,109107,118210,118213,118223,118529,115547,118875,104072,117695,114320,107194,129783,129620,129784,109108,109106,116799,116534,101049,111257,106544,113156,106540,111258,111986,110904,101062,118338,119332,116746,106624,118425,108216,118380,119368,107073,112431,114394,118250,119309,118531,114797,118912,116882,119457,106249,116105,110486,100923,111259,115438,105588,104535,114713,112252,99961,102944,101532,105141,100311,104699,113274,118487,101489,100248,107626,118458,101518,118493,118890,109183,86578,86588,86579,86589,101220,107677,86580,101083,114398,114399,116017,100258,107541,100249,101121,102655,110285,114768,104810,110284,107282,115067,103242,115065,104848,109866,99410,100757,109610,106493,102174,103456,107330,111563,107329,107328,107484,106067,109190,108008,104700,113339,113340,111775,100315,102088,114063,101057,107740,86590,106271);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (101628,116574,108091,108314,116576,115765,107696,107873,115767,107872,115762,116785,101376,118314,116522,111485,115682,114468,101684,115872,115479,109180,113355,101462,100525,104195,104194,109404,103670,101196,100728,118171,117939,113462,106429,104538,101694,109772,108213,114185,109041,99800,112110,104792,101596,117123,113163,105781,116744,100773,109436,99531,117725,129615,129777,129622,102989,117687,117686,101733,118980,119136,107565,119002,118691,101589,115437,100110,103688,118845,109109,118844,119059,108835,119093,119094,108836,119317,109107,118210,118213,118223,118529,115547,118875,104072,117695,114320,107194,129783,129620,129784,109108,109106,116799,116534,101049,111257,106544,113156,106540,111258,111986,110904,101062,118338,119332,116746,106624,118425,108216,118380,119368,107073,112431,114394,118250,119309,118531,114797,118912,116882,119457,106249,116105,110486,100923,111259,115438,105588,104535,114713,112252,99961,102944,101532,105141,100311,104699,113274,118487,101489,100248,107626,118458,101518,118493,118890,109183,86578,86588,86579,86589,101220,107677,86580,101083,114398,114399,116017,100258,107541,100249,101121,102655,110285,114768,104810,110284,107282,115067,103242,115065,104848,109866,99410,100757,109610,106493,102174,103456,107330,111563,107329,107328,107484,106067,109190,108008,104700,113339,113340,111775,100315,102088,114063,101057,107740,86590,106271);

delete quotes.* 
from quotes where instrumentid in (101628,116574,108091,108314,116576,115765,107696,107873,115767,107872,115762,116785,101376,118314,116522,111485,115682,114468,101684,115872,115479,109180,113355,101462,100525,104195,104194,109404,103670,101196,100728,118171,117939,113462,106429,104538,101694,109772,108213,114185,109041,99800,112110,104792,101596,117123,113163,105781,116744,100773,109436,99531,117725,129615,129777,129622,102989,117687,117686,101733,118980,119136,107565,119002,118691,101589,115437,100110,103688,118845,109109,118844,119059,108835,119093,119094,108836,119317,109107,118210,118213,118223,118529,115547,118875,104072,117695,114320,107194,129783,129620,129784,109108,109106,116799,116534,101049,111257,106544,113156,106540,111258,111986,110904,101062,118338,119332,116746,106624,118425,108216,118380,119368,107073,112431,114394,118250,119309,118531,114797,118912,116882,119457,106249,116105,110486,100923,111259,115438,105588,104535,114713,112252,99961,102944,101532,105141,100311,104699,113274,118487,101489,100248,107626,118458,101518,118493,118890,109183,86578,86588,86579,86589,101220,107677,86580,101083,114398,114399,116017,100258,107541,100249,101121,102655,110285,114768,104810,110284,107282,115067,103242,115065,104848,109866,99410,100757,109610,106493,102174,103456,107330,111563,107329,107328,107484,106067,109190,108008,104700,113339,113340,111775,100315,102088,114063,101057,107740,86590,106271);

commit;



begin;

select concat("FROM ",117529," TO ", 86629);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (117529,102715,105207,110725,114483,107493,108769,109646,101986,86581,102641,110643,108508,102735,104090,109100,108434,114173,108127,101070,116919,106158,118000,116198,117754,114631,117956,117957,119208,109446,106026,118307,105084,102471,105203,113895,106865,105832,106495,115441,115439,115440,101048,113160,111659,118784,107837,118485,102639,104349,108656,111812,105438,103684,107839,99979,115289,107242,103561,105477,102489,113461,112718,116545,106430,113882,111566,86601,119164,119165,106265,112117,114702,112116,112123,107478,102892,106266,106529,110652,109254,99506,103567,107195,108276,103196,103064,106156,112351,111638,101454,104131,104067,104157,118166,103656,107479,107480,107569,105920,114705,114701,107310,107436,106483,110424,101553,102800,113887,101047,115328,106859,129472,107458,114406,106435,114407,113471,106436,114470,100595,114767,115770,107698,107876,102143,110286,104629,115070,115068,100020,115386,101962,111390,106694,102830,106006,108714,104164,109209,104540,101954,100874,104490,109373,108055,129473,86625,105338,114838,114985,100606,104628,109400,109824,104811,109825,101967,110206,102119,104610,105227,99825,110305,110207,109392,106440,114083,114081,115771,103300,115388,107220,115394,86626,108658,107221,102160,107764,102804,113477,113484,114704,86627,86636,86628,103921,112832,109814,117602,106239,105890,116059,104008,102270,110427,105153,106207,113107,86629);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (117529,102715,105207,110725,114483,107493,108769,109646,101986,86581,102641,110643,108508,102735,104090,109100,108434,114173,108127,101070,116919,106158,118000,116198,117754,114631,117956,117957,119208,109446,106026,118307,105084,102471,105203,113895,106865,105832,106495,115441,115439,115440,101048,113160,111659,118784,107837,118485,102639,104349,108656,111812,105438,103684,107839,99979,115289,107242,103561,105477,102489,113461,112718,116545,106430,113882,111566,86601,119164,119165,106265,112117,114702,112116,112123,107478,102892,106266,106529,110652,109254,99506,103567,107195,108276,103196,103064,106156,112351,111638,101454,104131,104067,104157,118166,103656,107479,107480,107569,105920,114705,114701,107310,107436,106483,110424,101553,102800,113887,101047,115328,106859,129472,107458,114406,106435,114407,113471,106436,114470,100595,114767,115770,107698,107876,102143,110286,104629,115070,115068,100020,115386,101962,111390,106694,102830,106006,108714,104164,109209,104540,101954,100874,104490,109373,108055,129473,86625,105338,114838,114985,100606,104628,109400,109824,104811,109825,101967,110206,102119,104610,105227,99825,110305,110207,109392,106440,114083,114081,115771,103300,115388,107220,115394,86626,108658,107221,102160,107764,102804,113477,113484,114704,86627,86636,86628,103921,112832,109814,117602,106239,105890,116059,104008,102270,110427,105153,106207,113107,86629);

delete quotes.* 
from quotes where instrumentid in (117529,102715,105207,110725,114483,107493,108769,109646,101986,86581,102641,110643,108508,102735,104090,109100,108434,114173,108127,101070,116919,106158,118000,116198,117754,114631,117956,117957,119208,109446,106026,118307,105084,102471,105203,113895,106865,105832,106495,115441,115439,115440,101048,113160,111659,118784,107837,118485,102639,104349,108656,111812,105438,103684,107839,99979,115289,107242,103561,105477,102489,113461,112718,116545,106430,113882,111566,86601,119164,119165,106265,112117,114702,112116,112123,107478,102892,106266,106529,110652,109254,99506,103567,107195,108276,103196,103064,106156,112351,111638,101454,104131,104067,104157,118166,103656,107479,107480,107569,105920,114705,114701,107310,107436,106483,110424,101553,102800,113887,101047,115328,106859,129472,107458,114406,106435,114407,113471,106436,114470,100595,114767,115770,107698,107876,102143,110286,104629,115070,115068,100020,115386,101962,111390,106694,102830,106006,108714,104164,109209,104540,101954,100874,104490,109373,108055,129473,86625,105338,114838,114985,100606,104628,109400,109824,104811,109825,101967,110206,102119,104610,105227,99825,110305,110207,109392,106440,114083,114081,115771,103300,115388,107220,115394,86626,108658,107221,102160,107764,102804,113477,113484,114704,86627,86636,86628,103921,112832,109814,117602,106239,105890,116059,104008,102270,110427,105153,106207,113107,86629);

commit;



begin;

select concat("FROM ",86637," TO ", 117306);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (86637,111965,86638,105257,110428,101434,107078,110339,99573,102422,86639,111795,110539,110853,117127,113902,100054,118176,102131,111616,101146,113487,117007,104190,100593,108389,116133,129804,86655,100528,104272,104127,103658,100561,113794,104625,116258,112948,111272,111273,115469,107639,104196,114769,86656,108417,117931,115490,115492,115496,103612,117281,116987,110855,116071,107752,107810,113489,107980,100485,116411,107184,107204,116410,116008,107900,103760,101170,114635,103355,107554,103595,109420,105908,107709,116663,109865,103330,129788,86657,107850,107285,104581,103419,107576,109607,107176,112716,108130,109850,100820,101688,86661,86658,112653,103024,110186,100551,100330,117828,115150,110420,86662,129923,105457,86663,109889,86664,102482,100751,117793,107746,103074,103320,101012,86683,105187,106836,107917,108619,104013,103799,129796,102389,112806,106188,105547,111982,116011,116872,108104,113948,102058,103654,107477,111006,115376,110715,108315,100800,108699,116862,86684,111219,105527,100045,117452,110867,116350,103471,102605,105604,107967,100975,101000,111197,100202,100870,116530,112471,110534,102536,114262,110596,111932,112510,106276,106275,106105,104038,115772,100852,104433,102527,114379,108299,116419,107567,105603,116526,102116,105411,118163,111730,106330,108895,114238,106391,103699,102911,106224,113099,117887,113597,117792,102072,105710,105468,114776,117306);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (86637,111965,86638,105257,110428,101434,107078,110339,99573,102422,86639,111795,110539,110853,117127,113902,100054,118176,102131,111616,101146,113487,117007,104190,100593,108389,116133,129804,86655,100528,104272,104127,103658,100561,113794,104625,116258,112948,111272,111273,115469,107639,104196,114769,86656,108417,117931,115490,115492,115496,103612,117281,116987,110855,116071,107752,107810,113489,107980,100485,116411,107184,107204,116410,116008,107900,103760,101170,114635,103355,107554,103595,109420,105908,107709,116663,109865,103330,129788,86657,107850,107285,104581,103419,107576,109607,107176,112716,108130,109850,100820,101688,86661,86658,112653,103024,110186,100551,100330,117828,115150,110420,86662,129923,105457,86663,109889,86664,102482,100751,117793,107746,103074,103320,101012,86683,105187,106836,107917,108619,104013,103799,129796,102389,112806,106188,105547,111982,116011,116872,108104,113948,102058,103654,107477,111006,115376,110715,108315,100800,108699,116862,86684,111219,105527,100045,117452,110867,116350,103471,102605,105604,107967,100975,101000,111197,100202,100870,116530,112471,110534,102536,114262,110596,111932,112510,106276,106275,106105,104038,115772,100852,104433,102527,114379,108299,116419,107567,105603,116526,102116,105411,118163,111730,106330,108895,114238,106391,103699,102911,106224,113099,117887,113597,117792,102072,105710,105468,114776,117306);

delete quotes.* 
from quotes where instrumentid in (86637,111965,86638,105257,110428,101434,107078,110339,99573,102422,86639,111795,110539,110853,117127,113902,100054,118176,102131,111616,101146,113487,117007,104190,100593,108389,116133,129804,86655,100528,104272,104127,103658,100561,113794,104625,116258,112948,111272,111273,115469,107639,104196,114769,86656,108417,117931,115490,115492,115496,103612,117281,116987,110855,116071,107752,107810,113489,107980,100485,116411,107184,107204,116410,116008,107900,103760,101170,114635,103355,107554,103595,109420,105908,107709,116663,109865,103330,129788,86657,107850,107285,104581,103419,107576,109607,107176,112716,108130,109850,100820,101688,86661,86658,112653,103024,110186,100551,100330,117828,115150,110420,86662,129923,105457,86663,109889,86664,102482,100751,117793,107746,103074,103320,101012,86683,105187,106836,107917,108619,104013,103799,129796,102389,112806,106188,105547,111982,116011,116872,108104,113948,102058,103654,107477,111006,115376,110715,108315,100800,108699,116862,86684,111219,105527,100045,117452,110867,116350,103471,102605,105604,107967,100975,101000,111197,100202,100870,116530,112471,110534,102536,114262,110596,111932,112510,106276,106275,106105,104038,115772,100852,104433,102527,114379,108299,116419,107567,105603,116526,102116,105411,118163,111730,106330,108895,114238,106391,103699,102911,106224,113099,117887,113597,117792,102072,105710,105468,114776,117306);

commit;



begin;

select concat("FROM ",114090," TO ", 86731);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (114090,114412,101234,112676,100235,117442,116031,107903,116009,117443,101555,101486,104049,104348,108653,111617,112655,104051,114826,101439,114825,106542,86702,102183,114581,101976,113183,101090,86703,103095,86704,107406,102322,106788,106949,110380,114338,110016,102773,104858,100293,86690,86705,86691,99384,117330,86706,108181,86692,113970,116260,101629,115480,107679,115893,109430,109792,110237,110595,100078,108422,109504,104424,109500,104208,116034,107540,116012,107539,107550,106551,113181,105369,110731,111998,101620,100022,112036,115484,107647,102069,102068,105081,107571,115449,113901,106603,101089,114368,114358,101108,113979,103392,116627,113985,100526,129625,107276,102315,108826,108079,108300,108076,99813,108980,104186,113980,99627,108963,115450,111686,117425,114325,99441,104037,117790,112684,106600,117312,107728,114524,106927,107071,100017,114282,110506,111776,100939,106110,116556,101968,101736,103692,117709,108211,100361,114473,117712,117697,116000,114812,107008,106997,103747,116028,107175,114818,103215,113121,106411,113134,115326,114996,107202,105079,102067,104263,105080,102071,110166,109781,104880,101550,117036,117037,108391,108305,108301,108078,108083,115896,115895,107675,115901,115467,100024,103624,117574,104000,101279,105878,101179,112632,100100,112828,112829,113115,112760,114800,103405,114597,116714,108520,115854,110713,100269,113005,103086,101904,86731);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (114090,114412,101234,112676,100235,117442,116031,107903,116009,117443,101555,101486,104049,104348,108653,111617,112655,104051,114826,101439,114825,106542,86702,102183,114581,101976,113183,101090,86703,103095,86704,107406,102322,106788,106949,110380,114338,110016,102773,104858,100293,86690,86705,86691,99384,117330,86706,108181,86692,113970,116260,101629,115480,107679,115893,109430,109792,110237,110595,100078,108422,109504,104424,109500,104208,116034,107540,116012,107539,107550,106551,113181,105369,110731,111998,101620,100022,112036,115484,107647,102069,102068,105081,107571,115449,113901,106603,101089,114368,114358,101108,113979,103392,116627,113985,100526,129625,107276,102315,108826,108079,108300,108076,99813,108980,104186,113980,99627,108963,115450,111686,117425,114325,99441,104037,117790,112684,106600,117312,107728,114524,106927,107071,100017,114282,110506,111776,100939,106110,116556,101968,101736,103692,117709,108211,100361,114473,117712,117697,116000,114812,107008,106997,103747,116028,107175,114818,103215,113121,106411,113134,115326,114996,107202,105079,102067,104263,105080,102071,110166,109781,104880,101550,117036,117037,108391,108305,108301,108078,108083,115896,115895,107675,115901,115467,100024,103624,117574,104000,101279,105878,101179,112632,100100,112828,112829,113115,112760,114800,103405,114597,116714,108520,115854,110713,100269,113005,103086,101904,86731);

delete quotes.* 
from quotes where instrumentid in (114090,114412,101234,112676,100235,117442,116031,107903,116009,117443,101555,101486,104049,104348,108653,111617,112655,104051,114826,101439,114825,106542,86702,102183,114581,101976,113183,101090,86703,103095,86704,107406,102322,106788,106949,110380,114338,110016,102773,104858,100293,86690,86705,86691,99384,117330,86706,108181,86692,113970,116260,101629,115480,107679,115893,109430,109792,110237,110595,100078,108422,109504,104424,109500,104208,116034,107540,116012,107539,107550,106551,113181,105369,110731,111998,101620,100022,112036,115484,107647,102069,102068,105081,107571,115449,113901,106603,101089,114368,114358,101108,113979,103392,116627,113985,100526,129625,107276,102315,108826,108079,108300,108076,99813,108980,104186,113980,99627,108963,115450,111686,117425,114325,99441,104037,117790,112684,106600,117312,107728,114524,106927,107071,100017,114282,110506,111776,100939,106110,116556,101968,101736,103692,117709,108211,100361,114473,117712,117697,116000,114812,107008,106997,103747,116028,107175,114818,103215,113121,106411,113134,115326,114996,107202,105079,102067,104263,105080,102071,110166,109781,104880,101550,117036,117037,108391,108305,108301,108078,108083,115896,115895,107675,115901,115467,100024,103624,117574,104000,101279,105878,101179,112632,100100,112828,112829,113115,112760,114800,103405,114597,116714,108520,115854,110713,100269,113005,103086,101904,86731);

commit;



begin;

select concat("FROM ",105976," TO ", 113205);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (105976,110224,104650,109414,103858,106991,114809,105996,114799,103413,114794,109405,117706,103844,86741,101074,107100,113890,106861,103211,104211,115018,109876,112511,102245,107183,105545,114596,86742,114978,107993,100131,116412,113893,108306,106711,115549,109326,101287,113296,115924,101221,109052,86753,115124,108082,107737,100955,105651,102445,111618,110482,101926,114523,101558,107624,109349,104478,103333,115935,101405,104567,115949,113633,115936,107768,106358,108929,100267,86754,86743,86755,107739,101889,109280,103777,117516,99549,117009,104981,115898,115902,99882,114857,106169,106509,99920,105521,108783,113637,102518,117417,106797,116471,113412,108227,113454,113413,117418,116953,116971,103201,115108,116470,107809,112613,101441,102829,106005,106805,107362,102331,101844,110938,105157,110942,105159,118058,117632,109194,112031,86769,102424,114171,113977,106562,86775,99604,110788,86770,104543,99563,86776,109142,111457,113864,102656,102960,101199,116959,109131,100716,109079,100883,101907,86771,110493,109606,110155,111358,118173,117228,101945,100613,110600,109797,102146,108349,101525,108010,100460,86772,109773,99861,101925,114100,114091,114094,109964,117692,116399,117698,110430,105642,115235,106491,113932,104940,129810,117039,105184,117919,109186,103082,86777,115461,113716,114277,112704,106510,107843,108345,117237,108828,117214,108700,109099,114608,118046,113205);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (105976,110224,104650,109414,103858,106991,114809,105996,114799,103413,114794,109405,117706,103844,86741,101074,107100,113890,106861,103211,104211,115018,109876,112511,102245,107183,105545,114596,86742,114978,107993,100131,116412,113893,108306,106711,115549,109326,101287,113296,115924,101221,109052,86753,115124,108082,107737,100955,105651,102445,111618,110482,101926,114523,101558,107624,109349,104478,103333,115935,101405,104567,115949,113633,115936,107768,106358,108929,100267,86754,86743,86755,107739,101889,109280,103777,117516,99549,117009,104981,115898,115902,99882,114857,106169,106509,99920,105521,108783,113637,102518,117417,106797,116471,113412,108227,113454,113413,117418,116953,116971,103201,115108,116470,107809,112613,101441,102829,106005,106805,107362,102331,101844,110938,105157,110942,105159,118058,117632,109194,112031,86769,102424,114171,113977,106562,86775,99604,110788,86770,104543,99563,86776,109142,111457,113864,102656,102960,101199,116959,109131,100716,109079,100883,101907,86771,110493,109606,110155,111358,118173,117228,101945,100613,110600,109797,102146,108349,101525,108010,100460,86772,109773,99861,101925,114100,114091,114094,109964,117692,116399,117698,110430,105642,115235,106491,113932,104940,129810,117039,105184,117919,109186,103082,86777,115461,113716,114277,112704,106510,107843,108345,117237,108828,117214,108700,109099,114608,118046,113205);

delete quotes.* 
from quotes where instrumentid in (105976,110224,104650,109414,103858,106991,114809,105996,114799,103413,114794,109405,117706,103844,86741,101074,107100,113890,106861,103211,104211,115018,109876,112511,102245,107183,105545,114596,86742,114978,107993,100131,116412,113893,108306,106711,115549,109326,101287,113296,115924,101221,109052,86753,115124,108082,107737,100955,105651,102445,111618,110482,101926,114523,101558,107624,109349,104478,103333,115935,101405,104567,115949,113633,115936,107768,106358,108929,100267,86754,86743,86755,107739,101889,109280,103777,117516,99549,117009,104981,115898,115902,99882,114857,106169,106509,99920,105521,108783,113637,102518,117417,106797,116471,113412,108227,113454,113413,117418,116953,116971,103201,115108,116470,107809,112613,101441,102829,106005,106805,107362,102331,101844,110938,105157,110942,105159,118058,117632,109194,112031,86769,102424,114171,113977,106562,86775,99604,110788,86770,104543,99563,86776,109142,111457,113864,102656,102960,101199,116959,109131,100716,109079,100883,101907,86771,110493,109606,110155,111358,118173,117228,101945,100613,110600,109797,102146,108349,101525,108010,100460,86772,109773,99861,101925,114100,114091,114094,109964,117692,116399,117698,110430,105642,115235,106491,113932,104940,129810,117039,105184,117919,109186,103082,86777,115461,113716,114277,112704,106510,107843,108345,117237,108828,117214,108700,109099,114608,118046,113205);

commit;



begin;

select concat("FROM ",117223," TO ", 107648);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (117223,117209,108825,108420,117456,104132,100289,101415,108140,109701,102169,112729,112726,112725,129590,116022,116557,116562,104632,100126,115769,107281,102687,118129,101134,106003,107644,107331,106850,114434,102523,111080,105418,111411,129040,129025,129006,129052,129051,129005,128803,128816,128891,128817,128917,129050,104526,104515,109774,109185,86791,86795,86792,86796,129789,100981,108199,117701,117702,108169,100768,106545,113625,107252,114670,103653,107255,101845,114808,103229,111527,111261,111531,111266,111529,117392,103909,107969,110773,113103,113234,112882,99424,108084,116560,116023,115182,100477,116571,116567,116566,101804,104232,104235,108346,103344,117240,108308,116568,108086,111533,111534,108234,104198,109407,106069,112392,106066,114762,101033,105032,99602,99715,104861,112160,112156,112155,99748,100090,110348,108830,109103,108348,103057,109102,107946,103228,116629,113040,112737,104532,107932,116825,115224,116826,114102,114418,100089,104039,99736,99912,102152,102196,113350,105852,116125,103883,104781,102504,86817,86810,100011,101076,100946,112593,115385,105655,100645,111052,112121,112317,116418,113515,117125,114881,86818,106122,109195,110480,106121,103335,102052,116230,117400,110846,117673,129485,105096,116463,113140,107838,99968,114671,115486,111540,99427,105606,108437,109192,105267,113341,114432,114419,101625,107472,115061,111264,129486,117053,107648);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (117223,117209,108825,108420,117456,104132,100289,101415,108140,109701,102169,112729,112726,112725,129590,116022,116557,116562,104632,100126,115769,107281,102687,118129,101134,106003,107644,107331,106850,114434,102523,111080,105418,111411,129040,129025,129006,129052,129051,129005,128803,128816,128891,128817,128917,129050,104526,104515,109774,109185,86791,86795,86792,86796,129789,100981,108199,117701,117702,108169,100768,106545,113625,107252,114670,103653,107255,101845,114808,103229,111527,111261,111531,111266,111529,117392,103909,107969,110773,113103,113234,112882,99424,108084,116560,116023,115182,100477,116571,116567,116566,101804,104232,104235,108346,103344,117240,108308,116568,108086,111533,111534,108234,104198,109407,106069,112392,106066,114762,101033,105032,99602,99715,104861,112160,112156,112155,99748,100090,110348,108830,109103,108348,103057,109102,107946,103228,116629,113040,112737,104532,107932,116825,115224,116826,114102,114418,100089,104039,99736,99912,102152,102196,113350,105852,116125,103883,104781,102504,86817,86810,100011,101076,100946,112593,115385,105655,100645,111052,112121,112317,116418,113515,117125,114881,86818,106122,109195,110480,106121,103335,102052,116230,117400,110846,117673,129485,105096,116463,113140,107838,99968,114671,115486,111540,99427,105606,108437,109192,105267,113341,114432,114419,101625,107472,115061,111264,129486,117053,107648);

delete quotes.* 
from quotes where instrumentid in (117223,117209,108825,108420,117456,104132,100289,101415,108140,109701,102169,112729,112726,112725,129590,116022,116557,116562,104632,100126,115769,107281,102687,118129,101134,106003,107644,107331,106850,114434,102523,111080,105418,111411,129040,129025,129006,129052,129051,129005,128803,128816,128891,128817,128917,129050,104526,104515,109774,109185,86791,86795,86792,86796,129789,100981,108199,117701,117702,108169,100768,106545,113625,107252,114670,103653,107255,101845,114808,103229,111527,111261,111531,111266,111529,117392,103909,107969,110773,113103,113234,112882,99424,108084,116560,116023,115182,100477,116571,116567,116566,101804,104232,104235,108346,103344,117240,108308,116568,108086,111533,111534,108234,104198,109407,106069,112392,106066,114762,101033,105032,99602,99715,104861,112160,112156,112155,99748,100090,110348,108830,109103,108348,103057,109102,107946,103228,116629,113040,112737,104532,107932,116825,115224,116826,114102,114418,100089,104039,99736,99912,102152,102196,113350,105852,116125,103883,104781,102504,86817,86810,100011,101076,100946,112593,115385,105655,100645,111052,112121,112317,116418,113515,117125,114881,86818,106122,109195,110480,106121,103335,102052,116230,117400,110846,117673,129485,105096,116463,113140,107838,99968,114671,115486,111540,99427,105606,108437,109192,105267,113341,114432,114419,101625,107472,115061,111264,129486,117053,107648);

commit;



begin;

select concat("FROM ",115470," TO ", 99664);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (115470,103605,108192,102963,114456,117289,100916,117907,109968,101656,108875,116863,109177,108158,109786,104787,101480,113292,106876,108671,115285,111112,116025,109168,109625,102040,113269,115213,102336,111657,115250,109938,86829,117264,99389,113188,116157,112891,113575,86837,116572,100839,114884,102394,111101,86838,106480,117429,100391,107733,103992,108210,109112,117453,112177,110449,106952,102777,110117,116726,116687,104579,114339,108224,109206,101841,99977,103668,108208,107357,128815,100643,111215,109450,115491,115489,115487,101667,110316,107503,105039,111063,112682,100666,104794,99565,114138,107050,86851,116337,114437,115159,110567,107778,112360,109126,115185,116013,99674,113800,105097,110157,110158,107140,112482,111206,105405,105667,111202,105403,110594,104915,110955,112857,113377,100895,108195,102540,117235,114203,99884,106894,109145,103750,106688,103422,112187,117699,108974,108973,100592,102115,108977,117047,108396,117044,108393,117040,117043,109068,105436,109819,104403,109089,112747,114411,108304,106096,102788,112759,116563,104474,108175,109980,108095,109840,102234,109780,100772,103817,104260,109779,113314,106639,107296,115097,101257,115098,115458,100159,100358,111924,111925,102233,110392,105351,105276,105493,107921,112530,106287,112535,106288,114550,114006,113661,110492,99705,112195,101335,110386,110035,104724,104723,108730,100039,110463,108484,117355,99664);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (115470,103605,108192,102963,114456,117289,100916,117907,109968,101656,108875,116863,109177,108158,109786,104787,101480,113292,106876,108671,115285,111112,116025,109168,109625,102040,113269,115213,102336,111657,115250,109938,86829,117264,99389,113188,116157,112891,113575,86837,116572,100839,114884,102394,111101,86838,106480,117429,100391,107733,103992,108210,109112,117453,112177,110449,106952,102777,110117,116726,116687,104579,114339,108224,109206,101841,99977,103668,108208,107357,128815,100643,111215,109450,115491,115489,115487,101667,110316,107503,105039,111063,112682,100666,104794,99565,114138,107050,86851,116337,114437,115159,110567,107778,112360,109126,115185,116013,99674,113800,105097,110157,110158,107140,112482,111206,105405,105667,111202,105403,110594,104915,110955,112857,113377,100895,108195,102540,117235,114203,99884,106894,109145,103750,106688,103422,112187,117699,108974,108973,100592,102115,108977,117047,108396,117044,108393,117040,117043,109068,105436,109819,104403,109089,112747,114411,108304,106096,102788,112759,116563,104474,108175,109980,108095,109840,102234,109780,100772,103817,104260,109779,113314,106639,107296,115097,101257,115098,115458,100159,100358,111924,111925,102233,110392,105351,105276,105493,107921,112530,106287,112535,106288,114550,114006,113661,110492,99705,112195,101335,110386,110035,104724,104723,108730,100039,110463,108484,117355,99664);

delete quotes.* 
from quotes where instrumentid in (115470,103605,108192,102963,114456,117289,100916,117907,109968,101656,108875,116863,109177,108158,109786,104787,101480,113292,106876,108671,115285,111112,116025,109168,109625,102040,113269,115213,102336,111657,115250,109938,86829,117264,99389,113188,116157,112891,113575,86837,116572,100839,114884,102394,111101,86838,106480,117429,100391,107733,103992,108210,109112,117453,112177,110449,106952,102777,110117,116726,116687,104579,114339,108224,109206,101841,99977,103668,108208,107357,128815,100643,111215,109450,115491,115489,115487,101667,110316,107503,105039,111063,112682,100666,104794,99565,114138,107050,86851,116337,114437,115159,110567,107778,112360,109126,115185,116013,99674,113800,105097,110157,110158,107140,112482,111206,105405,105667,111202,105403,110594,104915,110955,112857,113377,100895,108195,102540,117235,114203,99884,106894,109145,103750,106688,103422,112187,117699,108974,108973,100592,102115,108977,117047,108396,117044,108393,117040,117043,109068,105436,109819,104403,109089,112747,114411,108304,106096,102788,112759,116563,104474,108175,109980,108095,109840,102234,109780,100772,103817,104260,109779,113314,106639,107296,115097,101257,115098,115458,100159,100358,111924,111925,102233,110392,105351,105276,105493,107921,112530,106287,112535,106288,114550,114006,113661,110492,99705,112195,101335,110386,110035,104724,104723,108730,100039,110463,108484,117355,99664);

commit;



begin;

select concat("FROM ",103565," TO ", 104891);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (103565,115372,115405,108764,107214,108243,114664,104782,119096,101010,110462,116317,105216,111147,104312,112776,119158,114458,100620,114758,114457,106120,110770,112097,112452,112098,112455,114058,114059,104142,108369,107044,103297,115382,101583,109634,115383,114665,114666,114309,119076,112534,114148,115248,115249,106727,100578,103305,99757,99997,114785,86871,86877,86872,108254,109640,107457,110849,110020,114008,106675,105116,112732,114108,103116,117412,103338,86873,103425,116518,107848,112129,107278,102090,112722,107786,117648,117649,108218,99980,108550,108549,117541,116881,107781,112608,115497,107651,103611,115103,103065,107365,103301,107225,115802,115495,101622,101623,109784,109782,102066,100710,116870,114028,101536,104710,110816,116361,103613,115498,115511,103472,115520,115517,115500,115471,107655,111282,111543,111278,101330,115573,115251,115399,103574,114987,102672,115374,103296,104573,102161,108574,111283,111551,111544,114107,107665,114109,117052,108399,101552,116081,103842,117051,112516,104877,115514,103473,111289,107782,109923,118037,86896,86891,112717,117769,101821,106819,112859,100575,104183,104187,108400,113414,105807,102853,113429,103672,114486,108786,116247,117532,104353,99508,107046,114525,108374,116354,114667,107878,100130,105332,99999,107689,117341,108721,101915,117642,100040,117505,100356,101565,100721,110228,109259,106388,104997,102600,104534,104891);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (103565,115372,115405,108764,107214,108243,114664,104782,119096,101010,110462,116317,105216,111147,104312,112776,119158,114458,100620,114758,114457,106120,110770,112097,112452,112098,112455,114058,114059,104142,108369,107044,103297,115382,101583,109634,115383,114665,114666,114309,119076,112534,114148,115248,115249,106727,100578,103305,99757,99997,114785,86871,86877,86872,108254,109640,107457,110849,110020,114008,106675,105116,112732,114108,103116,117412,103338,86873,103425,116518,107848,112129,107278,102090,112722,107786,117648,117649,108218,99980,108550,108549,117541,116881,107781,112608,115497,107651,103611,115103,103065,107365,103301,107225,115802,115495,101622,101623,109784,109782,102066,100710,116870,114028,101536,104710,110816,116361,103613,115498,115511,103472,115520,115517,115500,115471,107655,111282,111543,111278,101330,115573,115251,115399,103574,114987,102672,115374,103296,104573,102161,108574,111283,111551,111544,114107,107665,114109,117052,108399,101552,116081,103842,117051,112516,104877,115514,103473,111289,107782,109923,118037,86896,86891,112717,117769,101821,106819,112859,100575,104183,104187,108400,113414,105807,102853,113429,103672,114486,108786,116247,117532,104353,99508,107046,114525,108374,116354,114667,107878,100130,105332,99999,107689,117341,108721,101915,117642,100040,117505,100356,101565,100721,110228,109259,106388,104997,102600,104534,104891);

delete quotes.* 
from quotes where instrumentid in (103565,115372,115405,108764,107214,108243,114664,104782,119096,101010,110462,116317,105216,111147,104312,112776,119158,114458,100620,114758,114457,106120,110770,112097,112452,112098,112455,114058,114059,104142,108369,107044,103297,115382,101583,109634,115383,114665,114666,114309,119076,112534,114148,115248,115249,106727,100578,103305,99757,99997,114785,86871,86877,86872,108254,109640,107457,110849,110020,114008,106675,105116,112732,114108,103116,117412,103338,86873,103425,116518,107848,112129,107278,102090,112722,107786,117648,117649,108218,99980,108550,108549,117541,116881,107781,112608,115497,107651,103611,115103,103065,107365,103301,107225,115802,115495,101622,101623,109784,109782,102066,100710,116870,114028,101536,104710,110816,116361,103613,115498,115511,103472,115520,115517,115500,115471,107655,111282,111543,111278,101330,115573,115251,115399,103574,114987,102672,115374,103296,104573,102161,108574,111283,111551,111544,114107,107665,114109,117052,108399,101552,116081,103842,117051,112516,104877,115514,103473,111289,107782,109923,118037,86896,86891,112717,117769,101821,106819,112859,100575,104183,104187,108400,113414,105807,102853,113429,103672,114486,108786,116247,117532,104353,99508,107046,114525,108374,116354,114667,107878,100130,105332,99999,107689,117341,108721,101915,117642,100040,117505,100356,101565,100721,110228,109259,106388,104997,102600,104534,104891);

commit;



begin;

select concat("FROM ",107167," TO ", 118994);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (107167,105098,99731,102889,113555,116306,113630,113629,112333,101274,111572,107625,107245,103941,119367,119366,118793,118455,113811,106812,113797,113801,114976,129593,107715,107895,107719,116672,105921,116162,116161,116167,104261,99901,109787,109785,111288,111556,111653,105690,105333,116749,100653,100189,111526,117415,100574,117416,110909,117126,86918,106530,119099,118990,118629,118659,118574,118359,118442,118386,119222,119221,105238,110658,115100,117420,113613,113211,105988,109043,128814,118550,118474,108221,104024,86919,86928,86920,112664,86921,110657,101388,105232,107173,116223,100149,99394,107159,112642,111218,104021,109888,103105,109881,113928,111560,107614,110236,99648,108236,110758,100094,107667,107668,109820,107982,107198,103310,115395,111294,102162,103715,111290,113951,113539,103820,104257,103641,118482,119003,119019,108729,110692,114481,113594,119073,118668,111026,101847,103233,109776,102178,107805,100669,103932,108058,116682,118594,113307,86938,105047,102501,101567,101513,108371,117645,106933,118495,118343,118496,108404,108272,115243,115562,105303,115624,117994,111664,104623,104703,104285,99858,104087,101284,118179,99809,118180,117317,129824,129787,129825,129799,117221,119461,118206,118260,100533,116690,109709,111429,102528,105432,129394,103862,118679,118340,118678,118609,119071,103884,118901,118521,99983,100068,117691,117331,108580,109419,118988,118994);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (107167,105098,99731,102889,113555,116306,113630,113629,112333,101274,111572,107625,107245,103941,119367,119366,118793,118455,113811,106812,113797,113801,114976,129593,107715,107895,107719,116672,105921,116162,116161,116167,104261,99901,109787,109785,111288,111556,111653,105690,105333,116749,100653,100189,111526,117415,100574,117416,110909,117126,86918,106530,119099,118990,118629,118659,118574,118359,118442,118386,119222,119221,105238,110658,115100,117420,113613,113211,105988,109043,128814,118550,118474,108221,104024,86919,86928,86920,112664,86921,110657,101388,105232,107173,116223,100149,99394,107159,112642,111218,104021,109888,103105,109881,113928,111560,107614,110236,99648,108236,110758,100094,107667,107668,109820,107982,107198,103310,115395,111294,102162,103715,111290,113951,113539,103820,104257,103641,118482,119003,119019,108729,110692,114481,113594,119073,118668,111026,101847,103233,109776,102178,107805,100669,103932,108058,116682,118594,113307,86938,105047,102501,101567,101513,108371,117645,106933,118495,118343,118496,108404,108272,115243,115562,105303,115624,117994,111664,104623,104703,104285,99858,104087,101284,118179,99809,118180,117317,129824,129787,129825,129799,117221,119461,118206,118260,100533,116690,109709,111429,102528,105432,129394,103862,118679,118340,118678,118609,119071,103884,118901,118521,99983,100068,117691,117331,108580,109419,118988,118994);

delete quotes.* 
from quotes where instrumentid in (107167,105098,99731,102889,113555,116306,113630,113629,112333,101274,111572,107625,107245,103941,119367,119366,118793,118455,113811,106812,113797,113801,114976,129593,107715,107895,107719,116672,105921,116162,116161,116167,104261,99901,109787,109785,111288,111556,111653,105690,105333,116749,100653,100189,111526,117415,100574,117416,110909,117126,86918,106530,119099,118990,118629,118659,118574,118359,118442,118386,119222,119221,105238,110658,115100,117420,113613,113211,105988,109043,128814,118550,118474,108221,104024,86919,86928,86920,112664,86921,110657,101388,105232,107173,116223,100149,99394,107159,112642,111218,104021,109888,103105,109881,113928,111560,107614,110236,99648,108236,110758,100094,107667,107668,109820,107982,107198,103310,115395,111294,102162,103715,111290,113951,113539,103820,104257,103641,118482,119003,119019,108729,110692,114481,113594,119073,118668,111026,101847,103233,109776,102178,107805,100669,103932,108058,116682,118594,113307,86938,105047,102501,101567,101513,108371,117645,106933,118495,118343,118496,108404,108272,115243,115562,105303,115624,117994,111664,104623,104703,104285,99858,104087,101284,118179,99809,118180,117317,129824,129787,129825,129799,117221,119461,118206,118260,100533,116690,109709,111429,102528,105432,129394,103862,118679,118340,118678,118609,119071,103884,118901,118521,99983,100068,117691,117331,108580,109419,118988,118994);

commit;



begin;

select concat("FROM ",119110," TO ", 106651);

insert into total_returns (instrumentid, date, total_return_index)  
select instrumentid, date, current_close 
from quotes where instrumentid in (119110,117952,114388,100976,101673,106539,105711,105085,103520,118522,119199,117997,117795,101803,86966,113617,113266,118961,115882,104128,117696,105113,108984,110187,118318,119052,108925,106073,105021,114528,114531,108372,117071,100535,114606,117810,106270,116216,106777,101778,116685,119174,105721,105830,108979,107423,109533,101855,109532,102649,109236,116916,106934,117636,119432,115111,117779,108267,117339,112601,107551,109080,101460,117324,103831,109945,108000,110577,100882,102919,99511,112285,105977,86956,115915,115668,86967,86957,86968,112955,129641,104583,112137,116069,117183,106095,108512,108758,108810,108927,101609,106981,116920,109221,111938,101997,113410,102642,115651,115099,113406,113799,105309,105876,102675,100138,111939,104213,111098,106397,113823,117841,102916,110087,117344,100516,101241,102690,112586,100587,116218,103532,107985,101189,110159,117347,108477,104878,107471,104718,104714,103721,108253,103034,113790,102780,105471,104175,113671,101479,100769,115863,115862,104872,102992,99616,103935,106815,100380,99487,99481,99774,101681,101289,113923,103877,115864,100335,106728,114940,113781,106804,112346,101037,117539,100406,101324,99557,115861,113468,105348,102936,111894,113688,106519,99399,86979,107083,101425,106651);

delete quotes_prices.* 
from quotes_prices left join quotes on quotes_prices.quote_id = quotes.id 
where instrumentid in (119110,117952,114388,100976,101673,106539,105711,105085,103520,118522,119199,117997,117795,101803,86966,113617,113266,118961,115882,104128,117696,105113,108984,110187,118318,119052,108925,106073,105021,114528,114531,108372,117071,100535,114606,117810,106270,116216,106777,101778,116685,119174,105721,105830,108979,107423,109533,101855,109532,102649,109236,116916,106934,117636,119432,115111,117779,108267,117339,112601,107551,109080,101460,117324,103831,109945,108000,110577,100882,102919,99511,112285,105977,86956,115915,115668,86967,86957,86968,112955,129641,104583,112137,116069,117183,106095,108512,108758,108810,108927,101609,106981,116920,109221,111938,101997,113410,102642,115651,115099,113406,113799,105309,105876,102675,100138,111939,104213,111098,106397,113823,117841,102916,110087,117344,100516,101241,102690,112586,100587,116218,103532,107985,101189,110159,117347,108477,104878,107471,104718,104714,103721,108253,103034,113790,102780,105471,104175,113671,101479,100769,115863,115862,104872,102992,99616,103935,106815,100380,99487,99481,99774,101681,101289,113923,103877,115864,100335,106728,114940,113781,106804,112346,101037,117539,100406,101324,99557,115861,113468,105348,102936,111894,113688,106519,99399,86979,107083,101425,106651);

delete quotes.* 
from quotes where instrumentid in (119110,117952,114388,100976,101673,106539,105711,105085,103520,118522,119199,117997,117795,101803,86966,113617,113266,118961,115882,104128,117696,105113,108984,110187,118318,119052,108925,106073,105021,114528,114531,108372,117071,100535,114606,117810,106270,116216,106777,101778,116685,119174,105721,105830,108979,107423,109533,101855,109532,102649,109236,116916,106934,117636,119432,115111,117779,108267,117339,112601,107551,109080,101460,117324,103831,109945,108000,110577,100882,102919,99511,112285,105977,86956,115915,115668,86967,86957,86968,112955,129641,104583,112137,116069,117183,106095,108512,108758,108810,108927,101609,106981,116920,109221,111938,101997,113410,102642,115651,115099,113406,113799,105309,105876,102675,100138,111939,104213,111098,106397,113823,117841,102916,110087,117344,100516,101241,102690,112586,100587,116218,103532,107985,101189,110159,117347,108477,104878,107471,104718,104714,103721,108253,103034,113790,102780,105471,104175,113671,101479,100769,115863,115862,104872,102992,99616,103935,106815,100380,99487,99481,99774,101681,101289,113923,103877,115864,100335,106728,114940,113781,106804,112346,101037,117539,100406,101324,99557,115861,113468,105348,102936,111894,113688,106519,99399,86979,107083,101425,106651);

commit;




