CREATE TABLE queued_big_reports (
    id                    BIGINT(20)   NOT NULL AUTO_INCREMENT,
    version               INT          NOT NULL DEFAULT 0,
    created_at            DATETIME     NOT NULL,
    big_reportid          VARCHAR(255) NOT NULL,
    report_name           VARCHAR(255) NOT NULL,
    report_key            VARCHAR(255) DEFAULT NULL,
    metadata              MEDIUMTEXT   NOT NULL,
    state                 VARCHAR(255) NOT NULL,
    state_transition_time DATETIME     NOT NULL,
    polled_time           DATETIME     DEFAULT NULL,
    sent_time             DATETIME     DEFAULT NULL,
    ignored_at            DATETIME     DEFAULT NULL,
    error_flag            TINYINT(1)   NOT NULL DEFAULT 0,
    PRIMARY KEY (id),
    UNIQUE KEY qbr_big_reportid (big_reportid),
    INDEX qbr_state_polled_time_error_flag (state, polled_time, error_flag),
    INDEX qbr_state_sent_time_polled_time_error_flag (state, sent_time, polled_time, error_flag),
    INDEX qbr_state_name_key (state, report_name, report_key)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

INSERT INTO schema_log (schema_version, date)
VALUES (62, now());