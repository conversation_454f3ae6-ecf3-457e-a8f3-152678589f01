-- Create tables for insider reports
drop database if exists insider;

create database insider character set 'utf8' collate utf8_general_ci;

use insider;

create table insider_reports (
	id bigint not null, 
	date date not null, 
	delta_date date default null, 
	ranks integer not null,
	unique(date, ranks),
	primary key (id)
) engine = InnoDB;

create table stock_reports (
	id bigint not null, 
	insider_report_id bigint not null, 
	symbol varchar(10) not null, 
	top_value numeric(16,12), 
	all_value numeric(16,12), 
	top_count numeric(16,12), 
	all_count numeric(16,12), 
	top_value_delta numeric(16,12), 
	all_value_delta numeric(16,12), 
	top_count_delta numeric(16,12), 
	all_count_delta numeric(16,12), 
	value_diff numeric(16,12), 
	value_diff_delta numeric(16,12), 
	primary key (id),
	index (insider_report_id),
	constraint foreign key (insider_report_id) references insider_reports (id)
) engine = InnoDB;

create table portfolio_ranks (
	id bigint not null, 
	insider_report_id bigint not null,
	portfolio_id bigint, 
	rank numeric(16,10), 
	primary key (id),
	index (insider_report_id), 
	constraint foreign key (insider_report_id) references insider_reports (id)
) engine = InnoDB;


