/*
<PERSON>D<PERSON> [master]> select table_name AS `Table`, round(((data_length + index_length) /1024/1024), 2) `Size in MB` FROM information_schema.TABLES WHERE table_schema = "master" AND table_name="sqs_jobs";
+----------+------------+
| Table    | Size in MB |
+----------+------------+
| sqs_jobs |       4.56 |
+----------+------------+

MariaD<PERSON> [master]> select count(1) from sqs_jobs;
+----------+
| count(1) |
+----------+
|     6574 |
+----------+
*/

ALTER TABLE sqs_jobs
  ADD COLUMN dag_id VARCHAR(255) DEFAULT NULL,
  ADD COLUMN task_id VARCHAR(255) DEFAULT NULL,
  ADD COLUMN dag_execution_date DATETIME DEFAULT NULL,
  ADD COLUMN try_number INT(11) DEFAULT NULL,
  MODIFY COLUMN chronos_config_name VARCHAR(255),
  DROP INDEX mesos_task_id,
  MODIFY COLUMN mesos_task_id VARCHAR(255);

INSERT INTO schema_log (schema_version, DATE) VALUES (75, now());
