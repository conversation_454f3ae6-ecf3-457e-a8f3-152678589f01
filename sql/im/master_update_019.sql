drop table if exists monthly_fundamental;
drop table if exists monthly_fundamental_data;
delete from schema_log where schema_version = 19;

create table monthly_fundamental (
  id bigint not null,
  instrument_id bigint not null,
  year int not null,
  month int not null,
  period_length varchar(50) not null,
  current_data_id bigint default null,
  primary key (id),
  unique instrument_year_month_period_unique (instrument_id, year, month, period_length)
) ENGINE=InnoDB;

create table monthly_fundamental_data (
  id bigint not null,
  monthly_fundamental_id bigint default null,
  version int not null,
  created_at datetime not null,
  deleted tinyint(1) not null default 1,
  value blob null default null,
  primary key (id),
  unique single_version (monthly_fundamental_id, version)
) ENGINE=InnoDB;

insert into schema_log (schema_version, date) values (19, now());


