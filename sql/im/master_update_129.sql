CREATE TABLE toggles (
                         id BIGINT NOT NULL AUTO_INCREMENT,
                         name VA<PERSON>HA<PERSON>(255) NOT NULL,
                         state TINYINT(1) NOT NULL,
                         PRIMARY KEY (id),
                         KEY name (name)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;

CREATE TABLE version_toggle_state (
                                      id BIGINT NOT NULL AUTO_INCREMENT,
                                      toggle_id BIGINT(20) NOT NULL,
                                      version INT(11) NOT NULL,
                                      created_at DATETIME NOT NULL,
                                      deleted TINYINT(1) NOT NULL,
                                      value TINYINT(1) NOT NULL,
                                      PRIMARY KEY (id),
                                      KEY toggle_id (toggle_id),
                                      <PERSON><PERSON><PERSON> created_at (created_at),
                                      CONSTRAINT toggle_id FOREIGN KEY (toggle_id) REFERENCES toggles (id)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;

INSERT INTO schema_log (schema_version, date) VALUES (129, NOW());