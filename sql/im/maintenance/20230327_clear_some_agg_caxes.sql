SET SQL_SAFE_UPDATES = 0;

delete vacacad from version_aggregated_corporate_action_corporate_action_details vacacad
join aggregated_corporate_actions aca on aca.id = vacacad.aggregated_corporate_action_id
where aca.type in ('DIVIDEND', 'MERGER', 'REVERSE_STOCK_SPLIT', 'SPINOFF', 'STOCK_SPLIT');

delete vacaicai from version_aggregated_corporate_action_idc_corporate_action_id vacaicai
join aggregated_corporate_actions aca on aca.id = vacaicai.aggregated_corporate_action_id
where aca.type in ('DIVIDEND', 'MERGER', 'REVERSE_STOCK_SPLIT', 'SPINOFF', 'STOCK_SPLIT');

delete vacacbi from version_aggregated_corporate_action_cost_basis_id vacacbi
join aggregated_corporate_actions aca on aca.id = vacacbi.aggregated_corporate_action_id
where aca.type in ('DIVIDEND', 'MERGER', 'REVERSE_STOCK_SPLIT', 'SPINOFF', 'STOCK_SPLIT');

delete vacaidi from version_aggregated_corporate_action_idc_dividend_id vacaidi
join aggregated_corporate_actions aca on aca.id = vacaidi.aggregated_corporate_action_id
where aca.type in ('DIVIDEND', 'MERGER', 'REVERSE_STOCK_SPLIT', 'SPINOFF', 'STOCK_SPLIT');

delete vacaoa from version_aggregated_corporate_action_overridden_at vacaoa
join aggregated_corporate_actions aca on aca.id = vacaoa.aggregated_corporate_action_id
where aca.type in ('DIVIDEND', 'MERGER', 'REVERSE_STOCK_SPLIT', 'SPINOFF', 'STOCK_SPLIT');

delete vacaob from version_aggregated_corporate_action_overridden_by vacaob
join aggregated_corporate_actions aca on aca.id = vacaob.aggregated_corporate_action_id
where aca.type in ('DIVIDEND', 'MERGER', 'REVERSE_STOCK_SPLIT', 'SPINOFF', 'STOCK_SPLIT');

delete aca from aggregated_corporate_actions aca where aca.type in ('DIVIDEND', 'MERGER', 'REVERSE_STOCK_SPLIT', 'SPINOFF', 'STOCK_SPLIT');

SET SQL_SAFE_UPDATES = 1;