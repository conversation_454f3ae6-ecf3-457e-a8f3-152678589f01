create database ftnews character set 'utf8' collate utf8_general_ci;

create table news (
  id bigint(20) not null auto_increment,
  headline varchar(255) not null,
  date datetime not null,
  push_date datetime null,
  url varchar(255) not null,
  superurl varchar(255) not null,
  primary key (id),
  unique (url),
  unique (superurl)
) engine = InnoDB;

create table news_tickers (
  id bigint(20) not null auto_increment,
  news_id bigint(20) not null,
  ticker varchar (255),
  primary key (id),
  foreign key (news_id) references news (id),
  unique (news_id, ticker)
) engine = InnoDB;
