#!/usr/bin/env python
import MySQLdb

ID_PADDING = 1000000000

# primary key must be padded to avoid conflict
update_primary_keys = [
  "balances",
  "cash_positions",
  "lots",
  "mirror_entry",
  "orders",
  "positions",
  "previous_trade_prices",
  "stocks",
  "stock_events",
  "trades"
]

# foreign keys must be updated using the id padding
update_foreign_keys = [
  {"tbl":"cash_positions", "keys":["trade_id"]},
  {"tbl":"lot_source_lots", "keys":["lot_id", "source_lot_id"]},
  {"tbl":"mirror_entry", "keys":["trade_id"]},
  {"tbl":"portfolio_current_positions", "keys":["position_id"]},
  {"tbl":"position_lots", "keys":["position_id", "lot_id"]},
  {"tbl":"positions", "keys":["trade_id"]},
  {"tbl":"previous_trade_prices", "keys":["trade_id"]},
  {"tbl":"trades", "keys":["order_id", "stock_event_id"]}
]

# the stock_id column must be rewritten using the instrumentid column
update_stock_id = [
  "orders",
  "positions",
  "stock_events",
  "trades"
]

# tables from which we need to copy data
copy_data_from_tables = [
  "balances",
  "cash_positions",
  "lots",
  "lot_source_lots",
  "mirror_entry",
  "orders",
  "portfolio_current_positions",
  "portfolios",
  "portfolio_tags",
  "position_lots",
  "positions",
  "previous_trade_prices",
  "trades"
]

def exec_statement(conn, query):
  cursor = conn.cursor()
  cursor.execute(query)
  cursor.close()

def exec_single(conn, query):
  cursor = conn.cursor()
  cursor.execute(query)
  row = cursor.fetchone()
  cursor.close()
  return row

def exec_multi(conn, query):
  cursor = conn.cursor()
  cursor.execute(query)
  for row in cursor.fetchall():
    yield row
  cursor.close()

def drop_foreign_keys():
  for shard in range(0, 11):
    try:
      conn = connect_to_shard(shard)
      exec_statement(conn, "alter table orders drop foreign key FKC3DF62E54CAF3620")
      exec_statement(conn, "alter table positions drop foreign key FK65C08C6A4CAF3620")
      exec_statement(conn, "alter table stock_events drop foreign key stock_events_ibfk_1")
      exec_statement(conn, "alter table trades drop foreign key FKCC663B8F4CAF3620, drop foreign key FKCC663B8FC2FCA0A7, drop key portfolio_id")
      conn.commit()
      conn.close()
    except:
      pass

def update_primary_keys_and_foreign_keys(shard):
  padding = shard * ID_PADDING
  queries = []
  for tbl in update_primary_keys:
    queries.append("update %s set id = id + %s" % (tbl, padding))
  for foreign_keys in update_foreign_keys:
    to_set = ", ".join(["%(c)s = %(c)s + %(p)s" % {"c":col, "p":padding} for col in foreign_keys['keys']])
    queries.append("update %s set %s" % (foreign_keys['tbl'], to_set))
  conn = connect_to_shard(shard)
  exec_statement(conn, "set foreign_key_checks = 0")
  for query in queries:
    exec_statement(conn, query)
  exec_statement(conn, "set foreign_key_checks = 1")
  conn.commit()
  conn.close()

def remap_stock_id_columns(shard):
  conn = connect_to_shard(shard)
  for tbl in update_stock_id:
    query = "update %s, shard0.stocks set %s.stock_id = shard0.stocks.id where %s.instrumentid = shard0.stocks.instrumentid" % (tbl, tbl, tbl)
    exec_statement(conn, query)
  conn.commit()
  conn.close()

def remap_stock_event_id_column(shard):
  conn = connect_to_shard(shard)
  query = "update trades, shard%s.stock_events, shard0.stock_events set trades.stock_event_id = shard0.stock_events.id where trades.stock_event_id = shard%s.stock_events.id and shard%s.stock_events.caxid = shard0.stock_events.caxid" % (shard, shard, shard)
  exec_statement(conn, query)
  conn.commit()
  conn.close()

def add_all_stock_events_to_shard0():
  conn = connect_to_shard(0)
  for shard in range(1, 11):
    fields_to_insert = "id,type,stock_id,instrumentid,record_date,payment_date,reverted,amount_per_share,split_type,cashout_price,split_ratio,shares_per_share,closing_price,issued_stock_id,caxid,version,reference_date,instrument_action,quotes"
    query = "insert ignore into stock_events (%s) select %s from shard%s.stock_events" % (fields_to_insert, fields_to_insert, shard)
    exec_statement(conn, query)
  conn.commit()
  conn.close()

def add_all_stocks_to_shard0():
  conn = connect_to_shard(0)
  for shard in range(1, 11):
    fields_to_insert = "id,symbol,instrumentid"
    query = "insert ignore into stocks (%s) select %s from shard%s.stocks" % (fields_to_insert, fields_to_insert, shard)
    exec_statement(conn, query)
  conn.commit()
  conn.close()

def connect_to_shard(shard):
  return MySQLdb.connect(
      host = "localhost",
      user = "root",
      passwd = "",
      db = "shard%s" % shard)

def copy_data(shard):
  conn = connect_to_shard(shard)
  exec_statement(conn, "set foreign_key_checks = 0")
  for tbl in copy_data_from_tables:
    fields=[]
    for row in exec_multi(conn, "desc %s" % tbl):
      fields.append(row[0])
    fields_names=",".join(fields)
    query = "insert into shard0.%s (%s) select %s from %s" % (tbl, fields_names, fields_names, tbl)
    exec_statement(conn, query)
  exec_statement(conn, "set foreign_key_checks = 1")
  conn.commit()
  conn.close()

def create_merging_log():
  conn = connect_to_shard(0)
  exec_statement(conn, "create table merging_log (entry_at datetime not null, comment text not null)")
  conn.commit()
  conn.close()

# lock the merge by creating a log table
create_merging_log()
drop_foreign_keys()

# update primary keys
# update foreign keys
for shard in range(1,11):
  update_primary_keys_and_foreign_keys(shard)

# merge stocks table
add_all_stocks_to_shard0()

# remap stock_id columns
for shard in range(1,11):
  remap_stock_id_columns(shard)

# merge stock_events table
add_all_stock_events_to_shard0()

# remap stock_event_id columns
for shard in range(1, 11):
  remap_stock_event_id_column(shard)

# ready! let's copy the data
for shard in range(1,11):
  copy_data(shard)

