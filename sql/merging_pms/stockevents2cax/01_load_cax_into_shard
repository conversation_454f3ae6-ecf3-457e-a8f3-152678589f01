#!/bin/sh
if [ ! -r "$1" ]; then
  echo "cannot read \"$1\""
  exit
fi
for row in `cat "$1"`; do
  shard=$(echo $row | cut -d, -f 1)
  se_id=$(echo $row | cut -d, -f 2)
  caxid=$(echo $row | cut -d, -f 3)
  is_cax_there=$(mysql -ureadonly -p9228f82e -hpm$shard `pmdb $shard` \
      -NBe"select count(*) from stock_events where caxid = $caxid")
  if [ 0 -eq $is_cax_there ]; then
    data=$(mysql -ureadonly -p9228f82e -hpm$shard `pmdb $shard` \
      -NBe"select instrumentid,date(payment_date - interval 2 week),date(payment_date + interval 2 week) \
           from stock_events where id = $se_id" | tr '\t' ' ' | sed -e 's/-//g')
    ikq pm$shard SyncCorpActionsWithMaster $data
    se_id_of_cax=$(mysql -ureadonly -p9228f82e -hpm$shard `pmdb $shard` \
      -NBe"select id from stock_events where caxid = $caxid")
    if echo "$se_id_of_cax" | egrep -q '[0-9]+'; then
      ikq pm$shard SetRevertedStockEvent $se_id_of_cax true
    else
      echo "unable to revert $caxid; most likely it was not loaded"
    fi
  fi
done

