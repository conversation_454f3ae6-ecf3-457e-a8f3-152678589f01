#!/bin/sh
function get_current_close() {
  instrumentid=$1
  current_date=$2
  current_quotes=$(ikq htf0 GetQuotes $instrumentid $current_date)
  if [ "$current_quotes" = '[]' ]; then
    echo "null"
  else
    current_close=$(echo $current_quotes | jgrep 0.c)
    if [ "null" = "$current_close" ]; then
      echo "null"
    else
      echo "$current_close" | sed -e 's/^\([^\.]*\(\..\{1,4\}\)\).*$/\1/'
    fi
  fi
}

instrumentid=$1
current_date=$2
if echo $instrumentid | egrep -v -q '[0-9]+'; then
  echo "$0 instrumentid date"
  exit 1
fi
if echo $current_date | egrep -v -q '20[01][0-9][0-1][0-9][0-3][0-9]'; then
  echo "$0 instrumentid date"
  exit 2
fi
current_close=$(get_current_close $instrumentid $current_date)
if [ "$current_close" = "null" ]; then
  echo "no quote on starting date"
  exit 3
fi
now=$(date +'%Y%m%d')
while [ "$now" -ne "$current_date" ]; do
  today_close=$(get_current_close $instrumentid $current_date)
  if [ "$today_close" = "null" ]; then
    echo "$current_date: add $current_close"
    echo -n "  > "
    ikq htf0 AddInstrumentIdBasedHistoricalQuote $instrumentid "$current_date" "$current_close"
  else
    current_close=$today_close
    echo "$current_date: updating close to $current_close"
  fi
  current_date=$(date -d "$current_date 1 day" +'%Y%m%d')
done

