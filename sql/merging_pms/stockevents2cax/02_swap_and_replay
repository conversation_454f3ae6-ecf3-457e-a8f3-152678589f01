#!/bin/sh
if [ ! -r "$1" ]; then
  echo "cannot read \"$1\""
  exit
fi

num_shards=`cat "$1" | cut -d, -f 1 | sort -u | wc -l`
if [ ! "1" -eq "$num_shards" ]; then
  echo "cannot work on multiple shards"
  exit
fi

shard=`cat "$1"| cut -d, -f 1 | sort -u`

echo "portfolios affected"
query=$(cat $1 | cut -d, -f 2 | tr '\n' ',' | \
    sed -e 's/^\(.*\),$/select distinct portfolio_id from trades where stock_event_id in (\1)/')
for pid in $(mysql -ureadonly -p9228f82e -hpm$shard `pmdb $shard` -NBe"$query"); do
  since_gains=$(mysql -ureadonly -p9228f82e -hpm$shard `pmdb $shard` \
      -NBe "select exp(sum(log(coalesce(day_gain,1)))) from balances where portfolio_id = $pid")
  echo "$pid (since gains $since_gains)"
done

echo "swaping"
for row in `cat "$1"`; do
  se_id=$(echo $row | cut -d, -f 2)
  caxid=$(echo $row | cut -d, -f 3)
  se_id_of_cax=$(mysql -ureadonly -p9228f82e -hpm$shard `pmdb $shard` \
      -NBe"select id from stock_events where caxid = $caxid")
  ikq pm$shard SetRevertedStockEvent $se_id true
  ikq pm$shard SetRevertedStockEvent $se_id_of_cax false
done

for pid in $(mysql -ureadonly -p9228f82e -hpm$shard `pmdb $shard` -NBe"$query"); do
  since_gains=$(mysql -ureadonly -p9228f82e -hpm$shard `pmdb $shard` \
      -NBe "select exp(sum(log(coalesce(day_gain,1)))) from balances where portfolio_id = $pid")
  echo "replaying $pid      (since gains $since_gains)"
  ikq pm$shard ReplayPortfolio $pid
  since_gains=$(mysql -ureadonly -p9228f82e -hpm$shard `pmdb $shard` \
      -NBe "select exp(sum(log(coalesce(day_gain,1)))) from balances where portfolio_id = $pid")
  echo "  -> done with $pid (since gains $since_gains)"
  echo
done
