#!/bin/sh

echo "max id must be smaller than 1 billion:"
for tbl in balances cash_positions lots mirror_entry orders positions previous_trade_prices trades; do
  for i in $(seq 0 10); do
    mysql -uroot -NBe "select max(id) from shard$i.$tbl"
  done
done | sort -n | tail -1
echo

echo "overlapping cax (should be empty):"
for i in $(seq 0 10); do
  mysql -uroot shard$i -NBe 'select distinct s.instrumentid from stocks s, stock_events e, trades t where e.stock_id = s.id and t.stock_event_id is not null and e.id = t.stock_event_id' > "cax_shard$i"
done
kiids=`mktemp`
for i in $(seq 0 10); do
  for j in $(seq 0 10); do
    if [ $i -ne $j ]; then
      comm -1 -2 cax_shard$i cax_shard$j >> $kiids
    fi
  done
done
sort -n -u $kiids

