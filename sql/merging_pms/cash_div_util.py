import datetime, decimal

def is_amt_per_share_close(se_value, lc_value, max_delta=decimal.Decimal('0.00')):
  return abs(se_value - lc_value) <= max_delta
  
def is_weekend_spread(se_exdate, gcx_exdate):
  return ((gcx_exdate-se_exdate == datetime.timedelta(days=3))
     and (gcx_exdate.weekday() == 0)
     and ( se_exdate.weekday() == 4))

#def date_map_func(hs):
#  dt0 = datetime.datetime.strptime(hs[0], '%Y-%m-%d')
#  dt1 = datetime.datetime.strptime(hs[1], '%Y-%m-%d')
#  return (datetime.date(dt0.year, dt0.month, dt0.day), datetime.date(dt1.year, dt1.month, dt1.day))

#holiday_spreads = map(date_map_func, [
#  ('2009-11-25','2009-11-27'), 
#  ('2008-12-31','2009-01-02'), 
#  ('2009-02-13','2009-02-17')
#])
#def is_holiday_spread(se_exdate, gcx_exdate):
#  for hs in holiday_spreads:
#    if (se_exdate == hs[0]) and (gcx_exdate == hs[1]):
#       return True
#  return False

#acceptable = dict([(x, True) for x in [
#]])
#def is_manual(gcx_id):
#  return gcx_id in acceptable

def is_sane_date_spread(se_exdate, gcx_exdate, gcx_id, max_days=1):
  if se_exdate == gcx_exdate:
    return True # exact match
  elif is_weekend_spread(se_exdate, gcx_exdate):
    return True # spread is over a weekend
#  elif is_holiday_spread(se_exdate, gcx_exdate):
#    return True # spread is over a weekend
  if (abs((gcx_exdate-se_exdate).days) <= max_days):
    return True # close enough?
  return False;

# +-10 date range, .03 a.p.s. tolerance
# All 4493
# ?'s 84
# !'s 293

def compare_cash_div(stock_event, local_cax):
  issues = list()
  if not local_cax.type() == 'cash-payment':
    issues.append("wrong kind")
  else:
    if not is_amt_per_share_close(stock_event.amount_per_share, local_cax.amountPerShare()):
      issues.append("amount per share %s vs %s" %(stock_event.amount_per_share, local_cax.amountPerShare()))
    if not is_sane_date_spread(stock_event.payment_date, local_cax.pay_date(), local_cax.id):
      issues.append("pay date (SE:%s vs GX:%s; delta:%s)" % 
              (stock_event.payment_date, local_cax.pay_date(), (local_cax.pay_date()-stock_event.payment_date).days))
    if not is_sane_date_spread(stock_event.record_date, local_cax.ex_date(), local_cax.id):
      issues.append("ex date (SE:%s vs GX:%s; delta:%s)" % 
              (stock_event.record_date, local_cax.ex_date(), (local_cax.ex_date()-stock_event.record_date).days))
  return None if len(issues) == 0 else "%s %s" % (len(issues), ",".join(issues))

