#!/usr/bin/env python
import MySQLdb


def exec_statement(conn, query):
  cursor = conn.cursor()
  cursor.execute(query)
  cursor.close()

def exec_single(conn, query):
  cursor = conn.cursor()
  cursor.execute(query)
  row = cursor.fetchone()
  cursor.close()
  return row

def exec_multi(conn, query):
  cursor = conn.cursor()
  cursor.execute(query)
  for row in cursor.fetchall():
    yield row
  cursor.close()

def connect_to_shard(shard):
  return MySQLdb.connect(
      host = "localhost",
      user = "root",
      passwd = "",
      db = "shard%s" % shard)

def current_positions(conn, extra):
  pids_to_held_instruments = {}
  query = "select c.portfolio_id,group_concat(p.instrumentid) from portfolio_current_positions c, positions p where c.position_id = p.id %s group by p.portfolio_id" % extra
  for row in exec_multi(conn, query):
    pids_to_held_instruments[row[0]] = set(row[1].split(','))
  return pids_to_held_instruments

def current_position_correct():
  for shard in range(1,11):
    print "shard %s" % shard
    conn_shardN = connect_to_shard(shard)
    pids_shardN = current_positions(conn_shardN, "")
    extra_criteria = " and c.portfolio_id in (%s) " % (','.join(["%s" % x for x in pids_shardN.keys()]))
    conn_shardN.close()
    conn_shard0 = connect_to_shard(0)
    pids_shard0 = current_positions(conn_shard0, extra_criteria)
    for pid in pids_shardN.keys():
      if not pids_shardN[pid] == pids_shard0[pid]:
        print pid
    conn_shard0.close()

def gen_no_dangling_pointers():
  update_foreign_keys = [
    {"tbl":"cash_positions", "keys":["trade_id"]},
    {"tbl":"lot_source_lots", "keys":["lot_id", "source_lot_id"]},
    {"tbl":"mirror_entry", "keys":["trade_id"]},
    {"tbl":"portfolio_current_positions", "keys":["position_id"]},
    {"tbl":"position_lots", "keys":["position_id", "lot_id"]},
    {"tbl":"positions", "keys":["trade_id"]},
    {"tbl":"previous_trade_prices", "keys":["trade_id"]},
    {"tbl":"trades", "keys":["order_id", "stock_event_id"]}
  ]
  conn_shard0 = connect_to_shard(0)
  for row in update_foreign_keys:
    tb_left = row['tbl']
    for key in row['keys']:
      id_left = key
      tb_right = "%ss" % id_left[:-3]
      id_right = "id"
      query = "\"select count(*) from %s t0 left outer join %s t1 on t0.%s = t1.%s where t0.%s is not null and t1.%s is null\"," % (tb_left, tb_right, id_left, id_right, id_left, id_right)
      print query
  conn_shard0.close()

def no_dangling_pointers():
  conn = connect_to_shard(0)
  queries = [
    "select count(*) from trades t left outer join stock_events e on t.stock_event_id = e.id where t.stock_event_id is not null and e.id is null",
    "select count(*) from trades t left outer join stocks s on t.stock_id = s.id where t.stock_id is not null and s.id is null",
    "select count(*) from cash_positions t0 left outer join trades t1 on t0.trade_id = t1.id where t0.trade_id is not null and t1.id is null",
    "select count(*) from lot_source_lots t0 left outer join lots t1 on t0.lot_id = t1.id where t0.lot_id is not null and t1.id is null",
    "select count(*) from lot_source_lots t0 left outer join lots t1 on t0.source_lot_id = t1.id where t0.source_lot_id is not null and t1.id is null",
    "select count(*) from mirror_entry t0 left outer join trades t1 on t0.trade_id = t1.id where t0.trade_id is not null and t1.id is null",
    "select count(*) from portfolio_current_positions t0 left outer join positions t1 on t0.position_id = t1.id where t0.position_id is not null and t1.id is null",
    "select count(*) from position_lots t0 left outer join positions t1 on t0.position_id = t1.id where t0.position_id is not null and t1.id is null",
    "select count(*) from position_lots t0 left outer join lots t1 on t0.lot_id = t1.id where t0.lot_id is not null and t1.id is null",
    "select count(*) from positions t0 left outer join trades t1 on t0.trade_id = t1.id where t0.trade_id is not null and t1.id is null and t0.portfolio_id is not null",
    "select count(*) from previous_trade_prices t0 left outer join trades t1 on t0.trade_id = t1.id where t0.trade_id is not null and t1.id is null",
    "select count(*) from trades t0 left outer join orders t1 on t0.order_id = t1.id where t0.order_id is not null and t1.id is null",
    "select count(*) from trades t0 left outer join stock_events t1 on t0.stock_event_id = t1.id where t0.stock_event_id is not null and t1.id is null",
    "select count(*) from trades t left outer join stocks s on t.instrumentid = s.instrumentid where t.instrumentid is not null and s.instrumentid is null",
    "select count(*) from trades t, stocks s where t.stock_id = s.id and t.instrumentid <> s.instrumentid",
    "select count(*) from orders t, stocks s where t.stock_id = s.id and t.instrumentid <> s.instrumentid",
    "select count(*) from positions t, stocks s where t.stock_id = s.id and t.instrumentid <> s.instrumentid",
    "select count(*) from stock_events t, stocks s where t.stock_id = s.id and t.instrumentid <> s.instrumentid"
  ]
  for query in queries:
    if not 0 == (exec_single(conn, query)[0]):
      print "%s is problematic" % query
  conn.close()

#gen_no_dangling_pointers()
current_position_correct()
no_dangling_pointers()

