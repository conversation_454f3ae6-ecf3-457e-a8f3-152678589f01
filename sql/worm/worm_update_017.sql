CREATE TABLE glacier_reconciliation_jobs (
  id                BIGINT(20)      NOT NULL AUTO_INCREMENT,
  version           BIGINT          NOT NULL DEFAULT 0,
  created_at        DATETIME        NOT NULL,
  from_date         <PERSON><PERSON><PERSON><PERSON><PERSON>        DEFAULT NULL,
  to_date           <PERSON><PERSON><PERSON><PERSON><PERSON>        DEFAULT NULL,
  state             VARCHAR(255)    NOT NULL,

  PRIMARY KEY (id)
) ENGINE = InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE version_glacier_reconciliation_job_state (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  glacier_reconciliation_job_id BIGINT(20) NOT NULL,
  version INT(11) NOT NULL,
  created_at DATETIME NOT NULL,
  deleted TINYINT(1) NOT NULL,
  value VARCHAR(255) NOT NULL,
  PRIMARY KEY (id),
  CONSTRAINT version_glacier_reconciliation_job_state_id
     FOREIGN KEY (glacier_reconciliation_job_id)
     REFERENCES glacier_reconciliation_jobs (id)
) engine=InnoDB DEFAULT charset=utf8;

ALTER TABLE glacier_inventory_retrieval_requests
    ADD COLUMN glacier_reconciliation_job_id BIGINT(20) DEFAULT NULL,
    ADD CONSTRAINT glacier_reconciliation_job_id
        FOREIGN KEY (glacier_reconciliation_job_id)
        REFERENCES glacier_reconciliation_jobs (id);

INSERT INTO schema_log (schema_version, DATE) VALUES (17, now());