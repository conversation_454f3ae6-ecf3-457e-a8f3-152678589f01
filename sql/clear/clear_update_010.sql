CREATE TABLE tinykv_logs (
  group_id    INT NOT NULL,
  log_id      BIGINT NOT NULL,
  store_id    INT NOT NULL,
  entered_at  DATETIME NOT NULL,
  is_delete   TINYINT(1) NOT NULL,
  store_key   VARCHAR(767) NOT NULL,
  value       VARBINARY(32768) DEFAULT NULL,
  PRIMARY KEY (group_id, log_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

CREATE TABLE tinykv_log_offsets (
  id           BIGINT NOT NULL AUTO_INCREMENT,
  group_id     INT,
  last_log_id  BIGINT NOT NULL,
  updated_at   DATETIME,
  PRIMARY KEY (id),
  UNIQUE KEY tinykv_log_offsets_group_id (group_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

CREATE TABLE tinykv_entries (
  store_id   INT NOT NULL,
  store_key  VARCHAR(767) NOT NULL,
  hash       INT UNSIGNED NOT NULL,
  value      VARBINARY(32768) NOT NULL,
  PRIMARY KEY (store_id, store_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

CREATE TABLE tinykv_settings (
  id            BIGINT NOT NULL AUTO_INCREMENT,
  settings_key  VARCHAR(999) NOT NULL,
  version       INT NOT NULL DEFAULT 0,
  value         MEDIUMBLOB   NOT NULL,
  PRIMARY KEY (id),
  UNIQUE KEY tinykv_settings_settings_key (settings_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

insert into schema_log (schema_version, date) values (10, now());
