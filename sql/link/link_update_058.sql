create table external_vendor_issues (
  id bigint(20) not null auto_increment,
  external_account_link_id bigint(20) not null,
  issue_type VARCHAR(255) not null,
  created_at datetime not null,
  details text,
  primary key (id),
  key evi_eal_id (external_account_link_id),
  constraint evi_eal_id foreign key (external_account_link_id) references external_account_links (id)
) engine=InnoDB;

insert into schema_log (schema_version, date) values (58, now());
