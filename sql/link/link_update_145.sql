/*
# Procedure below:

MariaDB [link]> select table_name AS `Table`, round(((data_length + index_length) /1024/1024), 2) `Size in MB` FROM information_schema.TABLES WHERE table_schema = "link" AND table_name="quovo_positions";;
+-----------------+------------+
| Table           | Size in MB |
+-----------------+------------+
| quovo_positions |    8654.13 |
+-----------------+------------+
1 <USER> <GROUP> set (0.00 sec)

MariaDB [link]> select count(*) from quovo_positions;
+----------+
| count(*) |
+----------+
| 22471048 |
+----------+
1 <USER> <GROUP> set (12.15 sec)

# Identify the master db
[alice@sv209 ~]$ dig +short db-sv2-user-master.wlth.fr
sv223.wlth.fr.
************

MariaDB [link]> show slave hosts;
+-----------+---------------+------+-----------+
| Server_id | Host          | Port | Master_id |
+-----------+---------------+------+-----------+
|       117 | sv217.wlth.fr | 3306 |       123 |
|       423 | ny423.wlth.fr | 3306 |       123 |
|       118 | sv218.wlth.fr | 3306 |       123 | <-- etl slave (see below)
+-----------+---------------+------+-----------+
3 <USER> <GROUP> set (0.00 sec)

# don't stop the etl slave
[alice@sv209 ~]$ dig +short db-sv2-user-etl.wlth.fr
sv218.wlth.fr.
***********

# Check slave status
mysql -uwfadmin -p -hsv217 link
show slave status;
mysql -uwfadmin -p -hny418 link
show slave status;

# Stop the check_slave_running alarm for sv217.wlth.fr in https://icinga-sv2.wlth.fr/
# Stop the check_slave_running alarm for ny418.wlth.fr in https://icinga-sv2.wlth.fr/

# Stop slave on sv217
ssh sv217
mysql -uwfadmin -p
show slave status;
stop slave;
show slave status;

# Stop slave on ny418
ssh ny418
mysql -uwfadmin -p
show slave status;
stop slave;
show slave status;

# Make changes on master db
ssh sv223
screen

# Dry run:
pt-online-schema-change \
    --alter "ADD COLUMN quovo_external_position_id BIGINT(20) DEFAULT NULL" \
    --charset utf8 \
    --ask-pass D=link,t=quovo_positions,A=utf8,h=localhost,u=wfadmin \
    --alter-foreign-keys-method drop_swap \
    --print \
    --dry-run

# Execute
pt-online-schema-change \
    --alter "ADD COLUMN quovo_external_position_id BIGINT(20) DEFAULT NULL" \
    --charset utf8 \
    --ask-pass D=link,t=quovo_positions,A=utf8,h=localhost,u=wfadmin \
    --alter-foreign-keys-method drop_swap \
    --print \
    --execute

# Exit screen
exit

# Start slave on sv217
ssh sv217
mysql -uwfadmin -p
show slave status;
start slave;
show slave status;

# Start slave on ny418
ssh ny418
mysql -uwfadmin -p
show slave status;
start slave;
show slave status;

# Start the check_slave_running alarm for sv218.wlth.fr in https://icinga-sv2.wlth.fr/
# Start the check_slave_running alarm for sv217.wlth.fr in https://icinga-sv2.wlth.fr/
# Start the check_slave_running alarm for ny418.wlth.fr in https://icinga-sv2.wlth.fr/
*/

# pt-online-schema-change
ALTER TABLE quovo_positions ADD COLUMN quovo_external_position_id BIGINT(20) DEFAULT NULL;
INSERT INTO schema_log (schema_version, DATE) VALUES (145, now());