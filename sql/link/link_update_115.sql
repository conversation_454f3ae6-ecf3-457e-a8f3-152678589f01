/*
Example timing data:
  time mysql -ureadonly -p -hsv218 link -sse 'select * from external_account_links' > /dev/null
real    1m23.796s
user    0m9.954s
sys     0m2.401s

# ssh link0
# ps aux | grep LinkServer
# Prod db host: sv218
# mysql> select @@hostname;
# +---------------+
# | @@hostname    |
# +---------------+
# | sv218.wlth.fr |
# +---------------+
#
# mysql> show slave hosts;
# +-----------+---------------+------+-------------------+-----------+
# | Server_id | Host          | Port | Rpl_recovery_rank | Master_id |
# +-----------+---------------+------+-------------------+-----------+
# |       117 | sv217.wlth.fr | 3306 |                 0 |       118 |
# |       417 | ny417.wlth.fr | 3306 |                 0 |       118 |
# |       123 | sv223.wlth.fr | 3306 |                 0 |       118 |
# +-----------+---------------+------+-------------------+-----------+

# Check slave status on sv223 and sv217
mysql -uwfadmin -p -hsv223 link
show slave status;
mysql -uwfadmin -p -hsv217 link
show slave status;
mysql -uwfadmin -p -hny417 link
show slave status;

# Stop the check_slave_running alarm for sv223.wlth.fr in https://icinga-sv2.wlth.fr/
# Stop the check_slave_running alarm for sv217.wlth.fr in https://icinga-sv2.wlth.fr/
# Stop the check_slave_running alarm for ny417.wlth.fr in https://icinga-sv2.wlth.fr/

# Stop slave on sv223
ssh sv223
mysql -uwfadmin -p
show slave status;
stop slave;
show slave status;

# Stop slave on sv217
ssh sv217
mysql -uwfadmin -p
show slave status;
stop slave;
show slave status;

# Stop slave on ny417
ssh ny417
mysql -uwfadmin -p
show slave status;
stop slave;
show slave status;

# Make changes on master db
ssh sv218
screen

# Dry run:
pt-online-schema-change \
    --alter "ADD INDEX external_account_links_type_state_institutionid (type,state,institutionid)" \
    --charset utf8 \
    --ask-pass D=link,t=external_account_links,A=utf8,h=localhost,u=wfadmin \
    --alter-foreign-keys-method drop_swap \
    --print \
    --dry-run

# Execute
pt-online-schema-change \
    --alter "ADD INDEX external_account_links_type_state_institutionid (type,state,institutionid)" \
    --charset utf8 \
    --ask-pass D=link,t=external_account_links,A=utf8,h=localhost,u=wfadmin \
    --alter-foreign-keys-method drop_swap \
    --print \
    --execute

# Exit screen
exit

# Start slave on sv223
ssh sv223
mysql -uwfadmin -p
show slave status;
start slave;
show slave status;

# Start slave on sv217
ssh sv217
mysql -uwfadmin -p
show slave status;
start slave;
show slave status;

# Start slave on ny417
ssh ny417
mysql -uwfadmin -p
show slave status;
start slave;
show slave status;

# Start the check_slave_running alarm for sv223.wlth.fr in https://icinga-sv2.wlth.fr/
# Start the check_slave_running alarm for sv217.wlth.fr in https://icinga-sv2.wlth.fr/
# Start the check_slave_running alarm for ny417.wlth.fr in https://icinga-sv2.wlth.fr/
*/

# pt-online-schema-change
ALTER TABLE external_account_links ADD INDEX external_account_links_type_state_institutionid (type,state,institutionid);

INSERT INTO schema_log (schema_version, date) VALUES (115, NOW());
