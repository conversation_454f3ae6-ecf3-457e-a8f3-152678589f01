/*
Example timing data:
  time mysql -ureadonly -p -hsv218 link -sse 'select * from quovo_transactions' > /dev/null
  real 2m40.114s
*/

ALTER TABLE quovo_transactions
    ADD COLUMN expense_category VARCHAR(255) DEFAULT NULL AFTER transaction_type;

/*
# Prod db host: sv218
# Dry run:
pt-online-schema-change \
    --alter "ADD COLUMN expense_category VARCHAR(255) DEFAULT NULL AFTER transaction_type" \
    --charset utf8 \
    --ask-pass D=link,t=quovo_transactions,A=utf8,h=localhost,u=kaching \
    --alter-foreign-keys-method drop_swap \
    --print \
    --dry-run

# Execute
pt-online-schema-change \
    --alter "ADD COLUMN expense_category VARCHAR(255) DEFAULT NULL AFTER transaction_type" \
    --charset utf8 \
    --ask-pass D=link,t=quovo_transactions,A=utf8,h=localhost,u=kaching \
    --alter-foreign-keys-method drop_swap \
    --print \
    --execute
*/

INSERT INTO schema_log (schema_version, date) VALUES (99, now());