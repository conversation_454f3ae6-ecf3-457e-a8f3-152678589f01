CREATE TABLE version_quovo_institution_login_notes (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  quovo_institution_id BIGINT(20) NOT NULL,
  version INT(11) NOT NULL,
  created_at DATETIME NOT NULL,
  deleted TINYINT(1) NOT NULL DEFAULT 1,
  value TEXT NOT NULL,
  PRIMARY KEY (id),
  CONSTRAINT version_quovo_institution_login_notes_quovo_institution_id
  FOREIGN KEY (quovo_institution_id) references quovo_institutions(id),
  KEY version_quovo_insitution_login_notes_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

ALTER TABLE quovo_institutions ADD COLUMN login_notes TEXT DEFAULT NULL;

INSERT INTO schema_log (schema_version, DATE) VALUES (82, now());