CREATE TABLE brokerage_account_aggregates (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  external_account_link_id bigint(20) NOT NULL,
  taxable tinyint NOT NULL,
  total_value decimal(20,8) DEFAULT NULL,
  cash_value decimal(20,8) DEFAULT NULL,
  unrecognized_value decimal(20,8) DEFAULT NULL,
  advisory_fees decimal(20,8) DEFAULT NULL,
  transaction_fees decimal(20,8) DEFAULT NULL,
  instrument_fees decimal(20,8) DEFAULT NULL,
  volatility decimal(20,8) DEFAULT NULL,
  PRIMARY KEY (id),
  KEY brokerage_account_aggregates_external_account_link_id (external_account_link_id),
  CONSTRAINT brokerage_account_aggregates_external_account_link_id FOREIGN KEY (external_account_link_id) REFERENCES external_account_links (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE brokerage_account_aggregate_external_accounts (
  external_account_id bigint(20) NOT NULL UNIQUE,
  brokerage_account_aggregate_id bigint(20) NOT NULL,
  CONSTRAINT external_account_id FOREIGN KEY (external_account_id) REFERENCES external_accounts (id),
  KEY brokerage_account_aggregate_id (brokerage_account_aggregate_id),
  CONSTRAINT brokerage_account_aggregate_id FOREIGN KEY (brokerage_account_aggregate_id) REFERENCES brokerage_account_aggregates (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO schema_log (schema_version, DATE) VALUES (26, now());
