-- Taken straight from htf0 on 2008-11-20
-- (after removing the DROP TABLE statements!!!)

-- MySQL dump 10.10
--
-- Host: localhost    Database: historical
-- ------------------------------------------------------
-- Server version       5.0.22

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `historical_quotes`
--

CREATE TABLE IF NOT EXISTS `historical_quotes` (
  `id` bigint(20) NOT NULL auto_increment,
  `symbol` varchar(12) NOT NULL,
  `date` date NOT NULL,
  `open` decimal(14,4) default NULL,
  `high` decimal(14,4) default NULL,
  `low` decimal(14,4) default NULL,
  `close` decimal(14,4) default NULL,
  `volume` bigint(20) default NULL,
  `adj_close` decimal(14,4) default NULL,
  PRIMARY KEY  (`id`),
  UNIQUE KEY `symbol` (`symbol`,`date`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;

--
-- Table structure for table `yahoo_historical`
--

CREATE TABLE IF NOT EXISTS `yahoo_historical` (
  `id` bigint(20) NOT NULL auto_increment,
  `symbol` varchar(12) default NULL,
  `date` date default NULL,
  `open` decimal(14,4) default NULL,
  `high` decimal(14,4) default NULL,
  `low` decimal(14,4) default NULL,
  `close` decimal(14,4) default NULL,
  `volume` bigint(20) default NULL,
  `adj_close` decimal(14,4) default NULL,
  PRIMARY KEY  (`id`),
  UNIQUE KEY `symbol` (`symbol`,`date`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;
