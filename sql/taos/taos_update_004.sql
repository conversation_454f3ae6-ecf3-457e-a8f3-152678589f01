/*

MariaDB [taos]> show create table logged_actions;
+----------------+-------------------------------------------------------------------------------+
| Table          | Create Table                                                                  |
+----------------+-------------------------------------------------------------------------------+
| logged_actions | CREATE TABLE `logged_actions` (                                               |
|                |   `id` int(11) NOT NULL AUTO_INCREMENT,                                       |
|                |   `admin_id` int(11) DEFAULT NULL,                                            |
|                |   `path` varchar(255) COLLATE utf8_bin DEFAULT NULL,                          |
|                |   `method` varchar(255) COLLATE utf8_bin DEFAULT NULL,                        |
|                |   `body` text COLLATE utf8_bin DEFAULT NULL,                                  |
|                |   `created_at` datetime / mariadb-5.3 / DEFAULT NULL,                         |
|                |   `ldap_user` varchar(255) COLLATE utf8_bin DEFAULT NULL,                     |
|                |   `elapsed_ms` int(11) DEFAULT NULL,                                          |
|                |   PRIMARY KEY (`id`),                                                         |
|                |   KEY `logged_actions_created_at` (`created_at`),                             |
|                |   KEY `ldap_user_index` (`ldap_user`)                                         |
|                | ) ENGINE=InnoDB AUTO_INCREMENT=14901704 DEFAULT CHARSET=utf8 COLLATE=utf8_bin |
+----------------+-------------------------------------------------------------------------------+

MariaDB [taos]> select count(*) from logged_actions;
+----------+
| count(*) |
+----------+
| 14879177 |
+----------+

MariaDB [taos]> select table_name AS `Table`, round(((data_length + index_length) /1024/1024), 2) `Size in MB` FROM information_schema.TA
                BLES WHERE table_schema = "taos" AND table_name="logged_actions";
+----------------+------------+
| Table          | Size in MB |
+----------------+------------+
| logged_actions | 2432.89    |
+----------------+------------+

# stop check slave icinga checks
# stop slaves

# Dry Run
pt-online-schema-change \
  --alter "DROP COLUMN body, ADD COLUMN verification varchar(255) DEFAULT NULL" \
  --alter-foreign-keys-method drop_swap \
  --charset utf8 \
  --ask-pass D=taos,t=logged_actions,A=utf8,h=localhost,u=wfadmin \
  --dry-run

# Execution
pt-online-schema-change \
  --alter "DROP COLUMN body, ADD COLUMN verification varchar(255) DEFAULT NULL" \
  --alter-foreign-keys-method drop_swap \
  --charset utf8 \
  --ask-pass D=taos,t=logged_actions,A=utf8,h=localhost,u=wfadmin \
  --execute

# Ensure ActiveRecord doesn't attempt to re-apply the migration in the future
INSERT INTO schema_migrations (version) VALUES ('20201005175507');

# restart slaves
# restart check slaves icinga checks

*/

-- Perform as pt-online-schema-change! This is the SQL equivalent of
-- https://stash.wlth.fr/projects/BACK/repos/taos/browse/db/migrate/20201005175507_drop_body_and_add_verification_on_logged_actions.rb
ALTER TABLE logged_actions
  DROP COLUMN body,
  ADD COLUMN verification varchar(255) DEFAULT NULL;
INSERT INTO schema_migrations (version) VALUES ('20201005175507');
