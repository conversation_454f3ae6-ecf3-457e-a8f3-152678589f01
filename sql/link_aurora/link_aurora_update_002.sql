CREATE TABLE external_account_transactions (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    user_id BIGINT(20) NOT NULL,
    external_account_link_id BIGINT(20) NOT NULL,
    external_account_id BIGINT(20) NOT NULL,
    vendor VARCHAR(255) NOT NULL,
    vendor_id VARCHAR(255) NOT NULL,
    amount DECIMAL(20,4) NOT NULL,
    transaction_date DATE NOT NULL,
    created_at DATETIME NOT NULL,
    updated_at DATETIME,
    currency VARCHAR(255),
    forex_rate DECIMAL(10,6),
    fees DECIMAL(20,4),
    instrument_id BIGINT(20),
    cusip VARCHAR(255),
    symbol VARCHAR(255),
    ticker_name VARCHAR(255),
    quantity DECIMAL(20,4),
    price DECIMAL(20,4),
    encrypted_memo TEXT NOT NULL,
    state VARCHAR(255) NOT NULL,
    method_of_transaction VARCHAR(255),
    merchant_name VARCHAR(255),
    encrypted_location TEXT,
    status VARCHAR(255),
    PRIMARY KEY (id),
    <PERSON><PERSON><PERSON> (user_id),
    <PERSON><PERSON><PERSON> (external_account_link_id),
    <PERSON><PERSON><PERSON> (external_account_id),
    KEY (transaction_date)
) ENGINE=InnoDB DEFAULT CHARSET = utf8mb4;

INSERT INTO schema_log (schema_version, date) VALUES (2, NOW());
