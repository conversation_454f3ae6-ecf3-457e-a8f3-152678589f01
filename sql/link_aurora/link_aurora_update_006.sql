/*

MySQL [link_aurora]> select table_name AS `Table`, round(((data_length + index_length) /1024/1024), 2) `Size in MB` FROM information_schema.TABLES WHERE table_schema = "link_aurora" AND table_name="external_account_transactions";
+-------------------------------+------------+
| Table                         | Size in MB |
+-------------------------------+------------+
| external_account_transactions |  102839.81 |
+-------------------------------+------------+
1 <USER> <GROUP> set (0.02 sec)

MySQL [link_aurora]> select table_rows `Approximate rows` FROM information_schema.TABLES WHERE table_schema = "link_aurora" AND table_name="external_account_transactions";
+------------------+
| Approximate rows |
+------------------+
|        ********* |
+------------------+
1 <USER> <GROUP> set (0.03 sec)

MySQL [link_aurora]> select table_name, column_name, referenced_table_name, referenced_column_name from information_schema.key_column_usage where referenced_table_name is not null and referenced_column_name is not null and table_schema = 'link_aurora' and table_name = 'external_account_transactions';
Empty set (0.03 sec)

MySQL [link_aurora]> describe external_account_transactions;
+--------------------------+---------------+------+-----+---------+----------------+
| Field                    | Type          | Null | Key | Default | Extra          |
+--------------------------+---------------+------+-----+---------+----------------+
| id                       | bigint(20)    | NO   | PRI | NULL    | auto_increment |
| user_id                  | bigint(20)    | NO   | MUL | NULL    |                |
| external_account_link_id | bigint(20)    | NO   | MUL | NULL    |                |
| external_account_id      | bigint(20)    | NO   | MUL | NULL    |                |
| vendor                   | varchar(255)  | NO   |     | NULL    |                |
| vendor_id                | varchar(255)  | NO   | MUL | NULL    |                |
| amount                   | decimal(20,4) | NO   |     | NULL    |                |
| transaction_date         | date          | NO   | MUL | NULL    |                |
| created_at               | datetime      | NO   |     | NULL    |                |
| updated_at               | datetime      | YES  |     | NULL    |                |
| currency                 | varchar(255)  | YES  |     | NULL    |                |
| forex_rate               | decimal(10,6) | YES  |     | NULL    |                |
| fees                     | decimal(20,4) | YES  |     | NULL    |                |
| instrument_id            | bigint(20)    | YES  |     | NULL    |                |
| cusip                    | varchar(255)  | YES  |     | NULL    |                |
| symbol                   | varchar(255)  | YES  |     | NULL    |                |
| ticker_name              | varchar(255)  | YES  |     | NULL    |                |
| quantity                 | decimal(20,4) | YES  |     | NULL    |                |
| price                    | decimal(20,4) | YES  |     | NULL    |                |
| encrypted_memo           | text          | NO   |     | NULL    |                |
| state                    | varchar(255)  | NO   |     | NULL    |                |
| method_of_transaction    | varchar(255)  | YES  |     | NULL    |                |
| merchant_name            | varchar(255)  | YES  |     | NULL    |                |
| encrypted_location       | text          | YES  |     | NULL    |                |
| status                   | varchar(255)  | YES  |     | NULL    |                |
| wf_category_guess        | varchar(255)  | YES  |     | NULL    |                |
+--------------------------+---------------+------+-----+---------+----------------+
26 <USER> <GROUP> set (0.02 sec)

# Take a snapshot from the Amazon RDS Console

# Dry Run
pt-online-schema-change --alter "MODIFY COLUMN forex_rate DECIMAL(20,8)" --charset utf8 --ask-pass D=link_aurora,t=external_account_transactions,A=utf8,h=db-link-aurora-master.wlth.fr,u=link_db_user --alter-foreign-keys-method drop_swap --dry-run

# Execution
pt-online-schema-change --alter "MODIFY COLUMN forex_rate DECIMAL(20,8)" --charset utf8 --ask-pass D=link_aurora,t=external_account_transactions, A=utf8,h=db-link-aurora-master.wlth.fr,u=link_db_user --alter-foreign-keys-method drop_swap --execution

*/

# pt-online-schema-change
ALTER TABLE external_account_transactions
  MODIFY COLUMN forex_rate DECIMAL(20,8);
  
INSERT INTO schema_log (schema_version, date) VALUES (6, NOW());