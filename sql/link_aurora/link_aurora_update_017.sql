mysql> select table_name AS `Table`, round(((data_length + index_length) /1024/1024), 2) `Size in MB` FROM information_schema.TABLES WHERE table_schema = "link_aurora" AND table_name="external_account_positions";
+----------------------------+------------+
| Table                      | Size in MB |
+----------------------------+------------+
| external_account_positions |   28379.16 |
+----------------------------+------------+
1 <USER> <GROUP> set (0.05 sec)

mysql> select table_rows `Approximate rows` FROM information_schema.TABLES WHERE table_schema = "link_aurora" AND table_name="external_account_positions";
+------------------+
| Approximate rows |
+------------------+
|         ******** |
+------------------+
1 <USER> <GROUP> set (0.04 sec)

mysql> select table_name, column_name, referenced_table_name, referenced_column_name from information_schema.key_column_usage where referenced_table_name is not null and referenced_column_name is not null and table_schema = 'link_aurora' and table_name = 'external_account_positions';
Empty set (0.30 sec)

mysql> describe external_account_positions;
+--------------------------+---------------+------+-----+---------+----------------+
| Field                    | Type          | Null | Key | Default | Extra          |
+--------------------------+---------------+------+-----+---------+----------------+
| id                       | bigint(20)    | NO   | PRI | NULL    | auto_increment |
| user_id                  | bigint(20)    | NO   | MUL | NULL    |                |
| external_account_link_id | bigint(20)    | NO   | MUL | NULL    |                |
| external_account_id      | bigint(20)    | NO   | MUL | NULL    |                |
| vendor                   | varchar(255)  | NO   |     | NULL    |                |
| vendor_id                | varchar(255)  | YES  |     | NULL    |                |
| state                    | varchar(255)  | NO   |     | NULL    |                |
| created_at               | datetime      | NO   | MUL | NULL    |                |
| deleted_at               | datetime      | YES  | MUL | NULL    |                |
| amount                   | decimal(20,4) | NO   |     | NULL    |                |
| type                     | varchar(255)  | NO   |     | NULL    |                |
| cost_basis               | decimal(20,4) | NO   |     | NULL    |                |
| currency                 | varchar(255)  | YES  |     | NULL    |                |
| forex_rate               | decimal(10,6) | YES  |     | NULL    |                |
| price                    | decimal(20,4) | YES  |     | NULL    |                |
| quantity                 | decimal(20,4) | YES  |     | NULL    |                |
| symbol                   | varchar(255)  | YES  |     | NULL    |                |
| name                     | varchar(255)  | YES  |     | NULL    |                |
| cusip                    | varchar(255)  | YES  |     | NULL    |                |
| isin                     | varchar(255)  | YES  |     | NULL    |                |
| instrument_id            | bigint(20)    | YES  |     | NULL    |                |
| is_short                 | tinyint(1)    | NO   |     | NULL    |                |
| asset_class              | varchar(255)  | NO   |     | NULL    |                |
| sector                   | varchar(255)  | NO   |     | NULL    |                |
| is_wf_created            | tinyint(1)    | NO   |     | NULL    |                |
| updated_at               | datetime      | YES  | MUL | NULL    |                |
+--------------------------+---------------+------+-----+---------+----------------+
26 <USER> <GROUP> set (0.04 sec)

# Take a snapshot from the Amazon RDS Console

# Dry Run
pt-online-schema-change --alter "MODIFY COLUMN forex_rate DECIMAL(20,8)" --charset utf8 --ask-pass D=link_aurora,t=external_account_positions,A=utf8,h=db-link-aurora-master.wlth.fr,u=link_db_user --alter-foreign-keys-method drop_swap --dry-run

# Execution
pt-online-schema-change --alter "MODIFY COLUMN forex_rate DECIMAL(20,8)" --charset utf8 --ask-pass D=link_aurora,t=external_account_positions,A=utf8,h=db-link-aurora-master.wlth.fr,u=link_db_user --alter-foreign-keys-method drop_swap --execute

*/

# pt-online-schema-change
ALTER TABLE external_account_positions
  MODIFY COLUMN forex_rate DECIMAL(20,8);

INSERT INTO schema_log (schema_version, date) VALUES (17, NOW());