CREATE TABLE version_external_account_transaction_vendor_id (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    external_account_transaction_id BIGINT(20) NOT NULL,
    version INTEGER NOT NULL,
    created_at DATETIME NOT NULL,
    deleted <PERSON><PERSON><PERSON><PERSON><PERSON> NOT NULL,
    value VARCHAR(255) NOT NULL,
    PRIMARY KEY (id),
    <PERSON><PERSON><PERSON> (external_account_transaction_id),
    <PERSON><PERSON><PERSON> created_at (created_at),
    <PERSON>OR<PERSON><PERSON><PERSON> KEY (external_account_transaction_id) REFERENCES external_account_transactions (id)
) ENGINE=InnoDB DEFAULT CHARSET = utf8mb4;

CREATE TABLE version_external_account_transaction_amount (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    external_account_transaction_id BIGINT(20) NOT NULL,
    version INTEGER NOT NULL,
    created_at DATETIME NOT NULL,
    deleted BOOLEAN NOT NULL,
    value DECIMAL(20,4) NOT NULL,
    PRIMARY KEY (id),
    <PERSON><PERSON><PERSON> (external_account_transaction_id),
    <PERSON><PERSON><PERSON> (created_at),
    <PERSON><PERSON><PERSON><PERSON><PERSON>EY (external_account_transaction_id) REFERENCES external_account_transactions (id)
) ENGINE=InnoDB DEFAULT CHARSET = utf8mb4;

CREATE TABLE version_external_account_transaction_encrypted_memo (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    external_account_transaction_id BIGINT(20) NOT NULL,
    version INTEGER NOT NULL,
    created_at DATETIME NOT NULL,
    deleted BOOLEAN NOT NULL,
    value TEXT NOT NULL,
    PRIMARY KEY (id),
    KEY (external_account_transaction_id),
    KEY (created_at),
    FOREIGN KEY (external_account_transaction_id) REFERENCES external_account_transactions (id)
) ENGINE=InnoDB DEFAULT CHARSET = utf8mb4;

CREATE TABLE version_external_account_transaction_state (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    external_account_transaction_id BIGINT(20) NOT NULL,
    version INTEGER NOT NULL,
    created_at DATETIME NOT NULL,
    deleted BOOLEAN NOT NULL,
    value VARCHAR(255) NOT NULL,
    PRIMARY KEY (id),
    KEY (external_account_transaction_id),
    KEY (created_at),
    FOREIGN KEY (external_account_transaction_id) REFERENCES external_account_transactions (id)
) ENGINE=InnoDB DEFAULT CHARSET = utf8mb4;

CREATE TABLE version_external_account_transaction_status (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    external_account_transaction_id BIGINT(20) NOT NULL,
    version INTEGER NOT NULL,
    created_at DATETIME NOT NULL,
    deleted BOOLEAN NOT NULL,
    value VARCHAR(255),
    PRIMARY KEY (id),
    KEY (external_account_transaction_id),
    KEY (created_at),
    FOREIGN KEY (external_account_transaction_id) REFERENCES external_account_transactions (id)
) ENGINE=InnoDB DEFAULT CHARSET = utf8mb4;

INSERT INTO schema_log (schema_version, date) VALUES (3, NOW());
