CREATE TABLE version_test_external_account_position_vendor_id (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    test_external_account_position_id BIGINT(20) NOT NULL,
    version INTEGER NOT NULL,
    created_at DATETIME NOT NULL,
    deleted <PERSON><PERSON><PERSON><PERSON>N NOT NULL,
    value VARCHAR(255) NOT NULL,
    PRIMARY KEY (id),
    <PERSON><PERSON><PERSON> (test_external_account_position_id),
    <PERSON><PERSON><PERSON> created_at (created_at),
    FOREI<PERSON><PERSON> KEY (test_external_account_position_id) REFERENCES test_external_account_positions (id)
) ENGINE=InnoDB DEFAULT CHARSET = utf8mb4;

CREATE TABLE version_test_external_account_position_state (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    test_external_account_position_id BIGINT(20) NOT NULL,
    version INTEGER NOT NULL,
    created_at DATETIME NOT NULL,
    deleted B<PERSON><PERSON>EAN NOT NULL,
    value VARCHAR(255) NOT NULL,
    PRIMARY KEY (id),
    <PERSON><PERSON><PERSON> (test_external_account_position_id),
    <PERSON><PERSON><PERSON> created_at (created_at),
    <PERSON><PERSON><PERSON><PERSON><PERSON> KEY (test_external_account_position_id) REFERENCES test_external_account_positions (id)
) ENGINE=InnoDB DEFAULT CHARSET = utf8mb4;

CREATE TABLE version_test_external_account_position_amount (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    test_external_account_position_id BIGINT(20) NOT NULL,
    version INTEGER NOT NULL,
    created_at DATETIME NOT NULL,
    deleted BOOLEAN NOT NULL,
    value DECIMAL(20,4) NOT NULL,
    PRIMARY KEY (id),
    KEY (test_external_account_position_id),
    KEY created_at (created_at),
    FOREIGN KEY (test_external_account_position_id) REFERENCES test_external_account_positions (id)
) ENGINE=InnoDB DEFAULT CHARSET = utf8mb4;

CREATE TABLE version_test_external_account_position_cost_basis (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    test_external_account_position_id BIGINT(20) NOT NULL,
    version INTEGER NOT NULL,
    created_at DATETIME NOT NULL,
    deleted BOOLEAN NOT NULL,
    value DECIMAL(20,4) NOT NULL,
    PRIMARY KEY (id),
    KEY (test_external_account_position_id),
    KEY created_at (created_at),
    FOREIGN KEY (test_external_account_position_id) REFERENCES test_external_account_positions (id)
) ENGINE=InnoDB DEFAULT CHARSET = utf8mb4;

CREATE TABLE version_test_external_account_position_price (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    test_external_account_position_id BIGINT(20) NOT NULL,
    version INTEGER NOT NULL,
    created_at DATETIME NOT NULL,
    deleted BOOLEAN NOT NULL,
    value DECIMAL(20,4) NOT NULL,
    PRIMARY KEY (id),
    KEY (test_external_account_position_id),
    KEY created_at (created_at),
    FOREIGN KEY (test_external_account_position_id) REFERENCES test_external_account_positions (id)
) ENGINE=InnoDB DEFAULT CHARSET = utf8mb4;

CREATE TABLE version_test_external_account_position_quantity (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    test_external_account_position_id BIGINT(20) NOT NULL,
    version INTEGER NOT NULL,
    created_at DATETIME NOT NULL,
    deleted BOOLEAN NOT NULL,
    value DECIMAL(20,4) NOT NULL,
    PRIMARY KEY (id),
    KEY (test_external_account_position_id),
    KEY created_at (created_at),
    FOREIGN KEY (test_external_account_position_id) REFERENCES test_external_account_positions (id)
) ENGINE=InnoDB DEFAULT CHARSET = utf8mb4;

INSERT INTO schema_log (schema_version, date) VALUES (9, NOW());