CREATE TABLE test_external_account_positions (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    user_id BIGINT(20) NOT NULL,
    external_account_link_id BIGINT(20) NOT NULL,
    external_account_id BIGINT(20) NOT NULL,
    vendor VARCHAR(255) NOT NULL,
    state VARCHAR(255) NOT NULL,
    vendor_id VARCHAR(255),
    created_at DATETIME NOT NULL,
    updated_at DATETIME,
    amount DECIMAL(20,4) NOT NULL,
    type VARCHAR(255) NOT NULL,
    cost_basis DECIMAL(20,4) NOT NULL,
    currency VARCHAR(255),
    forex_rate DECIMAL(10,6),
    price DECIMAL(20,4),
    quantity DECIMAL(20,4),
    symbol VARCHAR(255),
    name VARCHAR(255),
    cusip VARCHAR(255),
    isin VARCHAR(255),
    instrument_id BIGINT(20),
    is_short TINYINT(1),
    PRIMARY KEY (id),
    <PERSON><PERSON><PERSON> (user_id),
    <PERSON><PERSON><PERSON> (external_account_link_id),
    KEY (external_account_id)
) ENGINE=InnoDB DEFAULT CHARSET = utf8mb4;

INSERT INTO schema_log (schema_version, date) VALUES (8, NOW());