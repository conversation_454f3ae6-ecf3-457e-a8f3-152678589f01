ALTER TABLE test_external_account_positions MODIFY vendor_id VARCHAR(255) NOT NULL;

CREATE TABLE version_test_external_account_position_details (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    test_external_account_position_id BIGINT(20) NOT NULL,
    version INTEGER NOT NULL,
    created_at DATETIME NOT NULL,
    deleted BOOLEAN NOT NULL,
    vendor_id VARCHAR(255) NOT NULL,
    state VARCHAR(255) NOT NULL,
    amount DECIMAL(20,4) NOT NULL,
    cost_basis DECIMAL(20,4) NOT NULL,
    price DECIMAL(20,4),
    quantity DECIMAL(20,4),
    PRIMARY KEY (id),
    <PERSON>E<PERSON> (test_external_account_position_id),
    KEY vendor_id (vendor_id),
    KEY created_at (created_at),
    FOREIG<PERSON> KEY (test_external_account_position_id) REFERENCES test_external_account_positions (id)
) ENGINE=InnoDB DEFAULT CHARSET = utf8mb4;

INSERT INTO schema_log (schema_version, date) VALUES (11, NOW());