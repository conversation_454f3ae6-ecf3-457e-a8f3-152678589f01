/*
Our Aurora DB is running mysql 5.7, in which adding and dropping indices happens in place (does not lock the table)
https://dev.mysql.com/doc/refman/5.7/en/innodb-online-ddl-operations.html

MySQL [link_aurora]> show index from external_account_positions;
+----------------------------+------------+----------------------------+--------------+--------------------------+-----------+-------------+----------+--------+------+------------+---------+---------------+
| Table                      | Non_unique | Key_name                   | Seq_in_index | Column_name              | Collation | Cardinality | Sub_part | Packed | Null | Index_type | Comment | Index_comment |
+----------------------------+------------+----------------------------+--------------+--------------------------+-----------+-------------+----------+--------+------+------------+---------+---------------+
| external_account_positions |          0 | PRIMARY                    |            1 | id                       | A         |    ******** |     NULL | NULL   |      | BTREE      |         |               |
| external_account_positions |          1 | user_id                    |            1 | user_id                  | A         |      466908 |     NULL | NULL   |      | BTREE      |         |               |
| external_account_positions |          1 | external_account_link_id   |            1 | external_account_link_id | A         |     1104595 |     NULL | NULL   |      | BTREE      |         |               |
| external_account_positions |          1 | external_account_id        |            1 | external_account_id      | A         |     2071980 |     NULL | NULL   |      | BTREE      |         |               |
| external_account_positions |          1 | created_at                 |            1 | created_at               | A         |      212265 |     NULL | NULL   |      | BTREE      |         |               |
| external_account_positions |          1 | external_account_link_id_2 |            1 | external_account_link_id | A         |      950642 |     NULL | NULL   |      | BTREE      |         |               |
| external_account_positions |          1 | external_account_link_id_2 |            2 | deleted_at               | A         |     8531204 |     NULL | NULL   | YES  | BTREE      |         |               |
+----------------------------+------------+----------------------------+--------------+--------------------------+-----------+-------------+----------+--------+------+------------+---------+---------------+
7 <USER> <GROUP> set (0.02 sec)

MySQL [link_aurora]> show index from external_account_transactions;
+-------------------------------+------------+-----------------------------------------+--------------+--------------------------+-----------+-------------+----------+--------+------+------------+---------+---------------+
| Table                         | Non_unique | Key_name                                | Seq_in_index | Column_name              | Collation | Cardinality | Sub_part | Packed | Null | Index_type | Comment | Index_comment |
+-------------------------------+------------+-----------------------------------------+--------------+--------------------------+-----------+-------------+----------+--------+------+------------+---------+---------------+
| external_account_transactions |          0 | PRIMARY                                 |            1 | id                       | A         |   ********* |     NULL | NULL   |      | BTREE      |         |               |
| external_account_transactions |          1 | user_id                                 |            1 | user_id                  | A         |     1467121 |     NULL | NULL   |      | BTREE      |         |               |
| external_account_transactions |          1 | external_account_link_id                |            1 | external_account_link_id | A         |     2016004 |     NULL | NULL   |      | BTREE      |         |               |
| external_account_transactions |          1 | external_account_id                     |            1 | external_account_id      | A         |     3047568 |     NULL | NULL   |      | BTREE      |         |               |
| external_account_transactions |          1 | transaction_date                        |            1 | transaction_date         | A         |      500958 |     NULL | NULL   |      | BTREE      |         |               |
| external_account_transactions |          1 | external_account_transactions_vendor_id |            1 | vendor_id                | A         |   ********* |     NULL | NULL   |      | BTREE      |         |               |
+-------------------------------+------------+-----------------------------------------+--------------+--------------------------+-----------+-------------+----------+--------+------+------------+---------+---------------+
6 <USER> <GROUP> set (0.02 sec)

*/
ALTER TABLE external_account_positions
    DROP INDEX external_account_link_id_2,
    ADD INDEX updated_at (updated_at),
    ADD INDEX deleted_at (deleted_at);

ALTER TABLE external_account_transactions
    ADD INDEX created_at (created_at),
    ADD INDEX updated_at (updated_at);

INSERT INTO schema_log (schema_version, date) VALUES (16, NOW());