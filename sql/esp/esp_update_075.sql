CREATE TABLE oncall_survey_responses (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  submitted_at datetime NOT NULL,
  pagerduty_incident_id varchar(255) NOT NULL,
  assigned_team varchar(255) DEFAULT NULL,
  effort varchar(255) DEFAULT NULL,
  system varchar(255) DEFAULT NULL,
  jira_id varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  INDEX `oncall_survery_responses_at` (`submitted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO schema_log (schema_version, date) VALUES (75, now());