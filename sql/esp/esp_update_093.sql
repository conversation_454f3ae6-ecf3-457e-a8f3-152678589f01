/*
select table_name, table_rows, round(((data_length + index_length)/1024/1024), 2) as size_in_mb
rom information_schema.tables
where table_schema = 'esp' AND table_name = 'web_page_views';

+----------------+------------+------------+
| table_name     | table_rows | size_in_mb |
+----------------+------------+------------+
| web_page_views |    9236840 |    4821.41 |
+----------------+------------+------------+


select fk.table_name, fk.column_name, fk.referenced_table_name, fk.referenced_column_name, round(((t.data_length + t.index_length)/1024/1024), 2) as size_in_mb
from information_schema.key_column_usage fk
join information_schema.tables t on fk.table_name = t.table_name
where t.table_schema = 'esp' and fk.referenced_table_name = 'web_page_views';

Empty set (0.18 sec)

pt-online-schema-change --alter "MODIFY COLUMN at DATETIME" --charset utf8 --ask-pass --alter-foreign-keys-method drop_swap D=esp,t=web_page_views,A=utf8,h=db-sv2-esp-master.wlth.fr,u=wfadmin --dry-run
pt-online-schema-change --alter "MODIFY COLUMN at DATETIME" --charset utf8 --ask-pass --alter-foreign-keys-method drop_swap D=esp,t=web_page_views,A=utf8,h=db-sv2-esp-master.wlth.fr,u=wfadmin --execute
*/

ALTER TABLE web_page_views
MODIFY COLUMN at DATETIME;

INSERT INTO schema_log (schema_version, date) VALUES (93, now());
