drop table if exists balances_tmp;

CREATE temporary TABLE `balances_tmp` (
  `id` bigint(20) NOT NULL default '0',
  `total_value` decimal(14,2) NOT NULL default '0.00',
  `total_cost` decimal(14,2) NOT NULL default '0.00',
  `cash_deposit` decimal(14,2) NOT NULL default '0.00',
  `cash_withdrawal` decimal(14,2) NOT NULL default '0.00',
  `cash_for_interest` decimal(14,2) NOT NULL default '0.00',
  `date` date NOT NULL default '0000-00-00 00:00:00',
  `portfolio_id` bigint(20) default NULL,
  `beta` decimal(12,4) default NULL,
  `concentration` decimal(12,4) default NULL,
  `correlation` decimal(12,4) default NULL,
  `correlation2` decimal(12,4) default NULL,
  `turnover` decimal(12,4) default NULL
) ENGINE=MyIsam DEFAULT CHARSET=utf8;

insert into balances_tmp select * from balances;

drop table if exists balances;

CREATE TABLE `balances` (
  `id` bigint(20) NOT NULL default '0',
  `total_value` decimal(14,2) NOT NULL default '0.00',
  `total_cost` decimal(14,2) NOT NULL default '0.00',
  `cash_deposit` decimal(14,2) NOT NULL default '0.00',
  `cash_withdrawal` decimal(14,2) NOT NULL default '0.00',
  `cash_for_interest` decimal(14,2) NOT NULL default '0.00',
  `date` date NOT NULL default '0000-00-00 00:00:00',
  `portfolio_id` bigint(20) default NULL,
  `beta` decimal(12,4) default NULL,
  `concentration` decimal(12,4) default NULL,
  `correlation` decimal(12,4) default NULL,
  `correlation2` decimal(12,4) default NULL,
  `turnover` decimal(12,4) default NULL,
  PRIMARY KEY  (`id`),
  KEY `date` (`date`,`total_value`),
  INDEX (turnover),
  UNIQUE (`portfolio_id`,`date`),
  CONSTRAINT FOREIGN KEY (`portfolio_id`) REFERENCES `portfolios` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

alter table balances disable keys;
insert into balances select * from balances_tmp;
alter table balances enable keys;

insert into schema_log (schema_version, date) values (64, now());
