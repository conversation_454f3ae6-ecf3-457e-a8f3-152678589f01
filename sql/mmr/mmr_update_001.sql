CREATE DATABASE mmr
  CHARACTER SET 'utf8mb4'
  COLLATE utf8mb4_unicode_520_ci;

USE mmr;

CREATE TABLE schema_log (
  schema_version INT      NOT NULL DEFAULT 0,
  date           DATETIME NOT NULL,
  UNIQUE (schema_version)
)
  ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

CREATE TABLE sqs_jobs (
  id                       BIGINT           NOT NULL    AUTO_INCREMENT,
  start_time               DATETIME         NOT NULL,
  end_time                 DATETIME,
  message_id               VARCHAR(255)     NOT NULL,
  chronos_config_name      VARCHAR(255)     NOT NULL,
  query_name               VARCHAR(255)     NOT NULL,
  arguments                TEXT,
  mesos_task_id            VARCHAR(255)     NOT NULL,
  job_status               VARCHAR(255),
  service                  VARCHAR(255)     NOT NULL,
  node_identifier          VARCHAR(255)     NOT NULL,
  return_code              BIGINT,
  updated_chronos          BOOL,
  dag_id                   VARCHAR(255)     DEFAULT NULL,
  task_id                  VARCHAR(255)     DEFAULT NULL,
  dag_execution_date       DATETIME         DEFAULT NULL,
  try_number               INT(11)          DEFAULT NULL,
  PRIMARY KEY (id)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;

CREATE INDEX sqs_jobs_start_time ON sqs_jobs (start_time);
CREATE INDEX sqs_jobs_end_time ON sqs_jobs (end_time);
CREATE INDEX sqs_jobs_return_code ON sqs_jobs (return_code);
CREATE UNIQUE INDEX sqs_jobs_dag_id_task_id_dag_execution_date_try_number ON sqs_jobs (dag_id, task_id, dag_execution_date, try_number);

INSERT INTO schema_log (schema_version, date) VALUES (1, now());