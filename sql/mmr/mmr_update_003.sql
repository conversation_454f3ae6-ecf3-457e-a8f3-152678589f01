ALTER TABLE model_registry
    ADD COLUMN name VA<PERSON><PERSON><PERSON>(255) NOT NULL DEFAULT "",
    ADD COLUMN version INT NOT NULL DEFAULT 0,
    ADD COLUMN revision BIGINT(20) NOT NULL DEFAULT 0,
    ADD COLUMN location VARCHAR(255) NOT NULL DEFAULT "",
    ADD COLUMN approved TINYINT(1) NOT NULL DEFAULT 0,
    ADD COLUMN trained_at DATETIME NOT NULL DEFAULT '0000-00-00 00:00:00',
    ADD COLUMN producer VARCHAR(255),
    ADD COLUMN details VARCHAR(255),
    ADD UNIQUE model_registry_name_version_revision (name, version, revision);

INSERT INTO schema_log (schema_version, date) VALUES (3, now());
