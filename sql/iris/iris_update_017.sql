CREATE TABLE cross_service_query_calls (
  id BIGINT NOT NULL AUTO_INCREMENT,
  query VARCHAR(255) NOT NULL,
  caller_service VARCHAR(255) NOT NULL,
  callee_service VARCHAR(255) NOT NULL,
  last_called_at DATETIME NOT NULL,
  PRIMARY KEY (id),
  UNIQUE (query, caller_service, callee_service)
);

CREATE INDEX cross_service_query_calls_last_called_index ON cross_service_query_calls (last_called_at);

INSERT INTO schema_log (schema_version, date) VALUES (17, NOW());
