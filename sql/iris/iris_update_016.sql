CREATE TABLE ai_workflows (
  id           BIGINT       NOT NULL AUTO_INCREMENT,
  version      INT          NOT NULL DEFAULT 0,
  created_at   DATETIME     NOT NULL,
  type         VARCHAR(255) NOT NULL,
  type_version INT          NOT NULL,
  details      MEDIUMTEXT   NOT NULL,
  completed_at DATETIME     DEFAULT NULL,
  PRIMARY KEY (id),
  KEY ai_workflows_type_completed_at (type, completed_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

CREATE TABLE ai_sessions (
  id             BIGINT       NOT NULL AUTO_INCREMENT,
  version        INT          NOT NULL DEFAULT 0,
  ai_workflow_id BIGINT       NOT NULL,
  created_at     DATETIME     NOT NULL,
  agent_name     VARCHAR(255) NOT NULL,
  PRIMARY KEY (id),
  KEY ai_sessions_ai_workflow_id (ai_workflow_id) 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

CREATE TABLE ai_context_items (
  id             BIGINT       NOT NULL AUTO_INCREMENT,
  version        INT          NOT NULL DEFAULT 0,
  ai_session_id  BIGINT       NOT NULL,
  created_at     DATETIME     NOT NULL,
  type           VARCHAR(255),
  content_raw    MEDIUMTEXT,
  content_md     MEDIUMTEXT,
  PRIMARY KEY (id),
  KEY ai_context_items_ai_session_id (ai_session_id) 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

CREATE TABLE ai_api_calls (
  id             BIGINT       NOT NULL AUTO_INCREMENT,
  version        INT          NOT NULL DEFAULT 0,
  ai_session_id  BIGINT       NOT NULL,
  created_at     DATETIME     NOT NULL,
  model          VARCHAR(255),
  cost           DECIMAL(14, 2),
  details        TEXT,
  PRIMARY KEY (id),
  KEY ai_api_calls_ai_session_id (ai_session_id) 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

CREATE TABLE ai_research_questions (
  id             BIGINT       NOT NULL AUTO_INCREMENT,
  version        INT          NOT NULL DEFAULT 0,
  ai_workflow_id BIGINT       NOT NULL,
  parent_id      BIGINT       DEFAULT NULL,
  created_at     DATETIME     NOT NULL,
  state          VARCHAR(255) NOT NULL,
  updated_at     DATETIME     NOT NULL,
  question       TEXT         NOT NULL,
  details        MEDIUMTEXT   NOT NULL,
  answer         MEDIUMTEXT   DEFAULT NULL,
  PRIMARY KEY (id),
  KEY ai_research_questions_ai_workflow_id (ai_workflow_id),
  KEY ai_research_questions_parent_id (parent_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

CREATE TABLE ai_api_cache (
  id             BIGINT       NOT NULL AUTO_INCREMENT,
  version        INT          NOT NULL DEFAULT 0,
  ai_api_call_id BIGINT       NOT NULL,
  created_at     DATETIME     NOT NULL,
  expires_at     DATETIME     NOT NULL,
  cache_key_hash CHAR(64)     NOT NULL,
  cache_key      MEDIUMTEXT   NOT NULL,
  cache_value    MEDIUMTEXT   NOT NULL,
  PRIMARY KEY (id),
  UNIQUE KEY ai_api_cache_key_hash (cache_key_hash),
  KEY ai_api_cache_expires_at (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

CREATE TABLE documentation_prompts (
  id                BIGINT       NOT NULL AUTO_INCREMENT,
  version           INT          NOT NULL DEFAULT 0,
  created_at        DATETIME     NOT NULL,
  deleted_at        DATETIME     DEFAULT NULL,
  last_generated_at DATETIME     DEFAULT NULL,
  system_name_short VARCHAR(255) NOT NULL,
  system_name_long  VARCHAR(255) NOT NULL,
  details           MEDIUMTEXT   NOT NULL,
  PRIMARY KEY (id),
  UNIQUE KEY documentation_prompts_system_name_short (system_name_short) 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

CREATE TABLE documentation_results (
  id                      BIGINT       NOT NULL AUTO_INCREMENT,
  version                 INT          NOT NULL DEFAULT 0,
  documentation_prompt_id BIGINT       NOT NULL,
  ai_workflow_id          BIGINT       NOT NULL,
  created_at              DATETIME     NOT NULL,
  result                  MEDIUMTEXT   NOT NULL,
  PRIMARY KEY (id),
  KEY documentation_results_prompt_id (documentation_prompt_id),
  KEY documentation_results_ai_workflow_id (ai_workflow_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

CREATE TABLE pull_request_analysis_results (
  id             BIGINT       NOT NULL AUTO_INCREMENT,
  version        INT          NOT NULL DEFAULT 0,
  created_at     DATETIME     NOT NULL,
  repository     VARCHAR(255) NOT NULL,
  pull_requestid BIGINT       NOT NULL,
  type           VARCHAR(255) NOT NULL,
  ai_workflow_id BIGINT       DEFAULT NULL,
  result         MEDIUMTEXT   DEFAULT NULL,
  PRIMARY KEY (id),
  KEY pr_analysis_result_ai_workflow_id (ai_workflow_id),
  KEY pr_analysis_result_repository_prid (repository, pull_requestid),
  KEY pr_analysis_result_type_repository_prid (type, repository, pull_requestid)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

CREATE TABLE document_links (
  id             BIGINT        NOT NULL AUTO_INCREMENT,
  version        INT           NOT NULL DEFAULT 0,
  created_at     DATETIME      NOT NULL,
  deleted_at     DATETIME      DEFAULT NULL,
  document_ref   VARCHAR(767)  NOT NULL,
  link_type      VARCHAR(255)  NOT NULL,
  linkid         BIGINT        NOT NULL,
  score          DECIMAL(16,8) NOT NULL,
  details        TEXT          DEFAULT NULL,
  PRIMARY KEY (id),
  UNIQUE KEY document_links_unique (document_ref, link_type, linkid),
  KEY document_links_type_linkid (link_type, linkid)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

CREATE TABLE batch_queue_names (
  id        BIGINT       NOT NULL AUTO_INCREMENT,
  version   INT          NOT NULL DEFAULT 0,
  metadata  MEDIUMTEXT   DEFAULT NULL,
  name      VARCHAR(500) NOT NULL,
  PRIMARY KEY (id),
  UNIQUE KEY (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
 
CREATE TABLE batch_queue_items (
  id                  BIGINT       NOT NULL AUTO_INCREMENT,
  batch_queue_name_id BIGINT       NOT NULL,
  externalid          VARCHAR(500) DEFAULT NULL,
  payload             TEXT         NOT NULL,
  PRIMARY KEY (id),
  UNIQUE KEY (batch_queue_name_id, externalid)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
 
CREATE TABLE batch_queue_batches (
  id                  BIGINT(20)   NOT NULL AUTO_INCREMENT,
  version             INT          DEFAULT 0,
  batch_queue_name_id BIGINT(20)   NOT NULL,
  item_id_from        BIGINT(20)   NOT NULL,
  item_id_to          BIGINT(20)   NOT NULL,
  size                INT UNSIGNED NOT NULL,
  created_at          DATETIME     NOT NULL,
  sendable_since      DATETIME     DEFAULT NULL,
  polled_at           DATETIME     DEFAULT NULL,
  polled_by           VARCHAR(63)  DEFAULT NULL,
  sent_at             DATETIME     DEFAULT NULL,
  errored_at          DATETIME     DEFAULT NULL,
  ignored_at          DATETIME     DEFAULT NULL,
  batch_data          TEXT         DEFAULT NULL,
  message             VARCHAR(999) DEFAULT NULL,
  PRIMARY KEY (id),
  INDEX batch_queue_batches_polling_index (batch_queue_name_id, sendable_since, errored_at, polled_by, polled_at),
  UNIQUE KEY batch_queue_batches_item_index (batch_queue_name_id, item_id_from),
  CONSTRAINT batch_queue_batches_sendable CHECK ((sendable_since is null) = (sent_at is not null) or (ignored_at is not null) or (errored_at is not null))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE tinykv_entries (
  store_id   INT          NOT NULL,
  store_key  VARCHAR(767) NOT NULL,
  hash       INT UNSIGNED NOT NULL,
  value      VARBINARY(32768) NOT NULL,
  PRIMARY KEY (store_id, store_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;
 
CREATE TABLE tinykv_logs (
  group_id    INT          NOT NULL,
  log_id      BIGINT       NOT NULL,
  store_id    INT          NOT NULL,
  entered_at  DATETIME     NOT NULL,
  is_delete   TINYINT(1)   NOT NULL,
  store_key   VARCHAR(767) NOT NULL,
  value       VARBINARY(32768) DEFAULT NULL,
  PRIMARY KEY (group_id, log_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;
 
CREATE TABLE tinykv_log_offsets (
  id           BIGINT   NOT NULL AUTO_INCREMENT,
  group_id     INT,
  last_log_id  BIGINT   NOT NULL,
  updated_at   DATETIME,
  PRIMARY KEY (id),
  UNIQUE KEY tinykv_log_offsets_group_id (group_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;
 
CREATE TABLE tinykv_settings (
  id            BIGINT       NOT NULL AUTO_INCREMENT,
  settings_key  VARCHAR(767) NOT NULL,
  version       INT          NOT NULL DEFAULT 0,
  value         MEDIUMBLOB   NOT NULL,
  PRIMARY KEY (id),
  UNIQUE KEY tinykv_settings_settings_key (settings_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

INSERT INTO schema_log (schema_version, date) VALUES (16, NOW());