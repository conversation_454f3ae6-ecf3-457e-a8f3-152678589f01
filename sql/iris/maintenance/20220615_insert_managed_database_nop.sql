INSERT IGNORE INTO managed_databases (name, changelog_id, database_driver, database_fqdn, database_port, database_user,
                                      database_password_property, tool_to_database_fqdn, tool_to_database_port,
                                      tool_to_database_user, tool_to_database_password_property, tool_fqdn, tool_port,
                                      tool_user, tool_password_property, tool_workdir, tool_result_timeout_millis)
VALUES ('nop', (SELECT IFNULL((SELECT id FROM changelogs WHERE name = 'nop'), 0)), 'mysql', 'db-sv2-esp-master.wlth.fr',
        3306, 'wfadmin', 'iris.osc.password', 'localhost', 3306, 'wfadmin', 'iris.osc.password',
        'db-sv2-esp-master.wlth.fr', 22, 'iris', 'iris.osc.password', '/wfservices/iris',
        86400000);
