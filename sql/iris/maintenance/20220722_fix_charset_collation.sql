ALTER TABLE changelogs
    CHARSET = utf8mb4,
    COLLATE = utf8mb4_unicode_520_ci;
ALTER TABLE changesets
    CHARSET = utf8mb4,
    COLLATE = utf8mb4_unicode_520_ci;
ALTER TABLE completed_executions
    CHARSET = utf8mb4,
    COLLATE = utf8mb4_unicode_520_ci;
ALTER TABLE daemon_executions
    CHARSET = utf8mb4,
    COLLATE = utf8mb4_unicode_520_ci;
ALTER TABLE managed_databases
    CHARSET = utf8mb4,
    COLLATE = utf8mb4_unicode_520_ci;
ALTER TABLE resources
    CHARSET = utf8mb4,
    COLLATE = utf8mb4_unicode_520_ci;
ALTER TABLE schema_changes
    CHARSET = utf8mb4,
    COLLATE = utf8mb4_unicode_520_ci;
ALTER TABLE schema_change_steps
    CHARSET = utf8mb4,
    COLLATE = utf8mb4_unicode_520_ci;
ALTER TABLE schema_log
    CHARSET = utf8mb4,
    COLLATE = utf8mb4_unicode_520_ci;
ALTER TABLE sql_files
    CHARSET = utf8mb4,
    COLLATE = utf8mb4_unicode_520_ci;
ALTER TABLE sqs_jobs
    CHARSET = utf8mb4,
    COLLATE = utf8mb4_unicode_520_ci;
ALTER TABLE temp_sql_files
    CHARSET = utf8mb4,
    COLLATE = utf8mb4_unicode_520_ci;
