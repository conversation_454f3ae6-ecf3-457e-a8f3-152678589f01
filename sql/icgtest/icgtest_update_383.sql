CREATE TABLE corporate_action_replication_outcomes (
    id              BIGINT(20)      NOT NULL AUTO_INCREMENT,
    report_key      VARCHAR(255)    NOT NULL,
    queue_type      VARCHAR(20)     NOT NULL,
    created_at      DATETIME        NOT NULL,
    account_id      BIGINT(20)      NOT NULL,
    data            TEXT            DEFAULT NULL,
    PRIMARY KEY (id),
    INDEX corporate_action_replication_outcomes_created_at (created_at),
    INDEX corporate_action_replication_outcomes_queue_type_created_at (queue_type, created_at),
    INDEX corporate_action_replication_outcomes_report_key (report_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

INSERT INTO schema_log(schema_version, date) VALUES (383, now());