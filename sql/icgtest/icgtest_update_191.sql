CREATE TABLE redo_portfolio_sp_calls (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  called_at DATETIME NOT NULL,
  sub_account_reference VARCHAR(255) NOT NULL,
  transaction_number VARCHAR(255) DEFAULT NULL,
  symbol VARCHAR(32) NOT NULL,
  trade_date DATE DEFAULT NULL,
  PRIMARY KEY (id)
) ENGINE=InnoDB AUTO_INCREMENT=31 DEFAULT CHARSET=utf8;

INSERT INTO schema_log (schema_version, date) VALUES (191, now());
