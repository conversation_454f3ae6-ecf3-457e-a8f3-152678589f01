CREATE TABLE forge_ira_withdrawals (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    type VARCHAR(255) NOT NULL,
    ira_account_id BIGINT(20) NOT NULL,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    bank_withdrawal_id BIGINT(20) NOT NULL,
    ic_transaction_number VARCHAR(255) NOT NULL,
    forge_ira_investment_sale_id BIGINT(20) NOT NULL,
    amount DECIMAL(20,2) NOT NULL,
    withholdings TEXT DEFAULT NULL,
    payment_method VARCHAR(255) DEFAULT NULL,
    withdrawal_type VARCHAR(255) NOT NULL,
    distribution_codes TEXT NOT NULL,
    forge_withdrawal_id VARCHAR(255) DEFAULT NULL,
    PRIMARY KEY(id),
    CONSTRAINT fkey_forge_ira_withdrawals_ira_account_id FOREIGN KEY (ira_account_id) REFERENCES ira_accounts (id),
    CONSTRAINT fkey_forge_ira_withdrawals_fiis_id FOREIGN KEY (forge_ira_investment_sale_id) REFERENCES forge_ira_investment_sales (id)
) ENGINE = InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE forge_ira_excess_contribution_removals (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    type VARCHAR(255) NOT NULL,
    ira_account_id BIGINT(20) NOT NULL,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    bank_withdrawal_id BIGINT(20) NOT NULL,
    ic_transaction_number VARCHAR(255) NOT NULL,
    forge_ira_investment_sale_id BIGINT(20) NOT NULL,
    amount DECIMAL(20,2) NOT NULL,
    earnings DECIMAL(20,2) DEFAULT 0,
    original_contribution_date DATETIME NOT NULL,
    original_contribution_tax_year SMALLINT NOT NULL,
    withholdings TEXT DEFAULT NULL,
    payment_method VARCHAR(255) NOT NULL,
    distribution_codes TEXT NOT NULL,
    effective_date DATETIME NOT NULL,
    forge_excess_contribution_removal_id VARCHAR(255) DEFAULT NULL,
    PRIMARY KEY (id),
    CONSTRAINT fkey_forge_ira_ecr_ira_account_id FOREIGN KEY (ira_account_id) REFERENCES ira_accounts (id),
    CONSTRAINT fkey_forge_ira_ecr_fiis_id FOREIGN KEY (forge_ira_investment_sale_id) REFERENCES forge_ira_investment_sales (id)
) ENGINE = InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE forge_ira_deposits (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    type VARCHAR(255) NOT NULL,
    ira_account_id BIGINT(20) NOT NULL,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    bank_deposit_id BIGINT(20) NOT NULL,
    ic_transaction_number VARCHAR(255) NOT NULL,
    forge_ira_investment_purchase_id BIGINT(20) NOT NULL,
    amount DECIMAL(20,2) NOT NULL,
    deposit_type VARCHAR(255) NOT NULL,
    tax_year SMALLINT NOT NULL,
    payment_method VARCHAR(255) DEFAULT NULL,
    deposit_date DATETIME DEFAULT NULL,
    bucket_id BIGINT(20) DEFAULT NULL,
    symbol VARCHAR(255) DEFAULT NULL,
    effective_date DATE DEFAULT NULL,
    forge_deposits_id VARCHAR(255) DEFAULT NULL,
    PRIMARY KEY (id),
    CONSTRAINT fkey_forge_ira_deposits_ira_account_id FOREIGN KEY (ira_account_id) REFERENCES ira_accounts (id),
    CONSTRAINT fkey_forge_ira_deposits_fiip_id FOREIGN KEY (forge_ira_investment_purchase_id) REFERENCES forge_ira_investment_purchases (id)
) ENGINE = InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE forge_ira_rollovers (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    type VARCHAR(255) NOT NULL,
    ira_account_id BIGINT(20) NOT NULL,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    ic_transaction_number VARCHAR(255) DEFAULT NULL,
    bank_deposit_id BIGINT(20) DEFAULT NULL,
    bank_withdrawal_id BIGINT(20) DEFAULT NULL,
    bucket_id BIGINT(20) DEFAULT NULL,
    forge_ira_investment_purchase_id BIGINT(20) DEFAULT NULL,
    forge_ira_investment_sale_id BIGINT(20) DEFAULT NULL,
    effective_date DATETIME NOT NULL,
    is_full_withdrawal TINYINT(1) DEFAULT 0,
    amount DECIMAL(20,2) NOT NULL,
    tax_year SMALLINT NOT NULL,
    description TEXT DEFAULT NULL,
    distribution_code TEXT DEFAULT NULL,
    forge_rollover_id VARCHAR(255) DEFAULT NULL,
    PRIMARY KEY (id),
    CONSTRAINT fkey_forge_ira_rollovers_ira_account_id FOREIGN KEY (ira_account_id) REFERENCES ira_accounts (id),
    CONSTRAINT fkey_forge_ira_rollovers_fiip_id FOREIGN KEY (forge_ira_investment_purchase_id) REFERENCES forge_ira_investment_purchases (id),
    CONSTRAINT fkey_forge_ira_rollovers_fiis_id FOREIGN KEY (forge_ira_investment_sale_id) REFERENCES forge_ira_investment_sales (id)
) ENGINE = InnoDB DEFAULT CHARSET = utf8;

INSERT INTO schema_log (schema_version, date) VALUES (299, now());

COMMIT;