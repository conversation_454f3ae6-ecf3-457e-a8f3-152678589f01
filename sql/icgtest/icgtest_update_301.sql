CREATE TABLE forge_ira_account_transfers(
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    type VARCHAR(255) NOT NULL,
    ira_account_id BIGINT(20) NOT NULL,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    effective_date DATE NOT NULL,
    direction VARCHAR(255) DEFAULT NULL,
    extent VARCHAR(255) NOT NULL,
    processing_method VARCHAR(255) NOT NULL,
    from_custodian TEXT DEFAULT NULL,
    from_forge_account_id VARCHAR(255) DEFAULT NULL,
    from_account_number VARCHAR(255) DEFAULT NULL,
    from_account_type VARCHAR(255) DEFAULT NULL,
    to_custodian TEXT DEFAULT NULL,
    to_forge_account_id VARCHAR(255) DEFAULT NULL,
    to_account_number VARCHAR(255) DEFAULT NULL,
    to_account_type VA<PERSON>HAR(255) DEFAULT NULL,
    cash_amount DECIMAL(20,2) DEFAULT NULL,
    tax_year SMALLINT NOT NULL,
    description TEXT NOT NULL,
    completion_date DATETIME NOT NULL,
    assets TEXT NOT NULL,
    acats_transfer_id BIGINT(20) DEFAULT NULL,
    dtc_transfer_id BIGINT(20) DEFAULT NULL,
    destination_ira_account_id BIGINT(20) DEFAULT NULL,
    cross_account_transfer_id BIGINT(20) DEFAULT NULL,
    internal_asset_transfer_id BIGINT(20) DEFAULT NULL,
    bank_withdrawal_id BIGINT(20) DEFAULT NULL,
    forge_ira_investment_sale_id BIGINT(20) DEFAULT NULL,
    forge_account_transfer_id VARCHAR(255) DEFAULT NULL,
    PRIMARY KEY (id),
    CONSTRAINT fkey_forge_ira_acc_tran_ira_account_id FOREIGN KEY (ira_account_id) REFERENCES ira_accounts (id),
    CONSTRAINT fkey_forge_ira_acc_tran_ira_dest_account_id FOREIGN KEY (ira_account_id) REFERENCES ira_accounts (id),
    CONSTRAINT fkey_forge_ira_acc_tran_fiis_id FOREIGN KEY (forge_ira_investment_sale_id) REFERENCES forge_ira_investment_sales (id)
) ENGINE = InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE forge_ira_asset_transfers(
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    type VARCHAR(255) NOT NULL,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    source_ira_account_id BIGINT(20) NOT NULL,
    target_ira_account_id BIGINT(20) NOT NULL,
    cross_account_transfer_id BIGINT(20) DEFAULT NULL,
    internal_asset_transfer_id BIGINT(20) DEFAULT NULL,
    extent VARCHAR(255) NOT NULL,
    cash_amount DECIMAL(20,2) DEFAULT NULL,
    earnings DECIMAL(20,2) DEFAULT NULL,
    distribution_codes TEXT NOT NULL,
    tax_year SMALLINT NOT NULL,
    completion_date DATETIME NOT NULL,
    description TEXT DEFAULT NULL,
    forge_roth_conversion_id VARCHAR(255) DEFAULT NULL,
    withholding TEXT DEFAULT NULL,
    forge_recharacterization_id VARCHAR(255) DEFAULT NULL,
    original_contribution_date DATE DEFAULT NULL,
    original_tax_year SMALLINT DEFAULT NULL,
    irs_reporting_method VARCHAR(255) NOT NULL DEFAULT 'API',
    PRIMARY KEY (id),
    CONSTRAINT fkey_forge_ira_ass_tran_source_ira_account_id FOREIGN KEY (source_ira_account_id) REFERENCES ira_accounts (id),
    CONSTRAINT fkey_forge_ira_ass_tran_dest_ira_account_id FOREIGN KEY (target_ira_account_id) REFERENCES ira_accounts (id)
) ENGINE = InnoDB DEFAULT CHARSET = utf8;

INSERT INTO schema_log (schema_version, date) VALUES (301, now());

COMMIT;