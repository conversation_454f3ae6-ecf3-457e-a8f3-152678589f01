alter table forge_ira_withdrawal_withholdings
  ADD COLUMN fed_withholding_transaction_number VARCHAR(255) NULL AFTER ic_transaction_number,
  ADD COLUMN state_withholding_transaction_number VARCHAR(255) NULL AFTER fed_withholding_transaction_number,
  MODIFY COLUMN ic_transaction_number VARCHAR(255);

alter table forge_ira_excess_contribution_removal_withholdings
  ADD COLUMN fed_withholding_transaction_number VARCHAR(255) NULL AFTER ic_transaction_number,
  ADD COLUMN state_withholding_transaction_number VARCHAR(255) NULL AFTER fed_withholding_transaction_number,
  MODIFY COLUMN ic_transaction_number VARCHAR(255);

alter table forge_ira_asset_transfer_withholdings
  ADD COLUMN source_ira_account_id BIGINT(20) NOT NULL AFTER ira_account_id,
  ADD COLUMN target_ira_account_id BIGINT(20) NOT NULL AFTER source_ira_account_id,
  ADD COLUMN fed_withholding_transaction_number VARCHAR(255) NULL AFTER ic_transaction_number,
  ADD COLUMN state_withholding_transaction_number VARCHAR(255) NULL AFTER fed_withholding_transaction_number,
  MODIFY COLUMN ic_transaction_number VARCHAR(255),
  MODIFY COLUMN ira_account_id BIGINT(20);

INSERT INTO schema_log (schema_version, date) VALUES (346, now());