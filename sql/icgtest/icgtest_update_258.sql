CREATE TABLE blacklisted_instruments (
  id              BIGINT(20)   NOT NULL   AUTO_INCREMENT,
  version         INT(11)      NOT NULL DEFAULT 0,
  created_at      DATETIME     NOT NULL,
  deleted_at      DATETIME     DEFAULT NULL,
  expiration_date DATE         DEFAULT NULL,
  origin          VARCHAR(255) NOT NULL,
  process         VARCHAR(255) NOT NULL,
  requester       VARCHAR(255) DEFAULT NULL,
  instrumentid   BIGINT(20)   DEFAULT NULL,
  symbol          VARCHAR(255) DEFAULT NULL,
  cusip           VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (id),
  <PERSON>E<PERSON> blacklisted_instruments_deleted_at (deleted_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO schema_log (schema_version, date) values (258, now());
