create table proxy_date_records (
  id bigint(20) not null auto_increment,
  filename varchar(255) not null,
  created_date date not null,
  rsp_995_fileid varchar(255) not null,
  alternate_job_type varchar(255) not null,
  past_record_date date,
  master_security_description varchar(255) not null,
  registrant_security_description varchar(255) not null,
  registrant_record_date date not null,
  registrant_meeting_date date,
  registrant_cusip varchar(255) not null,
  proxy_or_interim varchar(255) not null,
  record_date_type varchar(255) not null,
  nobos_only tinyint(1) not null default 0,
  mailing_type varchar(255) not null,
  primary key (id)
) ENGINE=InnoDB;

insert into schema_log (schema_version, date) values (82, now());
