CREATE TABLE fpsl_custodian_account_loans (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    sharegain_file_id BIGINT(20) NOT NULL,
    fpsl_loan_instruction_id BIGINT(20) DEFAULT NULL,
    loan_id VARCHAR(255) NOT NULL,
    custodian_account_number VARCHAR(255) NOT NULL,
    borrower_code VARCHAR(255) NOT NULL,
    borrower_name VARCHAR(255) NOT NULL,
    isin VARCHAR(255) NOT NULL,
    instrument_id BIGINT(20) NOT NULL,
    instrument_type VARCHAR(255) NOT NULL,
    settlement_date DATE NOT NULL,
    end_date DATE DEFAULT NULL,
    original_units DECIMAL(20,8) NOT NULL,
    settled_units DECIMAL(20,8) NOT NULL,
    price DECIMAL(14,2) NOT NULL,
    loan_value DECIMAL(14,2) NOT NULL,
    lending_rate DECIMAL(20,8) NOT NULL,
    state VARCHAR(255) NOT NULL,
    PRIMARY KEY (id),
    CONSTRAINT fpsl_custodian_account_loans_sharegain_file_id FOREIGN KEY (sharegain_file_id) REFERENCES sharegain_files (id),
    CONSTRAINT fpsl_custodian_account_loans_fpsl_loan_instruction_id FOREIGN KEY (fpsl_loan_instruction_id) REFERENCES fpsl_loan_instructions (id)
 ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE version_fpsl_custodian_account_loans_state (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    fpsl_custodian_account_loan_id BIGINT(20) NOT NULL,
    version INT(11) NOT NULL,
    created_at DATETIME NOT NULL,
    deleted TINYINT(1) NOT NULL DEFAULT 1,
    value VARCHAR(255) NOT NULL,
    PRIMARY KEY (id),
    CONSTRAINT version_fpsl_custodian_account_loans_state_loan_id FOREIGN KEY (fpsl_custodian_account_loan_id) REFERENCES fpsl_custodian_account_loans (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

INSERT INTO schema_log (schema_version, date) VALUES (368, NOW());