CREATE TABLE daily_custodian_collateral_records
(
    id                  BIGINT         NOT NULL AUTO_INCREMENT,
    version             BIGINT         NOT NULL DEFAULT 0,
    sharegain_file_id   BIGINT         NOT NULL,
    report_date         DATE           NOT NULL,
    cash_account_number VA<PERSON>HAR(255),
    lender_code         <PERSON><PERSON><PERSON><PERSON>(255),
    ext_borrower_code   <PERSON><PERSON><PERSON><PERSON>(255),
    borrower_code       VA<PERSON><PERSON><PERSON>(50)    NOT NULL,
    borrower_name       <PERSON><PERSON><PERSON><PERSON>(255),
    collateral_type     VARCHAR(255),
    collateral_currency VARCHAR(255),
    margin              DECIMAL(10, 5),
    collateral_value    DECIMAL(20, 4) NOT NULL,
    PRIMARY KEY (id),
    CONSTRAINT fk_custodian_collateral_file_id
        FOREIGN KEY (sharegain_file_id) REFERENCES sharegain_files (id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

INSERT INTO schema_log (schema_version, date) VALUES (408, now());
