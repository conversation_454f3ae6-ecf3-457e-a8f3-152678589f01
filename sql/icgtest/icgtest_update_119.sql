ALTER TABLE profit_loss_entries
    ADD INDEX profit_loss_entries_effective_date (effective_date),
    ADD INDEX profit_loss_entries_effective_date_customer_reference (effective_date, customer_reference);

ALTER TABLE money_line_entries
    ADD INDEX money_line_entries_effective_date (effective_date),
	ADD INDEX money_line_entries_effective_date_customer_reference (effective_date, customer_reference);

ALTER TABLE direct_transaction_entries
    ADD INDEX direct_transaction_entries_effective_date (effective_date),
    ADD INDEX direct_transaction_entries_effective_date_customer_reference (effective_date, customer_reference);

INSERT INTO schema_log (schema_version, date) VALUES (119, now());