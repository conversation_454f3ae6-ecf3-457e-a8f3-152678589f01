create table corp_action_stock_excesses (
  id bigint(20) not null auto_increment,
  symbol varchar(255) not null,
  effective_date date not null,
  excess_quantity decimal(20,8) not null,
  cash_in_lieu_rate decimal(20,8) default null,
  net_amount_required_from_stock_excess decimal(20,8) default null,
  processed_at datetime default null,
  primary key (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

insert into schema_log (schema_version, date) values (85, now());
