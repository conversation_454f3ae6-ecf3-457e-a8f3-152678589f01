CREATE TABLE forge_ira_cash_disbursements (
    id BIGINT(20) NOT NULL AUTO_INCREMENT PRIMARY KEY,
    ira_account_id BIGINT(20) NOT NULL,
    created_at DATETIME NOT NULL,
    ic_transaction_number VARCHAR(255) NOT NULL,
    forge_excess_contribution_removal_id BIGINT(20) DEFAULT NULL,
    forge_ira_withdrawal_id BIGINT(20) DEFAULT NULL,
    forge_ira_rollover_id BIGINT(20) DEFAULT NULL,
    CONSTRAINT fkey_forge_excess_contribution_removal_parent_id FOREIGN KEY (forge_excess_contribution_removal_id) REFERENCES forge_ira_excess_contribution_removals (id),
    CONSTRAINT fkey_forge_ira_withdrawal_parent_id FOREIGN KEY (forge_ira_withdrawal_id) REFERENCES forge_ira_withdrawals (id),
    CONSTRAINT fkey_forge_ira_rollover_parent_id FOREIGN KEY (forge_ira_rollover_id) REFERENCES forge_ira_rollovers (id)

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE forge_ira_cash_receipts (
    id BIGINT(20) NOT NULL AUTO_INCREMENT PRIMARY KEY,
    ira_account_id BIGINT(20) NOT NULL,
    created_at DATETIME NOT NULL,
    ic_transaction_number VARCHAR(255) NOT NULL,
    forge_ira_deposit_id BIGINT(20) DEFAULT NULL,
    forge_incoming_ira_rollover_id BIGINT(20) DEFAULT NULL,
    CONSTRAINT forge_ira_deposit_id_parent_id FOREIGN KEY (forge_ira_deposit_id) REFERENCES forge_ira_deposits (id),
    CONSTRAINT forge_incoming_ira_rollover_id_parent_id FOREIGN KEY (forge_incoming_ira_rollover_id) REFERENCES forge_ira_rollovers (id)

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE forge_ira_deleted_checks (
    id BIGINT(20) NOT NULL AUTO_INCREMENT PRIMARY KEY,
    ira_account_id BIGINT(20) NOT NULL,
    created_at DATETIME NOT NULL,
    ic_transaction_number VARCHAR(255) NOT NULL,
    forge_outgoing_ira_rollover_id BIGINT(20) DEFAULT NULL,
    forge_ira_account_transfer_id BIGINT(20) DEFAULT NULL,
    CONSTRAINT forge_outgoing_ira_rollover_id_parent_id FOREIGN KEY (forge_outgoing_ira_rollover_id) REFERENCES forge_ira_rollovers (id),
    CONSTRAINT forge_ira_account_transfer_id_parent_id FOREIGN KEY (forge_ira_account_transfer_id) REFERENCES forge_ira_account_transfers (id)


) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

INSERT INTO schema_log (schema_version, date) VALUES (344, now());