ALTER TABLE ira_deposits
  ADD COLUMN dividend_type VARCHAR(255) DEFAULT NULL,
  ADD COLUMN symbol VARCHAR(255) DEFAULT NULL,
  ADD COLUMN effective_date DATE DEFAULT NULL,
  ADD INDEX ira_deposits_dividend_type_symbol_effective_date (dividend_type, symbol, effective_date),
  ADD CONSTRAINT ira_deposits_aid_dtype_symbol_edate UNIQUE (ira_account_id, dividend_type, symbol, effective_date);

INSERT INTO schema_log (schema_version, date) VALUES (211, now());