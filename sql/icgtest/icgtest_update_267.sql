CREATE TABLE wsc_taxpayer_supplemental_email_records (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  wsc_file_id BIGINT(20) NOT NULL,
  account_no VARCHAR(255) DEFAULT NULL,
  tin VARCHAR(255) DEFAULT NULL,
  tin_type VARCHAR(255) DEFAULT NULL,
  entity_code VARCHAR(255) DEFAULT NULL,
  email_address VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY(id),
  CONSTRAINT `wsc_taxpayer_supplemental_email_records_wsc_file_id`
    FOREIGN KEY (wsc_file_id) REFERENCES wsc_files (id)
) ENGINE=InnoDB;

INSERT INTO schema_log (schema_version, date) VALUES (267, now());