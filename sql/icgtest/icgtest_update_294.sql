ALTER TABLE prospectus_delivery_events
    ADD CONSTRAINT prospectus_delivery_events_exactly_one_prospectus_id
        CHECK ((filing_entry_id IS NOT NULL) XOR (prospectus_update_id IS NOT NULL));

ALTER TABLE last_prospectus_deliveries
    ADD CONSTRAINT last_prospectus_deliveries_exactly_one_prospectus_id
        CHECK ((filing_entry_id IS NOT NULL) XOR (prospectus_update_id IS NOT NULL));

INSERT INTO schema_log (schema_version, date) VALUES (294, now());