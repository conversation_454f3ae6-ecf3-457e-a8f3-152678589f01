CREATE TABLE fpsl_daily_client_interest_accrual_file_records (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    sharegain_file_id BIGINT(20) NOT NULL,
    created_at DATETIME NOT NULL,
    effective_date DATE NOT NULL,
    borrower_code VARCHAR(255) NOT NULL,
    loan_id VARCHAR(255) NOT NULL,
    bi_account_id_or_firm_account VARCHAR(255) NOT NULL,
    instrument_id BIGINT(20) NOT NULL,
    price DECIMAL(14,2) NOT NULL,
    quantity DECIMAL(20,8) NOT NULL,
    loan_value DECIMAL(14,2) NOT NULL,
    intrinsic_rate DECIMAL(8,6) NOT NULL,
    gross_intrinsic_accrual DECIMAL(16,6) NOT NULL,
    agent_revenue DECIMAL(16,6) NOT NULL,
    client_revenue DECIMAL(16,6) NOT NULL,
    aggregator_agent_fee_split DECIMAL(8,6) NOT NULL,
    client_aggregator_fee_split DECIMAL(8,6) NOT NULL,
    state VARCHAR(255) NOT NULL,
    <PERSON><PERSON>AR<PERSON> KEY (id),
    UNIQUE INDEX fd_client_ia_file_records_eff_date_loan_id_acct_id
        (sharegain_file_id, effective_date, loan_id, bi_account_id_or_firm_account),
    INDEX fd_client_ia_fr_eff_date_acct_id_borrower_code_instr_id
        (effective_date, bi_account_id_or_firm_account, borrower_code, instrument_id),
    CONSTRAINT fk_fd_client_ia_file_records_sharegain_file_id
        FOREIGN KEY (sharegain_file_id) REFERENCES sharegain_files (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

INSERT INTO schema_log (schema_version, date) VALUES (397, now());