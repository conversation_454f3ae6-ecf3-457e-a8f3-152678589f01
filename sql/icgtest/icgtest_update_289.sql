CREATE TABLE queued_money_market_dividend_payments (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  accountid BIGINT(20) NOT NULL,
  dividend_amount DECIMAL(20, 2) NOT NULL,
  money_market_fund VARCHAR(255) NOT NULL,
  period DATE NOT NULL,
  created_at DATETIME NOT NULL,
  polled_time DATETIME DEFAULT NULL,
  sent_time DATETIME DEFAULT NULL,
  ignored_at DATETIME DEFAULT NULL,
  error_flag TINYINT(1) NOT NULL DEFAULT 0,

  PRIMARY KEY (id),
  INDEX money_market_dividend_payments_accountid (accountid),
  INDEX money_market_dividend_payments_sent_polled_ignored_error_flag (sent_time, polled_time, ignored_at, error_flag),
  INDEX money_market_dividend_payments_error_flag (error_flag)
)
  ENGINE=InnoDB
  DEFAULT CHARSET=utf8;

INSERT INTO schema_log (schema_version, date) VALUES (289, now());
