CREATE TABLE wsc_taxpayer_account_transfer_records (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  wsc_file_id BIGINT(20) NOT NULL,
  transfer_type VARCHAR(255) DEFAULT NULL,
  account_no_a VARCHAR(255) DEFAULT NULL,
  account_no_b VARCHAR(255) DEFAULT NULL,
  transfer_date DATE DEFAULT NULL,
  PRIMARY KEY(id),
  CONSTRAINT `wsc_taxpayer_account_transfer_records_wsc_file_id`
    FOREIGN KEY (wsc_file_id) REFERENCES wsc_files (id)
) ENGINE=InnoDB;

INSERT INTO schema_log (schema_version, date) VALUES (272, now());