ALTER TABLE last_prospectus_deliveries MODIFY filing_entry_id BIGINT(20) DEFAULT NULL,
    ADD COLUMN prospectus_update_id BIGINT(20) DEFAULT NULL AFTER filing_entry_id,
    ADD CONSTRAINT last_prospectus_deliveries_prospectus_update_id FOREIGN KEY (prospectus_update_id) REFERENCES prospectus_updates (id);

ALTER TABLE prospectus_delivery_events MODIFY filing_entry_id BIGINT(20) DEFAULT NULL,
     ADD COLUMN prospectus_update_id BIGINT(20) DEFAULT NULL AFTER filing_entry_id,
     ADD CONSTRAINT prospectus_delivery_events_prospectus_update_id FOREIGN KEY (prospectus_update_id) REFERENCES prospectus_updates (id);

INSERT INTO schema_log (schema_version, date) VALUES (288, now());
