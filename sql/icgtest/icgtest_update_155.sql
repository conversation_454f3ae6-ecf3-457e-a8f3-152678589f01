CREATE TABLE worm_storage_inteliclear_files (
  id                      BIGINT(20)        NOT NULL AUTO_INCREMENT,
  inteliclear_file_id     BIGINT(20)        NOT NULL,
  created_at              DATETIME          NOT NULL,
  polled_time             DATETIME,
  sent_time               DATETIME,
  error_flag              TINYINT(1)        NOT NULL DEFAULT 0,
  PRIMARY KEY (id),
  CONSTRAINT worm_storage_inteliclear_files_inteliclear_file_id FOREIGN KEY (inteliclear_file_id) REFERENCES inteliclear_files (id),
  INDEX worm_storage_inteliclear_files_created_at (created_at),
  INDEX worm_storage_inteliclear_files_sent_time (sent_time),
  INDEX worm_storage_inteliclear_files_polled_time (polled_time)
) ENGINE = InnoDB DEFAULT CHARSET = utf8;

INSERT INTO schema_log (schema_version, date) VALUES (155, now());