CREATE TABLE fpsl_daily_custodian_interest_accrual_file_records
(
    id                                            BIGINT(20)     NOT NULL AUTO_INCREMENT,
    state                                         VARCHAR(255)   NOT NULL,
    sharegain_file_id                             BIGINT(20)     NOT NULL,
    created_at                                    DATETIME       NOT NULL,
    accrual_date                                  DATE           NOT NULL,
    borrower_code                                 VARCHAR(255)   NOT NULL,
    loan_id                                       VARCHAR(255)   NOT NULL,
    isin                                          VARCHAR(255)   NOT NULL,
    ticker                                        VARCHAR(255)   NOT NULL,
    price                                         DECIMAL(19, 4) NOT NULL,
    quantity                                      DECIMAL(19, 4) NOT NULL,
    loan_value                                    DECIMAL(19, 4) NOT NULL,
    intrinsic_rate                                DECIMAL(19, 4) NOT NULL,
    gross_intrinsic_accrual                       DECIMAL(19, 4) NOT NULL,
    borrower_investment_rate                      DECIMAL(19, 4) NOT NULL,
    gross_interest_owed_to_borrower               DECIMAL(19, 4) NOT NULL,
    rebate_rate                                   DECIMAL(19, 4) NOT NULL,
    rebate_amount                                 DECIMAL(19, 4) NOT NULL,
    lender_interest_rate                          DECIMAL(19, 4) NOT NULL,
    gross_interest_accrued_by_lender              DECIMAL(19, 4) NOT NULL,
    cost_of_carry_rate                            DECIMAL(19, 4) NOT NULL,
    cost_of_carry                                 DECIMAL(19, 4) NOT NULL,
    lender_interest_rate_minus_cost_of_carry_rate DECIMAL(19, 4) NOT NULL,
    lender_interest_adjusted_by_cost_of_carry     DECIMAL(19, 4) NOT NULL,
    excess_interest                               DECIMAL(19, 4) NOT NULL,
    aggregator_agent_fee_split                    DECIMAL(19, 4) NOT NULL,
    agent_revenue                                 DECIMAL(19, 4) NOT NULL,
    aggregator_revenue                            DECIMAL(19, 4) NOT NULL,
    PRIMARY KEY (id),
    FOREIGN KEY (sharegain_file_id) REFERENCES sharegain_files (id)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;

INSERT INTO schema_log (schema_version, date) VALUES (401, now());
