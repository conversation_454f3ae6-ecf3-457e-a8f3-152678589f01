CREATE TABLE transaction_metadata (
  transaction_id BIGINT(20) NOT NULL,
  metadata_key VARCHAR(255) NOT NULL,
  metadata_value VARCHAR(255) NOT NULL,
  CONSTRAINT fkey_transaction_metadata_transaction_id FOREIGN KEY (transaction_id) REFERENCES transactions (id),
  KEY transaction_metadata_key (metadata_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO schema_log (schema_version, date) VALUES (98, NOW());