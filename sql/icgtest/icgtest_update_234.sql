/*
# Table Data:

# MariaDB [inteliclear]> select count(*) From transaction_metadata;
# +----------+
# | count(*) |
# +----------+
# |  6197068 |
# +----------+
# 1 row in set (1.79 sec)

# MariaDB [inteliclear]> SELECT table_name AS "Table", round(((data_length + index_length) / 1024 / 1024), 2) "Size in MB" FROM information_schema.TABLES WHERE table_name = "transaction_metadata";
# +----------------------+------------+
# | Table                | Size in MB |
# +----------------------+------------+
# | transaction_metadata |     625.00 |
# +----------------------+------------+
# 2 rows in set (0.01 sec)

# MariaDB [inteliclear]> select fk.table_name, fk.column_name, fk.referenced_table_name, fk.referenced_column_name, round(((t.data_length + t.index_length)/1024/1024), 2) as size_in_mb
#     -> from information_schema.key_column_usage fk
#     -> join information_schema.tables t on fk.table_name = t.table_name
#     -> where t.table_schema = 'icgtest' and fk.referenced_table_name in ('transaction_metadata');
# Empty set (1.59 sec)

# MariaDB [inteliclear]> show table status in inteliclear like 'transaction_metadata';
# +----------------------+--------+---------+------------+---------+----------------+-------------+-----------------+--------------+-----------+----------------+---------------------+-------------+------------+-----------------+----------+----------------+---------+
# | Name                 | Engine | Version | Row_format | Rows    | Avg_row_length | Data_length | Max_data_length | Index_length | Data_free | Auto_increment | Create_time         | Update_time | Check_time | Collation       | Checksum | Create_options | Comment |
# +----------------------+--------+---------+------------+---------+----------------+-------------+-----------------+--------------+-----------+----------------+---------------------+-------------+------------+-----------------+----------+----------------+---------+
# | transaction_metadata | InnoDB |      10 | Compact    | 6172425 |             72 |   445644800 |               0 |    209715200 |   4194304 |           NULL | 2017-12-19 20:50:40 | NULL        | NULL       | utf8_general_ci |     NULL |                |         |
# +----------------------+--------+---------+------------+---------+----------------+-------------+-----------------+--------------+-----------+----------------+---------------------+-------------+------------+-----------------+----------+----------------+---------+
# 1 row in set (0.01 sec)

# Procedure below:

ssh db-sv2-trading-master.wlth.fr
mysql -uwfadmin -p
# Confirm slave hosts
show slave hosts;

# On slaves:
mysql -uwfadmin -p
show slave status\G;

# Silence icinga checks for readonly slave

# Stop replication on readonly slave
ssh db-sv2-trading-readonly.wlth.fr
mysql -uwfadmin -p
show slave status\G;
stop slave;
show slave status\G;

# Stop slave on ny424
ssh ny424
mysql -uwfadmin -p
show slave status;
stop slave;
show slave status;

# Do the changes on master
ssh db-sv2-trading-master.wlth.fr
tmux

# dry run:
pt-online-schema-change
  --alter "ADD INDEX transaction_metadata_key_value (metadata_key, metadata_value)"
  --charset utf8
  --ask-pass D=inteliclear,t=transaction_metadata,A=utf8,h=localhost,u=wfadmin
  --alter-foreign-keys-method drop_swap
  --print
  --dry-run

pt-online-schema-change
  --alter "DROP INDEX transaction_metadata_key"
  --charset utf8
  --ask-pass D=inteliclear,t=transaction_metadata,A=utf8,h=localhost,u=wfadmin
  --alter-foreign-keys-method drop_swap
  --print
  --dry-run

# execution
pt-online-schema-change
  --alter "ADD INDEX transaction_metadata_key_value (metadata_key, metadata_value)"
  --charset utf8
  --ask-pass D=inteliclear,t=transaction_metadata,A=utf8,h=localhost,u=wfadmin
  --alter-foreign-keys-method drop_swap
  --print
  --execute

# execution
pt-online-schema-change
--alter "DROP INDEX transaction_metadata_key"
--charset utf8
--ask-pass D=inteliclear,t=transaction_metadata,A=utf8,h=localhost,u=wfadmin
--alter-foreign-keys-method drop_swap
--print
--execute

# start slave on sv218
ssh db-sv2-trading-readonly.wlth.fr
mysql -uwfadmin -p
start slave;
show slave status \G;

# Start slave on ny424
ssh ny424
mysql -uwfadmin -p
show slave status;
start slave;
show slave status;

# Make sure the table updates are synced

# Re-enable icinga checks for readonly slave
*/

ALTER TABLE transaction_metadata
DROP INDEX transaction_metadata_key,
ADD INDEX transaction_metadata_key_value (metadata_key, metadata_value);

INSERT INTO schema_log (schema_version, DATE) VALUES (234, now());