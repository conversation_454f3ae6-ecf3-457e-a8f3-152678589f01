DROP TABLE fpsl_client_account_loans;

CREATE TABLE fpsl_client_account_loans (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    sharegain_file_id BIGINT(20) NOT NULL,
    effective_date DATE NOT NULL,
    bi_account_id_or_firm_account VARCHAR(255) NOT NULL,
    borrower_code VARCHAR(255) NOT NULL,
    borrower_name VARCHAR(255) NOT NULL,
    isin VARCHAR(255) NOT NULL,
    instrument_id BIGINT(20) NOT NULL,
    original_units DECIMAL(20,8) NOT NULL,
    settled_units DECIMAL(20,8) NOT NULL,
    PRIMARY KEY (id),
    CONSTRAINT fpsl_client_account_loans_sharegain_file_id FOREIGN KEY (sharegain_file_id) REFERENCES sharegain_files (id)
 ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

 INSERT INTO schema_log (schema_version, date) VALUES (375, now());