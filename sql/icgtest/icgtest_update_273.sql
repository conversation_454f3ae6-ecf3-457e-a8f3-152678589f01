ALTER TABLE ira_account_transfers
  ADD COLUMN cross_account_transferid BIGINT(20) DEFAULT NULL AFTER internal_asset_transferid,
  ADD UNIQUE KEY ira_account_transfers_cross_account_transferid (cross_account_transferid),
  ADD UNIQUE KEY ira_account_transfers_internal_asset_transferid (internal_asset_transferid);

ALTER TABLE ira_asset_transfers
  ADD COLUMN cross_account_transferid BIGINT(20) DEFAULT NULL AFTER internal_asset_transferid,
  ADD UNIQUE KEY ira_asset_transfers_cross_account_transferid (cross_account_transferid);

INSERT INTO schema_log (schema_version, date) VALUES (273, now());
