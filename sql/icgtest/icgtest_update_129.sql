CREATE TABLE ira_deposits (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  ira_account_id BIGINT(20) NOT NULL,
  ira_services_deposit_id BIGINT(20) DEFAULT NULL,
  deposit MEDIUMTEXT NOT NULL,
  target_environment VARCHAR(255) NOT NULL,
  created_at DATETIME NOT NULL,
  updated_at DATETIME NOT NULL,
  amount DECIMAL(16, 4) NOT NULL,
  PRIMARY KEY (id),
  UNIQUE KEY ira_deposits_ira_services_deposit_id (ira_services_deposit_id),
  KEY ira_deposits_ira_account_id (ira_account_id),
  CONSTRAINT fkey_ira_deposits_ira_account_id FOREIGN KEY (ira_account_id) REFERENCES ira_accounts (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO schema_log (schema_version, date) VALUES (129, now());