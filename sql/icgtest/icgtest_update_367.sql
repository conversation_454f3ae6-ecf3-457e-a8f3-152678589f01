CREATE TABLE fpsl_loan_instruction_file_records (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    sharegain_file_id BIGINT(20) NOT NULL,
    state VARCHAR(255) NOT NULL,
    effective_date DATE NOT NULL,
    trade_id VARCHAR(255) NOT NULL,
    record TEXT NOT NULL,
    PRIMARY KEY (id),
    INDEX idx_fpsl_loan_instruction_file_records_trade_id (trade_id),
    CONSTRAINT fkey_sharegain_file_id
        FOREIGN KEY (sharegain_file_id) REFERENCES sharegain_files (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE fpsl_loan_instructions (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    fpsl_loan_instruction_file_record_id BIGINT(20) NOT NULL,
    direction VARCHAR(255) NOT NULL,
    created_at DATETIME NOT NULL,
    settlement_date DATE NOT NULL,
    state VARCHAR(255) NOT NULL,
    end_date DATE DEFAULT NULL,
    custodian_account_number VARCHAR(255) NOT NULL,
    isin VARCHAR(255) NOT NULL,
    instrument_id BIGINT(20) NOT NULL,
    quantity DECIMAL(20,8) NOT NULL,
    price DECIMAL(14,2) NOT NULL,
    lending_rate DECIMAL(20,8) NOT NULL,
    trade_id VARCHAR(255) NOT NULL,
    link_trade_id VARCHAR(255) DEFAULT NULL,
    borrower_code VARCHAR(255) NOT NULL,
    journaled BIGINT(1) DEFAULT FALSE,
    PRIMARY KEY (id),
    INDEX fpsl_loan_instructions_trade_id (trade_id),
    CONSTRAINT fkey_fpsl_loan_instruction_file_record_id
        FOREIGN KEY (fpsl_loan_instruction_file_record_id) REFERENCES fpsl_loan_instruction_file_records (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

INSERT INTO schema_log (schema_version, date) VALUES (367, NOW());