create table wsc_files(
  id bigint(20) not null auto_increment,
  created_at varchar(255) not null,
  filename varchar(255) not null,
  file_date date not null,
  period_start date not null,
  period_end date not null,
  file_type varchar(255) not null,
  is_incoming tinyint(1) not null,
  n_records int not null,
  primary key (id)
) ENGINE=InnoDB;

create table wsc_transactions_records(
  id bigint(20) not null auto_increment,
  file_id varchar(255) default null,
  account_no varchar(255) default null,
  tax_lot varchar(255) default null,
  tax_year smallint default null,
  cusip_no varchar(255) default null,
  trans_code varchar(255) default null,
  settle_date date default null,
  trade_date date default null,
  amount decimal(12, 2) default null,
  with_held_amt decimal(14, 2) default null,
  foreign_tax decimal(14, 2) default null,
  accrd_int decimal(14, 2) default null,
  quantity decimal(21, 5) default null,
  price decimal(12, 5) default null,
  security_desc_1 varchar(255) default null,
  security_desc_2 varchar(255) default null,
  security_desc_3 varchar(255) default null,
  cancel_ind tinyint(1) default null,
  cust_reference varchar(255) default null,
  stmt_trail varchar(255) default null,
  currency_code varchar(255) default null,
  open_trans_tr_date date default null,
  open_trans_st_date date default null,
  orig_cost decimal(14, 2) default null,
  adj_avg_cost decimal(14, 2) default null,
  option_prem_proc decimal(14, 2) default null,
  gain_loss decimal(14, 2) default null,
  proc_gross_net varchar(255) default null,
  lot_relief_ind varchar(255) default null,
  cost_basis_ind varchar(255) default null,
  term_of_sale varchar(255) default null,
  ex_date date default null,
  rptd_els_wh_ind tinyint(1) default null,
  client_tax_code varchar(255) default null,
  end_note_id varchar(255) default null,
  recipient_no varchar(255) default null,
  payer_code varchar(255) default null,
  internal_trans_code varchar(255) default null,
  income_princ_ind varchar(255) default null,
  registration_code varchar(255) default null,
  state_res_with decimal(14, 2) default null,
  st_resident_code varchar(255) default null,
  state_sec_with decimal(14, 2) default null,
  st_security_code varchar(255) default null,
  country_code varchar(255) default null,
  covered_tax_lot_ind varchar(255) default null,
  dis_wash_sale_loss decimal(14, 2) default null,
  adj_basis_eff_date date default null,
  lot_hold_date date default null,
  accrd_mkt_discount decimal(14, 2) default null,
  no_loss_ind varchar(255) default null,
  bankruptcy_ind varchar(255) default null,
  borrow_liable_ind tinyint(1) default null,
  rel_tax_lot_id varchar(255) default null,
  ticker_symbol varchar(255) default null,
  option_symbol varchar(255) default null,
  unamort_bond_prem decimal(14, 2) default null,
  gift_date date default null,
  gift_fmv decimal(15, 2) default null,
  donor_adj_basis decimal(15, 2) default null,
  purchase_floating_rate decimal(6, 3) default null,
  purchase_spot_rate decimal(13, 6) default null,
  primary key (id),
  key wsc_transactions_record_file_id (file_id)
) ENGINE=InnoDB;

create table wsc_name_and_address_records(
  id bigint(20) not null auto_increment,
  file_id varchar(255) default null,
  account_no varchar(255) default null,
  tax_year smallint default null,
  account_title varchar(255) default null,
  first_name varchar(255) default null,
  last_name varchar(255) default null,
  legal_name_1 varchar(255) default null,
  legal_name_2 varchar(255) default null,
  legal_name_3 varchar(255) default null,
  legal_address_1 varchar(255) default null,
  legal_address_2 varchar(255) default null,
  legal_address_3 varchar(255) default null,
  legal_address_4 varchar(255) default null,
  legal_city varchar(255) default null,
  legal_st_prov_cde varchar(255) default null,
  legal_zip_code varchar(255) default null,
  legal_zip_code_ext varchar(255) default null,
  legal_cntry_code varchar(255) default null,
  free_form_ind varchar(255) default null,
  dom_rpt_ind varchar(255) default null,
  stmt_10_99type varchar(255) default null,
  stmt_10_42ind tinyint(1) default null,
  distr_contr_ind tinyint(1) default null,
  distr_contr_type varchar(255) default null,
  tin varchar(255) default null,
  tin_type varchar(255) default null,
  account_type varchar(255) default null,
  reg_rep_id varchar(255) default null,
  office_code varchar(255) default null,
  payer_id varchar(255) default null,
  logo_id varchar(255) default null,
  b_notice varchar(255) default null,
  w_8 tinyint(1) default null,
  name_control varchar(255) default null,
  prvt tinyint(1) default null,
  residence_state varchar(255) default null,
  entity_code varchar(255) default null,
  delivery_ind varchar(255) default null,
  mail_addr_ind tinyint(1) default null,
  custom_sort_1 varchar(255) default null,
  custom_sort_2 varchar(255) default null,
  email_addr varchar(255) default null,
  master_bene_ind varchar(255) default null,
  non_cov_supp_ind varchar(255) default null,
  bonafide_pr_ind varchar(255) default null,
  correspondent_id varchar(255) default null,
  hold_stmt_ind varchar(255) default null,
  companion_stmt_ind varchar(255) default null,
  lob_code varchar(255) default null,
  with_agent_id varchar(255) default null,
  recipient_status_ch_3 varchar(255) default null,
  recipient_status_ch_4 varchar(255) default null,
  recip_giin varchar(255) default null,
  recip_foreign_tax_id varchar(255) default null,
  recip_country_res varchar(255) default null,
  recip_cntry_code varchar(255) default null,
  inter_med_id varchar(255) default null,
  nrair_elig_ind tinyint(1) default null,
  birth_date date default null,
  date_of_death date default null,
  bene_percent decimal(5, 2) default null,
  master_acct_no varchar(255) default null,
  bene_designation varchar(255) default null,
  bene_relation varchar(255) default null,
  spouse_acct_no varchar(255) default null,
  orig_date_of_death date default null,
  total_distr_ind tinyint(1) default null,
  roth_1st_yr smallint default null,
  rmd_ind tinyint(1) default null,
  cert_chron_ill_date date default null,
  cert_term_ill_date date default null,
  plan_id varchar(255) default null,
  rmd_table_type varchar(255) default null,
  life_exp_factor decimal(3, 1) default null,
  rmd_type varchar(255) default null,
  orig_date_of_birth date default null,
  prim_cont_pers_ind varchar(255) default null,
  prim_account_no varchar(255) default null,
  form_8966ind varchar(255) default null,
  number_of_accounts int default null,
  sponsor_id varchar(255) default null,
  primary key(id),
  key wsc_name_and_address_record_file_id (file_id)
) ENGINE=InnoDB;

create table wsc_security_master_records (
  id bigint(20) not null auto_increment,
  file_id varchar(255) default null,
  cusip_no varchar(255) default null,
  sec_ind varchar(255) default null,
  tax_year smallint default null,
  security_desc_1 varchar(255) default null,
  security_desc_2 varchar(255) default null,
  security_desc_3 varchar(255) default null,
  security_type varchar(255) default null,
  tax_ind varchar(255) default null,
  cntry_code varchar(255) default null,
  issue_date date default null,
  maturity_date date default null,
  state_code varchar(255) default null,
  exchange_loc varchar(255) default null,
  coupon_rate decimal(6, 3) default null,
  coupon_type varchar(255) default null,
  next_call_date date default null,
  next_call_price decimal(7, 4) default null,
  ticker_symbol varchar(255) default null,
  end_note_id varchar(255) default null,
  option_symbol varchar(255) default null,
  class_share_exch varchar(255) default null,
  internal_sec_id varchar(255) default null,
  contra_cusip tinyint(1) default null,
  parent_cusip varchar(255) default null,
  parent_int_sec_id varchar(255) default null,
  coupon_freq varchar(255) default null,
  call_type varchar(255) default null,
  min_notification varchar(255) default null,
  day_count_method varchar(255) default null,
  first_coupon_dt date default null,
  last_coupn_dt date default null,
  currency_code varchar(255) default null,
  unit_factor varchar(255) default null,
  primary key(id),
  key wsc_security_master_record_file_id (file_id)
) ENGINE=InnoDB;

insert into schema_log (schema_version, date) values (118, now());
