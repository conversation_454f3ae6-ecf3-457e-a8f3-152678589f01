alter table forge_ira_account_transfers
    add unique index (internal_asset_transfer_id);
alter table forge_ira_account_transfers
    add unique index (cross_account_transfer_id);
alter table forge_ira_account_transfers
    add unique index (acats_transfer_id);
alter table forge_ira_account_transfers
    add unique index (dtc_transfer_id);

alter table forge_ira_asset_transfers
    add unique index (cross_account_transfer_id);
alter table forge_ira_asset_transfers
    add unique index (internal_asset_transfer_id);

alter table forge_ira_deposits
    add unique index (ira_account_id, dividend_type, symbol, effective_date);
alter table forge_ira_deposits
    add unique index (bank_deposit_id);
alter table forge_ira_deposits
    add unique index (forge_deposits_id);
alter table forge_ira_deposits
    add unique index (ic_transaction_number);
alter table forge_ira_deposits
    add unique index (bucket_id);

alter table forge_ira_excess_contribution_removals
    add unique index (forge_excess_contribution_removal_id);

alter table forge_ira_investment_purchases
    add unique index (forge_investment_purchase_id);

alter table forge_ira_rollovers
    add unique index (bank_deposit_id, ira_account_id);
alter table forge_ira_rollovers
    add unique index (bank_withdrawal_id);

alter table forge_ira_withdrawals
    add unique index (forge_withdrawal_id);
alter table forge_ira_withdrawals
    add unique index (bank_withdrawal_id);

INSERT INTO schema_log (schema_version, date) VALUES (303, now());

COMMIT;