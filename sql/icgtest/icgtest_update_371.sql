CREATE TABLE wf_advisors_collateral_report (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    effective_date DATE NOT NULL,
    total_value_lent DECIMAL(20, 8) NOT NULL,
    total_expected_collateral_value DECIMAL(20, 8) NOT NULL,
    previous_day_collateral DECIMAL(20, 8) NOT NULL,
    net_collateral_movement DECIMAL(20, 8) NOT NULL,
    PRIMARY KEY (id),
    UNIQUE (effective_date)
 ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

 CREATE TABLE wf_advisors_collateral_report_security_record (
     id BIGINT(20) NOT NULL AUTO_INCREMENT,
     wf_advisors_collateral_report_id BIGINT(20) NOT NULL,
     cusip varchar(255) NOT NULL,
     instrumentid bigint(20) NOT NULL,
     quantity_lent DECIMAL(20, 8) NOT NULL,
     previous_days_price DECIMAL(20, 8) NOT NULL,
     value_lent DECIMAL(20, 8) NOT NULL,
     expected_collateral_value DECIMAL(20, 8) NOT NULL,
     PRIMARY KEY (id),
     CONSTRAINT collateral_report_security_record_report_id FOREIGN KEY (wf_advisors_collateral_report_id) REFERENCES wf_advisors_collateral_report (id)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

 INSERT INTO schema_log (schema_version, date) VALUES (371, NOW());