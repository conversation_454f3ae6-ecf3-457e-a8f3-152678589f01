CREATE TABLE fpsl_daily_interest_splits (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    created_at DATETIME NOT NULL,
    effective_date DATE NOT NULL,
    year SMALLINT NOT NULL,
    state VARCHAR(255) NOT NULL,
    fpsl_daily_interest_split_details MEDIUMTEXT NOT NULL,
    total_daily_interest_accrual DECIMAL(20,8) DEFAULT NULL,
    PRIMARY KEY (id),
    UNIQUE INDEX fpsl_daily_interest_splits_eff_date (effective_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE version_fpsl_daily_interest_splits_total_daily_interest_accrual (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    fpsl_daily_interest_split_id BIGINT(20) NOT NULL,
    version INT(11) NOT NULL,
    created_at DATETIME NOT NULL,
    deleted TINYINT(1) NOT NULL DEFAULT 0,
    value DECIMAL(20,8) DEFAULT NULL,
    PRIMARY KEY (id),
    CONSTRAINT fk_version_fpsl_daily_interest_split_id
        FOREIGN KEY (fpsl_daily_interest_split_id) REFERENCES fpsl_daily_interest_splits (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

INSERT INTO schema_log(schema_version, date) VALUES (388, now());
