CREATE TABLE sharegain_cash_transfer_instructions (
    id BIGINT NOT NULL AUTO_INCREMENT,
    file_id BIGINT NOT NULL,
    effective_date DATE NOT NULL,
    from_account_number VARCHAR(255) NOT NULL,
    to_account_number VARCHAR(255) NOT NULL,
    amount DECIMAL(20, 8) NOT NULL,
    reference_id VARCHAR(255) NOT NULL,
    transfer_type VARCHAR(20) NOT NULL,
    PRIMARY KEY (id),
    CONSTRAINT fkey_sharegain_cash_transfer_instructions_file_id FOREIGN KEY (file_id) REFERENCES sharegain_files (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

INSERT INTO schema_log (schema_version, date) VALUES (372, NOW());
