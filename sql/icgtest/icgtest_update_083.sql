CREATE TABLE intact_files (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  effective_date DATE NOT NULL,
  cycle INT(11) NOT NULL,
  expired TINYINT(1) NOT NULL DEFAULT 0,
  created_at DATETIME NOT NULL,
  polled_at DATETIME DEFAULT NULL,
  sent_at DATETIME DEFAULT NULL,
  data LONGTEXT NOT NULL,
  PRIMARY KEY (id),
  KEY intact_files_effective_date (effective_date),
  KEY intact_files_polled_at (polled_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE intact_file_rbc_journals (
  intact_file_id BIGINT(20) NOT NULL,
  rbc_journal_id BIGINT(20) NOT NULL,
  UNIQUE KEY rbc_journal_id (rbc_journal_id),
  KEY `intact_file_rbc_journals_intact_file_id` (`intact_file_id`),
  CONSTRAINT `intact_file_rbc_journals_intact_file_id` FOR<PERSON><PERSON><PERSON>EY (`intact_file_id`) REFERENCES `intact_files` (`id`),
  CONSTRAINT `intact_file_rbc_journals_rbc_journal_id` FOREIGN KEY (`rbc_journal_id`) REFERENCES `rbc_journals` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO schema_log (schema_version, DATE) VALUES (83, now());