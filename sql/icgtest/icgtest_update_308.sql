DROP INDEX bank_deposit_id ON forge_ira_rollovers;
DROP INDEX bank_withdrawal_id on forge_ira_rollovers;

DROP INDEX bank_deposit_id ON forge_ira_deposits;
DROP INDEX bucket_id ON forge_ira_deposits;

DROP INDEX bank_withdrawal_id ON forge_ira_withdrawals;

ALTER TABLE forge_ira_deposits DROP COLUMN bank_deposit_id;
ALTER TABLE forge_ira_deposits DROP COLUMN bucket_id;

ALTER TABLE forge_ira_withdrawals DROP COLUMN bank_withdrawal_id;

ALTER TABLE forge_ira_rollovers DROP COLUMN bank_deposit_id;
ALTER TABLE forge_ira_rollovers DROP COLUMN bank_withdrawal_id;
ALTER TABLE forge_ira_rollovers DROP COLUMN bucket_id;

INSERT INTO schema_log (schema_version, date) VALUES (308, now());

COMMIT;