CREATE TABLE incoming_wsc_files (
  id                    BIGINT(20)   NOT NULL   AUTO_INCREMENT,
  downloaded_at         DATETIME     NOT NULL,
  filename              VARCHAR(255) NOT NULL,
  file_date             DATE         NOT NULL,
  state                 VARCHAR(255) NOT NULL,
  file_type             VARCHAR(255) NOT NULL,
  PRIMARY KEY (id),
  UNIQUE KEY (filename),
  KEY incoming_wsc_files_state (state)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE version_incoming_wsc_file_state (
  id                    BIGINT(20)   NOT NULL   AUTO_INCREMENT,
  incoming_wsc_file_id  BIGINT(20)   NOT NULL,
  version               INT(11)      NOT NULL,
  created_at            DATETIME     NOT NULL,
  deleted               TINYINT(1)   NOT NULL,
  value                 VARCHAR(255) NOT NULL,
  PRIMARY KEY (id),
  KEY version_incoming_wsc_file_state_id (incoming_wsc_file_id),
  CONSTRAINT version_incoming_wsc_file_state_id FOREIGN KEY (incoming_wsc_file_id) REFERENCES incoming_wsc_files (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO schema_log (schema_version, date) values (254, now());