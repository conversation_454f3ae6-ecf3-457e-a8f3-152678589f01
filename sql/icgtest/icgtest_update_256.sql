ALTER TABLE ira_accounts
ADD COLUMN account_state VARCHAR(255) NOT NULL DEFAULT 'ACTIVE',
ADD INDEX ira_accounts_account_state (account_state);

CREATE TABLE version_ira_account_account_state (
  id                    BIGINT(20)   NOT NULL   AUTO_INCREMENT,
  ira_account_id        BIGINT(20)   NOT NULL,
  version               INT(11)      NOT NULL,
  created_at            DATETIME     NOT NULL,
  deleted               TINYINT(1)   NOT NULL,
  value                 VARCHAR(255) NOT NULL,
  PRIMARY KEY (id),
  KEY version_ira_account_account_state (ira_account_id),
  CONSTRAINT version_ira_account_account_state_id FOREIGN KEY (ira_account_id) REFERENCES ira_accounts (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO schema_log (schema_version, date) values (256, now());
