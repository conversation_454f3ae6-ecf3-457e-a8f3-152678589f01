CREATE TABLE reserve_calculation_data (
    id BIGINT(20) NOT NULL AUTO_INCREMENT PRIMARY KEY,
    calculation_date date NOT NULL,
    wells_account_balances_via_wells text DEFAULT NULL,
    wells_account_balances_via_bank text DEFAULT NULL,
    ic_trial_balances text DEFAULT NULL,
    ic_wash_accounts longtext DEFAULT NULL,
    debits text DEFAULT NULL,
    fdic_covered_debits longtext DEFAULT NULL,
    rbc_balances text DEFAULT NULL,
    tbs_balances longtext DEFAULT NULL,
    customer_credits DECIMAL(20,2) DEFAULT NULL,
    freed_iemg DECIMAL(20,2) DEFAULT NULL,
    freed_schf DECIMAL(20,2) DEFAULT NULL,
    ops_adjustment DECIMAL(20,2) DEFAULT NULL,
    checks_value DECIMAL(20,2) DEFAULT NULL,
    checks_volume DECIMAL(20,2) DEFAULT NULL,
    tnc_margin_debits DECIMAL(20,2) DEFAULT NULL,
    UNIQUE(calculation_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

INSERT INTO schema_log (schema_version, DATE) VALUES (360, now());