CREATE TABLE `atlas_snapshotting_directives` (
  `id`                    BIGINT(20)      NOT NULL AUTO_INCREMENT,
  `created_at`            DATETIME        NOT NULL,
  `account_number`        VARCHAR(255)    NOT NULL,
  `start_before_date`     DATE            DEFAULT NULL,
  `snapshot_boundaries`   MEDIUMTEXT      NOT NULL,
  `requester`             VARCHAR(255)    DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8;

INSERT INTO schema_log (schema_version, date) VALUES (261, now());