create table error_trades (
  id bigint(20) not null AUTO_INCREMENT,
  allocated_order_id bigint(20) not null,
  account_id bigint(20) not null,
  symbol varchar(255) not null,
  instrument_id bigint(20) not null,
  action varchar(255) not null,
  transaction_data text default null,
  progress varchar(255) not null,
  primary key (id),
  key error_trades_progress (progress)
) ENGINE = InnoDB DEFAULT CHARSET = utf8;

INSERT INTO schema_log (schema_version, date) VALUES (239, now());
