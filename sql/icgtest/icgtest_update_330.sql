# skip mirrored schemas test - confirmed that these are mirrored
CREATE TABLE version_wsc_transactions_record_ignored (
  id                        BIGINT(20)    NOT NULL AUTO_INCREMENT,
  wsc_transactions_record_id BIGINT(20)   NOT NULL,
  version                   INT(11)       NOT NULL,
  created_at                DATETIME      NOT NULL,
  deleted                   TINYINT(1)    NOT NULL,
  value                     TINYINT(1)    NOT NULL,
  PRIMARY KEY (id),
  CONSTRAINT fkey_vwtri_wsc_transactions_record_id FOREIGN KEY (wsc_transactions_record_id) REFERENCES wsc_transactions_records (id)
) engine=InnoDB DEFAULT charset=utf8mb4;

ALTER TABLE version_wsc_transactions_record_file_id
MODIFY COLUMN value BIGINT(20) DEFAULT NULL;

INSERT INTO schema_log (schema_version, date) VALUES (330, now());