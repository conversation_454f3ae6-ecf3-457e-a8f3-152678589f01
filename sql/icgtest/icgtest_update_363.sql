CREATE TABLE sharegain_files (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    created_at DATETIME NOT NULL,
    state VARCHAR(255) NOT NULL,
    type VARCHAR(255) NOT NULL,
    filename VARCHAR(255) NOT NULL,
    PRIMARY KEY (id),
    INDEX sharegain_file_state (state)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE sharegain_file_state (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    sharegain_file_id BIGINT NOT NULL,
    version INT(11) NOT NULL,
    created_at DATETIME NOT NULL,
    deleted TINYINT(1) NOT NULL,
    value VARCHAR(255) NOT NULL,
    PRIMARY KEY (id),
    INDEX version_sharegain_files_state_created_at (created_at),
    CONSTRAINT fkey_version_sharegain_files_state FOREIGN KEY (sharegain_file_id) REFERENCES sharegain_files (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

INSERT INTO schema_log (schema_version, date) VALUES (363, NOW());