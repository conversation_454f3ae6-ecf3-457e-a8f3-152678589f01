CREATE TABLE seg_runs (
    id             BIGINT(20) NOT NULL AUTO_INCREMENT,
    version        INT        NOT NULL DEFAULT 0,
    created_at     DATETIME   NOT NULL,
    effective_date DATE       NOT NULL,
    parameters     MEDIUMTEXT NOT NULL,
    approved_at    DATETIME   DEFAULT NULL,
    journaled_at   DATETIME   DEFAULT NULL,
    PRIMARY KEY (id),
    KEY seg_runs_effective_date (effective_date)
) ENGINE = InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE segregated_securities (
    id                      BIGINT(20)    NOT NULL AUTO_INCREMENT,
    version                 INT           NOT NULL DEFAULT 0,
    seg_run_id              BIGINT(20)    NOT NULL,
    accountid               BIGINT(20)    NOT NULL,
    net_account_cash        DECIMAL(20,2) NOT NULL,
    cusip                   VARCHAR(255)  NOT NULL,
    price                   DECIMAL(20,2) NOT NULL,
    current_freed_quantity  DECIMAL(20,5) NOT NULL,
    proposed_freed_quantity DECIMAL(20,5) NOT NULL,
    proposed_freed_value    DECIMAL(20,2) NOT NULL,
    processed_at            DATETIME      DEFAULT NULL,
    PRIMARY KEY (id),
    CONSTRAINT segregated_securities_seg_run_id FOREIGN KEY (seg_run_id) REFERENCES seg_runs (id),
    CONSTRAINT segregated_securities_acct_run_cusip UNIQUE (accountid, seg_run_id, cusip)
) ENGINE = InnoDB DEFAULT CHARSET = utf8;

INSERT INTO schema_log (schema_version, date) VALUES (297, now());