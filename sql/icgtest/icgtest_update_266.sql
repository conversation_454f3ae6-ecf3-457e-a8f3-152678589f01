create table wsc_taxpayer_position_records (
  id bigint(20) not null auto_increment,
  wsc_file_id bigint(20) not null,
  account_no varchar(255) default null,
  security varchar(255) default null,
  tin varchar(255) default null,
  tin_type varchar(255) default null,
  entity_code varchar(255) default null,
  units_held varchar(255) default null,
  mailing_address_city varchar(255) default null,
  mailing_address_state_code varchar(255) default null,
  mailing_address_postal_code varchar(255) default null,
  legal_state_code varchar(255) default null,
  name_line_1 varchar(255) default null,
  name_line_2 varchar(255) default null,
  name_line_3 varchar(255) default null,
  address_line_1 varchar(255) default null,
  address_line_2 varchar(255) default null,
  custodial_tax_id_number varchar(255) default null,
  ein_990_t_number varchar(255) default null,
  primary key(id),
  constraint `wsc_taxpayer_position_records_wsc_file_id`
    foreign key (wsc_file_id) references wsc_files (id)
) ENGINE=InnoDB;

INSERT INTO schema_log (schema_version, date) VALUES (266, now());