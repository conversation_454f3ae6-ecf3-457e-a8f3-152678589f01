CREATE TABLE `data_platform_account_snapshots` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `effective_date` date NOT NULL,
  `account_number` varchar(255) NOT NULL,
  `snapshot` mediumtext NOT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `data_platform_account_snapshots_effective_date_account_number` (`effective_date`,`account_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO schema_log (schema_version, date) VALUES (237, now());