CREATE TABLE prospectus_updates
(
    id             BIGINT(20)   PRIMARY KEY AUTO_INCREMENT,
    symbol         VARCHAR(255) NOT NULL,
    cusip          VARCHAR(255) NOT NULL,
    effective_date DATE         NOT NULL,
    url            VARCHAR(255) NOT NULL,
    UNIQUE KEY (cusip)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8;

CREATE TABLE version_prospectus_updates_effective_date
(
    id                   BIGINT(20)   PRIMARY KEY AUTO_INCREMENT,
    prospectus_update_id BIGINT(20)   NOT NULL,
    version              INT(11)      NOT NULL,
    created_at           DATETIME     NOT NULL,
    deleted              TINYINT(1)   NOT NULL DEFAULT 0,
    value                DATE         NOT NULL,
    CONSTRAINT fk_version_prospectus_updates_effective_date_prospectus_updates FOREIGN KEY (prospectus_update_id)
        REFERENCES prospectus_updates (id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8;

INSERT INTO schema_log (schema_version, date) VALUES (287, now());