create table outgoing_eds_client_detail_records (
  id bigint not null auto_increment,
  client_number varchar(255) not null,
  account_number varchar(255) not null,
  tax_id varchar(255),
  tax_type varchar(255) not null,
  email_address varchar(255) not null,
  salutation varchar(255),
  first_name varchar(255) not null,
  last_name varchar(255) not null,
  user_def_1 varchar(255),
  user_def_2 varchar(255),
  enrollment_number varchar(255) not null,
  pin_verify tinyint(1) not null,
  language varchar(255) not null,
  format varchar(255) not null,
  pin int,
  proxy_enrollment varchar(255) not null,
  interim_enrollment varchar(255) not null,
  confirm_enrollment varchar(255) not null,
  statements_enrollment varchar(255) not null,
  reorg_enrollment varchar(255) not null,
  margin_enrollment varchar(255) not null,
  prospectus_enrollment varchar(255) not null,
  tax_enrollment varchar(255) not null,
  newsletter_enrollment varchar(255) not null,
  record_creation_time datetime not null,
  file_creation_time datetime,
  client_correspondent varchar(255),
  process_type varchar(255),
  broker_maintain_flag varchar(255),
  primary key (id)
) ENGINE=InnoDB;

create table incoming_eds_client_detail_records (
  id bigint not null auto_increment,
  client_number varchar(255) not null,
  account_number varchar(255) not null,
  tax_id varchar(255),
  tax_type varchar(255) not null,
  email_address varchar(255) not null,
  salutation varchar(255),
  first_name varchar(255) not null,
  last_name varchar(255) not null,
  user_def_1 varchar(255),
  user_def_2 varchar(255),
  enrollment_number varchar(255) not null,
  pin_verify tinyint(1) not null,
  language varchar(255) not null,
  format varchar(255) not null,
  pin int,
  proxy_efail varchar(255) not null,
  interim_efail varchar(255) not null,
  confirm_efail varchar(255) not null,
  statements_efail varchar(255) not null,
  reorg_efail varchar(255) not null,
  margin_efail varchar(255) not null,
  prospectus_efail varchar(255) not null,
  tax_efail varchar(255) not null,
  newsletter_efail varchar(255) not null,
  record_creation_time datetime not null,
  file_creation_time datetime,
  client_correspondent varchar(255),
  process_type varchar(255),
  broker_maintain_flag varchar(255),
  primary key (id)
) ENGINE=InnoDB;

insert into schema_log (schema_version, date) values(78, now());