ALTER TABLE derived_cash_balances
  ADD COLUMN correspondent <PERSON><PERSON><PERSON><PERSON>(255) DEFAULT NULL AFTER id,
  ADD COLUMN office VARCHAR(255) DEFAULT NULL AFTER correspondent,
  ADD COLUMN account_number VARCHAR(255) DEFAULT NULL AFTER office,
  ADD COLUMN account_type VARCHAR(255) DEFAULT NULL AFTER account_number,
  ADD COLUMN sub_account_number VARCHAR(255) DEFAULT NULL AFTER account_type,
  ADD KEY key_account_locations_on_date (effective_date, correspondent, office, account_type, sub_account_number),
  ADD KEY key_accounts_on_date (effective_date, account_number),
  ADD KEY key_sub_account_references_on_date (effective_date, sub_account_reference),
  ADD KEY key_account (account_number);
  
ALTER TABLE derived_position_balances
  ADD COLUMN correspondent VARCHAR(255) DEFAULT NULL AFTER id,
  ADD COLUMN office VARCHAR(255) DEFAULT NULL AFTER correspondent,
  ADD COLUMN account_number VARCHAR(255) DEFAULT NULL AFTER office,
  ADD COLUMN account_type VARCHAR(255) DEFAULT NULL AFTER account_number,
  ADD COLUMN sub_account_number VARCHAR(255) DEFAULT NULL AFTER account_type,
  ADD KEY key_account_locations_on_date (effective_date, correspondent, office, account_type, sub_account_number),
  ADD KEY key_accounts_on_date (effective_date, account_number),
  ADD KEY key_sub_account_references_on_date (effective_date, sub_account_reference),
  ADD KEY key_account (account_number);
  
INSERT INTO schema_log (schema_version, date) VALUES (76, NOW());