CREATE TABLE wsc_transactions_record_references (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  transaction_number VARCHAR(255) NOT NULL,
  cancel_ind TINYINT(1) NOT NULL,
  PRIMARY KEY(id),
  UNIQUE KEY(transaction_number)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

ALTER TABLE wsc_transactions_records
  ADD COLUMN reference_id BIGINT(20) DEFAULT NULL AFTER ic_system_date;

ALTER TABLE wsc_transactions_records
  ADD CONSTRAINT fkey_wsc_transactions_record_reference FOREIGN KEY (reference_id) REFERENCES wsc_transactions_record_references(id);

INSERT INTO schema_log (schema_version, date) VALUES (163, now());
