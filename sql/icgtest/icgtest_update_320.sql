ALTER TABLE forge_ira_account_transfers ADD ic_transaction_number VARCHAR(255);
ALTER TABLE forge_ira_asset_transfers ADD source_transaction_number VARCHAR(255);
ALTER TABLE forge_ira_asset_transfers ADD target_transaction_number VARCHAR(255);

ALTER TABLE forge_ira_account_transfers ADD INDEX forge_ira_account_transfers_ic_transaction_number (ic_transaction_number);
ALTER TABLE forge_ira_asset_transfers ADD INDEX forge_ira_asset_transfers_source_transaction_number (source_transaction_number);
ALTER TABLE forge_ira_asset_transfers ADD INDEX forge_ira_asset_transfers_target_transaction_number (target_transaction_number);

INSERT INTO schema_log (schema_version, date) VALUES (320, now());

COMMIT;