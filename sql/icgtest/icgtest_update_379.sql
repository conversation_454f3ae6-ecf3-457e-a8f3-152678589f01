CREATE TABLE sharegain_collateral_report_records (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  sharegain_file_id BIGINT(20) NOT NULL,
  effective_date DATE NOT NULL,
  bi_account_id_or_firm_account VARCHAR(255) NOT NULL,
  borrower_code VARCHAR(255) NOT NULL,
  collateral_value decimal(14,2) NOT NULL,
  PRIMARY KEY (id),
  CONSTRAINT fkey_sg_collateral_report_records_sharegain_file_id FOREIGN KEY (sharegain_file_id) REFERENCES sharegain_files (id),
  UNIQUE KEY idx_sg_collateral_report_records_acc_id_date_borrower (bi_account_id_or_firm_account,effective_date,borrower_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

INSERT INTO schema_log (schema_version, date) VALUES(379, now());