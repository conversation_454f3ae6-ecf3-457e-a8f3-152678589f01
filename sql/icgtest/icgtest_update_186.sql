CREATE TABLE version_wsc_file_state (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  wsc_file_id BIGINT(20) NOT NULL,
  version BIGINT(11) NOT NULL,
  created_at DATETIME NOT NULL,
  deleted TINYINT(1) NOT NULL DEFAULT 0,
  value VARCHAR(255) NOT NULL,
  PRIMARY KEY (id),
  KEY version_wsc_file_state_id (wsc_file_id),
  CONSTRAINT version_wsc_file_state_id FOREIGN KEY (wsc_file_id) REFERENCES wsc_files (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

ALTER TABLE wsc_files
  ADD COLUMN state VARCHAR(255) DEFAULT NULL AFTER created_at;

INSERT INTO schema_log (schema_version, date) values (186, now());
