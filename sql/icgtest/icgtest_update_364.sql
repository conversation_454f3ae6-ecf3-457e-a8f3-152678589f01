DROP TABLE fpsl_eligible_positions;

CREATE TABLE fpsl_eligible_positions (
    id BIGINT(20) NOT NULL AUTO_INCREMENT PRIMARY KEY,
    created_at datetime NOT NULL,
    accountid bigint(20) NOT NULL,
    instrumentid bigint(20) NOT NULL,
    current_eligible_quantity decimal(20,5) NOT NULL,
    proposed_eligible_quantity decimal(20,5) NOT NULL,
    cycle int NOT NULL,
    state varchar(255) NOT NULL,
    sharegain_file_id BIGINT(20) DEFAULT NULL,
    eligibility_json mediumtext NOT NULL,
    UNIQUE KEY fpsl_eligible_positions_unique(accountid, instrumentid, cycle),
    CONSTRAINT fkey_fpsl_eligible_positions_sharegain_file_id FOREIGN KEY (sharegain_file_id) REFERENCES sharegain_files (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE version_fpsl_eligible_positions_state (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    fpsl_eligible_position_id BIGINT NOT NULL,
    version INT(11) NOT NULL,
    created_at DATETIME NOT NULL,
    deleted TINYINT(1) NOT NULL,
    value VARCHAR(255) NOT NULL,
    PRIMARY KEY (id),
    INDEX version_fpsl_eligible_positions_state_created_at (created_at),
    CONSTRAINT fkey_fpsl_eligible_positions_state FOREIGN KEY (fpsl_eligible_position_id) REFERENCES fpsl_eligible_positions (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

INSERT INTO schema_log (schema_version, date) VALUES (364, NOW());