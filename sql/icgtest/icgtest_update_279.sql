CREATE TABLE margin_account_transfer_consolidation_requests (
  id                             BIGINT(20)      NOT NULL AUTO_INCREMENT,
  accountid                      BIGINT(20)      NOT NULL,
  request_date                   DATE            NOT NULL,
  created_at                     DATETIME        NOT NULL,
  full_transfer                  TINYINT(1)      NOT NULL,
  polled_time                    DATETIME        DEFAULT NULL,
  sent_time                      DATETIME        DEFAULT NULL,
  ignored_at                     DATETIME        DEFAULT NULL,
  error_flag                     TINYINT(1)      NOT NULL DEFAULT 0,
  
  PRIMARY KEY (id),
  INDEX margin_consolidation_requests_polled_ignored_error_flag (polled_time, ignored_at, error_flag),
  INDEX margin_consolidation_requests_sent_polled_ignored_error_flag (sent_time, polled_time, ignored_at, error_flag),
  INDEX margin_consolidation_requests_accountid (accountid)
)
  ENGINE=InnoDB
  DEFAULT CHARSET=utf8;

INSERT INTO schema_log (schema_version, date) VALUES (279, now());
