CREATE TABLE wsc_taxpayer_transaction_records (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  wsc_file_id BIGINT(20) NOT NULL,
  account_no VARCHAR(255) DEFAULT NULL,
  tin VARCHAR(255) DEFAULT NULL,
  tin_type VARCHAR(255) DEFAULT NULL,
  acquisition_code VARCHAR(255) DEFAULT NULL,
  purchase_trade_date DATE DEFAULT NULL,
  purchase_settlement_date DATE DEFAULT NULL,
  purchase_number_of_units DECIMAL(15, 5) DEFAULT NULL,
  purchase_price_per_unit DECIMAL(11, 5) DEFAULT NULL,
  purchase_cost DECIMAL(14, 4) DEFAULT NULL,
  original_purchase_settlement_date DATE DEFAULT NULL,
  purchase_statement_trailer VARCHAR(255) DEFAULT NULL,
  disposition_code VARCHAR(255) DEFAULT NULL,
  sale_trade_date DATE DEFAULT NULL,
  sale_settlement_date DATE DEFAULT NULL,
  sale_number_of_units DECIMAL(15, 5) DEFAULT NULL,
  sale_price_per_unit DECIMAL(11, 5) DEFAULT NULL,
  sale_cost DECIMAL(14, 4) DEFAULT NULL,
  sale_statement_trailer VARCHAR(255) DEFAULT NULL,
  counterparty_firm_id VARCHAR(255) DEFAULT NULL,
  counterparty_account_no VARCHAR(255) DEFAULT NULL,
  transfer_type VARCHAR(255) DEFAULT NULL,
  reference_id VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY(id),
  CONSTRAINT `wsc_taxpayer_transaction_records_wsc_file_id`
    FOREIGN KEY (wsc_file_id) REFERENCES wsc_files (id)
) ENGINE=InnoDB;

CREATE TABLE wsc_taxpayer_distribution_records (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  wsc_file_id BIGINT(20) NOT NULL,
  account_no VARCHAR(255) DEFAULT NULL,
  tin VARCHAR(255) DEFAULT NULL,
  tin_type VARCHAR(255) DEFAULT NULL,
  payment_code VARCHAR(255) DEFAULT NULL,
  record_date DATE DEFAULT NULL,
  payment_date DATE DEFAULT NULL,
  number_of_units DECIMAL(15, 5) DEFAULT NULL,
  distribution_per_unit DECIMAL(11, 5) DEFAULT NULL,
  amount DECIMAL(14, 4) DEFAULT NULL,
  ubti_tax_payment_year VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY(id),
  CONSTRAINT `wsc_taxpayer_distribution_records_wsc_file_id`
    FOREIGN KEY (wsc_file_id) REFERENCES wsc_files (id)
) ENGINE=InnoDB;

INSERT INTO schema_log (schema_version, date) VALUES (271, now());