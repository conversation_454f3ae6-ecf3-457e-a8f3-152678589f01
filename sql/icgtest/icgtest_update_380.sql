CREATE TABLE wilmington_files (
  id BIGINT NOT NULL AUTO_INCREMENT,
  type VA<PERSON>HAR(30) NOT NULL,
  effective_date DATE NOT NULL,
  file_name VARCHAR(255) NOT NULL,
  state VARCHAR(30) NOT NULL,
  PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE version_wilmington_files_state (
  id BIGINT NOT NULL AUTO_INCREMENT,
  wilmington_file_id BIGINT NOT NULL,
  version INT NOT NULL,
  created_at DATETIME NOT NULL,
  deleted BOOLEAN NOT NULL,
  value VARCHAR(30) NOT NULL,
  PRIMARY KEY (id),
  CONSTRAINT wilmington_files_state FOREIGN KEY (wilmington_file_id) REFERENCES wilmington_files (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE daily_client_collateral_records (
  id BIGINT NOT NULL AUTO_INCREMENT,
  version BIGINT NOT NULL DEFAULT 0,
  wilmington_file_id BIGINT NULL,
  accountid BIGINT NOT NULL,
  effective_date DATE NOT NULL,
  collateral_amount DECIMAL(20,2) NOT NULL,
  state VARCHAR(30) NOT NULL,
  PRIMARY KEY (id),
  UNIQUE KEY (accountid, effective_date),
  CONSTRAINT daily_client_collateral_records_wilmington_file_id FOREIGN KEY (wilmington_file_id)
    REFERENCES wilmington_files (id),
  CONSTRAINT daily_client_collateral_records_accountid FOREIGN KEY (accountid) REFERENCES brokerage_accounts (accountid)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE version_daily_client_collateral_records_state (
  id BIGINT NOT NULL AUTO_INCREMENT,
  record_id BIGINT NOT NULL,
  version INT NOT NULL,
  created_at DATETIME NOT NULL,
  deleted BOOLEAN NOT NULL,
  value VARCHAR(30) NOT NULL,
  PRIMARY KEY (id),
  CONSTRAINT daily_client_collateral_records_state FOREIGN KEY (record_id)
    REFERENCES daily_client_collateral_records (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE daily_client_collateral_movements (
  id BIGINT NOT NULL AUTO_INCREMENT,
  accountid BIGINT NOT NULL,
  effective_date DATE NOT NULL,
  current_date_collateral_record_id BIGINT NULL,
  previous_date_collateral_record_id BIGINT NULL,
  net_movement DECIMAL(20,2) NOT NULL,
  UNIQUE KEY (accountid, effective_date),
  PRIMARY KEY (id),
  CONSTRAINT daily_client_collateral_movements_current_date_record_id FOREIGN KEY (current_date_collateral_record_id)
    REFERENCES daily_client_collateral_records (id),
  CONSTRAINT daily_client_collateral_movements_previous_date_record_id FOREIGN KEY (previous_date_collateral_record_id)
    REFERENCES daily_client_collateral_records (id),
  CONSTRAINT daily_client_collateral_movements_accountid FOREIGN KEY (accountid) REFERENCES brokerage_accounts (accountid)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

INSERT INTO schema_log (schema_version, date) VALUES(380, now());
