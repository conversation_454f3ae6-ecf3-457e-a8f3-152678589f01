ALTER TABLE fpsl_custodian_account_loans
DROP FOREIGN KEY fpsl_custodian_account_loans_fpsl_loan_instruction_id;

DROP TABLE fpsl_loan_instructions;

CREATE TABLE fpsl_loan_instructions (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    fpsl_loan_instruction_file_record_id BIGINT(20) NOT NULL,
    direction VARCHAR(255) NOT NULL,
    created_at DATETIME NOT NULL,
    settlement_date DATE NOT NULL,
    state VARCHAR(255) NOT NULL,
    end_date DATE DEFAULT NULL,
    custodian_account_number VARCHAR(255) NOT NULL,
    isin VARCHAR(255) NOT NULL,
    instrument_id BIGINT(20) NOT NULL,
    quantity DECIMAL(20,8) NOT NULL,
    price DECIMAL(14,2) NOT NULL,
    cash_value DECIMAL(14,2) NOT NULL,
    lending_rate DECIMAL(20,8) NOT NULL,
    trade_id VARCHAR(255) NOT NULL,
    link_trade_id VARCHAR(255) DEFAULT NULL,
    borrower_code VARCHAR(255) NOT NULL,
    journaled BIGINT(1) DEFAULT FALSE,
    PRIMARY KEY (id),
    INDEX fpsl_loan_instructions_trade_id (trade_id),
    CONSTRAINT fkey_fpsl_loan_instruction_file_record_id
        FOREIGN KEY (fpsl_loan_instruction_file_record_id) REFERENCES fpsl_loan_instruction_file_records (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

ALTER TABLE fpsl_custodian_account_loans
ADD CONSTRAINT fpsl_custodian_account_loans_fpsl_loan_instruction_id FOREIGN KEY (fpsl_loan_instruction_id) REFERENCES fpsl_loan_instructions (id);

INSERT INTO schema_log (schema_version, date) VALUES (374, now());