CREATE TABLE capital_reserve_balances (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  created_at DATETIME NOT NULL,
  capital_reserve_calculation_id BIGINT(20) NOT NULL,
  amount DECIMAL(20,2) NOT NULL,
  PRIMARY KEY (id),
  CONSTRAINT fkey_capital_reserve_balances_calculation_id FOREIGN KEY (capital_reserve_calculation_id) REFERENCES capital_reserve_calculations (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO schema_log (schema_version, date) VALUES (231, now());