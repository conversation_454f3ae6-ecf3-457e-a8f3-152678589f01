CREATE TABLE `position_entries` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `downloaded_at` datetime NOT NULL,
  `effective_date` date NOT NULL,
  `entry` mediumtext NOT NULL,
  `customer_reference` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY position_entries_effective_date (effective_date),
  KEY position_entries_effective_date_customer_reference (effective_date, customer_reference)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO schema_log (schema_version, date) VALUES (262, now());