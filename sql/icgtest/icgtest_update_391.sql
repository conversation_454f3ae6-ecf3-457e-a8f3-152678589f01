CREATE TABLE fpsl_monthly_custodian_interest_revenues (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    sharegain_file_id BIGINT(20) NOT NULL,
    effective_date DATE NOT NULL,
    borrower_code VARCHAR(255) NOT NULL,
    account_id_or_firm_account VARCHAR(255) NOT NULL,
    fpsl_monthly_custodian_interest_revenue_details MEDIUMTEXT NOT NULL,
    state VARCHAR(255) NOT NULL,
    PRIMARY KEY (id),
    UNIQUE INDEX fpsl_monthly_custodian_interest_revenues_eff_date_borrower_code
        (effective_date, borrower_code),
    CONSTRAINT fk_fpsl_monthly_custodian_interest_revenues_sharegain_file_id
        FOREIGN KEY (sharegain_file_id) REFERENCES sharegain_files (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE version_fpsl_monthly_custodian_interest_revenue_state (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    fpsl_monthly_custodian_interest_revenue_id BIGINT(20) NOT NULL,
    version INT(11) NOT NULL,
    created_at DATETIME NOT NULL,
    deleted TINYINT(1) NOT NULL DEFAULT 0,
    value VARCHAR(255) NOT NULL,
    PRIMARY KEY (id),
    CONSTRAINT fk_version_fpsl_monthly_custodian_interest_revenue_id
        FOREIGN KEY (fpsl_monthly_custodian_interest_revenue_id) REFERENCES fpsl_monthly_custodian_interest_revenues (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

INSERT INTO schema_log(schema_version, date) VALUES (391, now());