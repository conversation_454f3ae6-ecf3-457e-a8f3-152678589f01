CREATE TABLE fpsl_custodian_account_loan_details (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    fpsl_custodian_account_loan_id BIGINT(20) NOT NULL,
    loan_id VARCHAR(255) NOT NULL,
    effective_date DATE NOT NULL,
    original_units DECIMAL(20,8) NOT NULL,
    settled_units DECIMAL(20,8) NOT NULL,
    price DECIMAL(14,2) NOT NULL,
    loan_value DECIMAL(14,2) NOT NULL,
    PRIMARY KEY (id),
    CONSTRAINT fkey_cust_account_loan_details_fpsl_cust_account_loan_id FOREIGN KEY (fpsl_custodian_account_loan_id) REFERENCES fpsl_custodian_account_loans (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

INSERT INTO schema_log (schema_version, date) VALUES (369, NOW());