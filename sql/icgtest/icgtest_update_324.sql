alter table forge_ira_account_transfers
    drop index internal_asset_transfer_id;
alter table forge_ira_account_transfers
    add index (internal_asset_transfer_id);

alter table forge_ira_account_transfers
    drop index cross_account_transfer_id;
alter table forge_ira_account_transfers
    add index (cross_account_transfer_id);

alter table forge_ira_account_transfers
    drop index acats_transfer_id;
alter table forge_ira_account_transfers
    add index (acats_transfer_id);

alter table forge_ira_account_transfers
    drop index dtc_transfer_id;
alter table forge_ira_account_transfers
    add index (dtc_transfer_id);

INSERT INTO schema_log (schema_version, date) VALUES (324, now());

COMMIT;