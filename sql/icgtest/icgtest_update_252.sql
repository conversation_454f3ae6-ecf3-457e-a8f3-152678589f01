CREATE TABLE ira_transaction_adjustments (
  id                    BIGINT(20)   NOT NULL AUTO_INCREMENT,
  type                  VA<PERSON><PERSON><PERSON>(255) NOT NULL,
  created_at            DATETIME     NOT NULL,
  transaction_number    VARCHAR(255) NOT NULL,
  notes                 VARCHAR(255) NOT NULL,
  effective_date        DATE,
  PRIMARY KEY (id),
  KEY ira_transaction_adjustments_transaction_number (transaction_number)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO schema_log (schema_version, date) values (252, now());
