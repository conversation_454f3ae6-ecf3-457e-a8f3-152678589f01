CREATE TABLE ira_excess_contribution_removals (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  ira_account_id BIGINT(20) NOT NULL,
  ira_investment_sale_id BIGINT(20) NOT NULL,
  bank_withdrawalid BIGINT(20) NOT NULL,
  transaction_number VARCHAR(255) DEFAULT NULL,
  ira_services_excess_contribution_removalid VARCHAR(255) DEFAULT NULL,
  target_environment VARCHAR(255) NOT NULL,
  created_at DATETIME NOT NULL,
  updated_at DATETIME NOT NULL,
  PRIMARY KEY (id),
  UNIQUE KEY ira_ecrs_ira_services_ecrid (ira_services_excess_contribution_removalid),
  KEY ira_ecrs_ira_account_id (ira_account_id),
  CONSTRAINT fkey_ira_ecrs_ira_account_id FOREIGN KEY (ira_account_id) REFERENCES ira_accounts (id),
  CONSTRAINT fkey_ira_ecrs_ira_investment_sale_id FOREIGN KEY (ira_investment_sale_id) REFERENCES ira_investment_sales (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE version_ira_excess_contribution_removals_transaction_number (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  ira_excess_contribution_removal_id BIGINT(20) NOT NULL,
  version INT(11) NOT NULL,
  created_at DATETIME NOT NULL,
  deleted TINYINT(1) NOT NULL DEFAULT 0,
  value VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (id),
  CONSTRAINT fkey_version_ira_ecrs_txn_number_ira_ecr_id FOREIGN KEY (ira_excess_contribution_removal_id) REFERENCES ira_excess_contribution_removals (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO schema_log (schema_version, date) VALUES (193, now());