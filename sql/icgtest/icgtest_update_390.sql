DROP TABLE daily_client_collateral_movements;

DROP TABLE version_daily_client_collateral_records_state;

DROP TABLE daily_client_collateral_records;

CREATE TABLE daily_client_collateral_records
(
    id                            BIGINT         NOT NULL AUTO_INCREMENT,
    version                       BIGINT         NOT NULL DEFAULT 0,
    wilmington_file_id            BIGINT NULL,
    bi_account_id_or_firm_account VARCHAR(255)   NOT NULL,
    effective_date                DATE           NOT NULL,
    collateral_amount             DECIMAL(20, 2) NOT NULL,
    state                         VARCHAR(30)    NOT NULL,
    PRIMARY KEY (id),
    UNIQUE KEY (bi_account_id_or_firm_account, effective_date),
    INDEX idx_daily_client_collateral_records_effective_date (effective_date),
    CONSTRAINT daily_client_collateral_records_wilmington_file_id FOREIGN KEY (wilmington_file_id)
        REFERENCES wilmington_files (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE version_daily_client_collateral_records_state
(
    id         BIGINT      NOT NULL AUTO_INCREMENT,
    record_id  BIGINT      NOT NULL,
    version    INT         NOT NULL,
    created_at DATETIME    NOT NULL,
    deleted    BOOLEAN     NOT NULL,
    value      VARCHAR(30) NOT NULL,
    PRIMARY KEY (id),
    CONSTRAINT daily_client_collateral_records_state FOREIGN KEY (record_id)
        REFERENCES daily_client_collateral_records (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE daily_client_collateral_movements
(
    id                                 BIGINT         NOT NULL AUTO_INCREMENT,
    bi_account_id_or_firm_account      VARCHAR(255)   NOT NULL,
    effective_date                     DATE           NOT NULL,
    current_date_collateral_record_id  BIGINT NULL,
    previous_date_collateral_record_id BIGINT NULL,
    net_movement                       DECIMAL(20, 2) NOT NULL,
    PRIMARY KEY (id),
    UNIQUE KEY (bi_account_id_or_firm_account, effective_date),
    CONSTRAINT daily_client_collateral_movements_current_date_record_id FOREIGN KEY (current_date_collateral_record_id)
        REFERENCES daily_client_collateral_records (id),
    CONSTRAINT daily_client_collateral_movements_previous_date_record_id FOREIGN KEY (previous_date_collateral_record_id)
        REFERENCES daily_client_collateral_records (id),
    INDEX idx_daily_client_collateral_movements_effective_date (effective_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

INSERT INTO schema_log (schema_version, date) VALUES (390, NOW());
