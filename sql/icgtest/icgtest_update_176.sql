CREATE TABLE ira_investment_sales (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  created_at DATETIME NOT NULL,
  updated_at DATETIME NOT NULL,
  target_environment VARCHAR(255) NOT NULL,
  ira_account_id BIGINT(20) NOT NULL,
  ira_investment_id BIGINT(20) NOT NULL,
  ira_services_investment_saleid VARCHAR(255),
  PRIMARY KEY (id),
  CONSTRAINT fkey_ira_investment_sales_ira_account_id FOREIGN KEY (ira_account_id) REFERENCES ira_accounts (id),
  CONSTRAINT fkey_ira_investment_sales_ira_inv_id FOREIGN KEY (ira_investment_id) REFERENCES ira_investments (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO schema_log (schema_version, date) VALUES (176, now());