CREATE TABLE forge_ira_withdrawal_withholdings (
    id BIGINT(20) NOT NULL AUTO_INCREMENT PRIMARY KEY,
    forge_ira_withdrawal_id BIGINT(20) NOT NULL,
    ira_account_id BIGINT(20) NOT NULL,
    ic_transaction_number VARCHAR(255) NOT NULL,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    amount DECIMAL(20,2) NOT NULL,
    withdrawal_date DATE NOT NULL,
    withholdings TEXT NOT NULL,
    forge_investment_sale_id BIGINT(20) NOT NULL,
    forge_withdrawal_id VARCHAR(255) DEFAULT NULL,
    reversed_at datetime DEFAULT NULL,
    reversal_reason VARCHAR(255) DEFAULT NULL,
    reversal_description VARCHAR(255) DEFAULT NULL,
    forge_withdrawal_reversal_id VARCHAR(255) DEFAULT NULL,
    CONSTRAINT fkey_forge_ira_withdrawal_withholdings_parent_id FOREIGN KEY (forge_ira_withdrawal_id) REFERENCES forge_ira_withdrawals (id),
    CONSTRAINT fkey_forge_ira_withdrawal_withholdings_ira_account_id FOREIGN KEY (ira_account_id) REFERENCES ira_accounts (id)

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE forge_ira_excess_contribution_removal_withholdings (
    id BIGINT(20) NOT NULL AUTO_INCREMENT PRIMARY KEY,
    forge_ira_excess_contribution_removal_id BIGINT(20) NOT NULL,
    ira_account_id BIGINT(20) NOT NULL,
    ic_transaction_number VARCHAR(255) NOT NULL,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    amount DECIMAL(20,2) NOT NULL,
    effective_date DATE NOT NULL,
    withholdings TEXT NOT NULL,
    forge_ira_investment_sale_id BIGINT(20) NOT NULL,
    forge_excess_contribution_removal_id VARCHAR(255) DEFAULT NULL,
    is_deleted TINYINT(1) NOT NULL,
    CONSTRAINT fkey_forge_ira_ecr_whgs_parent_id FOREIGN KEY (forge_ira_excess_contribution_removal_id) REFERENCES forge_ira_excess_contribution_removals (id),
    CONSTRAINT fkey_forge_ira_ecr_whgs_ira_account_id FOREIGN KEY (ira_account_id) REFERENCES ira_accounts (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE forge_ira_asset_transfer_withholdings (
    id BIGINT(20) NOT NULL AUTO_INCREMENT PRIMARY KEY,
    forge_ira_asset_transfer_id BIGINT(20) NOT NULL,
    ira_account_id BIGINT(20) NOT NULL,
    ic_transaction_number VARCHAR(255) NOT NULL,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    completion_date DATE NOT NULL,
    withholdings TEXT NOT NULL,
    forge_roth_conversion_id VARCHAR(255) DEFAULT NULL,
    is_deleted TINYINT(1) NOT NULL,
    CONSTRAINT fkey_forge_ira_ass_tran_whgs_withholdings_parent_id FOREIGN KEY (forge_ira_asset_transfer_id) REFERENCES forge_ira_asset_transfers (id),
    CONSTRAINT fkey_forge_ira_ass_tran_whgs_ira_account_id FOREIGN KEY (ira_account_id) REFERENCES ira_accounts (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

INSERT INTO schema_log (schema_version, date) VALUES (343, now());
