CREATE TABLE `transaction_specified_lot_instructions` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `lot_quantity` DECIMAL(16,6) NOT NULL,
  `open_date` DATETIME NOT NULL,
  `lot_cost` DECIMAL(20,8) not null,
  `transaction_id` BIGINT(20) not null,
  CONSTRAINT `fkey_transaction_specified_lot_instructions_transaction_id`
  FOREIGN KEY (transaction_id) REFERENCES transactions (id),
  PRIMARY KEY (id)
) ENGINE = InnoDB DEFAULT CHARSET = utf8;

INSERT INTO schema_log (schema_version, DATE) VALUES (265, NOW());