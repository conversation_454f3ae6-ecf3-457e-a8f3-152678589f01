CREATE TABLE divroc_record_overrides (
  id BIGINT(20) NOT NULL AUTO_INCREMENT PRIMARY KEY,
  divroc_record_id BIGINT(20) NOT NULL,
  divroc_percent DECIMAL(9, 6) DEFAULT NULL,
  settle_date DATE DEFAULT NULL,
  state VARCHAR(255) DEFAULT NULL,
  reason VARCHAR(255) NOT NULL,
  CONSTRAINT fkey_divroc_record_overrides_divroc_record_id FOREIGN KEY (divroc_record_id) REFERENCES divroc_records (id),
  unique (divroc_record_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

INSERT INTO schema_log (schema_version, date) VALUES (340, now());