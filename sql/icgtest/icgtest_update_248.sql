CREATE TABLE ira_irs_forms_data (
  id             BIGINT(20)   NOT NULL AUTO_INCREMENT,
  ira_account_id BIGINT(20)   NOT NULL,
  form_type      VARCHAR(255) NOT NULL,
  tax_year       SMALLINT     NOT NULL,
  forms          <PERSON>ON<PERSON>TEXT     NOT NULL,
  state          VARCHAR(255) NOT NULL,
  created_at     DATETIME     NOT NULL,
  PRIMARY KEY (id),
  UNIQUE KEY ira_irs_forms_data_ira_account_id_form_type_year (ira_account_id, form_type, tax_year),
  CONSTRAINT ira_irs_forms_data_ira_account_id FOREIGN KEY (ira_account_id) REFERENCES ira_accounts (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE version_ira_irs_forms_data_form_data_info(
  id                    BIGINT(20)   NOT NULL AUTO_INCREMENT,
  ira_irs_forms_data_id BIGINT(20)   NOT NULL,
  version               INT(11)      NOT NULL,
  created_at            DATETIME     NOT NULL,
  deleted               TINYINT(1)   NOT NULL,
  forms                 LONGTEXT     NOT NULL,
  state                 VARCHAR(255) NOT NULL,
  PRIMARY KEY (id),
  KEY version_ira_irs_forms_data_ira_irs_forms_data_id (ira_irs_forms_data_id),
  CONSTRAINT version_ira_irs_forms_data_ira_irs_forms_data_id FOREIGN KEY (ira_irs_forms_data_id) REFERENCES
  ira_irs_forms_data (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO schema_log (schema_version, date) values (248, now());