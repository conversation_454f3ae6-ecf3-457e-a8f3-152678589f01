CREATE TABLE version_margin_interests_period_end_interest (
  id                              BIGINT(20)     NOT NULL AUTO_INCREMENT,
  margin_interest_id              BIGINT(20)     NOT NULL,
  version                         INT(11)        NOT NULL,
  created_at                      DATETIME       NOT NULL,
  deleted                         TINYINT(1)     NOT NULL,
  custodian_period_end_interest   DECIMAL(14, 2) DEFAULT NULL,
  wf_period_end_interest          DECIMAL(14, 2) DEFAULT NULL,
  PRIMARY KEY (id),
  CONSTRAINT fkey_version_period_end_interests_margin_interest_id FOREIGN KEY (margin_interest_id) REFERENCES margin_interests (id)
) engine=InnoDB DEFAULT charset=utf8;

INSERT INTO schema_log (schema_version, date) VALUES (93, now());