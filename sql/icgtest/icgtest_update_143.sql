CREATE TABLE transaction_cancellations (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  transaction_number varchar(255) NOT NULL,
  created_at datetime NOT NULL,
  polled_time datetime DEFAULT NULL,
  sent_time datetime DEFAULT NULL,
  error_flag tinyint(1) NOT NULL,
  PRIMARY KEY (id),
  KEY transaction_cancellations_polled_time_error_flag (polled_time,error_flag),
  KEY transaction_cancellations_sent_time_polled_time_error_flag (sent_time, polled_time, error_flag)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO schema_log (schema_version, DATE) VALUES (143, now());