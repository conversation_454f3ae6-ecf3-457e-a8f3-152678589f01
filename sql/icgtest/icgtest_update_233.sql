CREATE TABLE `queued_worm_uploads` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `type` varchar(255) NOT NULL,
  `version` bigint(20) NOT NULL DEFAULT '0',
  `worm_archiveid` char(36) NOT NULL,
  `created_at` datetime NOT NULL,
  `polled_time` datetime DEFAULT NULL,
  `sent_time` datetime DEFAULT NULL,
  `error_flag` tinyint(1) NOT NULL DEFAULT '0',
  `metadata` mediumtext,
  `file_path` varchar(255) DEFAULT NULL,
  `json_data` mediumtext,
  PRIMARY KEY (`id`),
  KEY `queued_worm_uploads_worm_archiveid` (`worm_archiveid`),
  KEY `queued_worm_uploads_sent_time_polled_time_error_flag` (`sent_time`,`polled_time`,`error_flag`),
  KEY `queued_worm_uploads_polled_time_error_flag` (`polled_time`,`error_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO schema_log (schema_version, date) VALUES (233, NOW());
