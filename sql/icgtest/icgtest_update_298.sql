CREATE TABLE forge_ira_investments (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    ira_account_id BIGINT(20) NOT NULL,
    created_at DATETIME NOT NULL,
    forge_investment_id VARCHAR(255) NOT NULL,
    PRIMARY KEY (id),
    UNIQUE KEY fii_ira_account_id_forge_investment_id (ira_account_id, forge_investment_id),
    CONSTRAINT fkey_forge_ira_investments_ira_account_id FOREIGN KEY (ira_account_id) REFERENCES ira_accounts (id)
) ENGINE=InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE forge_ira_investment_valuations (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    ira_account_id BIGINT(20) NOT NULL,
    created_at DATETIME NOT NULL,
    update_at DATETIME NOT NULL,
    amount DECIMAL(20,2) NOT NULL,
    forge_ira_investment_id BIGINT(20) NOT NULL,
    effective_date DATE DEFAULT NULL,
    reference_type VARCHAR(255) DEFAULT NULL,
    reference BIGINT(20) DEFAULT NULL,
    forge_investment_valuation_id VARCHAR(255) DEFAULT NULL,
    PRIMARY KEY (id),
    UNIQUE KEY unique_fiiv_reference_reference_type (reference_type, reference),
    CONSTRAINT fkey_fiiv_ira_investment_id_ira_account_id FOREIGN KEY (ira_account_id) REFERENCES ira_accounts (id),
    CONSTRAINT fkey_fiiv_ira_investment_id FOREIGN KEY (forge_ira_investment_id) REFERENCES ira_investments (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE forge_ira_investment_sales (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    ira_account_id BIGINT(20) NOT NULL,
    created_at DATETIME NOT NULL,
    update_at DATETIME NOT NULL,
    amount DECIMAL(20,2) NOT NULL,
    forge_ira_investment_id BIGINT(20) NOT NULL,
    previous_forge_ira_valuation_id BIGINT(20) DEFAULT NULL,
    forge_investment_sales_id VARCHAR(255) DEFAULT NULL,
    PRIMARY KEY (id),
    CONSTRAINT fkey_fiis_ira_account_id FOREIGN KEY (ira_account_id) REFERENCES ira_accounts (id),
    CONSTRAINT fkey_fiis_forge_ira_inv_id FOREIGN KEY (forge_ira_investment_id) REFERENCES forge_ira_investments (id),
    CONSTRAINT fkey_fiis_forge_ira_prev_valuation_id FOREIGN KEY (previous_forge_ira_valuation_id) REFERENCES forge_ira_investment_valuations (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE forge_ira_investment_purchases (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    ira_account_id BIGINT(20) NOT NULL,
    created_at DATETIME NOT NULL,
    update_at DATETIME NOT NULL,
    amount DECIMAL(20,2) NOT NULL,
    deposit_date DATETIME NOT NULL,
    forge_ira_investment_id BIGINT(20) DEFAULT NULL,
    forge_investment_purchase_id VARCHAR(255) DEFAULT NULL,
    PRIMARY KEY (id),
    CONSTRAINT fkey_fiip_ira_account_id FOREIGN KEY (ira_account_id) REFERENCES ira_accounts (id),
    CONSTRAINT fkey_fiip_forge_ira_inv_id FOREIGN KEY (forge_ira_investment_id) REFERENCES forge_ira_investments (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO schema_log (schema_version, date) VALUES (298, now());

COMMIT;