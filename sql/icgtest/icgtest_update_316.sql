ALTER TABLE forge_ira_investments ADD CONSTRAINT UNIQUE (forge_investment_id);
ALTER TABLE forge_ira_investment_sales ADD CONSTRAINT UNIQUE (forge_investment_sales_id);
ALTER TABLE forge_ira_investment_valuations ADD CONSTRAINT UNIQUE (forge_investment_valuation_id);
ALTER TABLE forge_ira_rollovers ADD CONSTRAINT UNIQUE (forge_rollover_id);
ALTER TABLE forge_ira_account_transfers ADD CONSTRAINT UNIQUE (forge_account_transfer_id);
ALTER TABLE forge_ira_asset_transfers ADD CONSTRAINT UNIQUE (forge_recharacterization_id);
ALTER TABLE forge_ira_asset_transfers ADD CONSTRAINT UNIQUE (forge_roth_conversion_id);

INSERT INTO schema_log (schema_version, date) VALUES (316, now());

COMMIT;