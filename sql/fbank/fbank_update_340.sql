ALTER TABLE unexpected_bai2_transactions
    DROP CONSTRAINT `__unexpected_bai2_transactions_wells_bank_account_id`,
    DROP CONSTRAINT `__unexpected_bai2_transactions_wells_file_id`,
    DROP INDEX unexpected_bai2_transactions_wells_bank_account_id,
    DROP INDEX unexpected_bai2_transactions_wells_file_id,
    DROP COLUMN wells_file_id,
    DROP COLUMN wells_bank_account_id;

ALTER TABLE daily_rtp_withdrawal_amounts
    DROP CONSTRAINT `__daily_rtp_withdrawal_amounts_ibfk_1`,
    DROP INDEX wells_bank_account_id,
    DROP COLUMN wells_bank_account_id;

ALTER TABLE wells_account_transfer_limits
    DROP CONSTRAINT `__fkey_wells_bank_account_transfer_limit`,
    DROP INDEX unique_limit_type_wells_bank_account,
    DROP COLUMN wells_bank_account_id;

ALTER TABLE real_time_wells_bank_account_balances
    DROP CONSTRAINT `__rtwbab_wells_bank_account_id`,
    DROP INDEX wells_bank_account_id,
    DROP COLUMN wells_bank_account_id;

INSERT INTO schema_log (schema_version, date) VALUES (340, now());