CREATE TABLE umb_transaction_evaluations (
  id                                BIGINT(20)   NOT NULL AUTO_INCREMENT,
  type                              <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
  trace                             VARCHAR(255) NOT NULL,
  evaluated_at                      DATETIME     NOT NULL,
  umb_account_number_information_id BIGINT(20)   NULL,
  details                           TEXT         NOT NULL,
  PRIMARY KEY (id),
  INDEX umb_transaction_evaluations_trace (trace),
  CONSTRAINT umb_transaction_evaluations_umb_account_number_information_id
    FOREIGN KEY (umb_account_number_information_id)
    REFERENCES umb_account_number_information (id)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 AUTO_INCREMENT = ***********;

INSERT INTO schema_log (schema_version, date) VALUES (348, now());
