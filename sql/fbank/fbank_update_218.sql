CREATE TABLE version_acats_transfers_risk_metric_requestid
(
    id                BIGINT(20) NOT NULL AUTO_INCREMENT,
    acats_transfer_id BIGINT(20) NOT NULL,
    version           INT(11)    NOT NULL,
    created_at        DATETIME   NOT NULL,
    deleted           TINYINT(1) NOT NULL DEFAULT 0,
    value             BIGINT(20)          DEFAULT NULL,
    PRIMARY KEY (id),
    CONSTRAINT version_acats_transfers_risk_metric_requestid_acats_transfer_id
        FOREIGN KEY (acats_transfer_id)
            REFERENCES acats_transfers (id)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 AUTO_INCREMENT = 10000000000;

INSERT INTO schema_log (schema_version, date) VALUES (218, NOW());