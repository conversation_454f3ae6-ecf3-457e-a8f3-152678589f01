CREATE TABLE umb_account_number_information
(
    id                        BIGINT(20)   NOT NULL AUTO_INCREMENT,
    umb_account_number        VARCHAR(255) NOT NULL,
    account_id                BIGINT(20)   NOT NULL,
    is_locked_for_withdrawals BOOLEAN      NOT NULL,
    is_locked_for_deposits    BOOLEAN      NOT NULL,
    status                    VARCHAR(255) NOT NULL,
    CONSTRAINT umb_account_number_information_umb_account_number UNIQUE (umb_account_number),
    PRIMARY KEY (id)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 AUTO_INCREMENT = ***********;

INSERT INTO schema_log (schema_version, DATE) VALUES (331, now());