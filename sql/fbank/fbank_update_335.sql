CREATE TABLE umb_wire_direct_deposits (
  id                    BIGINT(20)      NOT NULL AUTO_INCREMENT,
  bank_transfer_id      BIGINT(20)      NOT NULL,
  created_at            DATETIME        NOT NULL,
  amount                DECIMAL(14,2)   NOT NULL,
  source_account        VARCHAR(255)    NOT NULL,
  target_account        VARCHAR(255)    NOT NULL,
  effective_date        DATE            NOT NULL,
  wire_reference_number VARCHAR(255)    NOT NULL,
  PRIMARY KEY (id),
  CONSTRAINT umb_direct_deposit_bank_transfer
  FOREIGN KEY (bank_transfer_id) REFERENCES bank_transfers (id)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 AUTO_INCREMENT = ***********;

INSERT INTO schema_log(schema_version, date) VALUES (335, NOW());