ALTER TABLE real_time_wells_bank_account_balances
    ADD UNIQUE INDEX account_name (account_name);

ALTER TABLE wells_account_transfer_limits
    ADD UNIQUE account_name_transfer_limit_type (account_name, transfer_limit_type);

ALTER TABLE daily_rtp_withdrawal_amounts
    ADD UNIQUE account_name_accountid_effective_date (account_name, accountid, effective_date);

INSERT INTO schema_log (schema_version, date) VALUES (337, now());