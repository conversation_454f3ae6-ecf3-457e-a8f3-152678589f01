CREATE TABLE queued_complete_intra_bank_transfers (
    id                              BIGINT(20)          NOT NULL AUTO_INCREMENT,
    version                         BIGINT              NOT NULL,
    intra_bank_transfer_id          BIGINT(20)          NOT NULL,
    effective_date                  DATE                NOT NULL,
    created_at                      DATETIME            NOT NULL,
    error_flag                      TINYINT(1)          NOT NULL DEFAULT 0,
    polled_time                     DATETIME            DEFAULT NULL,
    sent_time                       DATETIME            DEFAULT NULL,
    ignored_at                      DATETIME            DEFAULT NULL,

    PRIMARY KEY (id),
    INDEX queued_complete_intra_bank_transfers_polled_error (polled_time, error_flag),
    INDEX queued_complete_intra_bank_transfers_sent_polled_error (sent_time, polled_time, error_flag),
    CONSTRAINT queued_complete_intra_bank_transfers_intra_bank_transfer_id
        FOREIGN KEY (intra_bank_transfer_id)
        REFERENCES intra_bank_transfers (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 AUTO_INCREMENT = ***********;

INSERT INTO schema_log (schema_version, date) VALUES (305, NOW());