ALTER TABLE deposits
    ADD COLUMN cash_category_id BIGINT(20) DEFAULT NULL,

    ADD CONSTRAINT deposit_to_cash_category_id_fk
        FOREIGN KEY (cash_category_id) REFERENCES cash_categories (id);

ALTER TABLE withdrawals
    ADD COLUMN cash_category_id BIGINT(20) DEFAULT NULL,

    ADD CONSTRAINT withdrawal_to_cash_category_id_fk
        FOREIGN KEY (cash_category_id) REFERENCES cash_categories (id);

ALTER TABLE cross_account_transfers
    ADD COLUMN cash_category_id BIGINT(20) DEFAULT NULL,

    ADD CONSTRAINT cross_account_transfers_to_cash_category_id_fk
        FOREIGN KEY (cash_category_id) REFERENCES cash_categories (id);

INSERT INTO schema_log (schema_version, date) VALUES (117, now());