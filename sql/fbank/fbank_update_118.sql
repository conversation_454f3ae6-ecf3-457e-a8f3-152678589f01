ALTER TABLE cash_categories
  ADD COLUMN is_designated_backup TINYINT(1) NOT NULL DEFAULT FALSE;

CREATE TABLE version_cash_category_is_designated_backup (
  id                        BIGINT(20)      NOT NULL AUTO_INCREMENT,
  cash_category_id          BIGINT(20)      NOT NULL,
  version                   INT             NOT NULL,
  created_at                DATETIME        NOT NULL,
  deleted                   TINYINT(1)      NOT NULL,
  value                     TINYINT(1)      NOT NULL,

  PRIMARY KEY (id),
  CONSTRAINT version_cash_category_is_designated_backup_to_cc_id_fk
    FOREIGN KEY (cash_category_id)
    REFERENCES cash_categories (id)

) ENGINE = InnoDB DEFAULT CHARSET = utf8 AUTO_INCREMENT=10000000000;

INSERT INTO schema_log (schema_version, date) VALUES (118, now());