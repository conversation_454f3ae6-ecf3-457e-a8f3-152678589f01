CREATE TABLE partner_bank_balances (
    id                     BIGINT(20)    NOT NULL AUTO_INCREMENT,
    created_at             DATETIME      NOT NULL,
    effective_date         DATE          NOT NULL,
    account_name           VARCHAR(255)  NOT NULL,
    opening_ledger_balance DECIMAL(20,2) NOT NULL,
    closing_ledger_balance DECIMAL(20,2) NOT NULL,
    available_balance      DECIMAL(20,2) NOT NULL,
    collected_balance      DECIMAL(20,2) NOT NULL,
    total_credits          DECIMAL(20,2) NOT NULL,
    total_debits           DECIMAL(20,2) NOT NULL,
    PRIMARY KEY (id),
    UNIQUE account_name_effective_date (account_name, effective_date)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 AUTO_INCREMENT = ***********;

INSERT INTO schema_log (schema_version, date) VALUES (334, now());