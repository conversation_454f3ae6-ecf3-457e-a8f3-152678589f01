DROP TABLE version_umb_account_number_information_status;

CREATE TABLE version_umb_account_number_information_status
(
    id                                BIGINT(20)   NOT NULL AUTO_INCREMENT,
    umb_account_number_information_id BIGINT(20)   NOT NULL,
    version                           INT          NOT NULL,
    created_at                        DATETIME     NOT NULL,
    deleted                           TINYINT(1)   NOT NULL,
    value                             VARCHAR(255) NOT NULL,
    PRIMARY KEY (id),
    INDEX version_umb_account_number_information_status_created_at (created_at),
    CONSTRAINT umb_account_number_information_status
    FOREIGN KEY (umb_account_number_information_id) REFERENCES umb_account_number_information (id)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 AUTO_INCREMENT = ***********;

INSERT INTO schema_log (schema_version, DATE) VALUES (372, now());