CREATE TABLE queued_umb_wire_file_generations (
  id                                BIGINT(20)    NOT NULL AUTO_INCREMENT,
  intra_bank_transfer_id            BIGINT(20)    NOT NULL,
  created_at                        DATETIME      NOT NULL,
  polled_time                       DATETIME      DEFAULT NULL,
  sent_time                         DATETIME      DEFAULT NULL,
  error_flag                        TINYINT(1)    NOT NULL DEFAULT 0,
  PRIMARY KEY (id),
  <PERSON><PERSON><PERSON> (intra_bank_transfer_id),
  <PERSON><PERSON><PERSON> queued_umb_wire_file_generations_polled_time_error_flag (polled_time, error_flag),
  KEY quwfg_sent_time_polled_time_error_flag (sent_time, polled_time, error_flag),
  CONSTRAINT queued_umb_wire_file_generations_intra_bank_transfer_id
    FOREIGN KEY (intra_bank_transfer_id)
    REFERENCES intra_bank_transfers (id)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 AUTO_INCREMENT = ***********;

CREATE TABLE queued_umb_file_uploads (
  id                                BIGINT(20)    NOT NULL AUTO_INCREMENT,
  umb_file_id                       BIGINT(20)    NOT NULL,
  created_at                        DATETIME      NOT NULL,
  polled_time                       DATETIME      DEFAULT NULL,
  sent_time                         DATETIME      DEFAULT NULL,
  error_flag                        TINYINT(1)    NOT NULL DEFAULT 0,
  PRIMARY KEY (id),
  KEY (umb_file_id),
  KEY queued_umb_file_uploads_polled_time_error_flag (polled_time, error_flag),
  KEY queued_umb_file_uploads_sent_time_polled_time_error_flag (sent_time, polled_time, error_flag),
  CONSTRAINT queued_umb_file_uploads_umb_file_id
    FOREIGN KEY (umb_file_id)
    REFERENCES umb_files (id)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 AUTO_INCREMENT = ***********;

INSERT INTO schema_log (schema_version, date) VALUES (347, NOW());