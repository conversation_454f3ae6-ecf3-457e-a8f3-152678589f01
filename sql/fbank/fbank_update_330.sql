CREATE TABLE umb_wire_withdrawals (
  id                    BIGINT(20)      NOT NULL AUTO_INCREMENT,
  bank_transfer_id      BIGINT(20)      NOT NULL,
  created_at            DATETIME        NOT NULL,
  amount                DECIMAL(14,2)   NOT NULL,
  source_account        VARCHAR(255)    NOT NULL,
  target_account        VARCHAR(255)    NOT NULL,
  umb_file_id           BIGINT(20)      NOT NULL,
  effective_date        DATE            NOT NULL,
  wire_reference_number VARCHAR(255)    NOT NULL,
  PRIMARY KEY (id),
  INDEX wire_file_id (umb_file_id),
  CONSTRAINT umb_wires_file_id
  FOREIGN KEY (umb_file_id) REFERENCES umb_files (id),
  CONSTRAINT umb_wires_bank_transfer
  FOREIGN KEY (bank_transfer_id) REFERENCES bank_transfers (id)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 AUTO_INCREMENT = ***********;

INSERT INTO schema_log(schema_version, date) VALUES (330, NOW());