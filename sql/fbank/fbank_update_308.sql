CREATE TABLE transaction_monitoring_requests (
    id                                  BIGINT(20)      NOT NULL AUTO_INCREMENT,
    created_at                          DATETIME        NOT NULL,
    polled_time                         DATETIME        DEFAULT NULL,
    sent_time                           D<PERSON>ETIME        DEFAULT NULL ,
    error_flag                          TINYINT(1)      NOT NULL DEFAULT 0,
    ignored_at                          DATETIME        DEFAULT NULL,
    transaction_monitoring_input_id     BIGINT(20)      NOT NULL,
    PRIMARY KEY (id),
    UNIQUE KEY transaction_monitoring_requests_transaction_monitoring_input_id (transaction_monitoring_input_id),
    KEY transaction_monitoring_requests_created_at (created_at),
    CONSTRAINT transaction_monitoring_requests_transaction_monitoring_input_id FOREIGN KEY (transaction_monitoring_input_id) REFERENCES transaction_monitoring_inputs (id)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 AUTO_INCREMENT = 10000000000;

INSERT INTO schema_log (schema_version, date) VALUES (308, now());