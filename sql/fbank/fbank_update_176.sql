CREATE TABLE ach_participants (
  id                            BIGINT(20)      NOT NULL AUTO_INCREMENT,
  routing_number                VARCHAR(255)    NOT NULL,
  office_code                   VARCHAR(255)    NOT NULL,
  servicing_frb_number          VARCHAR(255)    NOT NULL,
  record_type_code              VA<PERSON>HAR(255)    NOT NULL,
  change_date                   VARCHAR(255)    NOT NULL,
  new_routing_number            VARCHAR(255)    NOT NULL,
  customer_name                 VARCHAR(255)    NOT NULL,
  customer_address              VARCHAR(255)    NOT NULL,
  customer_city                 VARCHAR(255)    NOT NULL,
  customer_state                VARCHAR(255)    NOT NULL,
  customer_zip                  VARCHAR(255)    NOT NULL,
  customer_zip_ext              VARCHAR(255)    NOT NULL,
  customer_area_code            VARCHAR(255)    NOT NULL,
  customer_phone_prefix         VARCHAR(255)    NOT NULL,
  customer_phone_suffix         VARCHAR(255)    NOT NULL,
  institution_status_code       VARCHAR(255)    NOT NULL,
  PRIMARY KEY (id),
  UNIQUE KEY ach_participants_routing_number (routing_number)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 AUTO_INCREMENT = ***********;

CREATE TABLE wire_participants (
  id                            BIGINT(20)      NOT NULL AUTO_INCREMENT,
  routing_number                VARCHAR(255)    NOT NULL,
  telegraphic_name              VARCHAR(255)    NOT NULL,
  customer_name                 VARCHAR(255)    NOT NULL,
  customer_state                VARCHAR(255)    NOT NULL,
  customer_city                 VARCHAR(255)    NOT NULL,
  funds_eligibility             VARCHAR(255)    NOT NULL,
  funds_settlement_only_status  VARCHAR(255)    NOT NULL,
  securities_eligibility        VARCHAR(255)    NOT NULL,
  change_date                   VARCHAR(255)    NOT NULL,
  PRIMARY KEY (id),
  UNIQUE KEY wire_participants_routing_number (routing_number)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 AUTO_INCREMENT = ***********;

INSERT INTO schema_log (schema_version, DATE) VALUES (176, now());
