-- MySQL dump 10.14  Distrib 5.5.52-MariaD<PERSON>, for Linux (x86_64)
--
-- Host: db-sv2-maxscale.wlth.fr    Database: bank
-- ------------------------------------------------------
-- Server version	10.2.12 2.3.9-maxscale

SET UNIQUE_CHECKS=0;
SET FOREIGN_KEY_CHECKS=0;

--
-- Table structure for table `acats_associations`
--

DROP TABLE IF EXISTS `acats_associations`;
CREATE TABLE `acats_associations` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_at` datetime NOT NULL,
  `acats_transfer_id` bigint(20) NOT NULL,
  `associated_acats_transfer_id` bigint(20) NOT NULL,
  `association_type` varchar(255) NOT NULL,
  `active` tinyint(1) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `acats_associations_acats_transfer_id` (`acats_transfer_id`),
  KEY `acats_associations_associated_acats_transfer_id` (`associated_acats_transfer_id`),
  CONSTRAINT `acats_associations_acats_transfer_id` FOREIGN KEY (`acats_transfer_id`) REFERENCES `acats_transfers` (`id`),
  CONSTRAINT `acats_associations_associated_acats_transfer_id` FOREIGN KEY (`associated_acats_transfer_id`) REFERENCES `acats_transfers` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `acats_files`
--

DROP TABLE IF EXISTS `acats_files`;
CREATE TABLE `acats_files` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_at` datetime NOT NULL,
  `file_direction` varchar(255) NOT NULL,
  `state` varchar(255) NOT NULL,
  `file_type` varchar(255) NOT NULL,
  `file_name` varchar(255) NOT NULL,
  `content` mediumtext,
  `uploaded_to_s3_at` datetime DEFAULT NULL,
  `environment` varchar(255) DEFAULT 'RBC_TEST',
  PRIMARY KEY (`id`),
  KEY `acats_files_file_name` (`file_name`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `acats_input_transactions`
--

DROP TABLE IF EXISTS `acats_input_transactions`;
CREATE TABLE `acats_input_transactions` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_at` datetime NOT NULL,
  `state` varchar(255) NOT NULL,
  `transaction_type` varchar(255) NOT NULL,
  `action_type` varchar(255) NOT NULL,
  `acats_transfer_id` bigint(20) DEFAULT NULL,
  `acats_file_id` bigint(20) DEFAULT NULL,
  `transaction` mediumtext,
  `environment` varchar(255) DEFAULT 'RBC_TEST',
  PRIMARY KEY (`id`),
  KEY `acats_input_transactions_acats_transfer_id` (`acats_transfer_id`),
  KEY `acats_input_transactions_acats_file_id` (`acats_file_id`),
  KEY `acats_input_transactions_state` (`state`),
  CONSTRAINT `acats_input_transactions_acats_file_id` FOREIGN KEY (`acats_file_id`) REFERENCES `acats_files` (`id`),
  CONSTRAINT `acats_input_transactions_acats_transfer_id` FOREIGN KEY (`acats_transfer_id`) REFERENCES `acats_transfers` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `acats_multicycle_records`
--

DROP TABLE IF EXISTS `acats_multicycle_records`;
CREATE TABLE `acats_multicycle_records` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `sub_type` varchar(255) NOT NULL,
  `acats_file_id` bigint(20) DEFAULT NULL,
  `file_cycle` varchar(255) DEFAULT NULL,
  `control_number` varchar(20) DEFAULT NULL,
  `processing_date` date DEFAULT NULL,
  `asset_sequence_number` int(11) DEFAULT NULL,
  `transfer_status` varchar(255) DEFAULT NULL,
  `days_in_status` int(11) DEFAULT NULL,
  `system_action` varchar(255) DEFAULT NULL,
  `system_reject_reason` varchar(255) DEFAULT NULL,
  `distribution_side` varchar(255) DEFAULT NULL,
  `record` mediumtext,
  `processed` tinyint(1) NOT NULL,
  `transaction_type` varchar(255) DEFAULT NULL,
  `dtc_acats_id` bigint(20) DEFAULT NULL,
  `dtc_acats_asset_id` bigint(20) DEFAULT NULL,
  `environment` varchar(255) DEFAULT 'RBC_TEST',
  PRIMARY KEY (`id`),
  KEY `acats_multicycle_records_acats_file_id` (`acats_file_id`),
  KEY `acats_multicycle_records_control_number` (`control_number`),
  KEY `acats_multicycle_records_dtc_acats_id` (`dtc_acats_id`),
  KEY `acats_multicycle_records_dtc_acats_asset_id` (`dtc_acats_asset_id`),
  KEY `acats_multicycle_records_processing_date_file_cycle` (`processing_date`,`file_cycle`),
  CONSTRAINT `acats_multicycle_records_acats_file_id` FOREIGN KEY (`acats_file_id`) REFERENCES `acats_files` (`id`),
  CONSTRAINT `acats_multicycle_records_dtc_acats_asset_id` FOREIGN KEY (`dtc_acats_asset_id`) REFERENCES `dtc_acats_assets` (`id`),
  CONSTRAINT `acats_multicycle_records_dtc_acats_id` FOREIGN KEY (`dtc_acats_id`) REFERENCES `dtc_acats` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `acats_notifications`
--

DROP TABLE IF EXISTS `acats_notifications`;
CREATE TABLE `acats_notifications` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `acats_transfer_id` bigint(20) NOT NULL,
  `notification_type` varchar(255) NOT NULL,
  `created_at` datetime NOT NULL,
  `expiration` datetime DEFAULT NULL,
  `action_taken` varchar(255) DEFAULT NULL,
  `details` mediumtext,
  `state` varchar(255) DEFAULT NULL,
  `environment` varchar(255) DEFAULT 'RBC_TEST',
  `responded_by` varchar(255) DEFAULT NULL,
  `ops_comments` text,
  `creation_date` date DEFAULT NULL,
  `creation_cycle` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `acats_notification_transfer_id` (`acats_transfer_id`),
  CONSTRAINT `acats_notification_transfer_id` FOREIGN KEY (`acats_transfer_id`) REFERENCES `acats_transfers` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `acats_position_records`
--

DROP TABLE IF EXISTS `acats_position_records`;
CREATE TABLE `acats_position_records` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `sub_type` varchar(255) NOT NULL,
  `acats_file_id` bigint(20) DEFAULT NULL,
  `control_number` varchar(20) DEFAULT NULL,
  `settlement_timing` varchar(255) DEFAULT NULL,
  `settlement_date` date DEFAULT NULL,
  `processing_date` date DEFAULT NULL,
  `asset_sequence_number` int(11) DEFAULT NULL,
  `transfer_status` varchar(255) DEFAULT NULL,
  `days_in_status` int(11) DEFAULT NULL,
  `record` mediumtext,
  `processed` tinyint(1) NOT NULL,
  `dtc_acats_id` bigint(20) DEFAULT NULL,
  `dtc_acats_asset_id` bigint(20) DEFAULT NULL,
  `environment` varchar(255) DEFAULT 'RBC_TEST',
  PRIMARY KEY (`id`),
  KEY `acats_position_records_acats_file_id` (`acats_file_id`),
  KEY `acats_position_records_control_number` (`control_number`),
  KEY `acats_position_records_dtc_acats_id` (`dtc_acats_id`),
  KEY `acats_position_records_dtc_acats_asset_id` (`dtc_acats_asset_id`),
  KEY `acats_position_records_processing_date` (`processing_date`),
  CONSTRAINT `acats_position_records_acats_file_id` FOREIGN KEY (`acats_file_id`) REFERENCES `acats_files` (`id`),
  CONSTRAINT `acats_position_records_dtc_acats_asset_id` FOREIGN KEY (`dtc_acats_asset_id`) REFERENCES `dtc_acats_assets` (`id`),
  CONSTRAINT `acats_position_records_dtc_acats_id` FOREIGN KEY (`dtc_acats_id`) REFERENCES `dtc_acats` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `acats_transfer_instructions`
--

DROP TABLE IF EXISTS `acats_transfer_instructions`;
CREATE TABLE `acats_transfer_instructions` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `acats_transfer_id` bigint(20) NOT NULL,
  `type` varchar(255) NOT NULL,
  `partial_or_all` varchar(255) NOT NULL,
  `value` decimal(20,8) DEFAULT NULL,
  `instrument_id` bigint(20) DEFAULT NULL,
  `state` varchar(255) NOT NULL,
  `isin` varchar(255) DEFAULT NULL,
  `cusip` varchar(255) DEFAULT NULL,
  `asset_pricing_category` varchar(255) DEFAULT NULL,
  `new_fund_customer_account_number` varchar(255) DEFAULT NULL,
  `reregistration_date` date DEFAULT NULL,
  `externally_provided_market_value` decimal(20,8) DEFAULT NULL,
  `margin` tinyint(1) DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `acats_transfer_instructions_acats_transfer_id` (`acats_transfer_id`),
  KEY `acats_transfer_instructions_instrument_id` (`instrument_id`),
  CONSTRAINT `acats_transfer_instructions_acats_transfer_id` FOREIGN KEY (`acats_transfer_id`) REFERENCES `acats_transfers` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `acats_transfers`
--

DROP TABLE IF EXISTS `acats_transfers`;
CREATE TABLE `acats_transfers` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_at` datetime NOT NULL,
  `state` varchar(255) NOT NULL,
  `accountid` bigint(20) DEFAULT NULL,
  `initiator` varchar(255) NOT NULL,
  `transfer_type` varchar(255) NOT NULL,
  `dtc_acats_id` bigint(20) DEFAULT NULL,
  `reject_edited` tinyint(1) NOT NULL DEFAULT '0',
  `contraparty` mediumtext NOT NULL,
  `account_number` varchar(255) DEFAULT NULL,
  `account_info` mediumtext,
  `comments` tinytext,
  `associated_acats_id` bigint(20) DEFAULT NULL,
  `residual_account_id` bigint(20) DEFAULT NULL,
  `transfer_reason` varchar(255) DEFAULT NULL,
  `wealthfront_receiver_ptf` tinyint(1) DEFAULT '0',
  `account_transfer_id` bigint(20) DEFAULT NULL,
  `environment` varchar(255) DEFAULT 'RBC_TEST',
  `client_confirmed` tinyint(1) NOT NULL DEFAULT '0',
  `client_confirmed_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `acats_transfers_dtc_acats_id` (`dtc_acats_id`),
  KEY `acats_transfers_accountid` (`accountid`),
  KEY `acats_transfers_residual_account_id` (`residual_account_id`),
  KEY `acat_transfers_account_transfer_id` (`account_transfer_id`),
  KEY `acats_transfers_state` (`state`),
  KEY `acats_transfers_account_number` (`account_number`),
  CONSTRAINT `acats_transfers_residual_account_id` FOREIGN KEY (`residual_account_id`) REFERENCES `residual_accounts` (`id`),
  CONSTRAINT `acat_transfers_account_transfer_id` FOREIGN KEY (`account_transfer_id`) REFERENCES `account_transfers` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `account_blocks`
--

DROP TABLE IF EXISTS `account_blocks`;
CREATE TABLE `account_blocks` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `type` varchar(255) NOT NULL,
  `brokerage_account_id` bigint(20) NOT NULL,
  `category` varchar(255) NOT NULL,
  `reason` varchar(255) NOT NULL,
  `requester` varchar(255) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `account_blocks_brokerage_account_id` (`brokerage_account_id`),
  CONSTRAINT `fkey_account_blocks_brokerage_account_id` FOREIGN KEY (`brokerage_account_id`) REFERENCES `brokerage_accounts` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `account_transfer_acats`
--

DROP TABLE IF EXISTS `account_transfer_acats`;
CREATE TABLE `account_transfer_acats` (
  `account_transfer_id` bigint(20) NOT NULL,
  `acats_transfer_id` bigint(20) NOT NULL,
  PRIMARY KEY (`account_transfer_id`,`acats_transfer_id`),
  KEY `account_transfer_acats_acats_transfer_id` (`acats_transfer_id`),
  CONSTRAINT `account_transfer_acats_acats_transfer_id` FOREIGN KEY (`acats_transfer_id`) REFERENCES `acats_transfers` (`id`),
  CONSTRAINT `account_transfer_acats_account_transfer_id` FOREIGN KEY (`account_transfer_id`) REFERENCES `account_transfers` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Table structure for table `account_transfer_associated_acats`
--

DROP TABLE IF EXISTS `account_transfer_associated_acats`;
CREATE TABLE `account_transfer_associated_acats` (
  `account_transfer_id` bigint(20) NOT NULL,
  `acats_transfer_id` bigint(20) NOT NULL,
  PRIMARY KEY (`account_transfer_id`,`acats_transfer_id`),
  KEY `account_transfer_associated_acats_acats_transfer_id` (`acats_transfer_id`),
  CONSTRAINT `account_transfer_associated_acats_acats_transfer_id` FOREIGN KEY (`acats_transfer_id`) REFERENCES `acats_transfers` (`id`),
  CONSTRAINT `account_transfer_associated_acats_account_transfer_id` FOREIGN KEY (`account_transfer_id`) REFERENCES `account_transfers` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Table structure for table `account_transfer_blocks`
--

DROP TABLE IF EXISTS `account_transfer_blocks`;
CREATE TABLE `account_transfer_blocks` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `acats_transfer_id` bigint(20) DEFAULT NULL,
  `dtc_transfer_id` bigint(255) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `is_external_block` tinyint(1) NOT NULL,
  `block_id` bigint(20) NOT NULL,
  `type` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `account_transfer_blocks_acats_transfer_id` (`acats_transfer_id`),
  KEY `account_transfer_blocks_dtc_transfer_id` (`dtc_transfer_id`),
  CONSTRAINT `account_transfer_blocks_acats_transfer_id` FOREIGN KEY (`acats_transfer_id`) REFERENCES `acats_transfers` (`id`),
  CONSTRAINT `account_transfer_blocks_dtc_transfer_id` FOREIGN KEY (`dtc_transfer_id`) REFERENCES `dtc_transfers` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `account_transfer_cancellations`
--

DROP TABLE IF EXISTS `account_transfer_cancellations`;
CREATE TABLE `account_transfer_cancellations` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `transfer_type` varchar(255) NOT NULL,
  `accountid` bigint(20) DEFAULT NULL,
  `reason` varchar(255) NOT NULL,
  `account_transfer_id` bigint(20) DEFAULT NULL,
  `clearing_number` varchar(255) NOT NULL,
  `brokerage_overrideid` bigint(20) DEFAULT NULL,
  `canceled_at` datetime NOT NULL,
  `acats_transfer_id` bigint(20) DEFAULT NULL,
  `dtc_transfer_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `acats_transfer_id` (`acats_transfer_id`,`dtc_transfer_id`),
  KEY `acats_transfer_cancellations_dtc_transfer_id` (`dtc_transfer_id`),
  KEY `account_transfer_cancellations_account_transfer_id` (`account_transfer_id`),
  KEY `account_transfer_cancellations_clearing_number` (`clearing_number`),
  KEY `account_transfer_cancellations_brokerage_overrideid` (`brokerage_overrideid`),
  CONSTRAINT `acats_transfer_cancellations_acats_transfer_id` FOREIGN KEY (`acats_transfer_id`) REFERENCES `acats_transfers` (`id`),
  CONSTRAINT `acats_transfer_cancellations_dtc_transfer_id` FOREIGN KEY (`dtc_transfer_id`) REFERENCES `dtc_transfers` (`id`),
  CONSTRAINT `account_transfer_cancellations_account_transfer_id` FOREIGN KEY (`account_transfer_id`) REFERENCES `account_transfers` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `account_transfer_failures`
--

DROP TABLE IF EXISTS `account_transfer_failures`;
CREATE TABLE `account_transfer_failures` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `transfer_type` varchar(255) NOT NULL,
  `accountid` bigint(20) NOT NULL,
  `reason` varchar(255) NOT NULL,
  `account_transfer_id` bigint(20) DEFAULT NULL,
  `clearing_number` varchar(255) NOT NULL,
  `brokerage_overrideid` bigint(20) DEFAULT NULL,
  `failed_at` datetime NOT NULL,
  `acats_transfer_id` bigint(20) DEFAULT NULL,
  `dtc_transfer_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `acats_transfer_id` (`acats_transfer_id`,`dtc_transfer_id`),
  KEY `acats_transfer_failures_dtc_transfer_id` (`dtc_transfer_id`),
  KEY `account_transfer_failures_account_transfer_id` (`account_transfer_id`),
  KEY `account_transfer_failures_clearing_number` (`clearing_number`),
  KEY `account_transfer_failures_brokerage_overrideid` (`brokerage_overrideid`),
  CONSTRAINT `acats_transfer_failures_acats_transfer_id` FOREIGN KEY (`acats_transfer_id`) REFERENCES `acats_transfers` (`id`),
  CONSTRAINT `acats_transfer_failures_dtc_transfer_id` FOREIGN KEY (`dtc_transfer_id`) REFERENCES `dtc_transfers` (`id`),
  CONSTRAINT `account_transfer_failures_account_transfer_id` FOREIGN KEY (`account_transfer_id`) REFERENCES `account_transfers` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `account_transfers`
--

DROP TABLE IF EXISTS `account_transfers`;
CREATE TABLE `account_transfers` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_at` datetime NOT NULL,
  `state` varchar(255) NOT NULL,
  `brokerage_account_id` bigint(20) DEFAULT NULL,
  `brokerage_override_id` bigint(20) NOT NULL,
  `firm_account_number` varchar(255) NOT NULL,
  `ssds_plan_id` bigint(20) DEFAULT NULL,
  `signature_id` bigint(20) DEFAULT NULL,
  `rejected_edited` tinyint(1) NOT NULL DEFAULT '0',
  `note` mediumtext,
  `transfer_type` varchar(255) NOT NULL,
  `information` mediumtext,
  `settlement_date` date DEFAULT NULL,
  `settled_market_value` decimal(20,2) DEFAULT NULL,
  `external_account_id` bigint(20) DEFAULT NULL,
  `contra_account_closed` tinyint(1) DEFAULT '1',
  `user_id` bigint(20) DEFAULT NULL,
  `accountid` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `account_transfers_state` (`state`),
  KEY `account_transfers_ssds_plan_id` (`ssds_plan_id`),
  KEY `account_transfers_brokerage_account_id` (`brokerage_account_id`),
  KEY `account_transfers_user_id` (`user_id`),
  KEY `account_transfers_accountid` (`accountid`),
  CONSTRAINT `account_transfers_brokerage_account_id` FOREIGN KEY (`brokerage_account_id`) REFERENCES `brokerage_accounts` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `ach_deposits`
--

DROP TABLE IF EXISTS `ach_deposits`;
CREATE TABLE `ach_deposits` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `release_date` date DEFAULT NULL,
  `state` varchar(255) NOT NULL,
  `transfer_details` text,
  `micro_deposit` tinyint(1) NOT NULL,
  `wells_ach_transfer_id` bigint(20) DEFAULT NULL,
  `ach_relationship_id` bigint(20) DEFAULT NULL,
  `micro_deposits_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `ach_deposits_id` (`id`),
  KEY `ach_deposits_ach_relationship_id` (`ach_relationship_id`),
  KEY `ach_deposits_wells_ach_transfer_id` (`wells_ach_transfer_id`),
  KEY `ach_deposits_micro_deposits_id` (`micro_deposits_id`),
  KEY `ach_deposits_state` (`state`),
  CONSTRAINT `ach_deposits_ach_relationship_id` FOREIGN KEY (`ach_relationship_id`) REFERENCES `ach_relationships` (`id`),
  CONSTRAINT `ach_deposits_id` FOREIGN KEY (`id`) REFERENCES `bank_transfers` (`id`),
  CONSTRAINT `ach_deposits_micro_deposits_id` FOREIGN KEY (`micro_deposits_id`) REFERENCES `ach_relationship_micro_deposits` (`id`),
  CONSTRAINT `ach_deposits_wells_ach_transfer_id` FOREIGN KEY (`wells_ach_transfer_id`) REFERENCES `wells_ach_transfers` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `ach_direct_deposits`
--

DROP TABLE IF EXISTS `ach_direct_deposits`;
CREATE TABLE `ach_direct_deposits` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `state` varchar(255) NOT NULL,
  `wells_ach_direct_deposit_id` bigint(20) DEFAULT NULL,
  `release_date` date DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `ach_direct_deposits_wells_ach_direct_deposit_id` (`wells_ach_direct_deposit_id`),
  KEY `ach_direct_deposits_id` (`id`),
  CONSTRAINT `ach_direct_deposits_id` FOREIGN KEY (`id`) REFERENCES `bank_transfers` (`id`),
  CONSTRAINT `ach_direct_deposits_wells_ach_direct_deposit_id` FOREIGN KEY (`wells_ach_direct_deposit_id`) REFERENCES `wells_ach_direct_deposits` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `ach_relationship_failures`
--

DROP TABLE IF EXISTS `ach_relationship_failures`;
CREATE TABLE `ach_relationship_failures` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `ach_relationship_id` bigint(20) NOT NULL,
  `failure_reason` varchar(255) NOT NULL,
  `failed_at` datetime NOT NULL,
  `failed_bank_transfer_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `ach_relationship_failures_ach_relationship_id` (`ach_relationship_id`),
  KEY `ach_relationship_failures_failed_bank_transfer_id` (`failed_bank_transfer_id`),
  CONSTRAINT `ach_relationship_failures_ach_relationship_id` FOREIGN KEY (`ach_relationship_id`) REFERENCES `ach_relationships` (`id`),
  CONSTRAINT `ach_relationship_failures_failed_bank_transfer_id` FOREIGN KEY (`failed_bank_transfer_id`) REFERENCES `bank_transfers` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `ach_relationship_micro_deposits`
--

DROP TABLE IF EXISTS `ach_relationship_micro_deposits`;
CREATE TABLE `ach_relationship_micro_deposits` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `version` bigint(20) NOT NULL,
  `state` varchar(255) NOT NULL,
  `ach_relationship_id` bigint(20) DEFAULT NULL,
  `failed_confirmation_attempts` int(11) NOT NULL,
  `allowed_failed_confirmation_attempts` int(11) NOT NULL,
  `micro_withdrawal_1_id` bigint(20) DEFAULT NULL,
  `micro_withdrawal_2_id` bigint(20) DEFAULT NULL,
  `micro_deposit_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `ach_relationship_micro_deposits_ach_relationship_id` (`ach_relationship_id`),
  KEY `ach_relationship_micro_deposits_micro_withdrawal_1_id` (`micro_withdrawal_1_id`),
  KEY `ach_relationship_micro_deposits_micro_withdrawal_2_id` (`micro_withdrawal_2_id`),
  KEY `ach_relationship_micro_deposits_micro_deposit_id` (`micro_deposit_id`),
  CONSTRAINT `ach_relationship_micro_deposits_ach_relationship_id` FOREIGN KEY (`ach_relationship_id`) REFERENCES `ach_relationships` (`id`),
  CONSTRAINT `ach_relationship_micro_deposits_micro_deposit_id` FOREIGN KEY (`micro_deposit_id`) REFERENCES `ach_deposits` (`id`),
  CONSTRAINT `ach_relationship_micro_deposits_micro_withdrawal_1_id` FOREIGN KEY (`micro_withdrawal_1_id`) REFERENCES `ach_withdrawals` (`id`),
  CONSTRAINT `ach_relationship_micro_deposits_micro_withdrawal_2_id` FOREIGN KEY (`micro_withdrawal_2_id`) REFERENCES `ach_withdrawals` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `ach_relationships`
--

DROP TABLE IF EXISTS `ach_relationships`;
CREATE TABLE `ach_relationships` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `version` bigint(20) NOT NULL DEFAULT '0',
  `holderid` bigint(20) NOT NULL,
  `userid` bigint(20) DEFAULT NULL,
  `state` varchar(255) NOT NULL,
  `created_at` datetime NOT NULL,
  `relationship_details` mediumtext NOT NULL,
  `authentication_strategy` varchar(255) DEFAULT NULL,
  `external_account_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `ach_relationships_userid` (`userid`),
  KEY `ach_relationships_state_authentication_strategy` (`state`,`authentication_strategy`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `ach_return_files`
--

DROP TABLE IF EXISTS `ach_return_files`;
CREATE TABLE `ach_return_files` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `effective_date` date NOT NULL,
  `created_at` datetime NOT NULL,
  `processed_at` datetime DEFAULT NULL,
  `contents` longtext NOT NULL,
  `wells_file_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `ach_return_files_wells_file_id` (`wells_file_id`),
  CONSTRAINT `ach_return_files_wells_file_id` FOREIGN KEY (`wells_file_id`) REFERENCES `wells_files` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `ach_returns`
--

DROP TABLE IF EXISTS `ach_returns`;
CREATE TABLE `ach_returns` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `bank_transfer_id` bigint(20) NOT NULL,
  `ach_return_file_id` bigint(20) NOT NULL,
  `return_reason` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `ach_returns_ach_return_file_id` (`ach_return_file_id`),
  KEY `ach_returns_bank_transfer_id` (`bank_transfer_id`),
  CONSTRAINT `ach_returns_ach_return_file_id` FOREIGN KEY (`ach_return_file_id`) REFERENCES `ach_return_files` (`id`),
  CONSTRAINT `ach_returns_bank_transfer_id` FOREIGN KEY (`bank_transfer_id`) REFERENCES `bank_transfers` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `ach_withdrawals`
--

DROP TABLE IF EXISTS `ach_withdrawals`;
CREATE TABLE `ach_withdrawals` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `state` varchar(255) NOT NULL,
  `transfer_details` text,
  `micro_withdrawal` tinyint(1) NOT NULL,
  `wells_ach_transfer_id` bigint(20) DEFAULT NULL,
  `ach_relationship_id` bigint(20) DEFAULT NULL,
  `micro_deposits_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `ach_withdrawals_id` (`id`),
  KEY `ach_withdrawals_ach_relationship_id` (`ach_relationship_id`),
  KEY `ach_withdrawals_wells_ach_transfer_id` (`wells_ach_transfer_id`),
  KEY `ach_withdrawals_micro_deposits_id` (`micro_deposits_id`),
  KEY `ach_withdrawals_state` (`state`),
  CONSTRAINT `ach_withdrawals_ach_relationship_id` FOREIGN KEY (`ach_relationship_id`) REFERENCES `ach_relationships` (`id`),
  CONSTRAINT `ach_withdrawals_id` FOREIGN KEY (`id`) REFERENCES `bank_transfers` (`id`),
  CONSTRAINT `ach_withdrawals_micro_deposits_id` FOREIGN KEY (`micro_deposits_id`) REFERENCES `ach_relationship_micro_deposits` (`id`),
  CONSTRAINT `ach_withdrawals_wells_ach_transfer_id` FOREIGN KEY (`wells_ach_transfer_id`) REFERENCES `wells_ach_transfers` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `ascensus_deposits`
--

DROP TABLE IF EXISTS `ascensus_deposits`;
CREATE TABLE `ascensus_deposits` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `state` varchar(255) NOT NULL,
  `ready_to_be_released_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  PRIMARY KEY (`id`),
  KEY `ascensus_deposits_id` (`id`),
  CONSTRAINT `ascensus_deposits_id` FOREIGN KEY (`id`) REFERENCES `deposits` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `ascensus_withdrawals`
--

DROP TABLE IF EXISTS `ascensus_withdrawals`;
CREATE TABLE `ascensus_withdrawals` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `state` varchar(255) NOT NULL,
  `withdrawal_type` varchar(255) DEFAULT NULL,
  `recipient` varchar(255) DEFAULT NULL,
  `initiator` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `ascensus_withdrawals_id` (`id`),
  CONSTRAINT `ascensus_withdrawals_id` FOREIGN KEY (`id`) REFERENCES `withdrawals` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `audit_cash_flow`
--

DROP TABLE IF EXISTS `audit_cash_flow`;
CREATE TABLE `audit_cash_flow` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `version` int(11) NOT NULL DEFAULT '0',
  `transaction_num` varchar(255) DEFAULT NULL,
  `accountid` bigint(20) NOT NULL,
  `trade_date` date NOT NULL,
  `settlement_date` date NOT NULL,
  `net_amount` decimal(14,2) NOT NULL,
  `code` varchar(255) NOT NULL,
  `type` varchar(255) DEFAULT NULL,
  `state` varchar(255) NOT NULL,
  `counterparty_institution` varchar(255) DEFAULT NULL,
  `counterparty_name` varchar(255) DEFAULT NULL,
  `counterparty_account` varchar(255) DEFAULT NULL,
  `metadata` mediumtext,
  PRIMARY KEY (`id`),
  UNIQUE KEY `transaction_num` (`transaction_num`),
  KEY `accountid` (`accountid`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `bai2_snapshots`
--

DROP TABLE IF EXISTS `bai2_snapshots`;
CREATE TABLE `bai2_snapshots` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `effective_date` date NOT NULL,
  `created_at` datetime NOT NULL,
  `snapshot` longtext NOT NULL,
  `processed_at` datetime DEFAULT NULL,
  `wells_file_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `bai2_snapshots_wells_file_id` (`wells_file_id`),
  CONSTRAINT `bai2_snapshots_wells_file_id` FOREIGN KEY (`wells_file_id`) REFERENCES `wells_files` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `bank_transfers`
--

DROP TABLE IF EXISTS `bank_transfers`;
CREATE TABLE `bank_transfers` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `version` bigint(20) NOT NULL DEFAULT '0',
  `created_at` datetime NOT NULL,
  `amount` decimal(20,2) NOT NULL,
  `wealthfront_account` varchar(255) NOT NULL DEFAULT 'ASCENSUS_FBO',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `brokerage_account_balances`
--

DROP TABLE IF EXISTS `brokerage_account_balances`;
CREATE TABLE `brokerage_account_balances` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `calculated_at` datetime NOT NULL,
  `effective_date` date NOT NULL,
  `location` varchar(255) NOT NULL DEFAULT 'OMNI_FBO',
  `brokerage_account_id` bigint(20) NOT NULL,
  `amount` decimal(20,2) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `brokerage_account_balances_brkg_acct_id_eff_date_location` (`brokerage_account_id`,`effective_date`,`location`),
  KEY `fkey_brokerage_account_balances_account_id` (`brokerage_account_id`),
  CONSTRAINT `_fkey_brokerage_account_balances_account_id` FOREIGN KEY (`brokerage_account_id`) REFERENCES `brokerage_accounts` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `brokerage_accounts`
--

DROP TABLE IF EXISTS `brokerage_accounts`;
CREATE TABLE `brokerage_accounts` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `version` bigint(20) NOT NULL DEFAULT '0',
  `updated_at` datetime NOT NULL DEFAULT '1900-01-01 00:00:00',
  `created_at` datetime DEFAULT NULL,
  `closed_at` datetime DEFAULT NULL,
  `state` varchar(255) DEFAULT NULL,
  `accountid` bigint(20) NOT NULL,
  `userid` bigint(20) DEFAULT NULL,
  `brokerage` varchar(255) NOT NULL,
  `account_type` varchar(255) NOT NULL DEFAULT 'CASH',
  `account_kind` varchar(255) DEFAULT NULL,
  `settlement_location` varchar(255) NOT NULL DEFAULT 'RBC',
  PRIMARY KEY (`id`),
  UNIQUE KEY `accountid` (`accountid`),
  KEY `brokerage_accounts_userid` (`userid`),
  KEY `brokerage_accounts_closed_at` (`closed_at`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `bucket_actions`
--

DROP TABLE IF EXISTS `bucket_actions`;
CREATE TABLE `bucket_actions` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `version` int(11) NOT NULL,
  `bucket_id` bigint(20) NOT NULL,
  `amount` decimal(14,2) DEFAULT NULL,
  `effective_date` date NOT NULL,
  `bucket_location_edge` varchar(255) NOT NULL,
  `created_at` datetime NOT NULL,
  `polled_time` datetime DEFAULT NULL,
  `sent_time` datetime DEFAULT NULL,
  `error_flag` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `bucket_actions_bucket_id` (`bucket_id`),
  KEY `bucket_actions_sent_time_polled_time_error_flag` (`sent_time`,`polled_time`,`error_flag`),
  KEY `bucket_actions_polled_time_error_flag` (`polled_time`,`error_flag`),
  CONSTRAINT `bucket_actions_bucket_id` FOREIGN KEY (`bucket_id`) REFERENCES `buckets` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `bucket_amounts`
--

DROP TABLE IF EXISTS `bucket_amounts`;
CREATE TABLE `bucket_amounts` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `version` int(11) NOT NULL,
  `bucket_id` bigint(20) NOT NULL,
  `type` varchar(255) NOT NULL,
  `location` varchar(255) NOT NULL,
  `amount` decimal(20,2) DEFAULT NULL,
  `pending_full_amount` tinyint(1) DEFAULT '0',
  `exchange_bucket_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `bucket_amounts_exchange_bucket_id` (`exchange_bucket_id`),
  KEY `bucket_amounts_bucket_id_type_location` (`bucket_id`,`type`,`location`),
  CONSTRAINT `bucket_amounts_bucket_id` FOREIGN KEY (`bucket_id`) REFERENCES `buckets` (`id`),
  CONSTRAINT `bucket_amounts_exchange_bucket_id` FOREIGN KEY (`exchange_bucket_id`) REFERENCES `buckets` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `bucket_events`
--

DROP TABLE IF EXISTS `bucket_events`;
CREATE TABLE `bucket_events` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `version` int(11) NOT NULL,
  `bucket_id` bigint(20) NOT NULL,
  `type` varchar(255) NOT NULL,
  `location` varchar(255) NOT NULL,
  `contra_location` varchar(255) DEFAULT NULL,
  `amount` decimal(20,2) DEFAULT NULL,
  `pending_to_defined_amount_conversion` tinyint(1) DEFAULT '0',
  `effective_date` date NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `bucket_events_bucket_id` (`bucket_id`),
  KEY `bucket_events_effective_date` (`effective_date`),
  CONSTRAINT `bucket_events_bucket_id` FOREIGN KEY (`bucket_id`) REFERENCES `buckets` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `bucket_to_cash_sweep_settlement_wire_batch`
--

DROP TABLE IF EXISTS `bucket_to_cash_sweep_settlement_wire_batch`;
CREATE TABLE `bucket_to_cash_sweep_settlement_wire_batch` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `version` int(11) NOT NULL DEFAULT '0',
  `bucket_id` bigint(20) NOT NULL,
  `cash_sweep_settlement_wire_batch_id` bigint(20) NOT NULL,
  `amount` decimal(20,2) NOT NULL,
  `state` varchar(255) NOT NULL,
  `entity_direction` varchar(255) NOT NULL DEFAULT 'FORWARD',
  PRIMARY KEY (`id`),
  KEY `bucket_to_cash_sweep_settlement_wire_batch_bucket_id` (`bucket_id`),
  KEY `bucket_to_cash_sweep_settlement_wire_batch_batch_id` (`cash_sweep_settlement_wire_batch_id`),
  CONSTRAINT `bucket_to_cash_sweep_settlement_wire_batch_batch_id` FOREIGN KEY (`cash_sweep_settlement_wire_batch_id`) REFERENCES `cash_sweep_settlement_wire_batches` (`id`),
  CONSTRAINT `bucket_to_cash_sweep_settlement_wire_batch_bucket_id` FOREIGN KEY (`bucket_id`) REFERENCES `buckets` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `bucket_to_incoming_brokerage_wire`
--

DROP TABLE IF EXISTS `bucket_to_incoming_brokerage_wire`;
CREATE TABLE `bucket_to_incoming_brokerage_wire` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `version` int(11) NOT NULL,
  `bucket_id` bigint(20) NOT NULL,
  `incoming_brokerage_wire_id` bigint(20) NOT NULL,
  `amount` decimal(20,2) NOT NULL,
  `bucket_location_edge` varchar(255) NOT NULL,
  `state` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `bucket_to_incoming_brokerage_wire_incoming_brokerage_wire_id` (`incoming_brokerage_wire_id`),
  KEY `bucket_to_incoming_brokerage_wire_bucket_id` (`bucket_id`),
  CONSTRAINT `bucket_to_incoming_brokerage_wire_incoming_brokerage_wire_id` FOREIGN KEY (`incoming_brokerage_wire_id`) REFERENCES `incoming_brokerage_wires` (`id`),
  CONSTRAINT `bucket_to_incoming_brokerage_wire_bucket_id` FOREIGN KEY (`bucket_id`) REFERENCES `buckets` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `bucket_to_outgoing_brokerage_wire`
--

DROP TABLE IF EXISTS `bucket_to_outgoing_brokerage_wire`;
CREATE TABLE `bucket_to_outgoing_brokerage_wire` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `bucket_id` bigint(20) NOT NULL,
  `outgoing_brokerage_wire_id` bigint(20) NOT NULL,
  `amount` decimal(20,2) NOT NULL,
  `bucket_location_edge` varchar(255) NOT NULL,
  `bank_transaction_queued_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `bucket_to_outgoing_brokerage_wire_bucket_id` (`bucket_id`),
  KEY `bucket_to_outgoing_brokerage_wire_outgoing_brokerage_wire_id` (`outgoing_brokerage_wire_id`),
  CONSTRAINT `bucket_to_outgoing_brokerage_wire_ibfk_1` FOREIGN KEY (`bucket_id`) REFERENCES `buckets` (`id`),
  CONSTRAINT `bucket_to_outgoing_brokerage_wire_ibfk_2` FOREIGN KEY (`outgoing_brokerage_wire_id`) REFERENCES `outgoing_brokerage_wires` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `buckets`
--

DROP TABLE IF EXISTS `buckets`;
CREATE TABLE `buckets` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `brokerage_account_id` bigint(20) NOT NULL,
  `amount` decimal(20,2) DEFAULT NULL,
  `pending_full_amount` tinyint(1) DEFAULT '0',
  `state` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `buckets_brokerage_account_id` (`brokerage_account_id`),
  CONSTRAINT `buckets_brokerage_account_id` FOREIGN KEY (`brokerage_account_id`) REFERENCES `brokerage_accounts` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `cash_account_balance_events`
--

DROP TABLE IF EXISTS `cash_account_balance_events`;
CREATE TABLE `cash_account_balance_events` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `version` int(11) NOT NULL,
  `cash_account_balance_id` bigint(20) NOT NULL,
  `initiating_entity_type` varchar(255) DEFAULT NULL,
  `initiating_entityid` bigint(20) DEFAULT NULL,
  `reason` varchar(255) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `execution_trigger` varchar(255) NOT NULL,
  `execution_date` date DEFAULT NULL,
  `executed_at` datetime DEFAULT NULL,
  `wells_fargo_available_current` decimal(13,2) NOT NULL,
  `wells_fargo_available_pending` decimal(13,2) NOT NULL,
  `green_dot_available_current` decimal(13,2) NOT NULL,
  `green_dot_available_pending` decimal(13,2) NOT NULL,
  `green_dot_ledger_current` decimal(13,2) NOT NULL,
  `green_dot_ledger_pending` decimal(13,2) NOT NULL,
  `wells_fargo_operational` decimal(13,2) NOT NULL,
  `tbs_sweep` decimal(13,2) NOT NULL,
  `green_dot_dda` decimal(13,2) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `cash_account_balance_events_cash_account_balance_id` (`cash_account_balance_id`),
  KEY `cash_account_balance_events_initiating_entity` (`initiating_entity_type`,`initiating_entityid`),
  CONSTRAINT `cash_account_balance_events_cash_account_balance_id` FOREIGN KEY (`cash_account_balance_id`) REFERENCES `cash_account_balances` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `cash_account_balances`
--

DROP TABLE IF EXISTS `cash_account_balances`;
CREATE TABLE `cash_account_balances` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `version` int(11) NOT NULL,
  `brokerage_account_id` bigint(20) NOT NULL,
  `updated_at` datetime NOT NULL,
  `wells_fargo_available_current` decimal(13,2) NOT NULL,
  `wells_fargo_available_pending` decimal(13,2) NOT NULL,
  `green_dot_available_current` decimal(13,2) NOT NULL,
  `green_dot_available_pending` decimal(13,2) NOT NULL,
  `green_dot_ledger_current` decimal(13,2) NOT NULL,
  `green_dot_ledger_pending` decimal(13,2) NOT NULL,
  `wells_fargo_operational` decimal(13,2) NOT NULL,
  `tbs_sweep` decimal(13,2) NOT NULL,
  `green_dot_dda` decimal(13,2) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `brokerage_account_id` (`brokerage_account_id`),
  CONSTRAINT `cash_account_balances_brokerage_account_id` FOREIGN KEY (`brokerage_account_id`) REFERENCES `brokerage_accounts` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `cash_sweep_settlement_wire_batches`
--

DROP TABLE IF EXISTS `cash_sweep_settlement_wire_batches`;
CREATE TABLE `cash_sweep_settlement_wire_batches` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `version` int(11) NOT NULL DEFAULT '0',
  `created_at` datetime NOT NULL,
  `effective_date` date NOT NULL,
  `net_expected_amount` decimal(20,2) NOT NULL,
  `net_linked_transfer_amount` decimal(20,2) NOT NULL,
  `net_linked_settlement_wire_amount` decimal(20,2) NOT NULL,
  `tbs_settlement_batchid` bigint(20) NOT NULL,
  `state` varchar(255) NOT NULL,
  `updated_at` datetime NOT NULL DEFAULT '1970-01-01 00:00:00',
  PRIMARY KEY (`id`),
  KEY `cash_sweep_settlement_wire_batches_state` (`state`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `cash_sweep_settlement_wires`
--

DROP TABLE IF EXISTS `cash_sweep_settlement_wires`;
CREATE TABLE `cash_sweep_settlement_wires` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `version` int(11) NOT NULL DEFAULT '0',
  `created_at` datetime NOT NULL,
  `amount` decimal(20,2) NOT NULL,
  `direction` varchar(255) NOT NULL,
  `counterparty_bank` varchar(255) NOT NULL,
  `counterparty_account_number` varchar(255) NOT NULL,
  `state` varchar(255) NOT NULL,
  `cash_sweep_settlement_wire_batch_id` bigint(20) DEFAULT NULL,
  `wire_withdrawal_id` bigint(20) DEFAULT NULL,
  `drawdown_wire_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `cash_sweep_settlement_wires_state` (`state`),
  KEY `cash_sweep_settlement_wires_batch_id` (`cash_sweep_settlement_wire_batch_id`),
  KEY `cash_sweep_settlement_wires_wire_withdrawal_id` (`wire_withdrawal_id`),
  KEY `cash_sweep_settlement_wires_drawdown_wire_id` (`drawdown_wire_id`),
  CONSTRAINT `cash_sweep_settlement_wires_batch_id` FOREIGN KEY (`cash_sweep_settlement_wire_batch_id`) REFERENCES `cash_sweep_settlement_wire_batches` (`id`),
  CONSTRAINT `cash_sweep_settlement_wires_drawdown_wire_id` FOREIGN KEY (`drawdown_wire_id`) REFERENCES `drawdown_wires` (`id`),
  CONSTRAINT `cash_sweep_settlement_wires_wire_withdrawal_id` FOREIGN KEY (`wire_withdrawal_id`) REFERENCES `wire_withdrawals` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `cbrs_asset_to_tax_lots`
--

DROP TABLE IF EXISTS `cbrs_asset_to_tax_lots`;
CREATE TABLE `cbrs_asset_to_tax_lots` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `type` varchar(255) NOT NULL,
  `created_at` datetime NOT NULL,
  `cbrs_input_id` bigint(20) DEFAULT NULL,
  `cbrs_asset_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `atlas_creation_date` date DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `cbrs_asset_to_tax_lots_created_at` (`created_at`),
  KEY `cbrs_asset_to_tax_lots_cbrs_input_id` (`cbrs_input_id`),
  KEY `cbrs_asset_to_tax_lots_cbrs_asset_id` (`cbrs_asset_id`),
  CONSTRAINT `cbrs_asset_to_tax_lots_cbrs_asset_id` FOREIGN KEY (`cbrs_asset_id`) REFERENCES `cbrs_assets` (`id`),
  CONSTRAINT `cbrs_asset_to_tax_lots_cbrs_input_id` FOREIGN KEY (`cbrs_input_id`) REFERENCES `cbrs_inputs` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `cbrs_assets`
--

DROP TABLE IF EXISTS `cbrs_assets`;
CREATE TABLE `cbrs_assets` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `type` varchar(255) NOT NULL,
  `isin` varchar(255) NOT NULL,
  `acats_sequence_number` varchar(255) DEFAULT NULL,
  `category` varchar(255) NOT NULL,
  `state` varchar(255) NOT NULL,
  `quantity` decimal(20,8) NOT NULL,
  `created_at` datetime NOT NULL,
  `cbrs_transfer_id` bigint(20) NOT NULL,
  `requested` tinyint(1) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `cbrs_assets_created_at` (`created_at`),
  KEY `cbrs_assets_type_state` (`type`,`state`),
  KEY `cbrs_assets_cbrs_transfer_id` (`cbrs_transfer_id`),
  CONSTRAINT `cbrs_assets_cbrs_transfer_id` FOREIGN KEY (`cbrs_transfer_id`) REFERENCES `cbrs_transfers` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `cbrs_contraparties`
--

DROP TABLE IF EXISTS `cbrs_contraparties`;
CREATE TABLE `cbrs_contraparties` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `cbrs_account_number` varchar(255) NOT NULL,
  `cbrs_account_name` varchar(255) NOT NULL,
  `active_firm_number` varchar(255) NOT NULL,
  `active_firm_type` varchar(255) NOT NULL,
  `effective_date` date NOT NULL,
  `rejects_retirement_accounts` tinyint(1) NOT NULL,
  `search_firm_number` varchar(255) DEFAULT NULL,
  `search_firm_type` varchar(255) NOT NULL,
  `active` tinyint(1) NOT NULL,
  `fed_number` varchar(255) DEFAULT NULL,
  `fed_short_name` varchar(255) DEFAULT NULL,
  `fresh` tinyint(1) NOT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  `file_date` date NOT NULL DEFAULT '2016-05-07',
  PRIMARY KEY (`id`),
  KEY `cbrs_contraparties_updated_at` (`updated_at`),
  KEY `cbrs_contraparties_search_firm_number` (`search_firm_number`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `cbrs_files`
--

DROP TABLE IF EXISTS `cbrs_files`;
CREATE TABLE `cbrs_files` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `type` varchar(255) NOT NULL,
  `directory` varchar(255) NOT NULL,
  `filename` varchar(255) NOT NULL,
  `created_at` datetime NOT NULL,
  `state` varchar(255) NOT NULL,
  `auto_route_env` varchar(255) DEFAULT NULL,
  `rbc_env` varchar(255) DEFAULT NULL,
  `batch_counter` smallint(6) DEFAULT NULL,
  `header_date` date DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `cbrs_files_directory_filename` (`directory`,`filename`),
  KEY `cbrs_files_created_at` (`created_at`),
  KEY `cbrs_files_type_state` (`type`,`state`),
  KEY `cbrs_files_header_date_batch_counter` (`header_date`,`batch_counter`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `cbrs_inputs`
--

DROP TABLE IF EXISTS `cbrs_inputs`;
CREATE TABLE `cbrs_inputs` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `type` varchar(255) NOT NULL,
  `state` varchar(255) NOT NULL,
  `cbrs_file_id` bigint(20) DEFAULT NULL,
  `cbrs_asset_id` bigint(20) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `cost_basis_endpoint` varchar(255) DEFAULT 'CBRS',
  PRIMARY KEY (`id`),
  KEY `cbrs_inputs_created_at` (`created_at`),
  KEY `cbrs_inputs_state_cost_basis_endpoint` (`state`,`cost_basis_endpoint`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `cbrs_outputs`
--

DROP TABLE IF EXISTS `cbrs_outputs`;
CREATE TABLE `cbrs_outputs` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `type` varchar(255) NOT NULL,
  `content` longtext NOT NULL,
  `created_at` datetime NOT NULL,
  `state` varchar(255) NOT NULL,
  `cbrs_file_id` bigint(20) NOT NULL,
  `cbrs_transfer_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `cbrs_outputs_created_at` (`created_at`),
  KEY `cbrs_outputs_state` (`state`),
  KEY `cbrs_outputs_cbrs_file_id` (`cbrs_file_id`),
  KEY `cbrs_outputs_cbrs_transfer_id` (`cbrs_transfer_id`),
  CONSTRAINT `cbrs_outputs_cbrs_file_id` FOREIGN KEY (`cbrs_file_id`) REFERENCES `cbrs_files` (`id`),
  CONSTRAINT `cbrs_outputs_cbrs_transfer_id` FOREIGN KEY (`cbrs_transfer_id`) REFERENCES `cbrs_transfers` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `cbrs_reviewed_resolution_issues`
--

DROP TABLE IF EXISTS `cbrs_reviewed_resolution_issues`;
CREATE TABLE `cbrs_reviewed_resolution_issues` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `cbrs_asset_id` bigint(20) NOT NULL,
  `accountid` bigint(20) DEFAULT NULL,
  `reviewed_at` datetime NOT NULL,
  `reviewed_by` varchar(255) NOT NULL,
  `atlas_run_effective_date` date NOT NULL,
  `resolved_symbol` varchar(255) DEFAULT NULL,
  `resolved_cusip` varchar(255) DEFAULT NULL,
  `deleted` tinyint(1) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `cbrs_reviewed_resolution_issues_date_asset` (`atlas_run_effective_date`,`cbrs_asset_id`),
  KEY `cbrs_reviewed_resolution_issues_cbrs_asset_id` (`cbrs_asset_id`),
  CONSTRAINT `cbrs_reviewed_resolution_issues_cbrs_asset_id` FOREIGN KEY (`cbrs_asset_id`) REFERENCES `cbrs_assets` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `cbrs_tax_lots`
--

DROP TABLE IF EXISTS `cbrs_tax_lots`;
CREATE TABLE `cbrs_tax_lots` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `type` varchar(255) NOT NULL,
  `quantity` decimal(20,8) NOT NULL,
  `open_date` date DEFAULT NULL,
  `wash_sale_disallowed_loss` decimal(14,2) NOT NULL,
  `zero_basis` varchar(255) DEFAULT NULL,
  `noncovered_pending` varchar(255) DEFAULT NULL,
  `wash_sale` tinyint(1) NOT NULL,
  `cbrs_asset_to_tax_lot_id` bigint(20) NOT NULL,
  `wash_sale_adjusted_open_date` date DEFAULT NULL,
  `cost` decimal(14,2) DEFAULT '0.00',
  PRIMARY KEY (`id`),
  KEY `cbrs_tax_lots_cbrs_asset_to_tax_lot_id` (`cbrs_asset_to_tax_lot_id`),
  CONSTRAINT `cbrs_tax_lots_cbrs_asset_to_tax_lot_id` FOREIGN KEY (`cbrs_asset_to_tax_lot_id`) REFERENCES `cbrs_asset_to_tax_lots` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `cbrs_transfers`
--

DROP TABLE IF EXISTS `cbrs_transfers`;
CREATE TABLE `cbrs_transfers` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `type` varchar(255) NOT NULL,
  `transaction_type` varchar(255) NOT NULL,
  `transfer_control_number` varchar(255) NOT NULL,
  `our_firm_type` varchar(255) NOT NULL,
  `our_firm_number` varchar(255) NOT NULL,
  `contra_firm_type` varchar(255) NOT NULL,
  `contra_firm_number` varchar(255) NOT NULL,
  `our_account_number` varchar(255) NOT NULL,
  `contra_account_number` varchar(255) NOT NULL,
  `state` varchar(255) DEFAULT NULL,
  `settlement_date` date NOT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `cbrs_transfers_created_at` (`created_at`),
  KEY `cbrs_transfers_transfer_control_number` (`transfer_control_number`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `check_deposits`
--

DROP TABLE IF EXISTS `check_deposits`;
CREATE TABLE `check_deposits` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `release_date` date DEFAULT NULL,
  `inbound_check` text NOT NULL,
  `state` varchar(255) NOT NULL DEFAULT 'ESCROWING',
  `check_identifying_hash` varchar(128) DEFAULT NULL,
  `wells_check_deposit_id` bigint(20) DEFAULT NULL,
  `classification` varchar(255) DEFAULT 'UNKNOWN',
  PRIMARY KEY (`id`),
  KEY `check_deposits_id` (`id`),
  KEY `check_deposits_check_identifying_hash` (`check_identifying_hash`),
  KEY `check_deposits_wells_check_deposit_id` (`wells_check_deposit_id`),
  KEY `check_deposits_classification` (`classification`),
  CONSTRAINT `check_deposits_id` FOREIGN KEY (`id`) REFERENCES `bank_transfers` (`id`),
  CONSTRAINT `check_deposits_wells_check_deposit_id` FOREIGN KEY (`wells_check_deposit_id`) REFERENCES `wells_check_deposits` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `check_numbers`
--

DROP TABLE IF EXISTS `check_numbers`;
CREATE TABLE `check_numbers` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_at` datetime NOT NULL,
  `check_withdrawal_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `check_numbers_check_withdrawal_id` (`check_withdrawal_id`),
  CONSTRAINT `check_numbers_check_withdrawal_id` FOREIGN KEY (`check_withdrawal_id`) REFERENCES `check_withdrawals` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `check_withdrawals`
--

DROP TABLE IF EXISTS `check_withdrawals`;
CREATE TABLE `check_withdrawals` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `check_transfer` text NOT NULL,
  `state` varchar(255) NOT NULL,
  `wells_check_id` bigint(20) DEFAULT NULL,
  `check_number_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `check_withdrawals_check_number_id` (`check_number_id`),
  KEY `check_withdrawals_wells_check_id` (`wells_check_id`),
  KEY `check_withdrawals_state` (`state`),
  CONSTRAINT `check_withdrawals_check_number_id` FOREIGN KEY (`check_number_id`) REFERENCES `check_numbers` (`id`),
  CONSTRAINT `check_withdrawals_wells_check_id` FOREIGN KEY (`wells_check_id`) REFERENCES `wells_checks` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `conversion_ira_contributions`
--

DROP TABLE IF EXISTS `conversion_ira_contributions`;
CREATE TABLE `conversion_ira_contributions` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `account_name` varchar(20) NOT NULL,
  `adp_security_number` varchar(255) NOT NULL,
  `amount` decimal(20,2) NOT NULL,
  `account_id` bigint(20) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `conversion_ira_contributions_adp_security_number` (`adp_security_number`),
  KEY `conversion_ira_contributions_account_name` (`account_name`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `cross_account_transfers`
--

DROP TABLE IF EXISTS `cross_account_transfers`;
CREATE TABLE `cross_account_transfers` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `full_transfer` tinyint(1) NOT NULL,
  `amount` decimal(20,2) DEFAULT NULL,
  `state` varchar(255) NOT NULL,
  `initiator` varchar(255) NOT NULL,
  `source_bucket_id` bigint(20) NOT NULL,
  `target_bucket_id` bigint(20) NOT NULL,
  `ira_transfer_context_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `cross_account_transfers_source_bucket_id` (`source_bucket_id`),
  KEY `cross_account_transfers_target_bucket_id` (`target_bucket_id`),
  KEY `cross_account_transfers_ira_transfer_context_id` (`ira_transfer_context_id`),
  CONSTRAINT `cross_account_transfers_ira_transfer_context_id` FOREIGN KEY (`ira_transfer_context_id`) REFERENCES `ira_transfer_contexts` (`id`),
  CONSTRAINT `cross_account_transfers_source_bucket_id` FOREIGN KEY (`source_bucket_id`) REFERENCES `buckets` (`id`),
  CONSTRAINT `cross_account_transfers_target_bucket_id` FOREIGN KEY (`target_bucket_id`) REFERENCES `buckets` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `daily_transfer_totals`
--

DROP TABLE IF EXISTS `daily_transfer_totals`;
CREATE TABLE `daily_transfer_totals` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `transfer_limit_type` varchar(255) NOT NULL,
  `account_custodian` varchar(255) NOT NULL,
  `effective_date` date NOT NULL,
  `total_amount` decimal(20,2) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `daily_transfer_totals_type_custodian_date` (`transfer_limit_type`,`account_custodian`,`effective_date`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `datatrak_batch_counts`
--

DROP TABLE IF EXISTS `datatrak_batch_counts`;
CREATE TABLE `datatrak_batch_counts` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `sys_id` varchar(255) NOT NULL,
  `submission_date` date NOT NULL,
  `counter` smallint(6) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `datatrak_batch_counts_submission_date_sys_id` (`submission_date`,`sys_id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `deposit_bank_transfers`
--

DROP TABLE IF EXISTS `deposit_bank_transfers`;
CREATE TABLE `deposit_bank_transfers` (
  `deposit_id` bigint(20) NOT NULL,
  `bank_transfer_id` bigint(20) NOT NULL,
  PRIMARY KEY (`deposit_id`,`bank_transfer_id`),
  KEY `deposit_bank_transfers_bank_transfer_id` (`bank_transfer_id`),
  CONSTRAINT `deposit_bank_transfers_bank_transfer_id` FOREIGN KEY (`bank_transfer_id`) REFERENCES `bank_transfers` (`id`),
  CONSTRAINT `deposit_bank_transfers_deposit_id` FOREIGN KEY (`deposit_id`) REFERENCES `deposits` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Table structure for table `deposit_events`
--

DROP TABLE IF EXISTS `deposit_events`;
CREATE TABLE `deposit_events` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `type` varchar(255) NOT NULL,
  `created_at` datetime NOT NULL,
  `effective_date` date NOT NULL,
  `amount` decimal(20,2) NOT NULL,
  `deposit_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `deposit_events_deposit_id` (`deposit_id`),
  KEY `deposit_events_created_at` (`created_at`),
  KEY `deposit_events_type` (`type`),
  KEY `deposit_events_effective_date` (`effective_date`),
  CONSTRAINT `___deposit_events_deposit_id` FOREIGN KEY (`deposit_id`) REFERENCES `deposits` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `deposit_plan_estimates`
--

DROP TABLE IF EXISTS `deposit_plan_estimates`;
CREATE TABLE `deposit_plan_estimates` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `deposit_plan_id` bigint(20) NOT NULL,
  `evaluated_at` datetime NOT NULL,
  `estimated_transfers` text NOT NULL,
  PRIMARY KEY (`id`),
  KEY `deposit_plan_estimates_deposit_plan_id` (`deposit_plan_id`),
  CONSTRAINT `deposit_plan_estimates_deposit_plan_id` FOREIGN KEY (`deposit_plan_id`) REFERENCES `deposit_plans` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `deposit_plan_results`
--

DROP TABLE IF EXISTS `deposit_plan_results`;
CREATE TABLE `deposit_plan_results` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `deposit_plan_id` bigint(20) NOT NULL,
  `evaluated_at` datetime NOT NULL,
  `result` varchar(255) NOT NULL,
  `details` text NOT NULL,
  PRIMARY KEY (`id`),
  KEY `deposit_plan_results_deposit_plan_id` (`deposit_plan_id`),
  CONSTRAINT `deposit_plan_results_deposit_plan_id` FOREIGN KEY (`deposit_plan_id`) REFERENCES `deposit_plans` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Table structure for table `deposit_plans`
--

DROP TABLE IF EXISTS `deposit_plans`;
CREATE TABLE `deposit_plans` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `version` int(11) NOT NULL,
  `userid` bigint(20) NOT NULL,
  `created_at` datetime NOT NULL,
  `state` varchar(255) NOT NULL,
  `start_date` date NOT NULL,
  `next_deposit_date` date NOT NULL,
  `frequency` varchar(255) NOT NULL,
  `ach_relationship_id` bigint(20) NOT NULL,
  `rules` text NOT NULL,
  PRIMARY KEY (`id`),
  KEY `deposit_plans_userid` (`userid`),
  KEY `deposit_plans_state_next_deposit_date` (`state`,`next_deposit_date`),
  KEY `deposit_plans_ach_relationship_id` (`ach_relationship_id`),
  CONSTRAINT `deposit_plans_ach_relationship_id` FOREIGN KEY (`ach_relationship_id`) REFERENCES `ach_relationships` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `deposit_returning_bank_transfers`
--

DROP TABLE IF EXISTS `deposit_returning_bank_transfers`;
CREATE TABLE `deposit_returning_bank_transfers` (
  `deposit_id` bigint(20) NOT NULL,
  `bank_transfer_id` bigint(20) NOT NULL,
  PRIMARY KEY (`deposit_id`,`bank_transfer_id`),
  KEY `deposit_returning_bank_transfers_bank_transfer_id` (`bank_transfer_id`),
  CONSTRAINT `deposit_returning_bank_transfers_bank_transfer_id` FOREIGN KEY (`bank_transfer_id`) REFERENCES `bank_transfers` (`id`),
  CONSTRAINT `deposit_returning_bank_transfers_deposit_id` FOREIGN KEY (`deposit_id`) REFERENCES `deposits` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Table structure for table `deposit_to_cash_sweep_settlement_wire_batch`
--

DROP TABLE IF EXISTS `deposit_to_cash_sweep_settlement_wire_batch`;
CREATE TABLE `deposit_to_cash_sweep_settlement_wire_batch` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `version` int(11) NOT NULL DEFAULT '0',
  `deposit_id` bigint(20) NOT NULL,
  `cash_sweep_settlement_wire_batch_id` bigint(20) NOT NULL,
  `amount` decimal(20,2) NOT NULL,
  `entity_direction` varchar(255) NOT NULL DEFAULT 'FORWARD',
  `state` varchar(255) NOT NULL DEFAULT 'COMPLETED',
  PRIMARY KEY (`id`),
  KEY `deposit_to_cash_sweep_settlement_wire_batch_deposit_id` (`deposit_id`),
  KEY `deposit_to_cash_sweep_settlement_wire_batch_batch_id` (`cash_sweep_settlement_wire_batch_id`),
  CONSTRAINT `deposit_to_cash_sweep_settlement_wire_batch_batch_id` FOREIGN KEY (`cash_sweep_settlement_wire_batch_id`) REFERENCES `cash_sweep_settlement_wire_batches` (`id`),
  CONSTRAINT `deposit_to_cash_sweep_settlement_wire_batch_deposit_id` FOREIGN KEY (`deposit_id`) REFERENCES `deposits` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `deposit_to_incoming_brokerage_wire`
--

DROP TABLE IF EXISTS `deposit_to_incoming_brokerage_wire`;
CREATE TABLE `deposit_to_incoming_brokerage_wire` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `deposit_id` bigint(20) NOT NULL,
  `incoming_brokerage_wire_id` bigint(20) NOT NULL,
  `amount` decimal(20,2) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `deposit_to_incoming_brokerage_wire_deposit_id` (`deposit_id`),
  KEY `deposit_to_incoming_brokerage_wire_incoming_brokerage_wire_id` (`incoming_brokerage_wire_id`),
  CONSTRAINT `deposit_to_incoming_brokerage_wire_deposit_id` FOREIGN KEY (`deposit_id`) REFERENCES `deposits` (`id`),
  CONSTRAINT `deposit_to_incoming_brokerage_wire_incoming_brokerage_wire_id` FOREIGN KEY (`incoming_brokerage_wire_id`) REFERENCES `incoming_brokerage_wires` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `deposit_to_intra_bank_transfer`
--

DROP TABLE IF EXISTS `deposit_to_intra_bank_transfer`;
CREATE TABLE `deposit_to_intra_bank_transfer` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `version` int(11) DEFAULT '0',
  `deposit_id` bigint(20) NOT NULL,
  `intra_bank_transfer_id` bigint(20) NOT NULL,
  `amount` decimal(20,2) NOT NULL,
  `entity_direction` varchar(255) NOT NULL DEFAULT 'FORWARD',
  `state` varchar(255) NOT NULL DEFAULT 'COMPLETED',
  `bank_transaction_queued_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `deposit_to_intra_bank_transfer_deposit_id` (`deposit_id`),
  KEY `deposit_to_intra_bank_transfer_intra_bank_transfer_id` (`intra_bank_transfer_id`),
  CONSTRAINT `deposit_to_intra_bank_transfer_deposit_id` FOREIGN KEY (`deposit_id`) REFERENCES `deposits` (`id`),
  CONSTRAINT `deposit_to_intra_bank_transfer_intra_bank_transfer_id` FOREIGN KEY (`intra_bank_transfer_id`) REFERENCES `intra_bank_transfers` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `deposit_to_outgoing_brokerage_wire`
--

DROP TABLE IF EXISTS `deposit_to_outgoing_brokerage_wire`;
CREATE TABLE `deposit_to_outgoing_brokerage_wire` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `deposit_id` bigint(20) NOT NULL,
  `outgoing_brokerage_wire_id` bigint(20) NOT NULL,
  `amount` decimal(20,2) NOT NULL,
  `bank_transaction_queued_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `deposit_to_outgoing_brokerage_wire_deposit_id` (`deposit_id`),
  KEY `deposit_to_outgoing_brokerage_wire_outgoing_brokerage_wire_id` (`outgoing_brokerage_wire_id`),
  CONSTRAINT `deposit_to_outgoing_brokerage_wire_deposit_id` FOREIGN KEY (`deposit_id`) REFERENCES `deposits` (`id`),
  CONSTRAINT `deposit_to_outgoing_brokerage_wire_outgoing_brokerage_wire_id` FOREIGN KEY (`outgoing_brokerage_wire_id`) REFERENCES `outgoing_brokerage_wires` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `deposits`
--

DROP TABLE IF EXISTS `deposits`;
CREATE TABLE `deposits` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `version` bigint(20) NOT NULL,
  `created_at` datetime NOT NULL,
  `brokerage_account_id` bigint(20) DEFAULT NULL,
  `amount` decimal(20,2) NOT NULL,
  `transfer_instructions` varchar(255) DEFAULT NULL,
  `ach_relationship_id` bigint(20) DEFAULT NULL,
  `release_date` date DEFAULT NULL,
  `recurring_deposit_id` bigint(20) DEFAULT NULL,
  `recurring_effective_date` date DEFAULT NULL,
  `unscheduled_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `scheduled_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `received_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `released_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `scheduled_to_broker_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `sent_to_broker_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `completed_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `canceled_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `returned_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `returned_covered_from_bank_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `returned_from_broker_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `returned_by_contra_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `returning_to_client_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `scheduled_return_to_client_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `returned_to_client_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `deposit_plan_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `deposits_recurring_deposit_id_recurring_effective_date` (`recurring_deposit_id`,`recurring_effective_date`),
  KEY `deposits_brokerage_account_id` (`brokerage_account_id`),
  KEY `deposits_recurring_deposit_id` (`recurring_deposit_id`),
  KEY `deposits_ach_relationship_fkey` (`ach_relationship_id`),
  KEY `deposits_deposit_plan_id` (`deposit_plan_id`),
  CONSTRAINT `deposits_deposit_plan_id` FOREIGN KEY (`deposit_plan_id`) REFERENCES `deposit_plans` (`id`),
  CONSTRAINT `__deposits_ach_relationship_fkey` FOREIGN KEY (`ach_relationship_id`) REFERENCES `ach_relationships` (`id`),
  CONSTRAINT `__deposits_brokerage_account_id` FOREIGN KEY (`brokerage_account_id`) REFERENCES `brokerage_accounts` (`id`),
  CONSTRAINT `__deposits_recurring_deposit_id` FOREIGN KEY (`recurring_deposit_id`) REFERENCES `recurring_deposits` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `drawdown_wires`
--

DROP TABLE IF EXISTS `drawdown_wires`;
CREATE TABLE `drawdown_wires` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `wire` text NOT NULL,
  `state` varchar(255) NOT NULL,
  `wells_wire_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `drawdown_wires_state` (`state`),
  KEY `drawdown_wires_wells_wire_id` (`wells_wire_id`),
  CONSTRAINT `drawdown_wires_wells_wire_id` FOREIGN KEY (`wells_wire_id`) REFERENCES `wells_wires` (`id`),
  CONSTRAINT `drawdown_wire_id` FOREIGN KEY (`id`) REFERENCES `bank_transfers` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `dtc_acats`
--

DROP TABLE IF EXISTS `dtc_acats`;
CREATE TABLE `dtc_acats` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_at` datetime NOT NULL,
  `acats_transfer_id` bigint(20) DEFAULT NULL,
  `acats_transfer_type` varchar(255) NOT NULL,
  `dtc_acats_state` varchar(255) NOT NULL,
  `acats_control_number` varchar(20) DEFAULT NULL,
  `contra_clearing_number` varchar(10) DEFAULT NULL,
  `wf_initiated` tinyint(1) DEFAULT '0',
  `settlement_date` date DEFAULT NULL,
  `settled_market_value` decimal(14,2) DEFAULT NULL,
  `transfer_rejection_type` varchar(255) DEFAULT 'NOT_REJECTED',
  `environment` varchar(255) DEFAULT 'RBC_TEST',
  PRIMARY KEY (`id`),
  KEY `dtc_acats_acats_transfer_id` (`acats_transfer_id`),
  KEY `dtc_acats_acats_control_number` (`acats_control_number`),
  KEY `dtc_acats_settlement_date` (`settlement_date`),
  KEY `dtc_acats_state` (`dtc_acats_state`),
  KEY `dtc_acats_created_at` (`created_at`),
  CONSTRAINT `dtc_acats_acats_transfer_id` FOREIGN KEY (`acats_transfer_id`) REFERENCES `acats_transfers` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `dtc_acats_assets`
--

DROP TABLE IF EXISTS `dtc_acats_assets`;
CREATE TABLE `dtc_acats_assets` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `dtc_acats_id` bigint(20) NOT NULL,
  `asset_sequence_number` int(11) DEFAULT NULL,
  `asset_type` varchar(255) NOT NULL,
  `asset_category` varchar(255) DEFAULT NULL,
  `isin` varchar(255) DEFAULT NULL,
  `cusip` varchar(255) DEFAULT NULL,
  `partial_or_all` varchar(255) NOT NULL,
  `value` decimal(20,8) DEFAULT NULL,
  `state` varchar(255) NOT NULL,
  `margin` tinyint(1) DEFAULT '0',
  `settled_market_value` decimal(14,2) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `dtc_acats_assets_dtc_acats_id` (`dtc_acats_id`),
  KEY `dtc_acats_assets_isin` (`isin`),
  KEY `dtc_acats_assets_cusip` (`cusip`),
  CONSTRAINT `dtc_acats_assets_dtc_acats_id` FOREIGN KEY (`dtc_acats_id`) REFERENCES `dtc_acats` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `dtc_transfer_instructions`
--

DROP TABLE IF EXISTS `dtc_transfer_instructions`;
CREATE TABLE `dtc_transfer_instructions` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `dtc_transfer_id` bigint(20) NOT NULL,
  `instrumentid` bigint(10) NOT NULL,
  `symbol` varchar(255) NOT NULL,
  `quantity` decimal(20,8) DEFAULT NULL,
  `price` decimal(20,8) DEFAULT NULL,
  `lot_open_date` date DEFAULT NULL,
  `state` varchar(255) NOT NULL DEFAULT 'CREATED',
  PRIMARY KEY (`id`),
  KEY `dtc_transfer_instructions_dtc_transfer_id` (`dtc_transfer_id`),
  KEY `dtc_transfer_instructions_instrumentid` (`instrumentid`),
  CONSTRAINT `dtc_transfer_instructions_dtc_transfer_id` FOREIGN KEY (`dtc_transfer_id`) REFERENCES `dtc_transfers` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `dtc_transfers`
--

DROP TABLE IF EXISTS `dtc_transfers`;
CREATE TABLE `dtc_transfers` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_at` datetime NOT NULL,
  `state` varchar(255) NOT NULL,
  `requested_at` datetime DEFAULT NULL,
  `accountid` bigint(20) DEFAULT NULL,
  `account_number` varchar(255) DEFAULT NULL,
  `account_info` mediumtext,
  `contraparty` mediumtext NOT NULL,
  `initiator` varchar(255) NOT NULL,
  `rejected_edited` tinyint(1) NOT NULL DEFAULT '0',
  `comments` tinytext,
  `transfer_type` varchar(255) NOT NULL,
  `account_transfer_id` bigint(20) DEFAULT NULL,
  `has_mutual_funds` tinyint(1) NOT NULL DEFAULT '0',
  `settlement_date` date DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `dtc_transfers_accountid` (`accountid`),
  KEY `dtc_transfers_state` (`state`),
  KEY `dtc_transfers_account_number` (`account_number`),
  KEY `dtc_transfers_account_transfer_id` (`account_transfer_id`),
  CONSTRAINT `dtc_transfers_account_transfer_id` FOREIGN KEY (`account_transfer_id`) REFERENCES `account_transfers` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `file_transfer_totals`
--

DROP TABLE IF EXISTS `file_transfer_totals`;
CREATE TABLE `file_transfer_totals` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `transfer_limit_type` varchar(255) NOT NULL,
  `total_amount` decimal(20,2) NOT NULL,
  `requested_at` datetime NOT NULL,
  `effective_date` date NOT NULL,
  `daily_transfer_total_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `file_transfer_totals_daily_transfer_total_id` (`daily_transfer_total_id`),
  CONSTRAINT `file_transfer_totals_daily_transfer_total_id` FOREIGN KEY (`daily_transfer_total_id`) REFERENCES `daily_transfer_totals` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `funding_blocks`
--

DROP TABLE IF EXISTS `funding_blocks`;
CREATE TABLE `funding_blocks` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `type` varchar(255) NOT NULL,
  `deposit_id` bigint(20) DEFAULT NULL,
  `withdrawal_id` bigint(20) DEFAULT NULL,
  `category` varchar(255) NOT NULL,
  `reason` varchar(255) NOT NULL,
  `requester` varchar(255) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `loan_id` bigint(20) DEFAULT NULL,
  `cross_account_transfer_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `funding_blocks_deposit_id` (`deposit_id`),
  KEY `funding_blocks_withdrawal_id` (`withdrawal_id`),
  KEY `fkey_funding_blocks_loan_id` (`loan_id`),
  KEY `funding_blocks_cross_account_transfer_id` (`cross_account_transfer_id`),
  CONSTRAINT `fkey_funding_blocks_deposit_id` FOREIGN KEY (`deposit_id`) REFERENCES `deposits` (`id`),
  CONSTRAINT `fkey_funding_blocks_loan_id` FOREIGN KEY (`loan_id`) REFERENCES `loans` (`id`),
  CONSTRAINT `fkey_funding_blocks_withdrawal_id` FOREIGN KEY (`withdrawal_id`) REFERENCES `withdrawals` (`id`),
  CONSTRAINT `funding_blocks_cross_account_transfer_id` FOREIGN KEY (`cross_account_transfer_id`) REFERENCES `cross_account_transfers` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `green_dot_bank_accounts`
--

DROP TABLE IF EXISTS `green_dot_bank_accounts`;
CREATE TABLE `green_dot_bank_accounts` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `version` bigint(20) NOT NULL DEFAULT '0',
  `created_at` datetime NOT NULL,
  `brokerage_account_id` bigint(20) DEFAULT NULL,
  `green_dot_account_identifier` varchar(255) NOT NULL,
  `green_dot_user_identifier` varchar(255) NOT NULL,
  `bank_account_details` mediumtext NOT NULL,
  `accountid` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `green_dot_bank_accounts_green_dot_account_identifier` (`green_dot_account_identifier`),
  UNIQUE KEY `green_dot_bank_accounts_accountid` (`accountid`),
  UNIQUE KEY `green_dot_bank_accounts_brokerage_account_id` (`brokerage_account_id`),
  KEY `green_dot_bank_accounts_created_at` (`created_at`),
  CONSTRAINT `__green_dot_bank_accounts_brokerage_account_id` FOREIGN KEY (`brokerage_account_id`) REFERENCES `brokerage_accounts` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `hack_deposit_plans`
--

DROP TABLE IF EXISTS `hack_deposit_plans`;
CREATE TABLE `hack_deposit_plans` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `userid` bigint(20) NOT NULL,
  `state` varchar(255) NOT NULL,
  `external_accountid` bigint(20) NOT NULL,
  `frequency` varchar(255) DEFAULT NULL,
  `start_date` date DEFAULT NULL,
  `rules` text NOT NULL,
  PRIMARY KEY (`id`),
  KEY `hack_deposit_plans_userid_state` (`userid`,`state`),
  KEY `hack_deposit_plans_state` (`state`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `incoming_brokerage_wires`
--

DROP TABLE IF EXISTS `incoming_brokerage_wires`;
CREATE TABLE `incoming_brokerage_wires` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_at` datetime NOT NULL,
  `amount` decimal(20,2) NOT NULL,
  `brokerage` varchar(255) NOT NULL,
  `wire_reference_number` varchar(255) DEFAULT NULL,
  `effective_date` date DEFAULT NULL,
  `state` varchar(255) NOT NULL DEFAULT 'RECEIVED',
  `reason` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `incoming_events`
--

DROP TABLE IF EXISTS `incoming_events`;
CREATE TABLE `incoming_events` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `version` bigint(20) NOT NULL DEFAULT '0',
  `eventid` char(36) NOT NULL,
  `handler` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `created_at` datetime NOT NULL,
  `first_polled_at` datetime DEFAULT NULL,
  `last_polled_at` datetime DEFAULT NULL,
  `first_failed_at` datetime DEFAULT NULL,
  `last_failed_at` datetime DEFAULT NULL,
  `failed_at` datetime DEFAULT NULL,
  `succeeded_at` datetime DEFAULT NULL,
  `failed_attempts` int(11) NOT NULL DEFAULT '0',
  `dropped` tinyint(1) NOT NULL DEFAULT '0',
  `in_flight` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `incoming_events_eventid_handler` (`eventid`,`handler`),
  KEY `incoming_events_succeeded_at` (`succeeded_at`),
  KEY `incoming_events_failed_at_failed_attempts_dropped` (`failed_at`,`failed_attempts`,`dropped`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `intra_bank_transfers`
--

DROP TABLE IF EXISTS `intra_bank_transfers`;
CREATE TABLE `intra_bank_transfers` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `version` int(11) NOT NULL DEFAULT '0',
  `created_at` datetime NOT NULL,
  `effective_date` date NOT NULL,
  `amount` decimal(20,2) NOT NULL,
  `reason` varchar(255) NOT NULL,
  `state` varchar(255) NOT NULL,
  `wire_withdrawal_id` bigint(20) DEFAULT NULL,
  `wire_direct_deposit_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `intra_bank_transfers_wire_withdrawal_id` (`wire_withdrawal_id`),
  KEY `intra_bank_transfers_wire_direct_deposit_id` (`wire_direct_deposit_id`),
  CONSTRAINT `intra_bank_transfers_wire_direct_deposit_id` FOREIGN KEY (`wire_direct_deposit_id`) REFERENCES `wire_direct_deposits` (`id`),
  CONSTRAINT `intra_bank_transfers_wire_withdrawal_id` FOREIGN KEY (`wire_withdrawal_id`) REFERENCES `wire_withdrawals` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `intra_day_bai2_ach_tally`
--

DROP TABLE IF EXISTS `intra_day_bai2_ach_tally`;
CREATE TABLE `intra_day_bai2_ach_tally` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_at` datetime NOT NULL,
  `deposit_amount` decimal(20,2) NOT NULL,
  `withdrawal_amount` decimal(20,2) NOT NULL,
  `wells_file_id` bigint(20) NOT NULL,
  `effective_date` date NOT NULL,
  `account_number` varchar(255) NOT NULL DEFAULT '**********',
  PRIMARY KEY (`id`),
  UNIQUE KEY `wells_file_id` (`wells_file_id`,`account_number`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `ira_contribution_totals`
--

DROP TABLE IF EXISTS `ira_contribution_totals`;
CREATE TABLE `ira_contribution_totals` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `version` bigint(20) DEFAULT '0',
  `amount` decimal(20,2) NOT NULL,
  `contribution_type` varchar(255) NOT NULL,
  `contribution_year` smallint(6) DEFAULT NULL,
  `brokerage_account_id` bigint(20) NOT NULL,
  `updated_at` datetime NOT NULL,
  `pending_amount` decimal(20,2) DEFAULT '0.00',
  `completed_amount` decimal(20,2) DEFAULT '0.00',
  PRIMARY KEY (`id`),
  UNIQUE KEY `contribution_type` (`contribution_type`,`contribution_year`,`brokerage_account_id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `ira_contributions`
--

DROP TABLE IF EXISTS `ira_contributions`;
CREATE TABLE `ira_contributions` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `amount` decimal(20,2) NOT NULL,
  `contribution_type` varchar(255) NOT NULL,
  `contribution_year` smallint(6) NOT NULL,
  `review_state` varchar(255) NOT NULL,
  `reviewer` varchar(255) DEFAULT NULL,
  `ira_deposit_id` bigint(20) DEFAULT NULL,
  `brokerage_account_id` bigint(20) NOT NULL,
  `ira_contribution_total_id` bigint(20) DEFAULT NULL,
  `type` varchar(255) NOT NULL DEFAULT 'omni',
  `migrated_contribution_totalid` bigint(20) DEFAULT NULL,
  `conversion_ira_contribution_id` bigint(20) DEFAULT NULL,
  `postponed_contribution_reason` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `ira_deposit_id` (`ira_deposit_id`),
  KEY `ira_contributions_contribution_total_id` (`ira_contribution_total_id`),
  KEY `ira_contributions_conversion_ira_contribution_id` (`conversion_ira_contribution_id`),
  KEY `ira_contributions_brokerage_account_id` (`brokerage_account_id`),
  CONSTRAINT `_ira_contributions_contribution_total_id` FOREIGN KEY (`ira_contribution_total_id`) REFERENCES `ira_contribution_totals` (`id`),
  CONSTRAINT `_ira_contributions_conversion_ira_contribution_id` FOREIGN KEY (`conversion_ira_contribution_id`) REFERENCES `conversion_ira_contributions` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `ira_transaction_adjustments`
--

DROP TABLE IF EXISTS `ira_transaction_adjustments`;
CREATE TABLE `ira_transaction_adjustments` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `type` varchar(255) NOT NULL,
  `created_at` datetime NOT NULL,
  `notes` varchar(255) NOT NULL,
  `withdrawal_id` bigint(20) DEFAULT NULL,
  `deposit_id` bigint(20) DEFAULT NULL,
  `effective_date` date DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `fkey_ira_transaction_adjustments_withdrawal_id` (`withdrawal_id`),
  KEY `fkey_ira_transaction_adjustments_deposits_id` (`deposit_id`),
  CONSTRAINT `fkey_ira_transaction_adjustments_deposits_id` FOREIGN KEY (`deposit_id`) REFERENCES `omni_ira_deposits` (`id`),
  CONSTRAINT `fkey_ira_transaction_adjustments_withdrawal_id` FOREIGN KEY (`withdrawal_id`) REFERENCES `omni_ira_withdrawals` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `ira_transfer_contexts`
--

DROP TABLE IF EXISTS `ira_transfer_contexts`;
CREATE TABLE `ira_transfer_contexts` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `type` varchar(255) NOT NULL,
  `tax_year` smallint(6) NOT NULL,
  `date_completed` date DEFAULT NULL,
  `ira_distribution_instructions` text,
  PRIMARY KEY (`id`),
  KEY `ira_transfer_contexts_tax_year` (`tax_year`),
  KEY `ira_transfer_contexts_date_completed` (`date_completed`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Table structure for table `liquidation_loan_repayment_events`
--

DROP TABLE IF EXISTS `liquidation_loan_repayment_events`;
CREATE TABLE `liquidation_loan_repayment_events` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `type` varchar(255) NOT NULL,
  `created_at` datetime NOT NULL,
  `amount` decimal(20,2) NOT NULL,
  `liquidation_loan_repayment_id` bigint(20) NOT NULL,
  `effective_date` date NOT NULL,
  PRIMARY KEY (`id`),
  KEY `liquidation_loan_repayment_events_liquidation_loan_repayment_id` (`liquidation_loan_repayment_id`),
  CONSTRAINT `liquidation_loan_repayment_events_liquidation_loan_repayment_id` FOREIGN KEY (`liquidation_loan_repayment_id`) REFERENCES `liquidation_loan_repayments` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `liquidation_loan_repayments`
--

DROP TABLE IF EXISTS `liquidation_loan_repayments`;
CREATE TABLE `liquidation_loan_repayments` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `version` bigint(20) NOT NULL,
  `brokerage_account_id` bigint(20) NOT NULL,
  `created_at` datetime NOT NULL,
  `amount` decimal(20,2) DEFAULT NULL,
  `state` varchar(255) NOT NULL,
  `reason` varchar(255) NOT NULL,
  `pending_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `liquidated_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `completed_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `canceled_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `user_request_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `liquidation_loan_repayments_brokerage_account_id` (`brokerage_account_id`),
  KEY `repayments_user_requests_user_request_id` (`user_request_id`),
  CONSTRAINT `fkey_liquidation_loan_repayments_brokerage_account_id` FOREIGN KEY (`brokerage_account_id`) REFERENCES `brokerage_accounts` (`id`),
  CONSTRAINT `repayments_user_requests_user_request_id` FOREIGN KEY (`user_request_id`) REFERENCES `user_requests` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `loan_bank_transfers`
--

DROP TABLE IF EXISTS `loan_bank_transfers`;
CREATE TABLE `loan_bank_transfers` (
  `loan_id` bigint(20) NOT NULL,
  `bank_transfer_id` bigint(20) NOT NULL,
  PRIMARY KEY (`loan_id`,`bank_transfer_id`),
  KEY `fkey_loan_bank_transfers_bank_transfer_id` (`bank_transfer_id`),
  CONSTRAINT `fkey_loan_bank_transfers_bank_transfer_id` FOREIGN KEY (`bank_transfer_id`) REFERENCES `bank_transfers` (`id`),
  CONSTRAINT `fkey_loan_bank_transfers_loan_id` FOREIGN KEY (`loan_id`) REFERENCES `loans` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Table structure for table `loan_events`
--

DROP TABLE IF EXISTS `loan_events`;
CREATE TABLE `loan_events` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `type` varchar(255) NOT NULL,
  `created_at` datetime NOT NULL,
  `amount` decimal(20,2) NOT NULL,
  `loan_id` bigint(20) DEFAULT NULL,
  `effective_date` date NOT NULL,
  PRIMARY KEY (`id`),
  KEY `loan_events_loan_id` (`loan_id`),
  CONSTRAINT `fkey_loan_events_loan_id` FOREIGN KEY (`loan_id`) REFERENCES `loans` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `loan_to_incoming_brokerage_wire`
--

DROP TABLE IF EXISTS `loan_to_incoming_brokerage_wire`;
CREATE TABLE `loan_to_incoming_brokerage_wire` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `loan_id` bigint(20) NOT NULL,
  `incoming_brokerage_wire_id` bigint(20) NOT NULL,
  `amount` decimal(20,2) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `loan_to_incoming_brokerage_wire_loan_id` (`loan_id`),
  CONSTRAINT `fkey_loan_to_incoming_brokerage_wire_loan_id` FOREIGN KEY (`loan_id`) REFERENCES `loans` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `loan_to_outgoing_brokerage_wire`
--

DROP TABLE IF EXISTS `loan_to_outgoing_brokerage_wire`;
CREATE TABLE `loan_to_outgoing_brokerage_wire` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `loan_id` bigint(20) NOT NULL,
  `outgoing_brokerage_wire_id` bigint(20) NOT NULL,
  `amount` decimal(20,2) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `loan_to_outgoing_brokerage_wire_loan_id` (`loan_id`),
  KEY `loan_to_outgoing_brokerage_wire_outgoing_brokerage_wire_id` (`outgoing_brokerage_wire_id`),
  CONSTRAINT `loan_to_outgoing_brokerage_wire_ibfk_1` FOREIGN KEY (`loan_id`) REFERENCES `loans` (`id`),
  CONSTRAINT `loan_to_outgoing_brokerage_wire_ibfk_2` FOREIGN KEY (`outgoing_brokerage_wire_id`) REFERENCES `outgoing_brokerage_wires` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `loans`
--

DROP TABLE IF EXISTS `loans`;
CREATE TABLE `loans` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `version` bigint(20) NOT NULL,
  `brokerage_account_id` bigint(20) NOT NULL,
  `created_at` datetime NOT NULL,
  `amount` decimal(20,2) NOT NULL,
  `state` varchar(255) NOT NULL,
  `transfer_instructions` text NOT NULL,
  `ach_relationship_id` bigint(20) DEFAULT NULL,
  `risk_metric_requestid` bigint(20) DEFAULT NULL,
  `pending_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `requested_from_broker_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `received_from_broker_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `unscheduled_to_client_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `scheduled_to_client_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `sent_to_client_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `settled_to_client_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `completed_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `canceled_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `reason` varchar(255) DEFAULT NULL,
  `description` text,
  `returned_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `returned_scheduled_to_broker_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `returned_sent_to_broker_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  PRIMARY KEY (`id`),
  KEY `loans_brokerage_account_id` (`brokerage_account_id`),
  KEY `loans_ach_relationship_id` (`ach_relationship_id`),
  CONSTRAINT `fkey_loans_ach_relationship_id` FOREIGN KEY (`ach_relationship_id`) REFERENCES `ach_relationships` (`id`),
  CONSTRAINT `fkey_loans_brokerage_account_id` FOREIGN KEY (`brokerage_account_id`) REFERENCES `brokerage_accounts` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `mfrs_records`
--

DROP TABLE IF EXISTS `mfrs_records`;
CREATE TABLE `mfrs_records` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_at` datetime NOT NULL,
  `state` varchar(255) NOT NULL,
  `record` mediumtext NOT NULL,
  `associated_file_name` varchar(255) NOT NULL,
  `cusip` varchar(255) DEFAULT NULL,
  `symbol` varchar(255) DEFAULT NULL,
  `description1` varchar(255) DEFAULT NULL,
  `description2` varchar(255) DEFAULT NULL,
  `adp_security_number` varchar(255) DEFAULT NULL,
  `trade_switch` tinyint(1) DEFAULT NULL,
  `fund_agreement` tinyint(1) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `mfrs_records_cusip` (`cusip`),
  KEY `mfrs_records_symbol` (`symbol`),
  KEY `mfrs_records_associated_file_name` (`associated_file_name`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `omni_deposits`
--

DROP TABLE IF EXISTS `omni_deposits`;
CREATE TABLE `omni_deposits` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `state` varchar(255) NOT NULL,
  `pending_liquidation_for_return_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `liquidated_for_return_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `returned_requested_from_broker_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  PRIMARY KEY (`id`),
  KEY `omni_deposits_id` (`id`),
  KEY `omni_deposits_state` (`state`),
  CONSTRAINT `omni_deposits_id` FOREIGN KEY (`id`) REFERENCES `deposits` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `omni_ira_deposits`
--

DROP TABLE IF EXISTS `omni_ira_deposits`;
CREATE TABLE `omni_ira_deposits` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `ira_contribution_id` bigint(20) DEFAULT NULL,
  `contribution_year` smallint(6) DEFAULT NULL,
  `contribution_type` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `omni_ira_deposits_id` (`id`),
  KEY `ira_deposits_id` (`id`),
  KEY `omni_ira_deposits_ira_contribution_id` (`ira_contribution_id`),
  KEY `omni_ira_deposits_contribution_id` (`ira_contribution_id`),
  CONSTRAINT `ira_deposits_id` FOREIGN KEY (`id`) REFERENCES `deposits` (`id`),
  CONSTRAINT `omni_ira_deposits_contribution_id` FOREIGN KEY (`ira_contribution_id`) REFERENCES `ira_contributions` (`id`),
  CONSTRAINT `omni_ira_deposits_id` FOREIGN KEY (`id`) REFERENCES `omni_deposits` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `omni_ira_recurring_deposits`
--

DROP TABLE IF EXISTS `omni_ira_recurring_deposits`;
CREATE TABLE `omni_ira_recurring_deposits` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `contribution_year` smallint(6) NOT NULL,
  `contribution_type` varchar(255) NOT NULL,
  `ira_contribution_total_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `omni_ira_recurring_deposits` (`id`),
  KEY `ira_recurring_deposits_id` (`id`),
  KEY `omni_ira_recurring_deposits_contribution_total_id` (`ira_contribution_total_id`),
  CONSTRAINT `ira_recurring_deposits_id` FOREIGN KEY (`id`) REFERENCES `recurring_deposits` (`id`),
  CONSTRAINT `omni_ira_recurring_deposits` FOREIGN KEY (`id`) REFERENCES `recurring_deposits` (`id`),
  CONSTRAINT `omni_ira_recurring_deposits_contribution_total_id` FOREIGN KEY (`ira_contribution_total_id`) REFERENCES `ira_contribution_totals` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `omni_ira_withdrawal_to_outgoing_brokerage_wire`
--

DROP TABLE IF EXISTS `omni_ira_withdrawal_to_outgoing_brokerage_wire`;
CREATE TABLE `omni_ira_withdrawal_to_outgoing_brokerage_wire` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `omni_ira_withdrawal_id` bigint(20) NOT NULL,
  `outgoing_brokerage_wire_id` bigint(20) NOT NULL,
  `amount` decimal(20,2) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `omni_ira_withdrawal_to_outgoing_brokerage_wire_withdrawal_id` (`omni_ira_withdrawal_id`),
  KEY `omni_ira_withdrawal_to_outgoing_brokerage_wire_brokerage_wire_id` (`outgoing_brokerage_wire_id`),
  CONSTRAINT `omni_ira_withdrawal_to_outgoing_brokerage_wire_brokerage_wire_id` FOREIGN KEY (`outgoing_brokerage_wire_id`) REFERENCES `outgoing_brokerage_wires` (`id`),
  CONSTRAINT `omni_ira_withdrawal_to_outgoing_brokerage_wire_withdrawal_id` FOREIGN KEY (`omni_ira_withdrawal_id`) REFERENCES `omni_ira_withdrawals` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Table structure for table `omni_ira_withdrawals`
--

DROP TABLE IF EXISTS `omni_ira_withdrawals`;
CREATE TABLE `omni_ira_withdrawals` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `ira_distribution_instructions` text NOT NULL,
  `completed_withheld_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `scheduled_withheld_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `unscheduled_withheld_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  PRIMARY KEY (`id`),
  KEY `omni_ira_withdrawals_id` (`id`),
  CONSTRAINT `omni_ira_withdrawals_id` FOREIGN KEY (`id`) REFERENCES `omni_withdrawals` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `omni_withdrawal_approval_statuses`
--

DROP TABLE IF EXISTS `omni_withdrawal_approval_statuses`;
CREATE TABLE `omni_withdrawal_approval_statuses` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `version` int(11) NOT NULL,
  `omni_withdrawal_id` bigint(20) NOT NULL,
  `notes` mediumtext NOT NULL,
  PRIMARY KEY (`id`),
  KEY `omni_withdrawal_approval_statuses_omni_withdrawal_id` (`omni_withdrawal_id`),
  CONSTRAINT `omni_withdrawal_approval_statuses_omni_withdrawal_id` FOREIGN KEY (`omni_withdrawal_id`) REFERENCES `omni_withdrawals` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `omni_withdrawals`
--

DROP TABLE IF EXISTS `omni_withdrawals`;
CREATE TABLE `omni_withdrawals` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `state` varchar(255) NOT NULL,
  `requested_from_broker_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `returned_scheduled_to_broker_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `returned_sent_to_broker_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `reason` varchar(255) DEFAULT NULL,
  `description` text,
  `initiator` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `omni_withdrawals_id` (`id`),
  KEY `omni_withdrawals_state` (`state`),
  CONSTRAINT `omni_withdrawals_id` FOREIGN KEY (`id`) REFERENCES `withdrawals` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `outgoing_brokerage_wires`
--

DROP TABLE IF EXISTS `outgoing_brokerage_wires`;
CREATE TABLE `outgoing_brokerage_wires` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `version` bigint(20) NOT NULL,
  `created_at` datetime NOT NULL,
  `state` varchar(255) NOT NULL,
  `amount` decimal(20,2) NOT NULL,
  `brokerage` varchar(255) NOT NULL,
  `settlement_location` varchar(255) NOT NULL DEFAULT 'RBC',
  `wealthfront_account` varchar(255) DEFAULT NULL,
  `wire_withdrawal_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `outgoing_brokerage_wires_wire_withdrawal_id` (`wire_withdrawal_id`),
  CONSTRAINT `outgoing_brokerage_wires_wire_withdrawal_id` FOREIGN KEY (`wire_withdrawal_id`) REFERENCES `wire_withdrawals` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `outgoing_events`
--

DROP TABLE IF EXISTS `outgoing_events`;
CREATE TABLE `outgoing_events` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `version` bigint(20) NOT NULL DEFAULT '0',
  `eventid` char(36) NOT NULL,
  `receiver` varchar(255) DEFAULT NULL,
  `message` text NOT NULL,
  `created_at` datetime NOT NULL,
  `first_polled_at` datetime DEFAULT NULL,
  `last_polled_at` datetime DEFAULT NULL,
  `first_failed_at` datetime DEFAULT NULL,
  `last_failed_at` datetime DEFAULT NULL,
  `failed_at` datetime DEFAULT NULL,
  `succeeded_at` datetime DEFAULT NULL,
  `failed_attempts` int(11) NOT NULL DEFAULT '0',
  `dropped` tinyint(1) NOT NULL DEFAULT '0',
  `in_flight` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `outgoing_events_eventid_receiver` (`eventid`,`receiver`),
  KEY `outgoing_events_succeeded_at` (`succeeded_at`),
  KEY `outgoing_events_failed_at_failed_attempts_dropped` (`failed_at`,`failed_attempts`,`dropped`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `partner_bank_records`
--

DROP TABLE IF EXISTS `partner_bank_records`;
CREATE TABLE `partner_bank_records` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `version` int(11) NOT NULL,
  `type` varchar(255) NOT NULL,
  `wells_file_id` bigint(20) NOT NULL,
  `account` varchar(255) NOT NULL,
  `effective_date` date NOT NULL,
  `created_at` datetime NOT NULL,
  `processed_at` datetime DEFAULT NULL,
  `ignored_at` datetime DEFAULT NULL,
  `recordid` varchar(255) NOT NULL,
  `record` text,
  PRIMARY KEY (`id`),
  UNIQUE KEY `bank_account_transaction_records_wells_file_id_recordid` (`wells_file_id`,`recordid`),
  KEY `bank_account_transaction_records_type_processed_at` (`type`,`processed_at`),
  KEY `bank_account_transaction_records_effective_date` (`effective_date`),
  CONSTRAINT `partner_bank_records_ibfk_1` FOREIGN KEY (`wells_file_id`) REFERENCES `wells_files` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Table structure for table `payment_manager_file_uploads`
--

DROP TABLE IF EXISTS `payment_manager_file_uploads`;
CREATE TABLE `payment_manager_file_uploads` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_at` datetime NOT NULL,
  `successfully_uploaded_at` datetime DEFAULT NULL,
  `details` longtext,
  `failed` tinyint(4) NOT NULL,
  `transaction_count` int(11) NOT NULL,
  `total_amount` decimal(20,2) NOT NULL,
  `file_size` int(11) NOT NULL,
  `wells_file_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `payment_manager_file_uploads_count_amount_file_size` (`transaction_count`,`total_amount`,`file_size`),
  KEY `payment_manager_file_uploads_wells_file_id` (`wells_file_id`),
  CONSTRAINT `payment_manager_file_uploads_wells_file_id` FOREIGN KEY (`wells_file_id`) REFERENCES `wells_files` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `perfect_receivables_files`
--

DROP TABLE IF EXISTS `perfect_receivables_files`;
CREATE TABLE `perfect_receivables_files` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_at` datetime NOT NULL,
  `processed_at` datetime DEFAULT NULL,
  `effective_date` date NOT NULL,
  `perfect_receivables_file` longtext NOT NULL,
  `wells_file_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `perfect_receivables_files_wells_file_id` (`wells_file_id`),
  CONSTRAINT `perfect_receivables_files_wells_file_id` FOREIGN KEY (`wells_file_id`) REFERENCES `wells_files` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `queued_acat_transfer_processing_requests`
--

DROP TABLE IF EXISTS `queued_acat_transfer_processing_requests`;
CREATE TABLE `queued_acat_transfer_processing_requests` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `acats_transfer_id` bigint(20) NOT NULL,
  `processing_date` date NOT NULL,
  `file_cycle` varchar(255) NOT NULL,
  `created_at` datetime NOT NULL,
  `polled_time` datetime DEFAULT NULL,
  `sent_time` datetime DEFAULT NULL,
  `error_flag` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `queued_acat_transfer_requests_polled_time_error_flag` (`polled_time`,`error_flag`),
  KEY `queued_acat_transfer_requests_sent_time_polled_time_error_flag` (`sent_time`,`polled_time`,`error_flag`),
  KEY `acats_transfer_id` (`acats_transfer_id`),
  CONSTRAINT `queued_acat_transfer_processing_requests_ibfk_1` FOREIGN KEY (`acats_transfer_id`) REFERENCES `acats_transfers` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `queued_b50_requests`
--

DROP TABLE IF EXISTS `queued_b50_requests`;
CREATE TABLE `queued_b50_requests` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `accountid` bigint(20) NOT NULL,
  `cusip` varchar(255) NOT NULL,
  `effective_date` date NOT NULL,
  `environment` varchar(255) NOT NULL,
  `created_at` datetime NOT NULL,
  `acats_file_id` bigint(20) DEFAULT NULL,
  `send_name_and_address_entry` tinyint(1) DEFAULT '0',
  `is_b51_update` tinyint(1) DEFAULT '0',
  `is_delete` tinyint(1) DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `queued_b50_requests_effective_date_accountid_cusip` (`effective_date`,`accountid`,`cusip`),
  KEY `queued_b50_requests_effective_date` (`effective_date`),
  KEY `queued_b50_requests_acats_file_id` (`acats_file_id`),
  CONSTRAINT `queued_b50_requests_acats_file_id` FOREIGN KEY (`acats_file_id`) REFERENCES `acats_files` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `queued_bank_transactions`
--

DROP TABLE IF EXISTS `queued_bank_transactions`;
CREATE TABLE `queued_bank_transactions` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_at` datetime NOT NULL,
  `polled_time` datetime DEFAULT NULL,
  `sent_time` datetime DEFAULT NULL,
  `error_flag` tinyint(1) NOT NULL DEFAULT '0',
  `accountid` bigint(20) DEFAULT NULL,
  `amount` decimal(14,2) DEFAULT NULL,
  `transactionid` varchar(255) DEFAULT NULL,
  `effective_date` date DEFAULT NULL,
  `account_type` varchar(255) DEFAULT NULL,
  `custodian` varchar(255) DEFAULT 'OMNI',
  `wealthfront_account` varchar(255) DEFAULT NULL,
  `settlement_location` varchar(255) DEFAULT NULL,
  `cash_transaction_type` varchar(255) DEFAULT NULL,
  `bank_transaction_type` varchar(255) NOT NULL,
  `type` varchar(255) NOT NULL,
  `contribution_type` varchar(255) DEFAULT NULL,
  `contribution_year` smallint(6) DEFAULT NULL,
  `deposit_id` bigint(20) DEFAULT NULL,
  `withdrawal_id` bigint(20) DEFAULT NULL,
  `loan_id` bigint(20) DEFAULT NULL,
  `bucket_id` bigint(20) DEFAULT NULL,
  `withholding_type` varchar(255) DEFAULT NULL,
  `distribution_reason` varchar(255) DEFAULT NULL,
  `distribution_year` smallint(6) DEFAULT NULL,
  `ira_tax_code` varchar(255) DEFAULT NULL,
  `details` mediumtext,
  PRIMARY KEY (`id`),
  KEY `queued_bank_transactions_polled_time_error_flag` (`polled_time`,`error_flag`),
  KEY `queued_bank_transactions_sent_time_polled_time_error_flag` (`sent_time`,`polled_time`,`error_flag`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `queued_cbrs_tax_lot_transactions`
--

DROP TABLE IF EXISTS `queued_cbrs_tax_lot_transactions`;
CREATE TABLE `queued_cbrs_tax_lot_transactions` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `cbrs_asset_id` bigint(20) NOT NULL,
  `created_at` datetime NOT NULL,
  `polled_time` datetime DEFAULT NULL,
  `sent_time` datetime DEFAULT NULL,
  `error_flag` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `queued_cbrs_transactions_polled_time_error_flag` (`polled_time`,`error_flag`),
  KEY `queued_cbrs_transactions_sent_time_polled_time_error_flag` (`sent_time`,`polled_time`,`error_flag`),
  KEY `queued_cbrs_transactions_cbrs_asset_id` (`cbrs_asset_id`),
  CONSTRAINT `queued_cbrs_transactions_cbrs_asset_id` FOREIGN KEY (`cbrs_asset_id`) REFERENCES `cbrs_assets` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `queued_ira_bank_transactions`
--

DROP TABLE IF EXISTS `queued_ira_bank_transactions`;
CREATE TABLE `queued_ira_bank_transactions` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_at` datetime NOT NULL,
  `polled_time` datetime DEFAULT NULL,
  `sent_time` datetime DEFAULT NULL,
  `error_flag` tinyint(1) NOT NULL DEFAULT '0',
  `accountid` bigint(20) DEFAULT NULL,
  `omni_ira_withdrawal_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `queued_ira_bank_transactions_omni_ira_withdrawal_id` (`omni_ira_withdrawal_id`),
  KEY `queued_ira_bank_transactions_polled_time_error_flag` (`polled_time`,`error_flag`),
  KEY `queued_ira_bank_transactions_sent_time_polled_time_error_flag` (`sent_time`,`polled_time`,`error_flag`),
  CONSTRAINT `queued_ira_bank_transactions_omni_ira_withdrawal_id` FOREIGN KEY (`omni_ira_withdrawal_id`) REFERENCES `omni_ira_withdrawals` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Table structure for table `queued_risk_service_requests`
--

DROP TABLE IF EXISTS `queued_risk_service_requests`;
CREATE TABLE `queued_risk_service_requests` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_at` datetime NOT NULL,
  `polled_time` datetime DEFAULT NULL,
  `sent_time` datetime DEFAULT NULL,
  `error_flag` tinyint(1) NOT NULL DEFAULT '0',
  `userid` bigint(20) DEFAULT NULL,
  `accountid` bigint(20) DEFAULT NULL,
  `risk_externalid` bigint(20) DEFAULT NULL,
  `risk_service_request_type` varchar(255) NOT NULL,
  `withdrawal_id` bigint(20) DEFAULT NULL,
  `risk_metric_requestid` bigint(20) DEFAULT NULL,
  `risk_assessment_details` text,
  `error_suffix` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `queued_risk_service_requests_polled_time_error_flag` (`polled_time`,`error_flag`),
  KEY `queued_risk_service_requests_sent_time_polled_time_error_flag` (`sent_time`,`polled_time`,`error_flag`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `queued_transfers`
--

DROP TABLE IF EXISTS `queued_transfers`;
CREATE TABLE `queued_transfers` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `acats_transfer_id` bigint(20) NOT NULL,
  `created_at` datetime NOT NULL,
  `polled_time` datetime DEFAULT NULL,
  `sent_time` datetime DEFAULT NULL,
  `error_flag` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `queued_transfers_polled_time_error_flag` (`polled_time`,`error_flag`),
  KEY `queued_transfers_sent_time_polled_time_error_flag` (`sent_time`,`polled_time`,`error_flag`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `queued_wells_file_uploads_to_s3`
--

DROP TABLE IF EXISTS `queued_wells_file_uploads_to_s3`;
CREATE TABLE `queued_wells_file_uploads_to_s3` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `version` bigint(20) NOT NULL DEFAULT '0',
  `wells_file_id` bigint(20) NOT NULL,
  `created_at` datetime NOT NULL,
  `polled_time` datetime DEFAULT NULL,
  `sent_time` datetime DEFAULT NULL,
  `error_flag` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `queued_wells_file_uploads_to_s3_polled_time_error_flag` (`polled_time`,`error_flag`),
  KEY `queued_wells_file_uploads_to_s3_sent_time_polled_time_error_flag` (`sent_time`,`polled_time`,`error_flag`),
  KEY `queued_wells_file_uploads_to_s3_wells_file_id` (`wells_file_id`),
  CONSTRAINT `queued_wells_file_uploads_to_s3_wells_file_id` FOREIGN KEY (`wells_file_id`) REFERENCES `wells_files` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `rbc_brokerage_accounts`
--

DROP TABLE IF EXISTS `rbc_brokerage_accounts`;
CREATE TABLE `rbc_brokerage_accounts` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `accountid` bigint(20) NOT NULL,
  `created_at` datetime NOT NULL,
  `rbc_branch_code` varchar(255) NOT NULL,
  `rbc_account_number` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `rbc_brokerage_accounts_accountid` (`accountid`),
  UNIQUE KEY `rbc_brokerage_accounts_rbc_account_number` (`rbc_account_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Table structure for table `rbc_internal_transfer_instructions`
--

DROP TABLE IF EXISTS `rbc_internal_transfer_instructions`;
CREATE TABLE `rbc_internal_transfer_instructions` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `rbc_internal_transfer_id` bigint(20) NOT NULL,
  `type` varchar(255) NOT NULL,
  `partial_or_all` varchar(255) NOT NULL,
  `instrumentid` bigint(10) DEFAULT NULL,
  `symbol` varchar(255) DEFAULT NULL,
  `value` decimal(20,8) DEFAULT NULL,
  `cost_per_share` decimal(20,8) DEFAULT NULL,
  `net_cost` decimal(20,8) DEFAULT NULL,
  `state` varchar(255) NOT NULL,
  `lot_open_date` date DEFAULT NULL,
  `price` decimal(20,8) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `rbc_internal_transfer_instructions_rbc_internal_transfer_id` (`rbc_internal_transfer_id`),
  KEY `rbc_internal_transfer_instructions_instrumentid` (`instrumentid`),
  CONSTRAINT `rbc_internal_transfer_instructions_rbc_internal_transfer_id` FOREIGN KEY (`rbc_internal_transfer_id`) REFERENCES `rbc_internal_transfers` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `rbc_internal_transfers`
--

DROP TABLE IF EXISTS `rbc_internal_transfers`;
CREATE TABLE `rbc_internal_transfers` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `account_transfer_id` bigint(20) DEFAULT NULL,
  `state` varchar(255) NOT NULL,
  `created_at` datetime NOT NULL,
  `requested_at` datetime DEFAULT NULL,
  `settlement_date` date DEFAULT NULL,
  `accountid` bigint(20) DEFAULT NULL,
  `account_number` varchar(255) DEFAULT NULL,
  `account_info` mediumtext NOT NULL,
  `contraparty` mediumtext NOT NULL,
  `initiator` varchar(255) NOT NULL,
  `comments` tinytext,
  `transfer_type` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `rbc_internal_transfers_account_transfer_id` (`account_transfer_id`),
  KEY `rbc_internal_transfers_accountid` (`accountid`),
  KEY `rbc_internal_transfers_state` (`state`),
  KEY `rbc_internal_transfers_account_number` (`account_number`),
  CONSTRAINT `rbc_internal_transfers_account_transfer_id` FOREIGN KEY (`account_transfer_id`) REFERENCES `account_transfers` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `recurring_deposit_events`
--

DROP TABLE IF EXISTS `recurring_deposit_events`;
CREATE TABLE `recurring_deposit_events` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `type` varchar(255) NOT NULL,
  `canceled_deposit_id` bigint(20) DEFAULT NULL,
  `created_deposit_id` bigint(20) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `recurring_deposit_events_canceled_deposit_unique` (`canceled_deposit_id`),
  UNIQUE KEY `recurring_deposit_events_created_deposit_unique` (`created_deposit_id`),
  KEY `recurring_deposit_events_canceled_deposit_id` (`canceled_deposit_id`),
  KEY `recurring_deposit_events_created_deposit_id` (`created_deposit_id`),
  CONSTRAINT `recurring_deposit_events_canceled_transfer_id` FOREIGN KEY (`canceled_deposit_id`) REFERENCES `recurring_deposits` (`id`),
  CONSTRAINT `recurring_deposit_events_created_deposit_id` FOREIGN KEY (`created_deposit_id`) REFERENCES `recurring_deposits` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `recurring_deposits`
--

DROP TABLE IF EXISTS `recurring_deposits`;
CREATE TABLE `recurring_deposits` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `version` bigint(20) NOT NULL,
  `created_at` datetime NOT NULL,
  `brokerage_account_id` bigint(20) DEFAULT NULL,
  `amount` decimal(20,2) NOT NULL,
  `deposit_instructions` text,
  `ach_relationship_id` bigint(20) DEFAULT NULL,
  `state` varchar(255) NOT NULL,
  `ira_contribution_instructions` text,
  `recurring_strategy` text,
  `start_date` date DEFAULT NULL,
  `next_deposit_date` date DEFAULT NULL,
  `latest_impending_deposit_notification_date` date DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `recurring_deposits_brokerage_account_id` (`brokerage_account_id`),
  KEY `recurring_deposits_ach_relationship_fkey` (`ach_relationship_id`),
  KEY `recurring_deposits_next_deposit_date` (`next_deposit_date`),
  KEY `recurring_deposits_state` (`state`),
  CONSTRAINT `recurring_deposits_ach_relationship_fkey` FOREIGN KEY (`ach_relationship_id`) REFERENCES `ach_relationships` (`id`),
  CONSTRAINT `recurring_deposits_brokerage_account_id` FOREIGN KEY (`brokerage_account_id`) REFERENCES `brokerage_accounts` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `residual_accounts`
--

DROP TABLE IF EXISTS `residual_accounts`;
CREATE TABLE `residual_accounts` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `accountid` bigint(20) NOT NULL,
  `account_number` varchar(255) NOT NULL,
  `residual_start_date` date NOT NULL,
  `last_sweep_date` date DEFAULT NULL,
  `account_terminated` tinyint(1) NOT NULL,
  `acats_transfer_id` bigint(20) NOT NULL,
  `environment` varchar(255) DEFAULT 'RBC_TEST',
  PRIMARY KEY (`id`),
  KEY `residual_accounts_accountid` (`accountid`),
  KEY `residual_accounts_account_number` (`account_number`),
  KEY `residual_accounts_acats_transfer_id` (`acats_transfer_id`),
  CONSTRAINT `residual_accounts_acats_transfer_id` FOREIGN KEY (`acats_transfer_id`) REFERENCES `acats_transfers` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `residual_sweep_withdrawals`
--

DROP TABLE IF EXISTS `residual_sweep_withdrawals`;
CREATE TABLE `residual_sweep_withdrawals` (
  `residual_sweep_id` bigint(20) NOT NULL,
  `withdrawal_id` bigint(20) NOT NULL,
  PRIMARY KEY (`residual_sweep_id`,`withdrawal_id`),
  KEY `residual_sweep_withdrawals_withdrawal_id` (`withdrawal_id`),
  CONSTRAINT `residual_sweep_withdrawals_withdrawal_id` FOREIGN KEY (`withdrawal_id`) REFERENCES `withdrawals` (`id`),
  CONSTRAINT `residual_sweep_withdrawals_residual_sweep_id` FOREIGN KEY (`residual_sweep_id`) REFERENCES `residual_sweeps` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Table structure for table `residual_sweeps`
--

DROP TABLE IF EXISTS `residual_sweeps`;
CREATE TABLE `residual_sweeps` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_at` datetime NOT NULL,
  `last_sweep_date` date DEFAULT NULL,
  `brokerage_account_id` bigint(20) NOT NULL,
  `state` varchar(255) NOT NULL,
  `residual_sweep_transfer_instructions` mediumtext NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `brokerage_account_id` (`brokerage_account_id`),
  CONSTRAINT `residual_sweeps_brokerage_account_id` FOREIGN KEY (`brokerage_account_id`) REFERENCES `brokerage_accounts` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `schema_log`
--

-- DROP TABLE IF EXISTS `schema_log`;
-- -- -- CREATE TABLE `schema_log` (
--   `schema_version` int(11) NOT NULL DEFAULT '0',
--   `date` datetime NOT NULL,
--   UNIQUE KEY `schema_version` (`schema_version`)
-- ) ENGINE=InnoDB DEFAULT CHARSET=utf8;
-- 
--
-- Table structure for table `sqs_jobs`
--

DROP TABLE IF EXISTS `sqs_jobs`;
CREATE TABLE `sqs_jobs` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `start_time` datetime NOT NULL,
  `end_time` datetime DEFAULT NULL,
  `message_id` varchar(255) NOT NULL,
  `chronos_config_name` varchar(255) DEFAULT NULL,
  `query_name` varchar(255) NOT NULL,
  `arguments` text,
  `mesos_task_id` varchar(255) DEFAULT NULL,
  `job_status` varchar(255) DEFAULT NULL,
  `service` varchar(255) NOT NULL,
  `node_identifier` varchar(255) NOT NULL,
  `return_code` bigint(20) DEFAULT NULL,
  `updated_chronos` tinyint(1) DEFAULT NULL,
  `dag_id` varchar(255) DEFAULT NULL,
  `task_id` varchar(255) DEFAULT NULL,
  `dag_execution_date` datetime DEFAULT NULL,
  `try_number` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `sqs_jobs_start_time` (`start_time`),
  KEY `sqs_jobs_end_time` (`end_time`),
  KEY `sqs_jobs_return_code` (`return_code`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `sweep_deposits`
--

DROP TABLE IF EXISTS `sweep_deposits`;
CREATE TABLE `sweep_deposits` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `version` bigint(20) NOT NULL DEFAULT '0',
  `state` varchar(255) NOT NULL,
  `scheduled_to_sweep_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `sent_to_sweep_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `returned_pending_from_sweep_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `returned_scheduled_from_sweep_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `returned_received_from_sweep_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `scheduled_to_settlement_account_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `sent_to_settlement_account_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `returned_scheduled_from_settlement_account_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `returned_received_from_settlement_account_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  PRIMARY KEY (`id`),
  KEY `sweep_deposits_state` (`state`),
  CONSTRAINT `sweep_deposits_id` FOREIGN KEY (`id`) REFERENCES `deposits` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `sweep_withdrawals`
--

DROP TABLE IF EXISTS `sweep_withdrawals`;
CREATE TABLE `sweep_withdrawals` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `version` bigint(20) NOT NULL DEFAULT '0',
  `state` varchar(255) NOT NULL,
  `pending_from_sweep` decimal(20,2) NOT NULL DEFAULT '0.00',
  `requested_from_sweep` decimal(20,2) NOT NULL DEFAULT '0.00',
  `received_from_sweep` decimal(20,2) NOT NULL DEFAULT '0.00',
  `requested_from_broker_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `returned_scheduled_to_broker_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `returned_sent_to_broker_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `returned_scheduled_to_sweep` decimal(20,2) NOT NULL DEFAULT '0.00',
  `returned_sent_to_sweep` decimal(20,2) NOT NULL DEFAULT '0.00',
  `initiator` varchar(255) DEFAULT NULL,
  `reason` varchar(255) DEFAULT NULL,
  `description` text,
  PRIMARY KEY (`id`),
  KEY `sweep_withdrawals_state` (`state`),
  CONSTRAINT `sweep_withdrawals_id` FOREIGN KEY (`id`) REFERENCES `withdrawals` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `tagged_routing_numbers`
--

DROP TABLE IF EXISTS `tagged_routing_numbers`;
CREATE TABLE `tagged_routing_numbers` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `tag` varchar(255) NOT NULL,
  `routing_number` varchar(255) NOT NULL,
  `reason` varchar(255) NOT NULL,
  `taos_admin` varchar(255) NOT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `routing_number` (`routing_number`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `toggles`
--

DROP TABLE IF EXISTS `toggles`;
CREATE TABLE `toggles` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `state` tinyint(1) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `unexpected_bai2_transactions`
--

DROP TABLE IF EXISTS `unexpected_bai2_transactions`;
CREATE TABLE `unexpected_bai2_transactions` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_at` datetime NOT NULL,
  `effective_date` date NOT NULL,
  `state` varchar(255) NOT NULL,
  `version` int(11) NOT NULL,
  `processed_by` varchar(255) DEFAULT NULL,
  `note` text,
  `bank_transfer_id` bigint(20) DEFAULT NULL,
  `wells_file_id` bigint(20) NOT NULL DEFAULT '0',
  `wells_bank_account_id` bigint(20) NOT NULL DEFAULT '1',
  `bai_code` varchar(255) NOT NULL,
  `amount` decimal(20,2) NOT NULL,
  `transaction_record` text NOT NULL,
  `hash` varchar(128) NOT NULL,
  `error_details` text NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `hash` (`hash`),
  KEY `unexpected_bai2_transactions_state` (`state`),
  KEY `unexpected_bai2_transactions_bank_transfer_id` (`bank_transfer_id`),
  KEY `unexpected_bai2_transactions_wells_file_id` (`wells_file_id`),
  KEY `unexpected_bai2_transactions_wells_bank_account_id` (`wells_bank_account_id`),
  CONSTRAINT `unexpected_bai2_transactions_bank_transfer_id` FOREIGN KEY (`bank_transfer_id`) REFERENCES `bank_transfers` (`id`),
  CONSTRAINT `unexpected_bai2_transactions_wells_bank_account_id` FOREIGN KEY (`wells_bank_account_id`) REFERENCES `wells_bank_accounts` (`id`),
  CONSTRAINT `unexpected_bai2_transactions_wells_file_id` FOREIGN KEY (`wells_file_id`) REFERENCES `wells_files` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `user_requests`
--

DROP TABLE IF EXISTS `user_requests`;
CREATE TABLE `user_requests` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `version` bigint(20) NOT NULL,
  `type` varchar(255) NOT NULL,
  `brokerage_account_id` bigint(20) NOT NULL,
  `created_at` datetime NOT NULL,
  `state` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_requests_brokerage_account_id` (`brokerage_account_id`),
  KEY `user_requests_state` (`state`),
  CONSTRAINT `user_requests_brokerage_account_id` FOREIGN KEY (`brokerage_account_id`) REFERENCES `brokerage_accounts` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_acats_association_active`
--

DROP TABLE IF EXISTS `version_acats_association_active`;
CREATE TABLE `version_acats_association_active` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `acats_association_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` tinyint(1) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_acats_association_active_acats_association_id` (`acats_association_id`),
  CONSTRAINT `version_acats_transfer_active_acats_association_id` FOREIGN KEY (`acats_association_id`) REFERENCES `acats_associations` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_acats_file_state`
--

DROP TABLE IF EXISTS `version_acats_file_state`;
CREATE TABLE `version_acats_file_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `acats_file_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_acats_file_state_acats_file_id` (`acats_file_id`),
  CONSTRAINT `version_acats_file_state_acats_file_id` FOREIGN KEY (`acats_file_id`) REFERENCES `acats_files` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_acats_input_transaction_state`
--

DROP TABLE IF EXISTS `version_acats_input_transaction_state`;
CREATE TABLE `version_acats_input_transaction_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `acats_input_transaction_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_acats_input_transaction_state_acats_input_transaction_id` (`acats_input_transaction_id`),
  CONSTRAINT `version_acats_input_transaction_state_acats_input_transaction_id` FOREIGN KEY (`acats_input_transaction_id`) REFERENCES `acats_input_transactions` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_acats_multicycle_record_record`
--

DROP TABLE IF EXISTS `version_acats_multicycle_record_record`;
CREATE TABLE `version_acats_multicycle_record_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `acats_multicycle_record_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` mediumtext NOT NULL,
  PRIMARY KEY (`id`),
  KEY `vamr_record_acats_multicycle_record_id` (`acats_multicycle_record_id`),
  CONSTRAINT `vamr_record_acats_multicycle_record_id` FOREIGN KEY (`acats_multicycle_record_id`) REFERENCES `acats_multicycle_records` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_acats_notification_state`
--

DROP TABLE IF EXISTS `version_acats_notification_state`;
CREATE TABLE `version_acats_notification_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `acats_notification_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `fkey_acats_notification_state` (`acats_notification_id`),
  CONSTRAINT `fkey_acats_notification_state` FOREIGN KEY (`acats_notification_id`) REFERENCES `acats_notifications` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_acats_position_record_record`
--

DROP TABLE IF EXISTS `version_acats_position_record_record`;
CREATE TABLE `version_acats_position_record_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `acats_position_record_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` mediumtext NOT NULL,
  PRIMARY KEY (`id`),
  KEY `vapr_record_acats_position_record_id` (`acats_position_record_id`),
  CONSTRAINT `vapr_record_acats_position_record_id` FOREIGN KEY (`acats_position_record_id`) REFERENCES `acats_position_records` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_acats_transfer_contraparty`
--

DROP TABLE IF EXISTS `version_acats_transfer_contraparty`;
CREATE TABLE `version_acats_transfer_contraparty` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `acats_transfer_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` mediumtext NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_acats_transfer_contraparty_acats_transfer_id` (`acats_transfer_id`),
  CONSTRAINT `version_acats_transfer_contraparty_acats_transfer_id` FOREIGN KEY (`acats_transfer_id`) REFERENCES `acats_transfers` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_acats_transfer_instruction_state`
--

DROP TABLE IF EXISTS `version_acats_transfer_instruction_state`;
CREATE TABLE `version_acats_transfer_instruction_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `acats_transfer_instruction_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_acats_transfer_instruction_state_acats_transfer_id` (`acats_transfer_instruction_id`),
  CONSTRAINT `vatis_acats_transfer_instruction_id` FOREIGN KEY (`acats_transfer_instruction_id`) REFERENCES `acats_transfer_instructions` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_acats_transfer_state`
--

DROP TABLE IF EXISTS `version_acats_transfer_state`;
CREATE TABLE `version_acats_transfer_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `acats_transfer_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_acats_transfer_state_acats_transfer_id` (`acats_transfer_id`),
  CONSTRAINT `version_acats_transfer_state_acats_transfer_id` FOREIGN KEY (`acats_transfer_id`) REFERENCES `acats_transfers` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_account_transfer_notes`
--

DROP TABLE IF EXISTS `version_account_transfer_notes`;
CREATE TABLE `version_account_transfer_notes` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `account_transfer_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` mediumtext,
  PRIMARY KEY (`id`),
  KEY `version_account_transfer_note_account_transfer_id` (`account_transfer_id`),
  CONSTRAINT `version_account_transfer_note_account_transfer_id` FOREIGN KEY (`account_transfer_id`) REFERENCES `account_transfers` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_account_transfer_state`
--

DROP TABLE IF EXISTS `version_account_transfer_state`;
CREATE TABLE `version_account_transfer_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `account_transfer_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_account_transfer_state_account_transfer_id` (`account_transfer_id`),
  CONSTRAINT `version_account_transfer_state_account_transfer_id` FOREIGN KEY (`account_transfer_id`) REFERENCES `account_transfers` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_ach_deposit_state`
--

DROP TABLE IF EXISTS `version_ach_deposit_state`;
CREATE TABLE `version_ach_deposit_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `ach_deposit_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` bit(1) NOT NULL,
  `value` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_ach_deposit_state_ach_deposit_id` (`ach_deposit_id`),
  CONSTRAINT `version_ach_deposit_state_ach_deposit_id` FOREIGN KEY (`ach_deposit_id`) REFERENCES `ach_deposits` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_ach_direct_deposit_state`
--

DROP TABLE IF EXISTS `version_ach_direct_deposit_state`;
CREATE TABLE `version_ach_direct_deposit_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `ach_direct_deposit_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_ach_direct_deposit_state_ach_direct_deposit_id` (`ach_direct_deposit_id`),
  CONSTRAINT `version_ach_direct_deposit_state_ach_direct_deposit_id` FOREIGN KEY (`ach_direct_deposit_id`) REFERENCES `ach_direct_deposits` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_ach_relationship_micro_deposits_state`
--

DROP TABLE IF EXISTS `version_ach_relationship_micro_deposits_state`;
CREATE TABLE `version_ach_relationship_micro_deposits_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `ach_relationship_micro_deposits_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` bit(1) NOT NULL,
  `value` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_ach_relationship_micro_deposits_state_fk` (`ach_relationship_micro_deposits_id`),
  CONSTRAINT `version_ach_relationship_micro_deposits_state_fk` FOREIGN KEY (`ach_relationship_micro_deposits_id`) REFERENCES `ach_relationship_micro_deposits` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_ach_relationship_state`
--

DROP TABLE IF EXISTS `version_ach_relationship_state`;
CREATE TABLE `version_ach_relationship_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `ach_relationship_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL DEFAULT '0',
  `value` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_ach_relationship_state_ach_relationship_id` (`ach_relationship_id`),
  CONSTRAINT `version_ach_relationship_state_ach_relationship_id` FOREIGN KEY (`ach_relationship_id`) REFERENCES `ach_relationships` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_ach_withdrawal_state`
--

DROP TABLE IF EXISTS `version_ach_withdrawal_state`;
CREATE TABLE `version_ach_withdrawal_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `ach_withdrawal_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` bit(1) NOT NULL,
  `value` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_ach_withdrawal_state_ach_withdrawal_id` (`ach_withdrawal_id`),
  CONSTRAINT `version_ach_withdrawal_state_ach_withdrawal_id` FOREIGN KEY (`ach_withdrawal_id`) REFERENCES `ach_withdrawals` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_ascensus_deposit_amounts`
--

DROP TABLE IF EXISTS `version_ascensus_deposit_amounts`;
CREATE TABLE `version_ascensus_deposit_amounts` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `deposit_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `ready_to_be_released_amount` decimal(20,2) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `fkey_version_ascensus_deposit_amounts_deposit_id` (`deposit_id`),
  CONSTRAINT `fkey_version_ascensus_deposit_amounts_deposit_id` FOREIGN KEY (`deposit_id`) REFERENCES `ascensus_deposits` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_ascensus_deposit_state`
--

DROP TABLE IF EXISTS `version_ascensus_deposit_state`;
CREATE TABLE `version_ascensus_deposit_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `ascensus_deposit_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` bit(1) NOT NULL,
  `value` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_ascensus_deposit_state_ascensus_deposit_id` (`ascensus_deposit_id`),
  CONSTRAINT `version_ascensus_deposit_state_ascensus_deposit_id` FOREIGN KEY (`ascensus_deposit_id`) REFERENCES `ascensus_deposits` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_ascensus_withdrawal_state`
--

DROP TABLE IF EXISTS `version_ascensus_withdrawal_state`;
CREATE TABLE `version_ascensus_withdrawal_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `ascensus_withdrawal_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` bit(1) NOT NULL,
  `value` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_ascensus_withdrawal_state_ascensus_withdrawal_id` (`ascensus_withdrawal_id`),
  CONSTRAINT `version_ascensus_withdrawal_state_ascensus_withdrawal_id` FOREIGN KEY (`ascensus_withdrawal_id`) REFERENCES `ascensus_withdrawals` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_audit_cash_flow_metadata`
--

DROP TABLE IF EXISTS `version_audit_cash_flow_metadata`;
CREATE TABLE `version_audit_cash_flow_metadata` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `audit_cash_flow_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` mediumtext,
  PRIMARY KEY (`id`),
  KEY `version_audit_cash_flow_metadata_audit_cash_flow_id` (`audit_cash_flow_id`),
  CONSTRAINT `version_audit_cash_flow_metadata_audit_cash_flow_id` FOREIGN KEY (`audit_cash_flow_id`) REFERENCES `audit_cash_flow` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_audit_cash_flow_state`
--

DROP TABLE IF EXISTS `version_audit_cash_flow_state`;
CREATE TABLE `version_audit_cash_flow_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `audit_cash_flow_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_audit_cash_flow_state_audit_cash_flow_id` (`audit_cash_flow_id`),
  CONSTRAINT `version_audit_cash_flow_state_audit_cash_flow_id` FOREIGN KEY (`audit_cash_flow_id`) REFERENCES `audit_cash_flow` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_brokerage_account_state`
--

DROP TABLE IF EXISTS `version_brokerage_account_state`;
CREATE TABLE `version_brokerage_account_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `brokerage_account_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `version_brokerage_account_state_brokerage_account_id` (`brokerage_account_id`),
  CONSTRAINT `fkey_brokerage_account_state` FOREIGN KEY (`brokerage_account_id`) REFERENCES `brokerage_accounts` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_bucket_state`
--

DROP TABLE IF EXISTS `version_bucket_state`;
CREATE TABLE `version_bucket_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `bucket_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_bucket_state_bucket_id` (`bucket_id`),
  CONSTRAINT `version_bucket_state_bucket_id` FOREIGN KEY (`bucket_id`) REFERENCES `buckets` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_bucket_to_cash_sweep_settlement_wire_batch_state`
--

DROP TABLE IF EXISTS `version_bucket_to_cash_sweep_settlement_wire_batch_state`;
CREATE TABLE `version_bucket_to_cash_sweep_settlement_wire_batch_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `bucket_to_cash_sweep_settlement_wire_batch_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_bucket_to_cash_sweep_settlement_wire_batch_state_id` (`bucket_to_cash_sweep_settlement_wire_batch_id`),
  CONSTRAINT `version_bucket_to_cash_sweep_settlement_wire_batch_state_id` FOREIGN KEY (`bucket_to_cash_sweep_settlement_wire_batch_id`) REFERENCES `bucket_to_cash_sweep_settlement_wire_batch` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_bucket_to_incoming_brokerage_wire_state`
--

DROP TABLE IF EXISTS `version_bucket_to_incoming_brokerage_wire_state`;
CREATE TABLE `version_bucket_to_incoming_brokerage_wire_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `bucket_to_incoming_brokerage_wire_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_bkt_to_icmg_brkg_wire_state_bkt_to_icmg_brkg_wire_id` (`bucket_to_incoming_brokerage_wire_id`),
  CONSTRAINT `version_bkt_to_icmg_brkg_wire_state_bkt_to_icmg_brkg_wire_id` FOREIGN KEY (`bucket_to_incoming_brokerage_wire_id`) REFERENCES `bucket_to_incoming_brokerage_wire` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_cash_sweep_settlement_wire_batch_state`
--

DROP TABLE IF EXISTS `version_cash_sweep_settlement_wire_batch_state`;
CREATE TABLE `version_cash_sweep_settlement_wire_batch_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `cash_sweep_settlement_wire_batch_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_cash_sweep_settlement_wire_batch_state_id` (`cash_sweep_settlement_wire_batch_id`),
  CONSTRAINT `version_cash_sweep_settlement_wire_batch_state_id` FOREIGN KEY (`cash_sweep_settlement_wire_batch_id`) REFERENCES `cash_sweep_settlement_wire_batches` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_cash_sweep_settlement_wire_drawdown_wire_id`
--

DROP TABLE IF EXISTS `version_cash_sweep_settlement_wire_drawdown_wire_id`;
CREATE TABLE `version_cash_sweep_settlement_wire_drawdown_wire_id` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `cash_sweep_settlement_wire_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL DEFAULT '0',
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `version_cash_sweep_settlement_wire_drawdown_wire_id_settlement` (`cash_sweep_settlement_wire_id`),
  CONSTRAINT `version_cash_sweep_settlement_wire_drawdown_wire_id_settlement` FOREIGN KEY (`cash_sweep_settlement_wire_id`) REFERENCES `cash_sweep_settlement_wires` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_cash_sweep_settlement_wire_state`
--

DROP TABLE IF EXISTS `version_cash_sweep_settlement_wire_state`;
CREATE TABLE `version_cash_sweep_settlement_wire_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `cash_sweep_settlement_wire_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL DEFAULT '0',
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_cash_sweep_settlement_wire_state_id` (`cash_sweep_settlement_wire_id`),
  CONSTRAINT `version_cash_sweep_settlement_wire_state_id` FOREIGN KEY (`cash_sweep_settlement_wire_id`) REFERENCES `cash_sweep_settlement_wires` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_cash_sweep_settlement_wire_wire_withdrawal_id`
--

DROP TABLE IF EXISTS `version_cash_sweep_settlement_wire_wire_withdrawal_id`;
CREATE TABLE `version_cash_sweep_settlement_wire_wire_withdrawal_id` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `cash_sweep_settlement_wire_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL DEFAULT '0',
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `version_cash_sweep_settlement_wire_id` (`cash_sweep_settlement_wire_id`),
  CONSTRAINT `version_cash_sweep_settlement_wire_id` FOREIGN KEY (`cash_sweep_settlement_wire_id`) REFERENCES `cash_sweep_settlement_wires` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_cbrs_asset_state`
--

DROP TABLE IF EXISTS `version_cbrs_asset_state`;
CREATE TABLE `version_cbrs_asset_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `cbrs_asset_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_cbrs_asset_state_cbrs_asset_id` (`cbrs_asset_id`),
  CONSTRAINT `version_cbrs_asset_state_cbrs_asset_id` FOREIGN KEY (`cbrs_asset_id`) REFERENCES `cbrs_assets` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_cbrs_file_state`
--

DROP TABLE IF EXISTS `version_cbrs_file_state`;
CREATE TABLE `version_cbrs_file_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `cbrs_file_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_cbrs_file_state_cbrs_file_id` (`cbrs_file_id`),
  CONSTRAINT `version_cbrs_file_state_cbrs_file_id` FOREIGN KEY (`cbrs_file_id`) REFERENCES `cbrs_files` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_cbrs_input_file_id`
--

DROP TABLE IF EXISTS `version_cbrs_input_file_id`;
CREATE TABLE `version_cbrs_input_file_id` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `cbrs_input_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `version_cbrs_input_file_id_cbrs_input_id` (`cbrs_input_id`),
  KEY `version_cbrs_input_file_id_value` (`value`),
  CONSTRAINT `version_cbrs_input_file_id_cbrs_input_id` FOREIGN KEY (`cbrs_input_id`) REFERENCES `cbrs_inputs` (`id`),
  CONSTRAINT `version_cbrs_input_file_id_value` FOREIGN KEY (`value`) REFERENCES `cbrs_files` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_cbrs_input_state`
--

DROP TABLE IF EXISTS `version_cbrs_input_state`;
CREATE TABLE `version_cbrs_input_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `cbrs_input_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_cbrs_input_state_cbrs_input_id` (`cbrs_input_id`),
  CONSTRAINT `version_cbrs_input_state_cbrs_input_id` FOREIGN KEY (`cbrs_input_id`) REFERENCES `cbrs_inputs` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_cbrs_output_state`
--

DROP TABLE IF EXISTS `version_cbrs_output_state`;
CREATE TABLE `version_cbrs_output_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `cbrs_output_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_cbrs_output_state_cbrs_output_id` (`cbrs_output_id`),
  CONSTRAINT `version_cbrs_output_state_cbrs_output_id` FOREIGN KEY (`cbrs_output_id`) REFERENCES `cbrs_outputs` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_cbrs_transfer_state`
--

DROP TABLE IF EXISTS `version_cbrs_transfer_state`;
CREATE TABLE `version_cbrs_transfer_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `cbrs_transfer_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_cbrs_transfer_state_cbrs_transfer_id` (`cbrs_transfer_id`),
  CONSTRAINT `version_cbrs_transfer_state_cbrs_transfer_id` FOREIGN KEY (`cbrs_transfer_id`) REFERENCES `cbrs_transfers` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_check_deposit_state`
--

DROP TABLE IF EXISTS `version_check_deposit_state`;
CREATE TABLE `version_check_deposit_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `check_deposit_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_check_deposit_state_check_deposit_id` (`check_deposit_id`),
  CONSTRAINT `version_check_deposit_state_check_deposit_id` FOREIGN KEY (`check_deposit_id`) REFERENCES `check_deposits` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_check_withdrawal_state`
--

DROP TABLE IF EXISTS `version_check_withdrawal_state`;
CREATE TABLE `version_check_withdrawal_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `check_withdrawal_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_check_state_check_id` (`check_withdrawal_id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_cross_account_transfer_state`
--

DROP TABLE IF EXISTS `version_cross_account_transfer_state`;
CREATE TABLE `version_cross_account_transfer_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `cross_account_transfer_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_cross_account_transfer_id` (`cross_account_transfer_id`),
  CONSTRAINT `version_cross_account_transfer_id` FOREIGN KEY (`cross_account_transfer_id`) REFERENCES `cross_account_transfers` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_deposit_amounts`
--

DROP TABLE IF EXISTS `version_deposit_amounts`;
CREATE TABLE `version_deposit_amounts` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `deposit_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `unscheduled_amount` decimal(20,2) NOT NULL,
  `scheduled_amount` decimal(20,2) NOT NULL,
  `received_amount` decimal(20,2) NOT NULL,
  `released_amount` decimal(20,2) NOT NULL,
  `scheduled_to_broker_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `sent_to_broker_amount` decimal(20,2) NOT NULL,
  `completed_amount` decimal(20,2) NOT NULL,
  `canceled_amount` decimal(20,2) NOT NULL,
  `returned_amount` decimal(20,2) NOT NULL,
  `returned_covered_from_bank_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `returned_from_broker_amount` decimal(20,2) NOT NULL,
  `returning_to_client_amount` decimal(20,2) NOT NULL,
  `scheduled_return_to_client_amount` decimal(20,2) NOT NULL,
  `returned_to_client_amount` decimal(20,2) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `fkey_version_deposit_amounts_deposit_id` (`deposit_id`),
  CONSTRAINT `fkey_version_deposit_amounts_deposit_id` FOREIGN KEY (`deposit_id`) REFERENCES `deposits` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_deposit_plan_next_deposit_date`
--

DROP TABLE IF EXISTS `version_deposit_plan_next_deposit_date`;
CREATE TABLE `version_deposit_plan_next_deposit_date` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `deposit_plan_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` date NOT NULL,
  PRIMARY KEY (`id`),
  KEY `deposit_plan_id` (`deposit_plan_id`),
  CONSTRAINT `version_deposit_plan_next_deposit_date_deposit_plan_id` FOREIGN KEY (`deposit_plan_id`) REFERENCES `deposit_plans` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_deposit_plan_state`
--

DROP TABLE IF EXISTS `version_deposit_plan_state`;
CREATE TABLE `version_deposit_plan_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `deposit_plan_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `deposit_plan_id` (`deposit_plan_id`),
  CONSTRAINT `version_deposit_plan_state_deposit_plan_id` FOREIGN KEY (`deposit_plan_id`) REFERENCES `deposit_plans` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_deposit_to_cash_sweep_settlement_wire_batch_state`
--

DROP TABLE IF EXISTS `version_deposit_to_cash_sweep_settlement_wire_batch_state`;
CREATE TABLE `version_deposit_to_cash_sweep_settlement_wire_batch_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `deposit_to_cash_sweep_settlement_wire_batch_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_deposit_to_cash_sweep_settlement_wire_batch_state_id` (`deposit_to_cash_sweep_settlement_wire_batch_id`),
  CONSTRAINT `version_deposit_to_cash_sweep_settlement_wire_batch_state_id` FOREIGN KEY (`deposit_to_cash_sweep_settlement_wire_batch_id`) REFERENCES `deposit_to_cash_sweep_settlement_wire_batch` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_deposit_to_intra_bank_transfer_state`
--

DROP TABLE IF EXISTS `version_deposit_to_intra_bank_transfer_state`;
CREATE TABLE `version_deposit_to_intra_bank_transfer_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `deposit_to_intra_bank_transfer_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_deposit_to_intra_bank_transfer_state_id` (`deposit_to_intra_bank_transfer_id`),
  CONSTRAINT `version_deposit_to_intra_bank_transfer_state_id` FOREIGN KEY (`deposit_to_intra_bank_transfer_id`) REFERENCES `deposit_to_intra_bank_transfer` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_drawdown_wire_state`
--

DROP TABLE IF EXISTS `version_drawdown_wire_state`;
CREATE TABLE `version_drawdown_wire_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `drawdown_wire_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_drawdown_wire_state_drawdown_wire_id` (`drawdown_wire_id`),
  CONSTRAINT `version_drawdown_wire_state_drawdown_wire_id` FOREIGN KEY (`drawdown_wire_id`) REFERENCES `drawdown_wires` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_dtc_acats_asset_state`
--

DROP TABLE IF EXISTS `version_dtc_acats_asset_state`;
CREATE TABLE `version_dtc_acats_asset_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `dtc_acats_asset_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_dtc_acats_asset_state_dtc_acats_id` (`dtc_acats_asset_id`),
  CONSTRAINT `version_dtc_acats_asset_state_dtc_acats_asset_id` FOREIGN KEY (`dtc_acats_asset_id`) REFERENCES `dtc_acats_assets` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_dtc_acats_dtc_acats_state`
--

DROP TABLE IF EXISTS `version_dtc_acats_dtc_acats_state`;
CREATE TABLE `version_dtc_acats_dtc_acats_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `dtc_acats_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_dtc_acats_dtc_acats_state_dtc_acats_id` (`dtc_acats_id`),
  CONSTRAINT `version_dtc_acats_dtc_acats_state_dtc_acats_id` FOREIGN KEY (`dtc_acats_id`) REFERENCES `dtc_acats` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_dtc_acats_transfer_rejection_type`
--

DROP TABLE IF EXISTS `version_dtc_acats_transfer_rejection_type`;
CREATE TABLE `version_dtc_acats_transfer_rejection_type` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `dtc_acats_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_dtc_acats_transfer_rejection_types_dtc_acats_id` (`dtc_acats_id`),
  CONSTRAINT `version_dtc_acats_transfer_rejection_types_dtc_acats_id` FOREIGN KEY (`dtc_acats_id`) REFERENCES `dtc_acats` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_dtc_transfer_contraparty`
--

DROP TABLE IF EXISTS `version_dtc_transfer_contraparty`;
CREATE TABLE `version_dtc_transfer_contraparty` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `dtc_transfer_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` mediumtext NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_dtc_transfer_contraparty_dtc_transfer_id` (`dtc_transfer_id`),
  CONSTRAINT `version_dtc_transfer_contraparty_dtc_transfer_id` FOREIGN KEY (`dtc_transfer_id`) REFERENCES `dtc_transfers` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_dtc_transfer_instruction_state`
--

DROP TABLE IF EXISTS `version_dtc_transfer_instruction_state`;
CREATE TABLE `version_dtc_transfer_instruction_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `dtc_transfer_instruction_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` text NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_dtc_transfer_instruction_state_dtc_instruction_id` (`dtc_transfer_instruction_id`),
  CONSTRAINT `version_dtc_transfer_instruction_state_dtc_instruction_id` FOREIGN KEY (`dtc_transfer_instruction_id`) REFERENCES `dtc_transfer_instructions` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_dtc_transfer_state`
--

DROP TABLE IF EXISTS `version_dtc_transfer_state`;
CREATE TABLE `version_dtc_transfer_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `dtc_transfer_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` mediumtext NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_dtc_transfer_state_dtc_transfer_id` (`dtc_transfer_id`),
  CONSTRAINT `version_dtc_transfer_state_dtc_transfer_id` FOREIGN KEY (`dtc_transfer_id`) REFERENCES `dtc_transfers` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_incoming_brokerage_wire_state`
--

DROP TABLE IF EXISTS `version_incoming_brokerage_wire_state`;
CREATE TABLE `version_incoming_brokerage_wire_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `incoming_brokerage_wire_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_incoming_brokerage_wire_state_created_at` (`created_at`),
  KEY `fkey_version_incoming_brokerage_wire_state` (`incoming_brokerage_wire_id`),
  CONSTRAINT `fkey_version_incoming_brokerage_wire_state` FOREIGN KEY (`incoming_brokerage_wire_id`) REFERENCES `incoming_brokerage_wires` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_intra_bank_transfer_state`
--

DROP TABLE IF EXISTS `version_intra_bank_transfer_state`;
CREATE TABLE `version_intra_bank_transfer_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `intra_bank_transfer_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_intra_bank_transfer_state_intra_bank_transfer_id` (`intra_bank_transfer_id`),
  CONSTRAINT `version_intra_bank_transfer_state_intra_bank_transfer_id` FOREIGN KEY (`intra_bank_transfer_id`) REFERENCES `intra_bank_transfers` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_intra_bank_transfer_wire_direct_deposit_id`
--

DROP TABLE IF EXISTS `version_intra_bank_transfer_wire_direct_deposit_id`;
CREATE TABLE `version_intra_bank_transfer_wire_direct_deposit_id` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `intra_bank_transfer_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `version_intra_bank_transfer_wire_direct_deposit_id` (`intra_bank_transfer_id`),
  CONSTRAINT `version_intra_bank_transfer_wire_direct_deposit_id` FOREIGN KEY (`intra_bank_transfer_id`) REFERENCES `intra_bank_transfers` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_intra_bank_transfer_wire_withdrawal_id`
--

DROP TABLE IF EXISTS `version_intra_bank_transfer_wire_withdrawal_id`;
CREATE TABLE `version_intra_bank_transfer_wire_withdrawal_id` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `intra_bank_transfer_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `version_intra_bank_transfer_wire_withdrawal_id` (`intra_bank_transfer_id`),
  CONSTRAINT `version_intra_bank_transfer_wire_withdrawal_id` FOREIGN KEY (`intra_bank_transfer_id`) REFERENCES `intra_bank_transfers` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_ira_contribution_contribution_type`
--

DROP TABLE IF EXISTS `version_ira_contribution_contribution_type`;
CREATE TABLE `version_ira_contribution_contribution_type` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `ira_contribution_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` text NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_ira_contribution_type_ira_contribution_id` (`ira_contribution_id`),
  CONSTRAINT `version_ira_contribution_type_ira_contribution_id` FOREIGN KEY (`ira_contribution_id`) REFERENCES `ira_contributions` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_ira_contribution_contribution_year`
--

DROP TABLE IF EXISTS `version_ira_contribution_contribution_year`;
CREATE TABLE `version_ira_contribution_contribution_year` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `ira_contribution_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` text NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_ira_contribution_year_ira_contribution_id` (`ira_contribution_id`),
  CONSTRAINT `version_ira_contribution_year_ira_contribution_id` FOREIGN KEY (`ira_contribution_id`) REFERENCES `ira_contributions` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_ira_contribution_postponed_contribution_reason`
--

DROP TABLE IF EXISTS `version_ira_contribution_postponed_contribution_reason`;
CREATE TABLE `version_ira_contribution_postponed_contribution_reason` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `ira_contribution_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `version_ira_postponed_contribution_info_ira_contribution_id` (`ira_contribution_id`),
  CONSTRAINT `version_ira_postponed_contribution_info_ira_contribution_id` FOREIGN KEY (`ira_contribution_id`) REFERENCES `ira_contributions` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_ira_contribution_review_state`
--

DROP TABLE IF EXISTS `version_ira_contribution_review_state`;
CREATE TABLE `version_ira_contribution_review_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `ira_contribution_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` text NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_ira_contribution_review_state_ira_contribution_id` (`ira_contribution_id`),
  CONSTRAINT `version_ira_contribution_review_state_ira_contribution_id` FOREIGN KEY (`ira_contribution_id`) REFERENCES `ira_contributions` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_ira_contribution_total_amount`
--

DROP TABLE IF EXISTS `version_ira_contribution_total_amount`;
CREATE TABLE `version_ira_contribution_total_amount` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `ira_contribution_total_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` text NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_ira_contribution_total_amount_ira_contribution_total_id` (`ira_contribution_total_id`),
  CONSTRAINT `version_ira_contribution_total_amount_ira_contribution_total_id` FOREIGN KEY (`ira_contribution_total_id`) REFERENCES `ira_contribution_totals` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_ira_contribution_total_completed_amount`
--

DROP TABLE IF EXISTS `version_ira_contribution_total_completed_amount`;
CREATE TABLE `version_ira_contribution_total_completed_amount` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `ira_contribution_total_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` text NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_completed_amount_ira_contribution_total_id` (`ira_contribution_total_id`),
  CONSTRAINT `version_completed_amount_ira_contribution_total_id` FOREIGN KEY (`ira_contribution_total_id`) REFERENCES `ira_contribution_totals` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_ira_contribution_total_pending_amount`
--

DROP TABLE IF EXISTS `version_ira_contribution_total_pending_amount`;
CREATE TABLE `version_ira_contribution_total_pending_amount` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `ira_contribution_total_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` text NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_pending_amount_ira_contribution_total_id` (`ira_contribution_total_id`),
  CONSTRAINT `version_pending_amount_ira_contribution_total_id` FOREIGN KEY (`ira_contribution_total_id`) REFERENCES `ira_contribution_totals` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_ira_distribution_withheld_amounts`
--

DROP TABLE IF EXISTS `version_ira_distribution_withheld_amounts`;
CREATE TABLE `version_ira_distribution_withheld_amounts` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `omni_ira_withdrawal_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `unscheduled_withheld_amount` decimal(20,2) NOT NULL,
  `scheduled_withheld_amount` decimal(20,2) NOT NULL,
  `completed_withheld_amount` decimal(20,2) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_ira_distribution_withheld_amounts_omni_ira_withdrawal_id` (`omni_ira_withdrawal_id`),
  CONSTRAINT `version_ira_distribution_withheld_amounts_omni_ira_withdrawal_id` FOREIGN KEY (`omni_ira_withdrawal_id`) REFERENCES `omni_ira_withdrawals` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_liquidation_loan_repayment_state`
--

DROP TABLE IF EXISTS `version_liquidation_loan_repayment_state`;
CREATE TABLE `version_liquidation_loan_repayment_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `liquidation_loan_repayment_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL DEFAULT '0',
  `value` text,
  PRIMARY KEY (`id`),
  KEY `version_liquidation_loan_repayment_state_liquidation_ln_repay_id` (`liquidation_loan_repayment_id`),
  CONSTRAINT `version_liquidation_loan_repayment_state_liquidation_ln_repay_id` FOREIGN KEY (`liquidation_loan_repayment_id`) REFERENCES `liquidation_loan_repayments` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_loan_state`
--

DROP TABLE IF EXISTS `version_loan_state`;
CREATE TABLE `version_loan_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `loan_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL DEFAULT '0',
  `value` text,
  PRIMARY KEY (`id`),
  KEY `fkey_version_loan_state_loan_id` (`loan_id`),
  CONSTRAINT `fkey_version_loan_state_loan_id` FOREIGN KEY (`loan_id`) REFERENCES `loans` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_loan_transfer_instructions`
--

DROP TABLE IF EXISTS `version_loan_transfer_instructions`;
CREATE TABLE `version_loan_transfer_instructions` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `loan_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL DEFAULT '0',
  `value` text,
  PRIMARY KEY (`id`),
  KEY `fkey_version_loan_transfer_instructions_loan_id` (`loan_id`),
  CONSTRAINT `fkey_version_loan_transfer_instructions_loan_id` FOREIGN KEY (`loan_id`) REFERENCES `loans` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_omni_deposit_amounts`
--

DROP TABLE IF EXISTS `version_omni_deposit_amounts`;
CREATE TABLE `version_omni_deposit_amounts` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `deposit_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `pending_liquidation_for_return_amount` decimal(20,2) NOT NULL,
  `liquidated_for_return_amount` decimal(20,2) NOT NULL,
  `returned_requested_from_broker_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  PRIMARY KEY (`id`),
  KEY `fkey_version_omni_deposit_amounts_deposit_id` (`deposit_id`),
  CONSTRAINT `fkey_version_omni_deposit_amounts_deposit_id` FOREIGN KEY (`deposit_id`) REFERENCES `omni_deposits` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_omni_deposit_state`
--

DROP TABLE IF EXISTS `version_omni_deposit_state`;
CREATE TABLE `version_omni_deposit_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `omni_deposit_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` bit(1) NOT NULL,
  `value` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_omni_deposit_state_omni_deposit_id` (`omni_deposit_id`),
  CONSTRAINT `version_omni_deposit_state_omni_deposit_id` FOREIGN KEY (`omni_deposit_id`) REFERENCES `omni_deposits` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_omni_ira_deposit_contribution_type`
--

DROP TABLE IF EXISTS `version_omni_ira_deposit_contribution_type`;
CREATE TABLE `version_omni_ira_deposit_contribution_type` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `omni_ira_deposit_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` text NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_ira_contribution_type_omni_ira_deposit_id` (`omni_ira_deposit_id`),
  CONSTRAINT `version_ira_contribution_type_omni_ira_deposit_id` FOREIGN KEY (`omni_ira_deposit_id`) REFERENCES `omni_ira_deposits` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_omni_ira_deposit_contribution_year`
--

DROP TABLE IF EXISTS `version_omni_ira_deposit_contribution_year`;
CREATE TABLE `version_omni_ira_deposit_contribution_year` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `omni_ira_deposit_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` text NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_ira_contribution_year_omni_ira_deposit_id` (`omni_ira_deposit_id`),
  CONSTRAINT `version_ira_contribution_year_omni_ira_deposit_id` FOREIGN KEY (`omni_ira_deposit_id`) REFERENCES `omni_ira_deposits` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_omni_ira_recurring_deposit_contribution_year`
--

DROP TABLE IF EXISTS `version_omni_ira_recurring_deposit_contribution_year`;
CREATE TABLE `version_omni_ira_recurring_deposit_contribution_year` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `omni_ira_recurring_deposit_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` text NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_ira_recurring_contribution_year_deposit_id` (`omni_ira_recurring_deposit_id`),
  CONSTRAINT `version_ira_recurring_contribution_year_deposit_id` FOREIGN KEY (`omni_ira_recurring_deposit_id`) REFERENCES `omni_ira_recurring_deposits` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_omni_ira_withdrawal_ira_distribution_instructions`
--

DROP TABLE IF EXISTS `version_omni_ira_withdrawal_ira_distribution_instructions`;
CREATE TABLE `version_omni_ira_withdrawal_ira_distribution_instructions` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `omni_ira_withdrawal_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL DEFAULT '0',
  `value` text,
  PRIMARY KEY (`id`),
  KEY `version_ira_distribution_instructions_ira_withdrawal_id` (`omni_ira_withdrawal_id`),
  CONSTRAINT `version_ira_distribution_instructions_ira_withdrawal_id` FOREIGN KEY (`omni_ira_withdrawal_id`) REFERENCES `omni_ira_withdrawals` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_omni_withdrawal_amounts`
--

DROP TABLE IF EXISTS `version_omni_withdrawal_amounts`;
CREATE TABLE `version_omni_withdrawal_amounts` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `withdrawal_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `requested_from_broker_amount` decimal(20,2) NOT NULL,
  `returned_scheduled_to_broker_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `returned_sent_to_broker_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  PRIMARY KEY (`id`),
  KEY `fkey_version_omni_withdrawal_amounts_withdrawal_id` (`withdrawal_id`),
  CONSTRAINT `fkey_version_omni_withdrawal_amounts_withdrawal_id` FOREIGN KEY (`withdrawal_id`) REFERENCES `omni_withdrawals` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_omni_withdrawal_state`
--

DROP TABLE IF EXISTS `version_omni_withdrawal_state`;
CREATE TABLE `version_omni_withdrawal_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `omni_withdrawal_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` bit(1) NOT NULL,
  `value` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_omni_withdrawal_state_omni_withdrawal_id` (`omni_withdrawal_id`),
  CONSTRAINT `version_omni_withdrawal_state_omni_withdrawal_id` FOREIGN KEY (`omni_withdrawal_id`) REFERENCES `omni_withdrawals` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_outgoing_brokerage_wire_state`
--

DROP TABLE IF EXISTS `version_outgoing_brokerage_wire_state`;
CREATE TABLE `version_outgoing_brokerage_wire_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `outgoing_brokerage_wire_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` bit(1) NOT NULL,
  `value` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_outgoing_brokerage_wire_state_outgoing_brokerage_wire_id` (`outgoing_brokerage_wire_id`),
  CONSTRAINT `version_outgoing_brokerage_wire_state_outgoing_brokerage_wire_id` FOREIGN KEY (`outgoing_brokerage_wire_id`) REFERENCES `outgoing_brokerage_wires` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_outgoing_brokerage_wire_wire_withdrawal_id`
--

DROP TABLE IF EXISTS `version_outgoing_brokerage_wire_wire_withdrawal_id`;
CREATE TABLE `version_outgoing_brokerage_wire_wire_withdrawal_id` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `outgoing_brokerage_wire_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `version_outgoing_brokerage_withdrawal_id_brokerage_wire_id` (`outgoing_brokerage_wire_id`),
  CONSTRAINT `fkey_version_outgoing_brokerage_withdrawal_id_brokerage_wire_id` FOREIGN KEY (`outgoing_brokerage_wire_id`) REFERENCES `outgoing_brokerage_wires` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_rbc_internal_transfer_contraparty`
--

DROP TABLE IF EXISTS `version_rbc_internal_transfer_contraparty`;
CREATE TABLE `version_rbc_internal_transfer_contraparty` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `rbc_internal_transfer_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` mediumtext NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_intra_rbc_transfer_contraparty_rbc_internal_transfer_id` (`rbc_internal_transfer_id`),
  CONSTRAINT `version_intra_rbc_transfer_contraparty_rbc_internal_transfer_id` FOREIGN KEY (`rbc_internal_transfer_id`) REFERENCES `rbc_internal_transfers` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_rbc_internal_transfer_instruction_state`
--

DROP TABLE IF EXISTS `version_rbc_internal_transfer_instruction_state`;
CREATE TABLE `version_rbc_internal_transfer_instruction_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `rbc_internal_transfer_instruction_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` mediumtext NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_intra_rbc_instruc_state_intra_rbc_instruc_id` (`rbc_internal_transfer_instruction_id`),
  CONSTRAINT `version_intra_rbc_instruc_state_intra_rbc_instruc_id` FOREIGN KEY (`rbc_internal_transfer_instruction_id`) REFERENCES `rbc_internal_transfer_instructions` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_rbc_internal_transfer_state`
--

DROP TABLE IF EXISTS `version_rbc_internal_transfer_state`;
CREATE TABLE `version_rbc_internal_transfer_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `rbc_internal_transfer_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` mediumtext NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_rbc_internal_transfer_state_rbc_internal_transfer_id` (`rbc_internal_transfer_id`),
  CONSTRAINT `version_rbc_internal_transfer_state_rbc_internal_transfer_id` FOREIGN KEY (`rbc_internal_transfer_id`) REFERENCES `rbc_internal_transfers` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_recurring_deposit_state`
--

DROP TABLE IF EXISTS `version_recurring_deposit_state`;
CREATE TABLE `version_recurring_deposit_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `recurring_deposit_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` bit(1) NOT NULL,
  `value` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_recurring_deposit_state_recurring_deposit_id` (`recurring_deposit_id`),
  CONSTRAINT `version_recurring_deposit_state_recurring_deposit_id` FOREIGN KEY (`recurring_deposit_id`) REFERENCES `recurring_deposits` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_residual_sweep_state`
--

DROP TABLE IF EXISTS `version_residual_sweep_state`;
CREATE TABLE `version_residual_sweep_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `residual_sweep_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_residual_sweep_state_residual_sweep_id` (`residual_sweep_id`),
  CONSTRAINT `version_residual_sweep_state_residual_sweep_id` FOREIGN KEY (`residual_sweep_id`) REFERENCES `residual_sweeps` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_residual_sweeps_residual_sweep_transfer_instructions`
--

DROP TABLE IF EXISTS `version_residual_sweeps_residual_sweep_transfer_instructions`;
CREATE TABLE `version_residual_sweeps_residual_sweep_transfer_instructions` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `residual_sweep_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` mediumtext NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_residual_sweep_transfer_instructions_residual_sweep_id` (`residual_sweep_id`),
  CONSTRAINT `version_residual_sweep_transfer_instructions_residual_sweep_id` FOREIGN KEY (`residual_sweep_id`) REFERENCES `residual_sweeps` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_sweep_deposit_state`
--

DROP TABLE IF EXISTS `version_sweep_deposit_state`;
CREATE TABLE `version_sweep_deposit_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `sweep_deposit_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` bit(1) NOT NULL,
  `value` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_sweep_deposit_state_sweep_deposit_id` (`sweep_deposit_id`),
  CONSTRAINT `version_sweep_deposit_state_sweep_deposit_id` FOREIGN KEY (`sweep_deposit_id`) REFERENCES `sweep_deposits` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_sweep_withdrawal_state`
--

DROP TABLE IF EXISTS `version_sweep_withdrawal_state`;
CREATE TABLE `version_sweep_withdrawal_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `sweep_withdrawal_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` bit(1) NOT NULL,
  `value` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_sweep_withdrawal_state_sweep_withdrawal_id` (`sweep_withdrawal_id`),
  CONSTRAINT `version_sweep_withdrawal_state_sweep_withdrawal_id` FOREIGN KEY (`sweep_withdrawal_id`) REFERENCES `sweep_withdrawals` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_tagged_routing_number_tag`
--

DROP TABLE IF EXISTS `version_tagged_routing_number_tag`;
CREATE TABLE `version_tagged_routing_number_tag` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `tagged_routing_number_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL DEFAULT '0',
  `value` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_tagged_routing_number_tag_tagged_routing_number_id` (`tagged_routing_number_id`),
  CONSTRAINT `version_tagged_routing_number_tag_tagged_routing_number_id` FOREIGN KEY (`tagged_routing_number_id`) REFERENCES `tagged_routing_numbers` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_toggle_state`
--

DROP TABLE IF EXISTS `version_toggle_state`;
CREATE TABLE `version_toggle_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `toggle_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` tinyint(1) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `toggle_id` (`toggle_id`),
  KEY `created_at` (`created_at`),
  CONSTRAINT `toggle_id` FOREIGN KEY (`toggle_id`) REFERENCES `toggles` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_unexpected_bai2_transaction_state`
--

DROP TABLE IF EXISTS `version_unexpected_bai2_transaction_state`;
CREATE TABLE `version_unexpected_bai2_transaction_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `unexpected_bai2_transaction_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` text NOT NULL,
  PRIMARY KEY (`id`),
  KEY `unexpected_bai2_transaction_id` (`unexpected_bai2_transaction_id`),
  CONSTRAINT `version_unexpected_bai2_transaction_state_ibfk_1` FOREIGN KEY (`unexpected_bai2_transaction_id`) REFERENCES `unexpected_bai2_transactions` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_user_request_state`
--

DROP TABLE IF EXISTS `version_user_request_state`;
CREATE TABLE `version_user_request_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_request_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL DEFAULT '0',
  `value` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `version_user_request_state_user_request_id` (`user_request_id`),
  CONSTRAINT `version_user_request_state_user_request_id` FOREIGN KEY (`user_request_id`) REFERENCES `user_requests` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_wells_ach_direct_deposit_state`
--

DROP TABLE IF EXISTS `version_wells_ach_direct_deposit_state`;
CREATE TABLE `version_wells_ach_direct_deposit_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `wells_ach_direct_deposit_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_wells_ach_dd_state_wells_ach_direct_deposit_id` (`wells_ach_direct_deposit_id`),
  CONSTRAINT `version_wells_ach_dd_state_wells_ach_direct_deposit_id` FOREIGN KEY (`wells_ach_direct_deposit_id`) REFERENCES `wells_ach_direct_deposits` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_wells_ach_transfer_state`
--

DROP TABLE IF EXISTS `version_wells_ach_transfer_state`;
CREATE TABLE `version_wells_ach_transfer_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `wells_ach_transfer_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_wells_ach_transfer_state_wells_ach_transfer_id` (`wells_ach_transfer_id`),
  CONSTRAINT `version_wells_ach_transfer_state_wells_ach_transfer_id` FOREIGN KEY (`wells_ach_transfer_id`) REFERENCES `wells_ach_transfers` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_wells_check_deposit_state`
--

DROP TABLE IF EXISTS `version_wells_check_deposit_state`;
CREATE TABLE `version_wells_check_deposit_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `wells_check_deposit_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_wells_check_deposit_state_wells_check_deposit_id` (`wells_check_deposit_id`),
  CONSTRAINT `version_wells_check_deposit_state_wells_check_deposit_id` FOREIGN KEY (`wells_check_deposit_id`) REFERENCES `wells_check_deposits` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_wells_check_state`
--

DROP TABLE IF EXISTS `version_wells_check_state`;
CREATE TABLE `version_wells_check_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `wells_check_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_wells_check_state_wells_check_id` (`wells_check_id`),
  CONSTRAINT `version_wells_check_state_wells_check_id` FOREIGN KEY (`wells_check_id`) REFERENCES `wells_checks` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_wells_custodian_account_wpic_number`
--

DROP TABLE IF EXISTS `version_wells_custodian_account_wpic_number`;
CREATE TABLE `version_wells_custodian_account_wpic_number` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `wells_custodian_account_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `value` (`value`),
  KEY `version_wells_custodian_wpic_wells_custodian_account_id` (`wells_custodian_account_id`),
  CONSTRAINT `fkey_version_wells_custodian_wpic_wells_custodian_account_id` FOREIGN KEY (`wells_custodian_account_id`) REFERENCES `wells_custodian_accounts` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_wells_wire_direct_deposit_state`
--

DROP TABLE IF EXISTS `version_wells_wire_direct_deposit_state`;
CREATE TABLE `version_wells_wire_direct_deposit_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `wells_wire_direct_deposit_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_wells_wire_dd_state_wells_wire_direct_deposit_id` (`wells_wire_direct_deposit_id`),
  CONSTRAINT `version_wells_wire_dd_state_wells_wire_direct_deposit_id` FOREIGN KEY (`wells_wire_direct_deposit_id`) REFERENCES `wells_wire_direct_deposits` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_wells_wire_state`
--

DROP TABLE IF EXISTS `version_wells_wire_state`;
CREATE TABLE `version_wells_wire_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `wells_wire_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_wells_wire_state_wells_wire_id` (`wells_wire_id`),
  CONSTRAINT `version_wells_wire_state_wells_wire_id` FOREIGN KEY (`wells_wire_id`) REFERENCES `wells_wires` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_wire_direct_deposit_state`
--

DROP TABLE IF EXISTS `version_wire_direct_deposit_state`;
CREATE TABLE `version_wire_direct_deposit_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `wire_direct_deposit_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_wire_direct_deposit_state_wire_direct_deposit_id` (`wire_direct_deposit_id`),
  CONSTRAINT `version_wire_direct_deposit_state_wire_direct_deposit_id` FOREIGN KEY (`wire_direct_deposit_id`) REFERENCES `wire_direct_deposits` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_wire_withdrawal_state`
--

DROP TABLE IF EXISTS `version_wire_withdrawal_state`;
CREATE TABLE `version_wire_withdrawal_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `wire_withdrawal_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_wire_state_wire_id` (`wire_withdrawal_id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_withdrawal_amounts`
--

DROP TABLE IF EXISTS `version_withdrawal_amounts`;
CREATE TABLE `version_withdrawal_amounts` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `withdrawal_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `pending_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `liquidated_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `received_from_broker_amount` decimal(20,2) NOT NULL,
  `unscheduled_to_client_amount` decimal(20,2) NOT NULL,
  `scheduled_to_client_amount` decimal(20,2) NOT NULL,
  `sent_to_client_amount` decimal(20,2) NOT NULL,
  `settled_to_client_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `completed_amount` decimal(20,2) NOT NULL,
  `canceled_amount` decimal(20,2) NOT NULL,
  `returned_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  PRIMARY KEY (`id`),
  KEY `fkey_version_withdrawal_amounts_withdrawal_id` (`withdrawal_id`),
  CONSTRAINT `fkey_version_withdrawal_amounts_withdrawal_id` FOREIGN KEY (`withdrawal_id`) REFERENCES `withdrawals` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_withdrawal_to_cash_sweep_settlement_wire_batch_state`
--

DROP TABLE IF EXISTS `version_withdrawal_to_cash_sweep_settlement_wire_batch_state`;
CREATE TABLE `version_withdrawal_to_cash_sweep_settlement_wire_batch_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `withdrawal_to_cash_sweep_settlement_wire_batch_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_withdrawal_to_cash_sweep_settlement_wire_batch_state_id` (`withdrawal_to_cash_sweep_settlement_wire_batch_id`),
  CONSTRAINT `version_withdrawal_to_cash_sweep_settlement_wire_batch_state_id` FOREIGN KEY (`withdrawal_to_cash_sweep_settlement_wire_batch_id`) REFERENCES `withdrawal_to_cash_sweep_settlement_wire_batch` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_withdrawal_to_intra_bank_transfer_state`
--

DROP TABLE IF EXISTS `version_withdrawal_to_intra_bank_transfer_state`;
CREATE TABLE `version_withdrawal_to_intra_bank_transfer_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `withdrawal_to_intra_bank_transfer_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL,
  `value` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `version_withdrawal_to_intra_bank_transfer_state_id` (`withdrawal_to_intra_bank_transfer_id`),
  CONSTRAINT `version_withdrawal_to_intra_bank_transfer_state_id` FOREIGN KEY (`withdrawal_to_intra_bank_transfer_id`) REFERENCES `withdrawal_to_intra_bank_transfer` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `version_withdrawal_transfer_instructions`
--

DROP TABLE IF EXISTS `version_withdrawal_transfer_instructions`;
CREATE TABLE `version_withdrawal_transfer_instructions` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `withdrawal_id` bigint(20) NOT NULL,
  `version` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL DEFAULT '0',
  `value` text,
  PRIMARY KEY (`id`),
  KEY `fkey_version_withdrawal_transfer_instructions_withdrawal_id` (`withdrawal_id`),
  CONSTRAINT `fkey_version_withdrawal_transfer_instructions_withdrawal_id` FOREIGN KEY (`withdrawal_id`) REFERENCES `withdrawals` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `wells_account_transfer_limits`
--

DROP TABLE IF EXISTS `wells_account_transfer_limits`;
CREATE TABLE `wells_account_transfer_limits` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `wells_bank_account_id` bigint(20) NOT NULL,
  `transfer_limit_type` varchar(255) NOT NULL,
  `limit_amount` decimal(20,2) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_limit_type_wells_bank_account` (`wells_bank_account_id`,`transfer_limit_type`),
  CONSTRAINT `fkey_wells_bank_account_transfer_limit` FOREIGN KEY (`wells_bank_account_id`) REFERENCES `wells_bank_accounts` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `wells_ach_direct_deposits`
--

DROP TABLE IF EXISTS `wells_ach_direct_deposits`;
CREATE TABLE `wells_ach_direct_deposits` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `version` bigint(20) NOT NULL DEFAULT '0',
  `created_at` datetime NOT NULL,
  `state` varchar(255) NOT NULL,
  `wpic_number` varchar(255) NOT NULL,
  `ach_direct_deposit_id` bigint(20) NOT NULL,
  `amount` decimal(14,2) DEFAULT NULL,
  `effective_date` date DEFAULT NULL,
  `odfi_routing_number` varchar(255) NOT NULL DEFAULT '',
  `trace_number` varchar(255) NOT NULL DEFAULT '',
  `wells_ach_direct_deposit_metadata` longtext,
  `persisted_perfect_receivables_file_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `wells_ach_direct_deposits_ach_direct_deposit_id` (`ach_direct_deposit_id`),
  KEY `wells_ach_direct_deposits_persisted_perfect_receivables_file_id` (`persisted_perfect_receivables_file_id`),
  CONSTRAINT `wells_ach_direct_deposits_ach_direct_deposit_id` FOREIGN KEY (`ach_direct_deposit_id`) REFERENCES `ach_direct_deposits` (`id`),
  CONSTRAINT `wells_ach_direct_deposits_persisted_perfect_receivables_file_id` FOREIGN KEY (`persisted_perfect_receivables_file_id`) REFERENCES `perfect_receivables_files` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `wells_ach_transfers`
--

DROP TABLE IF EXISTS `wells_ach_transfers`;
CREATE TABLE `wells_ach_transfers` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `version` bigint(20) NOT NULL DEFAULT '0',
  `created_at` datetime NOT NULL,
  `payment_record` text NOT NULL,
  `state` varchar(255) NOT NULL,
  `paymentid` varchar(255) NOT NULL,
  `transaction_date` date DEFAULT NULL,
  `expected_settlement_date` date DEFAULT NULL,
  `transfer_type` varchar(255) DEFAULT NULL,
  `amount` decimal(14,2) DEFAULT NULL,
  `bank_transfer_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `wells_ach_transfers_paymentid` (`paymentid`),
  UNIQUE KEY `wells_ach_transfers_bank_transfer_id2` (`bank_transfer_id`),
  KEY `wells_ach_transfers_bank_transfer_id` (`bank_transfer_id`),
  KEY `wells_ach_transfers_state` (`state`),
  CONSTRAINT `wells_ach_transfers_bank_transfer_id2` FOREIGN KEY (`bank_transfer_id`) REFERENCES `bank_transfers` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `wells_bank_accounts`
--

DROP TABLE IF EXISTS `wells_bank_accounts`;
CREATE TABLE `wells_bank_accounts` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `wells_fargo_account_number` varchar(255) NOT NULL,
  `account_title` varchar(255) NOT NULL,
  `payment_manager_ach_debitid` varchar(255) DEFAULT NULL,
  `payment_manager_ach_creditid` varchar(255) DEFAULT NULL,
  `perfect_receivables_group_number` int(11) DEFAULT NULL,
  `check_lockbox_number` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `wells_fargo_account_number_must_be_unique` (`wells_fargo_account_number`),
  UNIQUE KEY `wells_bank_accounts_account_title` (`account_title`),
  UNIQUE KEY `payment_manager_ach_debitid_must_be_unique` (`payment_manager_ach_debitid`),
  UNIQUE KEY `payment_manager_ach_creditid_must_be_unique` (`payment_manager_ach_creditid`),
  UNIQUE KEY `perfect_receivables_group_number_must_be_unique` (`perfect_receivables_group_number`),
  UNIQUE KEY `check_lockbox_number_must_be_unique` (`check_lockbox_number`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `wells_bank_balances`
--

DROP TABLE IF EXISTS `wells_bank_balances`;
CREATE TABLE `wells_bank_balances` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `wells_bank_account_id` bigint(20) NOT NULL,
  `amount` decimal(20,2) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `effective_date` date DEFAULT NULL,
  `opening_ledger_balance` decimal(20,2) DEFAULT NULL,
  `closing_ledger_balance` decimal(20,2) DEFAULT NULL,
  `available_balance` decimal(20,2) DEFAULT NULL,
  `collected_balance` decimal(20,2) DEFAULT NULL,
  `total_credits` decimal(20,2) DEFAULT NULL,
  `total_debits` decimal(20,2) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `one_balance_per_effective_date_per_account` (`wells_bank_account_id`,`effective_date`),
  CONSTRAINT `wealthfront_wells_bank_account_id` FOREIGN KEY (`wells_bank_account_id`) REFERENCES `wells_bank_accounts` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `wells_check_deposits`
--

DROP TABLE IF EXISTS `wells_check_deposits`;
CREATE TABLE `wells_check_deposits` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `version` bigint(20) NOT NULL,
  `created_at` datetime NOT NULL,
  `state` varchar(255) NOT NULL,
  `account_name_field` varchar(255) DEFAULT NULL,
  `wpic_number_field` varchar(255) DEFAULT NULL,
  `amount` decimal(14,2) NOT NULL,
  `effective_date` date NOT NULL,
  `postmark_date` date DEFAULT NULL,
  `check_deposit_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `wells_check_deposits_check_deposit_id` (`check_deposit_id`),
  CONSTRAINT `wells_check_deposits_check_deposit_id` FOREIGN KEY (`check_deposit_id`) REFERENCES `check_deposits` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `wells_checks`
--

DROP TABLE IF EXISTS `wells_checks`;
CREATE TABLE `wells_checks` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `version` bigint(20) NOT NULL DEFAULT '0',
  `created_at` datetime NOT NULL,
  `payment_record` text NOT NULL,
  `state` varchar(255) NOT NULL,
  `check_withdrawal_id` bigint(20) DEFAULT NULL,
  `paymentid` varchar(255) NOT NULL,
  `transfer_type` varchar(255) DEFAULT NULL,
  `amount` decimal(14,2) DEFAULT NULL,
  `expected_sent_date` date DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `wells_checks_paymentid` (`paymentid`),
  UNIQUE KEY `wells_checks_check_withdrawal_id2` (`check_withdrawal_id`),
  KEY `wells_checks_check_withdrawal_id` (`check_withdrawal_id`),
  KEY `wells_checks_state` (`state`),
  CONSTRAINT `wells_checks_check_withdrawal_id2` FOREIGN KEY (`check_withdrawal_id`) REFERENCES `check_withdrawals` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `wells_custodian_accounts`
--

DROP TABLE IF EXISTS `wells_custodian_accounts`;
CREATE TABLE `wells_custodian_accounts` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_at` datetime NOT NULL,
  `external_accountid` bigint(20) NOT NULL,
  `wpic_number` varchar(255) NOT NULL,
  `brokerage_account_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `wpic_number` (`wpic_number`),
  KEY `wells_custodian_accounts_brokerage_account_id` (`brokerage_account_id`),
  KEY `wells_custodian_accounts_external_accountid` (`external_accountid`),
  CONSTRAINT `wells_custodian_accounts_brokerage_account_id` FOREIGN KEY (`brokerage_account_id`) REFERENCES `brokerage_accounts` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `wells_files`
--

DROP TABLE IF EXISTS `wells_files`;
CREATE TABLE `wells_files` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `type` varchar(255) NOT NULL,
  `path` varchar(255) NOT NULL,
  `downloaded_at` datetime NOT NULL,
  `processed_at` datetime DEFAULT NULL,
  `errors` tinyint(1) NOT NULL DEFAULT '0',
  `uploaded_to_s3_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `wells_files_path` (`path`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `wells_safe_t_status_updates`
--

DROP TABLE IF EXISTS `wells_safe_t_status_updates`;
CREATE TABLE `wells_safe_t_status_updates` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `wells_safe_t_status_id` bigint(20) DEFAULT NULL,
  `availability` varchar(255) NOT NULL,
  `update_source` varchar(255) NOT NULL,
  `status_message` text,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `wells_safe_t_status_updates_wells_safe_t_statuses` (`wells_safe_t_status_id`),
  KEY `wells_safe_t_status_updates_created_at` (`created_at`),
  CONSTRAINT `wells_safe_t_status_updates_wells_safe_t_statuses` FOREIGN KEY (`wells_safe_t_status_id`) REFERENCES `wells_safe_t_statuses` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `wells_safe_t_statuses`
--

DROP TABLE IF EXISTS `wells_safe_t_statuses`;
CREATE TABLE `wells_safe_t_statuses` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `current_availability` varchar(255) NOT NULL,
  `last_update_time` datetime NOT NULL,
  `last_availability_change_time` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `wells_wire_direct_deposits`
--

DROP TABLE IF EXISTS `wells_wire_direct_deposits`;
CREATE TABLE `wells_wire_direct_deposits` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `version` bigint(20) NOT NULL DEFAULT '0',
  `created_at` datetime NOT NULL,
  `state` varchar(255) NOT NULL,
  `wpic_number` varchar(255) DEFAULT NULL,
  `wire_direct_deposit_id` bigint(20) NOT NULL,
  `amount` decimal(14,2) DEFAULT NULL,
  `effective_date` date DEFAULT NULL,
  `wire_reference_number` varchar(255) NOT NULL DEFAULT '',
  PRIMARY KEY (`id`),
  KEY `wells_wire_direct_deposits_wire_direct_deposit_id` (`wire_direct_deposit_id`),
  CONSTRAINT `wells_wire_direct_deposits_wire_direct_deposit_id` FOREIGN KEY (`wire_direct_deposit_id`) REFERENCES `wire_direct_deposits` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `wells_wires`
--

DROP TABLE IF EXISTS `wells_wires`;
CREATE TABLE `wells_wires` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `version` bigint(20) NOT NULL DEFAULT '0',
  `created_at` datetime NOT NULL,
  `payment_record` text NOT NULL,
  `state` varchar(255) NOT NULL,
  `wire_withdrawal_id` bigint(20) DEFAULT NULL,
  `paymentid` varchar(255) NOT NULL,
  `wells_wire_confirm_reference_number` bigint(20) DEFAULT NULL,
  `wells_transaction_reference_number` bigint(20) DEFAULT NULL,
  `transfer_type` varchar(255) DEFAULT NULL,
  `amount` decimal(14,2) DEFAULT NULL,
  `drawdown_wire_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `wells_wires_paymentid` (`paymentid`),
  UNIQUE KEY `wells_transaction_reference_number` (`wells_transaction_reference_number`),
  UNIQUE KEY `wells_wires_wire_withdrawal_id2` (`wire_withdrawal_id`),
  KEY `wells_wires_wire_withdrawal_id` (`wire_withdrawal_id`),
  KEY `wells_wires_state` (`state`),
  KEY `wells_wires_drawdown_wire_id` (`drawdown_wire_id`),
  CONSTRAINT `wells_wires_drawdown_wire_id` FOREIGN KEY (`drawdown_wire_id`) REFERENCES `drawdown_wires` (`id`),
  CONSTRAINT `wells_wires_wire_withdrawal_id2` FOREIGN KEY (`wire_withdrawal_id`) REFERENCES `wire_withdrawals` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `wire_direct_deposits`
--

DROP TABLE IF EXISTS `wire_direct_deposits`;
CREATE TABLE `wire_direct_deposits` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `state` varchar(255) NOT NULL,
  `wells_wire_direct_deposit_id` bigint(20) DEFAULT NULL,
  `release_date` date DEFAULT NULL,
  `wire_counter_party_metadata` longtext,
  PRIMARY KEY (`id`),
  KEY `wire_direct_deposits_wells_wire_direct_deposit_id` (`wells_wire_direct_deposit_id`),
  KEY `wire_direct_deposits_id` (`id`),
  CONSTRAINT `wire_direct_deposits_id` FOREIGN KEY (`id`) REFERENCES `bank_transfers` (`id`),
  CONSTRAINT `wire_direct_deposits_wells_wire_direct_deposit_id` FOREIGN KEY (`wells_wire_direct_deposit_id`) REFERENCES `wells_wire_direct_deposits` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `wire_withdrawals`
--

DROP TABLE IF EXISTS `wire_withdrawals`;
CREATE TABLE `wire_withdrawals` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `wire` text,
  `state` varchar(255) NOT NULL,
  `wells_wire_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `wire_withdrawals_wells_wire_id` (`wells_wire_id`),
  KEY `wire_withdrawals_state` (`state`),
  CONSTRAINT `wire_withdrawals_wells_wire_id` FOREIGN KEY (`wells_wire_id`) REFERENCES `wells_wires` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `withdrawal_approval_statuses`
--

DROP TABLE IF EXISTS `withdrawal_approval_statuses`;
CREATE TABLE `withdrawal_approval_statuses` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `version` int(11) NOT NULL,
  `withdrawal_id` bigint(20) NOT NULL,
  `notes` mediumtext NOT NULL,
  PRIMARY KEY (`id`),
  KEY `withdrawal_approval_statuses_withdrawal_id` (`withdrawal_id`),
  CONSTRAINT `withdrawal_approval_statuses_withdrawal_id` FOREIGN KEY (`withdrawal_id`) REFERENCES `withdrawals` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `withdrawal_bank_transfers`
--

DROP TABLE IF EXISTS `withdrawal_bank_transfers`;
CREATE TABLE `withdrawal_bank_transfers` (
  `withdrawal_id` bigint(20) NOT NULL,
  `bank_transfer_id` bigint(20) NOT NULL,
  PRIMARY KEY (`withdrawal_id`,`bank_transfer_id`),
  KEY `withdrawal_bank_transfers_bank_transfer_id` (`bank_transfer_id`),
  CONSTRAINT `withdrawal_bank_transfers_bank_transfer_id` FOREIGN KEY (`bank_transfer_id`) REFERENCES `bank_transfers` (`id`),
  CONSTRAINT `withdrawal_bank_transfers_withdrawal_id` FOREIGN KEY (`withdrawal_id`) REFERENCES `withdrawals` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Table structure for table `withdrawal_events`
--

DROP TABLE IF EXISTS `withdrawal_events`;
CREATE TABLE `withdrawal_events` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `type` varchar(255) NOT NULL,
  `created_at` datetime NOT NULL,
  `effective_date` date NOT NULL,
  `amount` decimal(20,2) NOT NULL,
  `withdrawal_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `withdrawal_events_withdrawal_id` (`withdrawal_id`),
  KEY `withdrawal_events_type` (`type`),
  KEY `withdrawal_events_created_at` (`created_at`),
  KEY `withdrawal_events_effective_date` (`effective_date`),
  CONSTRAINT `withdrawal_events_withdrawal_id` FOREIGN KEY (`withdrawal_id`) REFERENCES `withdrawals` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `withdrawal_returning_bank_transfers`
--

DROP TABLE IF EXISTS `withdrawal_returning_bank_transfers`;
CREATE TABLE `withdrawal_returning_bank_transfers` (
  `withdrawal_id` bigint(20) NOT NULL,
  `bank_transfer_id` bigint(20) NOT NULL,
  PRIMARY KEY (`withdrawal_id`,`bank_transfer_id`),
  KEY `withdrawal_returning_bank_transfers_bank_transfer_id` (`bank_transfer_id`),
  CONSTRAINT `withdrawal_returning_bank_transfers_bank_transfer_id` FOREIGN KEY (`bank_transfer_id`) REFERENCES `bank_transfers` (`id`),
  CONSTRAINT `withdrawal_returning_bank_transfers_withdrawal_id` FOREIGN KEY (`withdrawal_id`) REFERENCES `withdrawals` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Table structure for table `withdrawal_to_cash_sweep_settlement_wire_batch`
--

DROP TABLE IF EXISTS `withdrawal_to_cash_sweep_settlement_wire_batch`;
CREATE TABLE `withdrawal_to_cash_sweep_settlement_wire_batch` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `version` int(11) NOT NULL DEFAULT '0',
  `withdrawal_id` bigint(20) NOT NULL,
  `cash_sweep_settlement_wire_batch_id` bigint(20) NOT NULL,
  `amount` decimal(20,2) NOT NULL,
  `entity_direction` varchar(255) NOT NULL DEFAULT 'FORWARD',
  `state` varchar(255) NOT NULL DEFAULT 'COMPLETED',
  PRIMARY KEY (`id`),
  KEY `withdrawal_to_cash_sweep_settlement_wire_batch_withdrawal_id` (`withdrawal_id`),
  KEY `withdrawal_to_cash_sweep_settlement_wire_batch_batch_id` (`cash_sweep_settlement_wire_batch_id`),
  CONSTRAINT `withdrawal_to_cash_sweep_settlement_wire_batch_batch_id` FOREIGN KEY (`cash_sweep_settlement_wire_batch_id`) REFERENCES `cash_sweep_settlement_wire_batches` (`id`),
  CONSTRAINT `withdrawal_to_cash_sweep_settlement_wire_batch_withdrawal_id` FOREIGN KEY (`withdrawal_id`) REFERENCES `withdrawals` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `withdrawal_to_incoming_brokerage_wire`
--

DROP TABLE IF EXISTS `withdrawal_to_incoming_brokerage_wire`;
CREATE TABLE `withdrawal_to_incoming_brokerage_wire` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `withdrawal_id` bigint(20) NOT NULL,
  `incoming_brokerage_wire_id` bigint(20) NOT NULL,
  `amount` decimal(20,2) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `withdrawal_to_incoming_brokerage_wire_withdrawal_id` (`withdrawal_id`),
  KEY `withdrawal_to_incoming_brokerage_wire_incoming_brokerage_wire_id` (`incoming_brokerage_wire_id`),
  CONSTRAINT `withdrawal_to_incoming_brokerage_wire_incoming_brokerage_wire_id` FOREIGN KEY (`incoming_brokerage_wire_id`) REFERENCES `incoming_brokerage_wires` (`id`),
  CONSTRAINT `withdrawal_to_incoming_brokerage_wire_withdrawal_id` FOREIGN KEY (`withdrawal_id`) REFERENCES `withdrawals` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `withdrawal_to_intra_bank_transfer`
--

DROP TABLE IF EXISTS `withdrawal_to_intra_bank_transfer`;
CREATE TABLE `withdrawal_to_intra_bank_transfer` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `version` int(11) DEFAULT '0',
  `withdrawal_id` bigint(20) NOT NULL,
  `intra_bank_transfer_id` bigint(20) NOT NULL,
  `amount` decimal(20,2) NOT NULL,
  `entity_direction` varchar(255) NOT NULL DEFAULT 'FORWARD',
  `state` varchar(255) NOT NULL DEFAULT 'COMPLETED',
  `bank_transaction_queued_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `withdrawal_to_intra_bank_transfer_withdrawal_id` (`withdrawal_id`),
  KEY `withdrawal_to_intra_bank_transfer_intra_bank_transfer_id` (`intra_bank_transfer_id`),
  CONSTRAINT `withdrawal_to_intra_bank_transfer_intra_bank_transfer_id` FOREIGN KEY (`intra_bank_transfer_id`) REFERENCES `intra_bank_transfers` (`id`),
  CONSTRAINT `withdrawal_to_intra_bank_transfer_withdrawal_id` FOREIGN KEY (`withdrawal_id`) REFERENCES `withdrawals` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `withdrawal_to_outgoing_brokerage_wire`
--

DROP TABLE IF EXISTS `withdrawal_to_outgoing_brokerage_wire`;
CREATE TABLE `withdrawal_to_outgoing_brokerage_wire` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `withdrawal_id` bigint(20) NOT NULL,
  `outgoing_brokerage_wire_id` bigint(20) NOT NULL,
  `amount` decimal(20,2) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `withdrawal_to_outgoing_brokerage_wire_withdrawal_id` (`withdrawal_id`),
  KEY `withdrawal_to_outgoing_brokerage_wire_outgoing_brokerage_wire_id` (`outgoing_brokerage_wire_id`),
  CONSTRAINT `withdrawal_to_outgoing_brokerage_wire_outgoing_brokerage_wire_id` FOREIGN KEY (`outgoing_brokerage_wire_id`) REFERENCES `outgoing_brokerage_wires` (`id`),
  CONSTRAINT `withdrawal_to_outgoing_brokerage_wire_withdrawal_id` FOREIGN KEY (`withdrawal_id`) REFERENCES `withdrawals` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

--
-- Table structure for table `withdrawals`
--

DROP TABLE IF EXISTS `withdrawals`;
CREATE TABLE `withdrawals` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `version` bigint(20) NOT NULL,
  `created_at` datetime NOT NULL,
  `brokerage_account_id` bigint(20) DEFAULT NULL,
  `amount` decimal(20,2) DEFAULT NULL,
  `full_withdrawal` tinyint(1) NOT NULL DEFAULT '0',
  `transfer_instructions` text,
  `ach_relationship_id` bigint(20) DEFAULT NULL,
  `pending_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `liquidated_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `received_from_broker_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `unscheduled_to_client_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `scheduled_to_client_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `sent_to_client_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `settled_to_client_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `completed_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `canceled_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `returned_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `risk_metric_requestid` bigint(20) DEFAULT NULL,
  `user_request_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `withdrawals_brokerage_account_id` (`brokerage_account_id`),
  KEY `withdrawals_ach_relationship_fkey` (`ach_relationship_id`),
  KEY `withdrawals_user_requests_user_request_id` (`user_request_id`),
  CONSTRAINT `withdrawals_ach_relationship_fkey` FOREIGN KEY (`ach_relationship_id`) REFERENCES `ach_relationships` (`id`),
  CONSTRAINT `withdrawals_brokerage_account_id` FOREIGN KEY (`brokerage_account_id`) REFERENCES `brokerage_accounts` (`id`),
  CONSTRAINT `withdrawals_user_requests_user_request_id` FOREIGN KEY (`user_request_id`) REFERENCES `user_requests` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=*********** DEFAULT CHARSET=utf8;

-- Dump completed on 2019-10-28 19:25:57

INSERT INTO schema_log (schema_version, date) VALUES (3, now());
