CREATE TABLE umb_return_evaluations (
  id                                BIGINT(20)   NOT NULL AUTO_INCREMENT,
  trace                             VARCHAR(255) NOT NULL,
  evaluated_at                      DATETIME     NOT NULL,
  result                            VARCHAR(255) NOT NULL,
  umb_account_number_information_id BIGINT(20)   NULL,
  PRIMARY KEY (id),
  INDEX umb_return_evaluations_trace (trace),
  CONSTRAINT umb_return_evaluations_umb_account_number_information_id
    FOREIGN KEY (umb_account_number_information_id)
    REFERENCES umb_account_number_information (id)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 AUTO_INCREMENT = ***********;

CREATE TABLE version_umb_return_evaluation_result (
  id                                  BIGINT(20)   NOT NULL AUTO_INCREMENT,
  umb_return_evaluation_id            BIGINT(20)   NOT NULL,
  version                             INT          NOT NULL,
  created_at                          DATETIME     NOT NULL,
  deleted                             TINYINT(1)   NOT NULL,
  value                               VARCHAR(255) NOT NULL,
  PRIMARY KEY (id),
  CONSTRAINT version_umb_return_evaluation_result
    FOREIGN KEY (umb_return_evaluation_id)
    REFERENCES umb_return_evaluations (id)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 AUTO_INCREMENT = ***********;

INSERT INTO schema_log (schema_version, date) VALUES (345, now());
