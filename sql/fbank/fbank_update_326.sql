CREATE TABLE ach_direct_withdrawals (
  id                            BIGINT          NOT NULL AUTO_INCREMENT,
  release_date                  DATE            NOT NULL,
  state                         VARCHAR(255)    NOT NULL,
  PRIMARY KEY (id),
  CONSTRAINT ach_direct_withdrawals_bank_transfer_id
    FOREIGN KEY (id) REFERENCES bank_transfers (id)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 AUTO_INCREMENT = ***********;

CREATE TABLE version_ach_direct_withdrawal_state (
  id                       BIGINT(20)   NOT NULL AUTO_INCREMENT,
  ach_direct_withdrawal_id BIGINT(20)   NOT NULL,
  version                  INT(11)      NOT NULL,
  created_at               DATETIME     NOT NULL,
  deleted                  TINYINT(1)   NOT NULL,
  value                    VARCHAR(255) NOT NULL,
  PRIMARY KEY (id),
  CONSTRAINT version_ach_direct_withdrawal_state_ach_direct_withdrawal_id
    FOREIGN KEY (ach_direct_withdrawal_id) REFERENCES ach_direct_withdrawals (id)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 AUTO_INCREMENT = ***********;

INSERT INTO schema_log (schema_version, DATE) VALUES (326, now());