CREATE TABLE tbs_sweep_account_reconciliation_results (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  tbs_sweep_account_id BIGINT(20) NOT NULL,
  effective_date DATE NOT NULL,
  reconciled TINYINT(1) NOT NULL,
  created_at DATETIME NOT NULL,
  PRIMARY KEY (id),
  CONSTRAINT tbs_account_reconciliations_sweep_account_id
    FOREIGN KEY (tbs_sweep_account_id)
    REFERENCES tbs_sweep_accounts (id)
) ENGINE = InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE tbs_sweep_account_reconciliation_issues (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  reconciliation_result_id BIGINT(20) NOT NULL,
  details TEXT NOT NULL,
  PRIMARY KEY (id),
  CONSTRAINT tbs_account_reconciliation_issues_result_id
    FOREIGN KEY (reconciliation_result_id)
    REFERENCES tbs_sweep_account_reconciliation_results (id)
) ENGINE = InnoDB DEFAULT CHARSET = utf8;

INSERT INTO schema_log (schema_version, date) VALUES (20, now());
