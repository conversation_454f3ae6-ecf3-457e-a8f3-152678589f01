CREATE TABLE service_restarts (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    created_at DATETIME NOT NULL,
    service_id VARCHAR(255) NOT NULL,
    revision VARCHAR(255) NOT NULL,
    state VARCHAR(255) NOT NULL,
    INDEX service_name_revision (service_id, revision),
    PRIMARY KEY (id)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;

CREATE TABLE version_service_restarts_state (
    id bigint(20) NOT NULL auto_increment,
    service_restart_id bigint(20) NOT NULL,
    version INT(11) NOT NULL,
    created_at datetime NOT NULL,
    deleted tinyint(1) DEFAULT NULL,
    value VARCHAR(255) NOT NULL,
    PRIMARY KEY (id),
    CONSTRAINT version_service_restarts_state_service_restart_id
        FOREIGN KEY (service_restart_id)
        REFERENCES service_restarts (id)
) ENGINE=InnoDB DEFAULT CHARSET = utf8mb4;

INSERT INTO schema_log (schema_version, date) values (22, now());