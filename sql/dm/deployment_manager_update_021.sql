CREATE TABLE service_query_metrics (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    created_at DATETIME NOT NULL,
    service_name VARCHAR(255) NOT NULL,
    service_id VARCHAR(255) NOT NULL,
    revision VARCHAR(255) NOT NULL,
    query VARCHAR(255) NOT NULL,
    total_count BIGINT(20) NOT NULL,
    failed_rate DOUBLE NOT NULL,
    invalid_rate DOUBLE NOT NULL,
    INDEX service_name_revision (service_name, revision),
    PRIMARY KEY (id)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;

CREATE TABLE service_downstream_metrics (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    created_at DATETIME NOT NULL,
    service_name VARCHAR(255) NOT NULL,
    service_id VARCHAR(255) NOT NULL,
    revision VARCHAR(255) NOT NULL,
    downstream_service_name VARCHAR(255) NOT NULL,
    downstream_service_id VARCHAR(255) NOT NULL,
    downstream_revision VARCHAR(255) NOT NULL,
    query VARCHAR(255) NOT NULL,
    total_count BIGINT(20) NOT NULL,
    failed_rate DOUBLE NOT NULL,
    invalid_rate DOUBLE NOT NULL,
    INDEX downstream_service_name_downstream_revision (downstream_service_name, downstream_revision),
    PRIMARY KEY (id)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;

insert into schema_log (schema_version, date) values (21, now());