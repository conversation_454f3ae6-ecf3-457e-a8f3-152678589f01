CREATE TABLE fpsl_account_loans (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    icg_fpsl_client_account_loan_id BIGINT(20) NOT NULL,
    effective_date DATE NOT NULL,
    created_at DATETIME NOT NULL,
    sub_account_id BIGINT(20) NOT NULL,
    borrower_code VARCHAR(255) NOT NULL,
    borrower_name VARCHAR(255) NOT NULL,
    instrument_id BIGINT(20) NOT NULL,
    settled_units DECIMAL(20,8) NOT NULL,
    PRIMARY KEY (id),
    UNIQUE INDEX idx_fpsl_account_loans_ed_acc_id (effective_date, sub_account_id),
    constraint fpsl_account_loans_sub_account_id foreign key (sub_account_id) references sub_accounts (id)
 ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

 INSERT INTO schema_log (schema_version, date) VALUES (849, NOW());