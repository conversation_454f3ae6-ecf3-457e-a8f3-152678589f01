/*
MariaDB [bi]> select table_name, round(((data_length + index_length) / 1024 / 1024), 2) as size_in_mb from information_schema.TABLES where table_schema = 'bi' and table_name = 'lots';
+------------+------------+
| table_name | size_in_mb |
+------------+------------+
| lots       | 27685.00   |
+------------+------------+
1 <USER> <GROUP> set

MariaDB [bi]> select format(count(*), 0) from lots;
+---------------------+
| format(count(*), 0) |
+---------------------+
| 425,573,205         |
+---------------------+
1 <USER> <GROUP> set

MariaDB [bi]> select table_name, column_name, referenced_table_name, referenced_column_name from information_schema.key_column_usage where referenced_table_name is not null and referenced_column_name is not null
              and table_schema = 'bi' and table_name = 'lots';
+------------+-------------+-----------------------+------------------------+
| table_name | column_name | referenced_table_name | referenced_column_name |
+------------+-------------+-----------------------+------------------------+
0 <USER> <GROUP> set

MariaDB [bi]> desc lots
+--------------------------+---------------+------+-----+---------------------+----------------+
| Field                    | Type          | Null | Key | Default             | Extra          |
+--------------------------+---------------+------+-----+---------------------+----------------+
| id                       | bigint(20)    | NO   | PRI | <null>              | auto_increment |
| quantity                 | decimal(14,2) | NO   |     | 0.00                |                |
| cost                     | decimal(16,4) | NO   |     | 0.0000              |                |
| commission               | decimal(16,4) | NO   |     | 0.0000              |                |
| buy_date                 | datetime      | NO   |     | 0000-00-00 00:00:00 |                |
| full_fractional_quantity | decimal(20,8) | YES  |     | <null>              |                |
| full_fractional_cost     | decimal(16,4) | YES  |     | <null>              |                |
+--------------------------+---------------+------+-----+---------------------+----------------+
7 <USER> <GROUP> set

# stop check slave icinga checks
# stop slaves

# Dry Run
pt-online-schema-change --alter "MODIFY COLUMN quantity DECIMAL(20,5) NOT NULL DEFAULT 0.00000, ADD COLUMN has_full_fractional_quantity TINYINT(1) NOT NULL DEFAULT (full_fractional_quantity IS NOT NULL);" --charset utf8 --ask-pass D=bi,t=lots,A=utf8,h=localhost,u=wfadmin --alter-foreign-keys-method drop_swap --set-vars lock_wait_timeout=2 --dry-run

# Execution
pt-online-schema-change --alter "MODIFY COLUMN quantity DECIMAL(20,5) NOT NULL DEFAULT 0.00000, ADD COLUMN has_full_fractional_quantity TINYINT(1) NOT NULL DEFAULT (full_fractional_quantity IS NOT NULL);" --charset utf8 --ask-pass D=bi,t=lots,A=utf8,h=localhost,u=wfadmin --alter-foreign-keys-method drop_swap --set-vars lock_wait_timeout=2 --execute

*/

ALTER TABLE lots
    MODIFY COLUMN quantity DECIMAL(20,5) NOT NULL DEFAULT 0.00000,
    ADD COLUMN has_full_fractional_quantity TINYINT(1) NOT NULL DEFAULT (full_fractional_quantity IS NOT NULL);

INSERT INTO schema_log (schema_version, date) VALUES (622, now());

