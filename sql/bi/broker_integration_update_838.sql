CREATE TABLE version_trade_correction_group_approved_by (
    id bigint(20) NOT NULL AUTO_INCREMENT,
    trade_correction_group_id bigint(20) NOT NULL,
    version int(11) NOT NULL,
    created_at datetime NOT NULL,
    deleted tinyint(1) NOT NULL,
    value varchar(255) NOT NULL,
    PRIMARY KEY (id),
    FOREIGN KEY (trade_correction_group_id) REFERENCES trade_correction_groups(id)
);

ALTER TABLE trade_correction_groups
    ADD COLUMN created_by varchar(255) DEFAULT 'anonymous',
    ADD COLUMN approved_by varchar(255) DEFAULT NULL;

INSERT INTO schema_log (schema_version, DATE) VALUES (838, now());