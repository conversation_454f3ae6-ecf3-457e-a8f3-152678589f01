CREATE TABLE fpsl_earnings (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    trade_id BIGINT(20) NOT NULL,
    sub_account_id BIGINT(20) NOT NULL,
    stock_portfolio_id BIGINT(20) DEFAULT NULL,
    instrument_id BIGINT(20) NOT NULL,
    amount DECIMAL(22, 10) NOT NULL,
    effective_date DATE NOT NULL,
    created_at DATETIME NOT NULL,
    last_updated_at DATETIME NOT NULL,
    PRIMARY KEY (id),
    constraint fpsl_earnings_sub_account_id foreign key (sub_account_id) references sub_accounts (id),
    constraint fpsl_earnings_trade_id foreign key (trade_id) references trades (id),
    INDEX fpsl_earnings_effective_date_idx (effective_date)

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

 INSERT INTO schema_log (schema_version, date) VALUES (856, NOW());
