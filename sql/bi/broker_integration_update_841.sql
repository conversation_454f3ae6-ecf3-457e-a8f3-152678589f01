CREATE TABLE account_locks (
    id                  BIGINT(20)      NOT NULL AUTO_INCREMENT,
    type                VA<PERSON>HAR(63)     NOT NULL,
    sub_account_id      BIGINT(20)      NOT NULL,
    state               VARCHAR(63)     NOT NULL,
    created_at          DATETIME        NOT NULL,
    PRIMARY KEY (id),
    FOREI<PERSON><PERSON> KEY (sub_account_id) REFERENCES sub_accounts (id),
    INDEX account_lock_sub_account_id (sub_account_id)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;

CREATE TABLE version_account_lock_state (
  id                    BIGINT(20)      NOT NULL AUTO_INCREMENT,
  account_lock_id       BIGINT(20)      NOT NULL,
  version               INT(11)         NOT NULL,
  created_at            DATETIME        NOT NULL,
  deleted               TINYINT(1)      NOT NULL,
  value                 VARCHAR(63)     NOT NULL,
  PRIMARY KEY (id),
  INDEX (account_lock_id),
  <PERSON>OR<PERSON><PERSON><PERSON> KEY (account_lock_id) REFERENCES account_locks (id)
) engine=InnoDB DEFAULT charset=utf8mb4;

INSERT INTO schema_log (schema_version, date) VALUES (841, now());