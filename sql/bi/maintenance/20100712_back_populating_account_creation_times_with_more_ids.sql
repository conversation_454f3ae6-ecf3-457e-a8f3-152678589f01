-- select concat(
--    "update sub_accounts set creation_time = '",  created_at, 
--    "' where id = ", sub_accountid, " and creation_time is null;")
-- from applications where sub_accountid is not null;

update sub_accounts set creation_time = '2009-09-04 13:18:45' where id = 1 and creation_time is null;
update sub_accounts set creation_time = '2009-09-08 20:39:26' where id = 22 and creation_time is null; 
update sub_accounts set creation_time = '2009-09-12 00:03:55' where id = 6 and creation_time is null;
update sub_accounts set creation_time = '2009-09-13 20:19:19' where id = 7 and creation_time is null;
update sub_accounts set creation_time = '2009-09-22 15:51:14' where id = 10 and creation_time is null; 
update sub_accounts set creation_time = '2009-09-23 17:32:53' where id = 9 and creation_time is null;
update sub_accounts set creation_time = '2009-09-24 20:59:08' where id = 8 and creation_time is null;
update sub_accounts set creation_time = '2009-10-03 16:43:18' where id = 147 and creation_time is null;
update sub_accounts set creation_time = '2009-10-03 16:49:18' where id = 23 and creation_time is null; 
update sub_accounts set creation_time = '2009-10-03 23:46:07' where id = 19 and creation_time is null; 
update sub_accounts set creation_time = '2009-10-04 20:07:04' where id = 20 and creation_time is null; 
update sub_accounts set creation_time = '2009-10-05 00:52:45' where id = 24 and creation_time is null; 
update sub_accounts set creation_time = '2009-10-05 08:45:44' where id = 21 and creation_time is null; 
update sub_accounts set creation_time = '2009-10-05 17:54:55' where id = 25 and creation_time is null; 
update sub_accounts set creation_time = '2009-10-06 13:04:35' where id = 26 and creation_time is null; 
update sub_accounts set creation_time = '2009-10-06 14:01:28' where id = 48 and creation_time is null; 
update sub_accounts set creation_time = '2009-10-06 18:02:31' where id = 27 and creation_time is null; 
update sub_accounts set creation_time = '2009-10-06 19:04:50' where id = 34 and creation_time is null; 
update sub_accounts set creation_time = '2009-10-07 18:36:25' where id = 46 and creation_time is null; 
update sub_accounts set creation_time = '2009-10-07 19:17:22' where id = 29 and creation_time is null; 
update sub_accounts set creation_time = '2009-10-08 12:58:08' where id = 30 and creation_time is null; 
update sub_accounts set creation_time = '2009-10-09 20:17:30' where id = 39 and creation_time is null; 
update sub_accounts set creation_time = '2009-10-11 21:23:10' where id = 47 and creation_time is null; 
update sub_accounts set creation_time = '2009-10-12 10:32:23' where id = 40 and creation_time is null; 
update sub_accounts set creation_time = '2009-10-12 13:05:06' where id = 28 and creation_time is null; 
update sub_accounts set creation_time = '2009-10-13 03:16:54' where id = 44 and creation_time is null; 
update sub_accounts set creation_time = '2009-10-13 23:17:14' where id = 279 and creation_time is null;
update sub_accounts set creation_time = '2009-10-14 13:02:50' where id = 63 and creation_time is null; 
update sub_accounts set creation_time = '2009-10-15 15:45:47' where id = 55 and creation_time is null; 
update sub_accounts set creation_time = '2009-10-15 16:47:57' where id = 65 and creation_time is null; 
update sub_accounts set creation_time = '2009-10-19 00:57:02' where id = 72 and creation_time is null; 
update sub_accounts set creation_time = '2009-10-19 01:20:14' where id = 62 and creation_time is null; 
update sub_accounts set creation_time = '2009-10-19 02:40:31' where id = 77 and creation_time is null; 
update sub_accounts set creation_time = '2009-10-19 03:00:01' where id = 73 and creation_time is null; 
update sub_accounts set creation_time = '2009-10-19 03:05:54' where id = 120 and creation_time is null;
update sub_accounts set creation_time = '2009-10-19 08:13:48' where id = 66 and creation_time is null; 
update sub_accounts set creation_time = '2009-10-19 08:46:33' where id = 69 and creation_time is null; 
update sub_accounts set creation_time = '2009-10-19 09:25:06' where id = 67 and creation_time is null; 
update sub_accounts set creation_time = '2009-10-19 09:44:18' where id = 131 and creation_time is null;
update sub_accounts set creation_time = '2009-10-19 10:53:00' where id = 76 and creation_time is null; 
update sub_accounts set creation_time = '2009-10-19 11:39:23' where id = 74 and creation_time is null; 
update sub_accounts set creation_time = '2009-10-19 12:50:56' where id = 68 and creation_time is null; 
update sub_accounts set creation_time = '2009-10-19 13:18:54' where id = 70 and creation_time is null; 
update sub_accounts set creation_time = '2009-10-19 13:22:04' where id = 75 and creation_time is null; 
update sub_accounts set creation_time = '2009-10-19 13:44:08' where id = 194 and creation_time is null;
update sub_accounts set creation_time = '2009-10-19 13:49:39' where id = 162 and creation_time is null;
update sub_accounts set creation_time = '2009-10-19 14:16:12' where id = 1106 and creation_time is null; 
update sub_accounts set creation_time = '2009-10-19 14:17:16' where id = 650 and creation_time is null;
update sub_accounts set creation_time = '2009-10-19 15:25:58' where id = 78 and creation_time is null; 
update sub_accounts set creation_time = '2009-10-19 15:40:30' where id = 83 and creation_time is null; 
update sub_accounts set creation_time = '2009-10-19 16:00:55' where id = 544 and creation_time is null;
update sub_accounts set creation_time = '2009-10-19 17:06:52' where id = 135 and creation_time is null;
update sub_accounts set creation_time = '2009-10-19 17:15:21' where id = 296 and creation_time is null;
update sub_accounts set creation_time = '2009-10-19 18:24:53' where id = 80 and creation_time is null; 
update sub_accounts set creation_time = '2009-10-19 19:34:04' where id = 85 and creation_time is null; 
update sub_accounts set creation_time = '2009-10-19 19:51:27' where id = 183 and creation_time is null;
update sub_accounts set creation_time = '2009-10-19 20:28:46' where id = 164 and creation_time is null;
update sub_accounts set creation_time = '2009-10-19 20:37:06' where id = 519 and creation_time is null;
update sub_accounts set creation_time = '2009-10-19 21:04:31' where id = 87 and creation_time is null; 
update sub_accounts set creation_time = '2009-10-19 22:15:12' where id = 694 and creation_time is null;
update sub_accounts set creation_time = '2009-10-19 22:54:53' where id = 81 and creation_time is null; 
update sub_accounts set creation_time = '2009-10-19 23:14:25' where id = 79 and creation_time is null; 
update sub_accounts set creation_time = '2009-10-20 00:13:45' where id = 257 and creation_time is null;
update sub_accounts set creation_time = '2009-10-20 01:04:35' where id = 89 and creation_time is null; 
update sub_accounts set creation_time = '2009-10-20 01:10:21' where id = 104 and creation_time is null;
update sub_accounts set creation_time = '2009-10-20 01:58:38' where id = 86 and creation_time is null; 
update sub_accounts set creation_time = '2009-10-20 02:05:41' where id = 91 and creation_time is null; 
update sub_accounts set creation_time = '2009-10-20 02:21:09' where id = 98 and creation_time is null; 
update sub_accounts set creation_time = '2009-10-20 06:09:15' where id = 96 and creation_time is null; 
update sub_accounts set creation_time = '2009-10-20 08:34:27' where id = 90 and creation_time is null; 
update sub_accounts set creation_time = '2009-10-20 09:08:03' where id = 88 and creation_time is null; 
update sub_accounts set creation_time = '2009-10-20 10:19:24' where id = 94 and creation_time is null; 
update sub_accounts set creation_time = '2009-10-20 10:56:45' where id = 97 and creation_time is null; 
update sub_accounts set creation_time = '2009-10-20 11:17:11' where id = 100 and creation_time is null;
update sub_accounts set creation_time = '2009-10-20 13:01:22' where id = 95 and creation_time is null; 
update sub_accounts set creation_time = '2009-10-20 13:06:08' where id = 99 and creation_time is null; 
update sub_accounts set creation_time = '2009-10-20 13:15:49' where id = 93 and creation_time is null; 
update sub_accounts set creation_time = '2009-10-20 13:20:19' where id = 92 and creation_time is null; 
update sub_accounts set creation_time = '2009-10-20 13:29:31' where id = 82 and creation_time is null; 
update sub_accounts set creation_time = '2009-10-20 13:50:55' where id = 206 and creation_time is null;
update sub_accounts set creation_time = '2009-10-20 14:14:06' where id = 102 and creation_time is null;
update sub_accounts set creation_time = '2009-10-20 14:22:17' where id = 127 and creation_time is null;
update sub_accounts set creation_time = '2009-10-20 14:40:57' where id = 101 and creation_time is null;
update sub_accounts set creation_time = '2009-10-20 14:44:22' where id = 84 and creation_time is null; 
update sub_accounts set creation_time = '2009-10-20 14:48:28' where id = 103 and creation_time is null;
update sub_accounts set creation_time = '2009-10-20 16:56:03' where id = 312 and creation_time is null;
update sub_accounts set creation_time = '2009-10-20 17:02:49' where id = 105 and creation_time is null;
update sub_accounts set creation_time = '2009-10-20 17:25:04' where id = 109 and creation_time is null;
update sub_accounts set creation_time = '2009-10-20 17:58:10' where id = 114 and creation_time is null;
update sub_accounts set creation_time = '2009-10-20 19:02:18' where id = 110 and creation_time is null;
update sub_accounts set creation_time = '2009-10-20 19:21:14' where id = 138 and creation_time is null;
update sub_accounts set creation_time = '2009-10-20 20:44:22' where id = 112 and creation_time is null;
update sub_accounts set creation_time = '2009-10-20 21:11:21' where id = 116 and creation_time is null;
update sub_accounts set creation_time = '2009-10-20 21:59:35' where id = 108 and creation_time is null;
update sub_accounts set creation_time = '2009-10-20 22:23:25' where id = 557 and creation_time is null;
update sub_accounts set creation_time = '2009-10-20 22:23:50' where id = 115 and creation_time is null;
update sub_accounts set creation_time = '2009-10-20 22:30:31' where id = 824 and creation_time is null;
update sub_accounts set creation_time = '2009-10-20 23:32:50' where id = 107 and creation_time is null;
update sub_accounts set creation_time = '2009-10-20 23:38:54' where id = 132 and creation_time is null;
update sub_accounts set creation_time = '2009-10-20 23:41:51' where id = 106 and creation_time is null;
update sub_accounts set creation_time = '2009-10-21 02:27:10' where id = 356 and creation_time is null;
update sub_accounts set creation_time = '2009-10-21 10:40:31' where id = 182 and creation_time is null;
update sub_accounts set creation_time = '2009-10-21 11:57:17' where id = 167 and creation_time is null;
update sub_accounts set creation_time = '2009-10-21 12:38:37' where id = 124 and creation_time is null;
update sub_accounts set creation_time = '2009-10-21 12:39:54' where id = 137 and creation_time is null;
update sub_accounts set creation_time = '2009-10-21 12:53:07' where id = 129 and creation_time is null;
update sub_accounts set creation_time = '2009-10-21 13:22:12' where id = 111 and creation_time is null;
update sub_accounts set creation_time = '2009-10-21 13:58:14' where id = 113 and creation_time is null;
update sub_accounts set creation_time = '2009-10-21 16:30:08' where id = 125 and creation_time is null;
update sub_accounts set creation_time = '2009-10-21 18:04:30' where id = 467 and creation_time is null;
update sub_accounts set creation_time = '2009-10-21 20:30:36' where id = 298 and creation_time is null;
update sub_accounts set creation_time = '2009-10-21 21:46:03' where id = 128 and creation_time is null;
update sub_accounts set creation_time = '2009-10-21 22:53:28' where id = 287 and creation_time is null;
update sub_accounts set creation_time = '2009-10-21 23:28:08' where id = 130 and creation_time is null;
update sub_accounts set creation_time = '2009-10-22 18:56:26' where id = 143 and creation_time is null;
update sub_accounts set creation_time = '2009-10-22 19:36:02' where id = 141 and creation_time is null;
update sub_accounts set creation_time = '2009-10-22 19:46:06' where id = 142 and creation_time is null;
update sub_accounts set creation_time = '2009-10-22 23:28:36' where id = 145 and creation_time is null;
update sub_accounts set creation_time = '2009-10-22 23:54:14' where id = 144 and creation_time is null;
update sub_accounts set creation_time = '2009-10-23 00:27:41' where id = 139 and creation_time is null;
update sub_accounts set creation_time = '2009-10-23 09:11:06' where id = 146 and creation_time is null;
update sub_accounts set creation_time = '2009-10-23 12:24:37' where id = 140 and creation_time is null;
update sub_accounts set creation_time = '2009-10-23 13:49:51' where id = 150 and creation_time is null;
update sub_accounts set creation_time = '2009-10-23 14:28:43' where id = 152 and creation_time is null;
update sub_accounts set creation_time = '2009-10-24 02:34:28' where id = 184 and creation_time is null;
update sub_accounts set creation_time = '2009-10-24 06:22:46' where id = 617 and creation_time is null;
update sub_accounts set creation_time = '2009-10-24 07:50:55' where id = 151 and creation_time is null;
update sub_accounts set creation_time = '2009-10-24 10:53:29' where id = 148 and creation_time is null;
update sub_accounts set creation_time = '2009-10-24 11:35:41' where id = 157 and creation_time is null;
update sub_accounts set creation_time = '2009-10-24 14:13:38' where id = 156 and creation_time is null;
update sub_accounts set creation_time = '2009-10-24 19:11:11' where id = 155 and creation_time is null;
update sub_accounts set creation_time = '2009-10-24 19:58:16' where id = 163 and creation_time is null;
update sub_accounts set creation_time = '2009-10-25 09:35:41' where id = 158 and creation_time is null;
update sub_accounts set creation_time = '2009-10-25 13:16:12' where id = 159 and creation_time is null;
update sub_accounts set creation_time = '2009-10-25 14:45:55' where id = 160 and creation_time is null;
update sub_accounts set creation_time = '2009-10-25 18:15:24' where id = 161 and creation_time is null;
update sub_accounts set creation_time = '2009-10-25 22:00:08' where id = 181 and creation_time is null;
update sub_accounts set creation_time = '2009-10-25 23:26:42' where id = 153 and creation_time is null;
update sub_accounts set creation_time = '2009-10-26 04:05:18' where id = 211 and creation_time is null;
update sub_accounts set creation_time = '2009-10-26 14:05:57' where id = 154 and creation_time is null;
update sub_accounts set creation_time = '2009-10-26 15:43:41' where id = 168 and creation_time is null;
update sub_accounts set creation_time = '2009-10-26 17:19:22' where id = 166 and creation_time is null;
update sub_accounts set creation_time = '2009-10-26 18:38:05' where id = 165 and creation_time is null;
update sub_accounts set creation_time = '2009-10-26 20:44:34' where id = 171 and creation_time is null;
update sub_accounts set creation_time = '2009-10-26 23:34:06' where id = 172 and creation_time is null;
update sub_accounts set creation_time = '2009-10-27 00:56:38' where id = 169 and creation_time is null;
update sub_accounts set creation_time = '2009-10-27 13:54:03' where id = 173 and creation_time is null;
update sub_accounts set creation_time = '2009-10-27 14:18:27' where id = 170 and creation_time is null;
update sub_accounts set creation_time = '2009-10-27 14:50:52' where id = 174 and creation_time is null;
update sub_accounts set creation_time = '2009-10-27 14:53:31' where id = 175 and creation_time is null;
update sub_accounts set creation_time = '2009-10-27 22:46:49' where id = 178 and creation_time is null;
update sub_accounts set creation_time = '2009-10-28 00:43:42' where id = 179 and creation_time is null;
update sub_accounts set creation_time = '2009-10-28 05:32:57' where id = 176 and creation_time is null;
update sub_accounts set creation_time = '2009-10-28 09:10:15' where id = 238 and creation_time is null;
update sub_accounts set creation_time = '2009-10-28 13:45:55' where id = 177 and creation_time is null;
update sub_accounts set creation_time = '2009-10-28 18:18:09' where id = 185 and creation_time is null;
update sub_accounts set creation_time = '2009-10-28 18:23:29' where id = 186 and creation_time is null;
update sub_accounts set creation_time = '2009-10-28 19:18:48' where id = 189 and creation_time is null;
update sub_accounts set creation_time = '2009-10-28 19:44:29' where id = 190 and creation_time is null;
update sub_accounts set creation_time = '2009-10-28 23:05:03' where id = 192 and creation_time is null;
update sub_accounts set creation_time = '2009-10-29 02:42:23' where id = 188 and creation_time is null;
update sub_accounts set creation_time = '2009-10-29 09:34:46' where id = 199 and creation_time is null;
update sub_accounts set creation_time = '2009-10-29 09:45:05' where id = 187 and creation_time is null;
update sub_accounts set creation_time = '2009-10-29 11:27:01' where id = 191 and creation_time is null;
update sub_accounts set creation_time = '2009-10-29 16:55:37' where id = 200 and creation_time is null;
update sub_accounts set creation_time = '2009-10-29 21:10:26' where id = 207 and creation_time is null;
update sub_accounts set creation_time = '2009-10-29 22:42:31' where id = 196 and creation_time is null;
update sub_accounts set creation_time = '2009-10-30 00:14:41' where id = 195 and creation_time is null;
update sub_accounts set creation_time = '2009-10-30 09:07:25' where id = 198 and creation_time is null;
update sub_accounts set creation_time = '2009-10-30 12:58:49' where id = 197 and creation_time is null;
update sub_accounts set creation_time = '2009-10-30 16:29:18' where id = 209 and creation_time is null;
update sub_accounts set creation_time = '2009-10-30 20:08:45' where id = 210 and creation_time is null;
update sub_accounts set creation_time = '2009-10-31 15:50:36' where id = 204 and creation_time is null;
update sub_accounts set creation_time = '2009-11-01 11:29:46' where id = 202 and creation_time is null;
update sub_accounts set creation_time = '2009-11-01 16:34:26' where id = 205 and creation_time is null;
update sub_accounts set creation_time = '2009-11-01 23:22:57' where id = 203 and creation_time is null;
update sub_accounts set creation_time = '2009-11-02 10:44:03' where id = 208 and creation_time is null;
update sub_accounts set creation_time = '2009-11-02 13:25:06' where id = 237 and creation_time is null;
update sub_accounts set creation_time = '2009-11-02 17:39:24' where id = 680 and creation_time is null;
update sub_accounts set creation_time = '2009-11-02 20:36:15' where id = 213 and creation_time is null;
update sub_accounts set creation_time = '2009-11-03 09:53:34' where id = 215 and creation_time is null;
update sub_accounts set creation_time = '2009-11-03 10:22:31' where id = 214 and creation_time is null;
update sub_accounts set creation_time = '2009-11-03 15:12:26' where id = 220 and creation_time is null;
update sub_accounts set creation_time = '2009-11-03 15:26:14' where id = 226 and creation_time is null;
update sub_accounts set creation_time = '2009-11-03 18:11:35' where id = 218 and creation_time is null;
update sub_accounts set creation_time = '2009-11-03 19:12:36' where id = 221 and creation_time is null;
update sub_accounts set creation_time = '2009-11-04 00:16:58' where id = 219 and creation_time is null;
update sub_accounts set creation_time = '2009-11-04 09:15:08' where id = 222 and creation_time is null;
update sub_accounts set creation_time = '2009-11-04 10:47:29' where id = 223 and creation_time is null;
update sub_accounts set creation_time = '2009-11-04 14:01:55' where id = 224 and creation_time is null;
update sub_accounts set creation_time = '2009-11-04 15:32:11' where id = 234 and creation_time is null;
update sub_accounts set creation_time = '2009-11-04 23:41:02' where id = 232 and creation_time is null;
update sub_accounts set creation_time = '2009-11-05 06:40:56' where id = 231 and creation_time is null;
update sub_accounts set creation_time = '2009-11-05 12:47:45' where id = 233 and creation_time is null;
update sub_accounts set creation_time = '2009-11-05 13:00:37' where id = 230 and creation_time is null;
update sub_accounts set creation_time = '2009-11-05 13:14:56' where id = 228 and creation_time is null;
update sub_accounts set creation_time = '2009-11-05 13:50:23' where id = 229 and creation_time is null;
update sub_accounts set creation_time = '2009-11-05 21:51:34' where id = 236 and creation_time is null;
update sub_accounts set creation_time = '2009-11-06 04:34:52' where id = 240 and creation_time is null;
update sub_accounts set creation_time = '2009-11-06 11:03:55' where id = 239 and creation_time is null;
update sub_accounts set creation_time = '2009-11-06 14:30:35' where id = 235 and creation_time is null;
update sub_accounts set creation_time = '2009-11-06 15:16:40' where id = 270 and creation_time is null;
update sub_accounts set creation_time = '2009-11-06 16:04:15' where id = 253 and creation_time is null;
update sub_accounts set creation_time = '2009-11-06 20:55:23' where id = 256 and creation_time is null;
update sub_accounts set creation_time = '2009-11-06 21:19:16' where id = 245 and creation_time is null;
update sub_accounts set creation_time = '2009-11-07 02:50:25' where id = 250 and creation_time is null;
update sub_accounts set creation_time = '2009-11-07 12:49:04' where id = 246 and creation_time is null;
update sub_accounts set creation_time = '2009-11-07 14:18:39' where id = 567 and creation_time is null;
update sub_accounts set creation_time = '2009-11-07 17:42:18' where id = 251 and creation_time is null;
update sub_accounts set creation_time = '2009-11-07 17:56:45' where id = 330 and creation_time is null;
update sub_accounts set creation_time = '2009-11-07 18:23:46' where id = 247 and creation_time is null;
update sub_accounts set creation_time = '2009-11-07 19:11:19' where id = 252 and creation_time is null;
update sub_accounts set creation_time = '2009-11-07 22:39:22' where id = 248 and creation_time is null;
update sub_accounts set creation_time = '2009-11-08 15:49:31' where id = 254 and creation_time is null;
update sub_accounts set creation_time = '2009-11-09 14:43:49' where id = 249 and creation_time is null;
update sub_accounts set creation_time = '2009-11-09 15:43:17' where id = 260 and creation_time is null;
update sub_accounts set creation_time = '2009-11-10 00:19:29' where id = 258 and creation_time is null;
update sub_accounts set creation_time = '2009-11-10 15:55:06' where id = 259 and creation_time is null;
update sub_accounts set creation_time = '2009-11-10 18:20:52' where id = 255 and creation_time is null;
update sub_accounts set creation_time = '2009-11-10 20:08:08' where id = 262 and creation_time is null;
update sub_accounts set creation_time = '2009-11-11 10:54:16' where id = 261 and creation_time is null;
update sub_accounts set creation_time = '2009-11-11 12:19:29' where id = 263 and creation_time is null;
update sub_accounts set creation_time = '2009-11-11 14:43:22' where id = 266 and creation_time is null;
update sub_accounts set creation_time = '2009-11-12 11:43:31' where id = 997 and creation_time is null;
update sub_accounts set creation_time = '2009-11-12 21:37:16' where id = 278 and creation_time is null;
update sub_accounts set creation_time = '2009-11-13 06:30:57' where id = 277 and creation_time is null;
update sub_accounts set creation_time = '2009-11-13 10:44:31' where id = 646 and creation_time is null;
update sub_accounts set creation_time = '2009-11-13 18:42:29' where id = 283 and creation_time is null;
update sub_accounts set creation_time = '2009-11-14 07:06:37' where id = 284 and creation_time is null;
update sub_accounts set creation_time = '2009-11-14 13:01:26' where id = 289 and creation_time is null;
update sub_accounts set creation_time = '2009-11-14 18:55:30' where id = 285 and creation_time is null;
update sub_accounts set creation_time = '2009-11-15 02:06:57' where id = 282 and creation_time is null;
update sub_accounts set creation_time = '2009-11-16 01:57:54' where id = 281 and creation_time is null;
update sub_accounts set creation_time = '2009-11-16 14:17:05' where id = 280 and creation_time is null;
update sub_accounts set creation_time = '2009-11-16 21:31:22' where id = 288 and creation_time is null;
update sub_accounts set creation_time = '2009-11-17 07:13:33' where id = 292 and creation_time is null;
update sub_accounts set creation_time = '2009-11-17 09:37:22' where id = 291 and creation_time is null;
update sub_accounts set creation_time = '2009-11-17 11:59:48' where id = 293 and creation_time is null;
update sub_accounts set creation_time = '2009-11-17 16:56:00' where id = 290 and creation_time is null;
update sub_accounts set creation_time = '2009-11-17 17:39:46' where id = 294 and creation_time is null;
update sub_accounts set creation_time = '2009-11-17 21:19:02' where id = 326 and creation_time is null;
update sub_accounts set creation_time = '2009-11-18 00:49:58' where id = 295 and creation_time is null;
update sub_accounts set creation_time = '2009-11-18 15:30:55' where id = 299 and creation_time is null;
update sub_accounts set creation_time = '2009-11-18 16:20:27' where id = 297 and creation_time is null;
update sub_accounts set creation_time = '2009-11-20 04:15:32' where id = 313 and creation_time is null;
update sub_accounts set creation_time = '2009-11-20 13:54:00' where id = 321 and creation_time is null;
update sub_accounts set creation_time = '2009-11-21 20:27:55' where id = 322 and creation_time is null;
update sub_accounts set creation_time = '2009-11-21 21:14:10' where id = 324 and creation_time is null;
update sub_accounts set creation_time = '2009-11-22 20:47:59' where id = 327 and creation_time is null;
update sub_accounts set creation_time = '2009-11-23 13:01:14' where id = 325 and creation_time is null;
update sub_accounts set creation_time = '2009-11-23 13:57:42' where id = 323 and creation_time is null;
update sub_accounts set creation_time = '2009-11-23 17:33:32' where id = 329 and creation_time is null;
update sub_accounts set creation_time = '2009-11-24 02:39:08' where id = 328 and creation_time is null;
update sub_accounts set creation_time = '2009-11-26 16:16:38' where id = 335 and creation_time is null;
update sub_accounts set creation_time = '2009-11-27 10:33:25' where id = 339 and creation_time is null;
update sub_accounts set creation_time = '2009-11-27 11:01:07' where id = 336 and creation_time is null;
update sub_accounts set creation_time = '2009-11-27 14:23:40' where id = 338 and creation_time is null;
update sub_accounts set creation_time = '2009-11-27 17:52:19' where id = 334 and creation_time is null;
update sub_accounts set creation_time = '2009-11-28 10:40:37' where id = 342 and creation_time is null;
update sub_accounts set creation_time = '2009-11-29 00:33:14' where id = 337 and creation_time is null;
update sub_accounts set creation_time = '2009-11-29 20:36:39' where id = 344 and creation_time is null;
update sub_accounts set creation_time = '2009-11-30 12:14:46' where id = 340 and creation_time is null;
update sub_accounts set creation_time = '2009-12-01 09:18:55' where id = 341 and creation_time is null;
update sub_accounts set creation_time = '2009-12-01 14:40:29' where id = 343 and creation_time is null;
update sub_accounts set creation_time = '2009-12-02 13:09:01' where id = 345 and creation_time is null;
update sub_accounts set creation_time = '2009-12-02 13:30:10' where id = 357 and creation_time is null;
update sub_accounts set creation_time = '2009-12-02 16:50:53' where id = 368 and creation_time is null;
update sub_accounts set creation_time = '2009-12-02 20:21:35' where id = 355 and creation_time is null;
update sub_accounts set creation_time = '2009-12-03 09:28:54' where id = 360 and creation_time is null;
update sub_accounts set creation_time = '2009-12-04 14:02:30' where id = 359 and creation_time is null;
update sub_accounts set creation_time = '2009-12-04 14:24:43' where id = 358 and creation_time is null;
update sub_accounts set creation_time = '2009-12-04 15:43:20' where id = 369 and creation_time is null;
update sub_accounts set creation_time = '2009-12-05 04:57:15' where id = 364 and creation_time is null;
update sub_accounts set creation_time = '2009-12-05 10:07:48' where id = 365 and creation_time is null;
update sub_accounts set creation_time = '2009-12-05 16:49:30' where id = 366 and creation_time is null;
update sub_accounts set creation_time = '2009-12-06 19:20:40' where id = 367 and creation_time is null;
update sub_accounts set creation_time = '2009-12-06 20:47:03' where id = 370 and creation_time is null;
update sub_accounts set creation_time = '2009-12-07 18:08:32' where id = 402 and creation_time is null;
update sub_accounts set creation_time = '2009-12-07 19:49:07' where id = 373 and creation_time is null;
update sub_accounts set creation_time = '2009-12-07 23:31:38' where id = 374 and creation_time is null;
update sub_accounts set creation_time = '2009-12-09 06:46:51' where id = 377 and creation_time is null;
update sub_accounts set creation_time = '2009-12-09 13:59:02' where id = 388 and creation_time is null;
update sub_accounts set creation_time = '2009-12-09 23:56:20' where id = 390 and creation_time is null;
update sub_accounts set creation_time = '2009-12-10 02:56:34' where id = 391 and creation_time is null;
update sub_accounts set creation_time = '2009-12-10 04:20:40' where id = 393 and creation_time is null;
update sub_accounts set creation_time = '2009-12-10 10:22:29' where id = 389 and creation_time is null;
update sub_accounts set creation_time = '2009-12-10 11:55:26' where id = 713 and creation_time is null;
update sub_accounts set creation_time = '2009-12-10 19:34:57' where id = 392 and creation_time is null;
update sub_accounts set creation_time = '2009-12-12 07:48:07' where id = 397 and creation_time is null;
update sub_accounts set creation_time = '2009-12-13 12:40:43' where id = 396 and creation_time is null;
update sub_accounts set creation_time = '2009-12-13 14:40:07' where id = 566 and creation_time is null;
update sub_accounts set creation_time = '2009-12-13 19:25:38' where id = 413 and creation_time is null;
update sub_accounts set creation_time = '2009-12-13 19:27:38' where id = 399 and creation_time is null;
update sub_accounts set creation_time = '2009-12-13 21:14:59' where id = 401 and creation_time is null;
update sub_accounts set creation_time = '2009-12-14 13:01:26' where id = 398 and creation_time is null;
update sub_accounts set creation_time = '2009-12-14 13:10:24' where id = 395 and creation_time is null;
update sub_accounts set creation_time = '2009-12-14 19:28:05' where id = 403 and creation_time is null;
update sub_accounts set creation_time = '2009-12-14 20:06:54' where id = 404 and creation_time is null;
update sub_accounts set creation_time = '2009-12-15 12:20:50' where id = 407 and creation_time is null;
update sub_accounts set creation_time = '2009-12-16 00:46:20' where id = 405 and creation_time is null;
update sub_accounts set creation_time = '2009-12-16 01:06:27' where id = 416 and creation_time is null;
update sub_accounts set creation_time = '2009-12-16 01:54:31' where id = 406 and creation_time is null;
update sub_accounts set creation_time = '2009-12-16 09:21:33' where id = 606 and creation_time is null;
update sub_accounts set creation_time = '2009-12-16 11:47:52' where id = 410 and creation_time is null;
update sub_accounts set creation_time = '2009-12-16 11:49:18' where id = 414 and creation_time is null;
update sub_accounts set creation_time = '2009-12-16 12:03:58' where id = 411 and creation_time is null;
update sub_accounts set creation_time = '2009-12-16 12:13:29' where id = 409 and creation_time is null;
update sub_accounts set creation_time = '2009-12-16 12:46:58' where id = 415 and creation_time is null;
update sub_accounts set creation_time = '2009-12-16 12:53:59' where id = 408 and creation_time is null;
update sub_accounts set creation_time = '2009-12-16 13:25:24' where id = 426 and creation_time is null;
update sub_accounts set creation_time = '2009-12-16 13:47:35' where id = 412 and creation_time is null;
update sub_accounts set creation_time = '2009-12-16 16:06:04' where id = 418 and creation_time is null;
update sub_accounts set creation_time = '2009-12-16 16:11:01' where id = 417 and creation_time is null;
update sub_accounts set creation_time = '2009-12-16 16:44:42' where id = 420 and creation_time is null;
update sub_accounts set creation_time = '2009-12-16 21:04:54' where id = 421 and creation_time is null;
update sub_accounts set creation_time = '2009-12-16 21:52:24' where id = 419 and creation_time is null;
update sub_accounts set creation_time = '2009-12-17 00:16:42' where id = 422 and creation_time is null;
update sub_accounts set creation_time = '2009-12-17 04:38:29' where id = 423 and creation_time is null;
update sub_accounts set creation_time = '2009-12-18 02:25:53' where id = 430 and creation_time is null;
update sub_accounts set creation_time = '2009-12-18 03:32:45' where id = 427 and creation_time is null;
update sub_accounts set creation_time = '2009-12-18 10:03:36' where id = 429 and creation_time is null;
update sub_accounts set creation_time = '2009-12-18 12:23:32' where id = 428 and creation_time is null;
update sub_accounts set creation_time = '2009-12-19 00:05:36' where id = 762 and creation_time is null;
update sub_accounts set creation_time = '2009-12-20 19:38:06' where id = 431 and creation_time is null;
update sub_accounts set creation_time = '2009-12-22 19:58:31' where id = 436 and creation_time is null;
update sub_accounts set creation_time = '2009-12-22 23:39:48' where id = 437 and creation_time is null;
update sub_accounts set creation_time = '2009-12-23 10:24:49' where id = 438 and creation_time is null;
update sub_accounts set creation_time = '2009-12-23 23:00:17' where id = 439 and creation_time is null;
update sub_accounts set creation_time = '2009-12-24 11:33:00' where id = 462 and creation_time is null;
update sub_accounts set creation_time = '2009-12-24 12:58:46' where id = 461 and creation_time is null;
update sub_accounts set creation_time = '2009-12-26 15:28:52' where id = 442 and creation_time is null;
update sub_accounts set creation_time = '2009-12-26 16:38:42' where id = 441 and creation_time is null;
update sub_accounts set creation_time = '2009-12-26 17:20:33' where id = 443 and creation_time is null;
update sub_accounts set creation_time = '2009-12-26 20:24:55' where id = 445 and creation_time is null;
update sub_accounts set creation_time = '2009-12-27 10:39:28' where id = 440 and creation_time is null;
update sub_accounts set creation_time = '2009-12-28 10:43:04' where id = 444 and creation_time is null;
update sub_accounts set creation_time = '2009-12-28 16:07:59' where id = 451 and creation_time is null;
update sub_accounts set creation_time = '2009-12-28 16:17:06' where id = 453 and creation_time is null;
update sub_accounts set creation_time = '2009-12-28 18:07:22' where id = 452 and creation_time is null;
update sub_accounts set creation_time = '2009-12-28 20:17:26' where id = 457 and creation_time is null;
update sub_accounts set creation_time = '2009-12-29 13:59:15' where id = 454 and creation_time is null;
update sub_accounts set creation_time = '2009-12-29 23:15:47' where id = 458 and creation_time is null;
update sub_accounts set creation_time = '2009-12-30 14:27:21' where id = 459 and creation_time is null;
update sub_accounts set creation_time = '2009-12-30 20:31:35' where id = 460 and creation_time is null;
update sub_accounts set creation_time = '2009-12-31 12:45:16' where id = 466 and creation_time is null;
update sub_accounts set creation_time = '2009-12-31 14:53:59' where id = 463 and creation_time is null;
update sub_accounts set creation_time = '2009-12-31 18:26:10' where id = 470 and creation_time is null;
update sub_accounts set creation_time = '2009-12-31 23:08:08' where id = 471 and creation_time is null;
update sub_accounts set creation_time = '2010-01-01 12:55:29' where id = 468 and creation_time is null;
update sub_accounts set creation_time = '2010-01-02 17:39:56' where id = 469 and creation_time is null;
update sub_accounts set creation_time = '2010-01-02 18:51:34' where id = 474 and creation_time is null;
update sub_accounts set creation_time = '2010-03-03 21:17:43' where id = 531 and creation_time is null;
update sub_accounts set creation_time = '2010-01-03 21:27:29' where id = 473 and creation_time is null;
update sub_accounts set creation_time = '2010-01-04 07:40:44' where id = 472 and creation_time is null;
update sub_accounts set creation_time = '2010-01-04 14:06:16' where id = 480 and creation_time is null;
update sub_accounts set creation_time = '2010-01-04 18:05:09' where id = 479 and creation_time is null;
update sub_accounts set creation_time = '2010-01-04 19:06:30' where id = 477 and creation_time is null;
update sub_accounts set creation_time = '2010-01-04 19:28:35' where id = 478 and creation_time is null;
update sub_accounts set creation_time = '2010-01-05 08:40:31' where id = 481 and creation_time is null;
update sub_accounts set creation_time = '2010-01-06 01:32:39' where id = 485 and creation_time is null;
update sub_accounts set creation_time = '2010-01-06 11:49:06' where id = 484 and creation_time is null;
update sub_accounts set creation_time = '2010-01-06 14:15:42' where id = 486 and creation_time is null;
update sub_accounts set creation_time = '2010-01-06 14:38:44' where id = 488 and creation_time is null;
update sub_accounts set creation_time = '2010-01-06 19:24:48' where id = 490 and creation_time is null;
update sub_accounts set creation_time = '2010-01-06 23:29:06' where id = 487 and creation_time is null;
update sub_accounts set creation_time = '2010-01-07 14:01:16' where id = 489 and creation_time is null;
update sub_accounts set creation_time = '2010-01-07 17:23:22' where id = 495 and creation_time is null;
update sub_accounts set creation_time = '2010-01-08 01:07:25' where id = 498 and creation_time is null;
update sub_accounts set creation_time = '2010-01-08 02:06:41' where id = 496 and creation_time is null;
update sub_accounts set creation_time = '2010-01-08 10:39:44' where id = 497 and creation_time is null;
update sub_accounts set creation_time = '2010-01-08 12:04:26' where id = 494 and creation_time is null;
update sub_accounts set creation_time = '2010-01-09 02:13:53' where id = 512 and creation_time is null;
update sub_accounts set creation_time = '2010-01-09 13:16:02' where id = 513 and creation_time is null;
update sub_accounts set creation_time = '2010-01-09 14:43:26' where id = 568 and creation_time is null;
update sub_accounts set creation_time = '2010-01-10 09:50:18' where id = 514 and creation_time is null;
update sub_accounts set creation_time = '2010-01-10 21:41:47' where id = 511 and creation_time is null;
update sub_accounts set creation_time = '2010-01-11 18:03:17' where id = 621 and creation_time is null;
update sub_accounts set creation_time = '2010-01-12 00:26:23' where id = 521 and creation_time is null;
update sub_accounts set creation_time = '2010-01-12 00:35:50' where id = 518 and creation_time is null;
update sub_accounts set creation_time = '2010-01-12 11:38:16' where id = 520 and creation_time is null;
update sub_accounts set creation_time = '2010-01-12 16:44:32' where id = 529 and creation_time is null;
update sub_accounts set creation_time = '2010-01-13 10:34:59' where id = 532 and creation_time is null;
update sub_accounts set creation_time = '2010-01-13 13:47:38' where id = 530 and creation_time is null;
update sub_accounts set creation_time = '2010-01-13 18:24:10' where id = 533 and creation_time is null;
update sub_accounts set creation_time = '2010-01-14 14:20:03' where id = 623 and creation_time is null;
update sub_accounts set creation_time = '2010-01-14 15:34:39' where id = 539 and creation_time is null;
update sub_accounts set creation_time = '2010-01-14 20:05:31' where id = 542 and creation_time is null;
update sub_accounts set creation_time = '2010-01-14 23:47:32' where id = 540 and creation_time is null;
update sub_accounts set creation_time = '2010-01-15 01:06:15' where id = 543 and creation_time is null;
update sub_accounts set creation_time = '2010-01-15 12:51:24' where id = 541 and creation_time is null;
update sub_accounts set creation_time = '2010-01-15 18:02:42' where id = 545 and creation_time is null;
update sub_accounts set creation_time = '2010-01-15 20:11:21' where id = 549 and creation_time is null;
update sub_accounts set creation_time = '2010-01-15 20:33:14' where id = 546 and creation_time is null;
update sub_accounts set creation_time = '2010-01-16 01:18:58' where id = 547 and creation_time is null;
update sub_accounts set creation_time = '2010-01-16 16:42:21' where id = 550 and creation_time is null;
update sub_accounts set creation_time = '2010-01-17 13:17:55' where id = 551 and creation_time is null;
update sub_accounts set creation_time = '2010-01-17 18:00:21' where id = 554 and creation_time is null;
update sub_accounts set creation_time = '2010-01-18 14:25:36' where id = 555 and creation_time is null;
update sub_accounts set creation_time = '2010-01-18 16:15:38' where id = 553 and creation_time is null;
update sub_accounts set creation_time = '2010-01-18 18:40:00' where id = 552 and creation_time is null;
update sub_accounts set creation_time = '2010-01-18 22:57:02' where id = 556 and creation_time is null;
update sub_accounts set creation_time = '2010-01-19 04:30:15' where id = 548 and creation_time is null;
update sub_accounts set creation_time = '2010-01-19 17:04:16' where id = 561 and creation_time is null;
update sub_accounts set creation_time = '2010-01-19 19:47:50' where id = 558 and creation_time is null;
update sub_accounts set creation_time = '2010-01-19 22:49:17' where id = 560 and creation_time is null;
update sub_accounts set creation_time = '2010-01-20 13:44:52' where id = 559 and creation_time is null;
update sub_accounts set creation_time = '2010-01-20 16:20:42' where id = 565 and creation_time is null;
update sub_accounts set creation_time = '2010-01-20 17:17:24' where id = 564 and creation_time is null;
update sub_accounts set creation_time = '2010-01-22 12:00:47' where id = 689 and creation_time is null;
update sub_accounts set creation_time = '2010-01-22 23:22:08' where id = 569 and creation_time is null;
update sub_accounts set creation_time = '2010-01-23 06:10:34' where id = 776 and creation_time is null;
update sub_accounts set creation_time = '2010-01-23 16:23:03' where id = 570 and creation_time is null;
update sub_accounts set creation_time = '2010-01-25 13:16:27' where id = 1125 and creation_time is null; 
update sub_accounts set creation_time = '2010-01-25 13:39:56' where id = 571 and creation_time is null;
update sub_accounts set creation_time = '2010-01-25 16:15:33' where id = 572 and creation_time is null;
update sub_accounts set creation_time = '2010-01-26 19:35:22' where id = 573 and creation_time is null;
update sub_accounts set creation_time = '2010-01-27 16:49:31' where id = 577 and creation_time is null;
update sub_accounts set creation_time = '2010-01-27 23:50:17' where id = 575 and creation_time is null;
update sub_accounts set creation_time = '2010-01-28 11:36:45' where id = 574 and creation_time is null;
update sub_accounts set creation_time = '2010-01-28 12:58:03' where id = 576 and creation_time is null;
update sub_accounts set creation_time = '2010-01-28 16:01:18' where id = 593 and creation_time is null;
update sub_accounts set creation_time = '2010-01-30 16:24:33' where id = 590 and creation_time is null;
update sub_accounts set creation_time = '2010-01-31 00:42:36' where id = 582 and creation_time is null;
update sub_accounts set creation_time = '2010-02-01 07:43:19' where id = 583 and creation_time is null;
update sub_accounts set creation_time = '2010-02-01 19:53:21' where id = 586 and creation_time is null;
update sub_accounts set creation_time = '2010-02-03 05:32:34' where id = 588 and creation_time is null;
update sub_accounts set creation_time = '2010-02-03 12:15:05' where id = 587 and creation_time is null;
update sub_accounts set creation_time = '2010-02-03 18:30:01' where id = 931 and creation_time is null;
update sub_accounts set creation_time = '2010-03-04 12:04:19' where id = 594 and creation_time is null;
update sub_accounts set creation_time = '2010-02-06 01:04:09' where id = 595 and creation_time is null;
update sub_accounts set creation_time = '2010-02-09 16:57:21' where id = 596 and creation_time is null;
update sub_accounts set creation_time = '2010-02-09 18:52:33' where id = 711 and creation_time is null;
update sub_accounts set creation_time = '2010-02-09 18:58:15' where id = 598 and creation_time is null;
update sub_accounts set creation_time = '2010-02-09 19:38:32' where id = 597 and creation_time is null;
update sub_accounts set creation_time = '2010-02-10 13:03:27' where id = 599 and creation_time is null;
update sub_accounts set creation_time = '2010-02-10 20:39:54' where id = 616 and creation_time is null;
update sub_accounts set creation_time = '2010-02-14 13:08:28' where id = 600 and creation_time is null;
update sub_accounts set creation_time = '2010-02-14 20:45:49' where id = 602 and creation_time is null;
update sub_accounts set creation_time = '2010-02-15 12:35:02' where id = 601 and creation_time is null;
update sub_accounts set creation_time = '2010-02-16 14:54:14' where id = 625 and creation_time is null;
update sub_accounts set creation_time = '2010-02-16 19:14:48' where id = 603 and creation_time is null;
update sub_accounts set creation_time = '2010-02-16 23:48:25' where id = 604 and creation_time is null;
update sub_accounts set creation_time = '2010-02-17 17:33:01' where id = 605 and creation_time is null;
update sub_accounts set creation_time = '2010-02-18 00:19:10' where id = 607 and creation_time is null;
update sub_accounts set creation_time = '2010-02-18 14:34:53' where id = 608 and creation_time is null;
update sub_accounts set creation_time = '2010-02-18 20:16:44' where id = 612 and creation_time is null;
update sub_accounts set creation_time = '2009-11-02 15:47:56' where id = 611 and creation_time is null;
update sub_accounts set creation_time = '2009-10-14 06:34:04' where id = 614 and creation_time is null;
update sub_accounts set creation_time = '2009-10-20 13:44:29' where id = 613 and creation_time is null;
update sub_accounts set creation_time = '2009-10-22 06:42:52' where id = 615 and creation_time is null;
update sub_accounts set creation_time = '2010-02-19 20:20:50' where id = 618 and creation_time is null;
update sub_accounts set creation_time = '2010-02-19 20:28:50' where id = 698 and creation_time is null;
update sub_accounts set creation_time = '2010-02-20 12:42:31' where id = 620 and creation_time is null;
update sub_accounts set creation_time = '2010-02-20 19:58:39' where id = 736 and creation_time is null;
update sub_accounts set creation_time = '2010-02-20 21:35:48' where id = 622 and creation_time is null;
update sub_accounts set creation_time = '2010-02-22 00:54:55' where id = 619 and creation_time is null;
update sub_accounts set creation_time = '2010-02-22 13:07:19' where id = 624 and creation_time is null;
update sub_accounts set creation_time = '2010-02-22 17:16:43' where id = 628 and creation_time is null;
update sub_accounts set creation_time = '2010-02-23 00:43:29' where id = 632 and creation_time is null;
update sub_accounts set creation_time = '2010-02-23 03:36:42' where id = 630 and creation_time is null;
update sub_accounts set creation_time = '2010-02-23 11:24:45' where id = 629 and creation_time is null;
update sub_accounts set creation_time = '2010-02-24 05:29:53' where id = 633 and creation_time is null;
update sub_accounts set creation_time = '2010-02-24 10:21:11' where id = 634 and creation_time is null;
update sub_accounts set creation_time = '2010-02-24 12:25:48' where id = 635 and creation_time is null;
update sub_accounts set creation_time = '2010-02-25 12:29:44' where id = 642 and creation_time is null;
update sub_accounts set creation_time = '2010-02-25 18:28:58' where id = 636 and creation_time is null;
update sub_accounts set creation_time = '2010-02-26 07:16:34' where id = 637 and creation_time is null;
update sub_accounts set creation_time = '2010-02-26 13:36:10' where id = 638 and creation_time is null;
update sub_accounts set creation_time = '2010-02-27 13:51:59' where id = 645 and creation_time is null;
update sub_accounts set creation_time = '2010-02-27 13:52:46' where id = 640 and creation_time is null;
update sub_accounts set creation_time = '2010-02-27 14:58:46' where id = 639 and creation_time is null;
update sub_accounts set creation_time = '2010-02-27 23:00:42' where id = 641 and creation_time is null;
update sub_accounts set creation_time = '2010-03-01 05:02:15' where id = 785 and creation_time is null;
update sub_accounts set creation_time = '2010-03-01 06:57:45' where id = 643 and creation_time is null;
update sub_accounts set creation_time = '2010-03-01 12:28:02' where id = 644 and creation_time is null;
update sub_accounts set creation_time = '2010-03-01 15:17:00' where id = 647 and creation_time is null;
update sub_accounts set creation_time = '2010-03-02 13:26:19' where id = 705 and creation_time is null;
update sub_accounts set creation_time = '2010-03-02 13:38:56' where id = 648 and creation_time is null;
update sub_accounts set creation_time = '2010-03-02 14:55:43' where id = 651 and creation_time is null;
update sub_accounts set creation_time = '2010-03-02 16:41:08' where id = 649 and creation_time is null;
update sub_accounts set creation_time = '2010-03-03 11:28:24' where id = 654 and creation_time is null;
update sub_accounts set creation_time = '2010-03-03 22:36:38' where id = 1000 and creation_time is null; 
update sub_accounts set creation_time = '2010-03-04 03:43:41' where id = 666 and creation_time is null;
update sub_accounts set creation_time = '2010-03-04 17:44:56' where id = 662 and creation_time is null;
update sub_accounts set creation_time = '2010-03-04 22:00:01' where id = 661 and creation_time is null;
update sub_accounts set creation_time = '2010-03-05 10:20:46' where id = 727 and creation_time is null;
update sub_accounts set creation_time = '2010-03-05 11:44:39' where id = 660 and creation_time is null;
update sub_accounts set creation_time = '2010-03-05 12:49:22' where id = 657 and creation_time is null;
update sub_accounts set creation_time = '2010-03-05 13:34:57' where id = 663 and creation_time is null;
update sub_accounts set creation_time = '2010-03-05 23:28:02' where id = 665 and creation_time is null;
update sub_accounts set creation_time = '2010-03-08 01:43:19' where id = 664 and creation_time is null;
update sub_accounts set creation_time = '2010-03-08 03:23:42' where id = 659 and creation_time is null;
update sub_accounts set creation_time = '2010-03-09 14:51:16' where id = 669 and creation_time is null;
update sub_accounts set creation_time = '2010-03-09 14:52:22' where id = 2698 and creation_time is null; 
update sub_accounts set creation_time = '2010-03-09 15:58:48' where id = 690 and creation_time is null;
update sub_accounts set creation_time = '2010-03-09 18:27:49' where id = 677 and creation_time is null;
update sub_accounts set creation_time = '2010-03-10 00:20:35' where id = 667 and creation_time is null;
update sub_accounts set creation_time = '2010-03-10 07:49:08' where id = 1143 and creation_time is null; 
update sub_accounts set creation_time = '2010-03-10 17:32:56' where id = 668 and creation_time is null;
update sub_accounts set creation_time = '2010-03-10 20:00:45' where id = 670 and creation_time is null;
update sub_accounts set creation_time = '2010-03-11 15:17:22' where id = 674 and creation_time is null;
update sub_accounts set creation_time = '2010-03-11 16:43:11' where id = 676 and creation_time is null;
update sub_accounts set creation_time = '2010-03-11 22:39:53' where id = 675 and creation_time is null;
update sub_accounts set creation_time = '2010-03-11 22:55:29' where id = 673 and creation_time is null;
update sub_accounts set creation_time = '2010-03-12 10:55:33' where id = 678 and creation_time is null;
update sub_accounts set creation_time = '2010-03-12 13:22:41' where id = 679 and creation_time is null;
update sub_accounts set creation_time = '2010-03-12 14:30:07' where id = 681 and creation_time is null;
update sub_accounts set creation_time = '2010-03-13 10:02:58' where id = 685 and creation_time is null;
update sub_accounts set creation_time = '2010-03-13 11:12:51' where id = 684 and creation_time is null;
update sub_accounts set creation_time = '2010-03-13 14:30:47' where id = 692 and creation_time is null;
update sub_accounts set creation_time = '2010-03-13 16:32:28' where id = 687 and creation_time is null;
update sub_accounts set creation_time = '2010-03-13 18:24:33' where id = 700 and creation_time is null;
update sub_accounts set creation_time = '2010-03-13 19:55:59' where id = 691 and creation_time is null;
update sub_accounts set creation_time = '2010-03-13 23:00:50' where id = 688 and creation_time is null;
update sub_accounts set creation_time = '2010-03-14 10:44:52' where id = 695 and creation_time is null;
update sub_accounts set creation_time = '2010-03-14 11:47:04' where id = 699 and creation_time is null;
update sub_accounts set creation_time = '2010-03-14 15:03:11' where id = 686 and creation_time is null;
update sub_accounts set creation_time = '2010-03-14 16:00:01' where id = 697 and creation_time is null;
update sub_accounts set creation_time = '2010-03-14 17:42:53' where id = 696 and creation_time is null;
update sub_accounts set creation_time = '2010-03-14 19:17:57' where id = 693 and creation_time is null;
update sub_accounts set creation_time = '2010-03-15 15:41:31' where id = 703 and creation_time is null;
update sub_accounts set creation_time = '2010-03-15 17:57:22' where id = 704 and creation_time is null;
update sub_accounts set creation_time = '2009-12-09 06:46:51' where id = 706 and creation_time is null;
update sub_accounts set creation_time = '2010-03-16 16:06:19' where id = 712 and creation_time is null;
update sub_accounts set creation_time = '2010-03-17 04:50:52' where id = 710 and creation_time is null;
update sub_accounts set creation_time = '2010-03-17 16:39:33' where id = 719 and creation_time is null;
update sub_accounts set creation_time = '2010-03-17 23:17:37' where id = 720 and creation_time is null;
update sub_accounts set creation_time = '2010-03-18 00:34:55' where id = 721 and creation_time is null;
update sub_accounts set creation_time = '2010-03-18 03:03:35' where id = 718 and creation_time is null;
update sub_accounts set creation_time = '2010-03-18 10:03:43' where id = 717 and creation_time is null;
update sub_accounts set creation_time = '2010-03-18 10:05:47' where id = 722 and creation_time is null;
update sub_accounts set creation_time = '2009-12-02 13:30:10' where id = 723 and creation_time is null;
update sub_accounts set creation_time = '2010-03-18 16:42:05' where id = 764 and creation_time is null;
update sub_accounts set creation_time = '2010-03-18 17:48:23' where id = 726 and creation_time is null;
update sub_accounts set creation_time = '2010-03-19 04:07:19' where id = 725 and creation_time is null;
update sub_accounts set creation_time = '2010-03-19 16:11:58' where id = 734 and creation_time is null;
update sub_accounts set creation_time = '2010-03-19 17:21:45' where id = 732 and creation_time is null;
update sub_accounts set creation_time = '2010-03-20 01:02:30' where id = 735 and creation_time is null;
update sub_accounts set creation_time = '2010-03-21 14:19:17' where id = 747 and creation_time is null;
update sub_accounts set creation_time = '2010-03-22 16:56:47' where id = 746 and creation_time is null;
update sub_accounts set creation_time = '2010-03-22 17:09:18' where id = 737 and creation_time is null;
update sub_accounts set creation_time = '2010-03-22 18:35:52' where id = 745 and creation_time is null;
update sub_accounts set creation_time = '2010-03-22 20:48:54' where id = 738 and creation_time is null;
update sub_accounts set creation_time = '2010-03-22 21:55:26' where id = 740 and creation_time is null;
update sub_accounts set creation_time = '2010-03-22 22:51:35' where id = 741 and creation_time is null;
update sub_accounts set creation_time = '2010-03-22 23:27:43' where id = 742 and creation_time is null;
update sub_accounts set creation_time = '2010-03-23 01:34:19' where id = 743 and creation_time is null;
update sub_accounts set creation_time = '2010-03-23 05:45:23' where id = 744 and creation_time is null;
update sub_accounts set creation_time = '2010-03-23 20:07:50' where id = 750 and creation_time is null;
update sub_accounts set creation_time = '2010-03-23 22:49:27' where id = 751 and creation_time is null;
update sub_accounts set creation_time = '2010-03-23 22:51:46' where id = 752 and creation_time is null;
update sub_accounts set creation_time = '2010-03-24 01:08:18' where id = 756 and creation_time is null;
update sub_accounts set creation_time = '2010-03-24 12:43:29' where id = 753 and creation_time is null;
update sub_accounts set creation_time = '2010-03-24 17:52:21' where id = 754 and creation_time is null;
update sub_accounts set creation_time = '2010-03-24 20:59:27' where id = 755 and creation_time is null;
update sub_accounts set creation_time = '2010-03-25 20:09:46' where id = 757 and creation_time is null;
update sub_accounts set creation_time = '2010-03-25 21:09:36' where id = 758 and creation_time is null;
update sub_accounts set creation_time = '2010-03-26 03:16:21' where id = 759 and creation_time is null;
update sub_accounts set creation_time = '2010-03-26 04:05:59' where id = 760 and creation_time is null;
update sub_accounts set creation_time = '2010-03-26 12:07:49' where id = 761 and creation_time is null;
update sub_accounts set creation_time = '2010-03-26 14:07:05' where id = 763 and creation_time is null;
update sub_accounts set creation_time = '2010-03-26 20:30:24' where id = 765 and creation_time is null;
update sub_accounts set creation_time = '2010-03-28 13:55:45' where id = 768 and creation_time is null;
update sub_accounts set creation_time = '2010-03-28 14:12:16' where id = 769 and creation_time is null;
update sub_accounts set creation_time = '2010-03-28 18:48:54' where id = 770 and creation_time is null;
update sub_accounts set creation_time = '2010-03-28 20:17:16' where id = 771 and creation_time is null;
update sub_accounts set creation_time = '2010-03-28 23:23:26' where id = 772 and creation_time is null;
update sub_accounts set creation_time = '2010-03-28 23:39:41' where id = 773 and creation_time is null;
update sub_accounts set creation_time = '2010-03-29 09:52:22' where id = 774 and creation_time is null;
update sub_accounts set creation_time = '2010-03-29 13:03:24' where id = 775 and creation_time is null;
update sub_accounts set creation_time = '2010-03-29 21:07:44' where id = 777 and creation_time is null;
update sub_accounts set creation_time = '2010-03-29 21:47:07' where id = 778 and creation_time is null;
update sub_accounts set creation_time = '2010-03-30 20:31:55' where id = 779 and creation_time is null;
update sub_accounts set creation_time = '2010-03-31 01:08:35' where id = 780 and creation_time is null;
update sub_accounts set creation_time = '2010-03-31 01:23:01' where id = 781 and creation_time is null;
update sub_accounts set creation_time = '2010-03-31 02:17:08' where id = 782 and creation_time is null;
update sub_accounts set creation_time = '2010-03-31 04:07:09' where id = 783 and creation_time is null;
update sub_accounts set creation_time = '2010-03-31 10:58:54' where id = 962 and creation_time is null;
update sub_accounts set creation_time = '2010-03-31 14:08:51' where id = 784 and creation_time is null;
update sub_accounts set creation_time = '2010-03-31 14:47:31' where id = 786 and creation_time is null;
update sub_accounts set creation_time = '2010-03-31 18:53:25' where id = 787 and creation_time is null;
update sub_accounts set creation_time = '2010-03-31 20:36:12' where id = 788 and creation_time is null;
update sub_accounts set creation_time = '2010-04-01 03:30:41' where id = 789 and creation_time is null;
update sub_accounts set creation_time = '2010-04-01 03:50:56' where id = 790 and creation_time is null;
update sub_accounts set creation_time = '2010-04-01 11:02:08' where id = 791 and creation_time is null;
update sub_accounts set creation_time = '2010-04-01 14:45:45' where id = 792 and creation_time is null;
update sub_accounts set creation_time = '2010-04-01 15:25:05' where id = 793 and creation_time is null;
update sub_accounts set creation_time = '2010-04-01 18:36:29' where id = 812 and creation_time is null;
update sub_accounts set creation_time = '2010-04-01 21:54:00' where id = 794 and creation_time is null;
update sub_accounts set creation_time = '2010-04-01 22:52:42' where id = 795 and creation_time is null;
update sub_accounts set creation_time = '2010-04-02 02:01:54' where id = 797 and creation_time is null;
update sub_accounts set creation_time = '2010-04-02 02:10:59' where id = 796 and creation_time is null;
update sub_accounts set creation_time = '2010-04-02 03:42:57' where id = 798 and creation_time is null;
update sub_accounts set creation_time = '2010-04-02 12:14:25' where id = 799 and creation_time is null;
update sub_accounts set creation_time = '2010-04-02 13:22:57' where id = 800 and creation_time is null;
update sub_accounts set creation_time = '2010-04-02 13:56:31' where id = 801 and creation_time is null;
update sub_accounts set creation_time = '2010-04-02 16:36:23' where id = 802 and creation_time is null;
update sub_accounts set creation_time = '2010-04-03 16:30:45' where id = 803 and creation_time is null;
update sub_accounts set creation_time = '2010-04-04 07:04:43' where id = 804 and creation_time is null;
update sub_accounts set creation_time = '2010-04-04 15:52:36' where id = 805 and creation_time is null;
update sub_accounts set creation_time = '2010-04-04 16:48:46' where id = 806 and creation_time is null;
update sub_accounts set creation_time = '2010-04-04 17:56:22' where id = 807 and creation_time is null;
update sub_accounts set creation_time = '2010-04-04 22:56:58' where id = 808 and creation_time is null;
update sub_accounts set creation_time = '2010-04-04 23:36:29' where id = 809 and creation_time is null;
update sub_accounts set creation_time = '2010-04-05 02:29:59' where id = 810 and creation_time is null;
update sub_accounts set creation_time = '2010-04-05 14:18:09' where id = 811 and creation_time is null;
update sub_accounts set creation_time = '2010-04-05 17:08:38' where id = 814 and creation_time is null;
update sub_accounts set creation_time = '2010-04-05 17:47:17' where id = 813 and creation_time is null;
update sub_accounts set creation_time = '2010-04-05 20:36:52' where id = 815 and creation_time is null;
update sub_accounts set creation_time = '2010-04-05 22:10:35' where id = 816 and creation_time is null;
update sub_accounts set creation_time = '2010-04-06 13:27:31' where id = 817 and creation_time is null;
update sub_accounts set creation_time = '2010-04-06 17:00:18' where id = 818 and creation_time is null;
update sub_accounts set creation_time = '2010-04-06 20:36:49' where id = 819 and creation_time is null;
update sub_accounts set creation_time = '2010-04-06 23:47:50' where id = 820 and creation_time is null;
update sub_accounts set creation_time = '2010-04-07 15:48:42' where id = 821 and creation_time is null;
update sub_accounts set creation_time = '2010-04-08 08:51:44' where id = 822 and creation_time is null;
update sub_accounts set creation_time = '2010-04-08 18:15:04' where id = 823 and creation_time is null;
update sub_accounts set creation_time = '2010-04-09 06:50:51' where id = 825 and creation_time is null;
update sub_accounts set creation_time = '2010-04-09 12:57:20' where id = 827 and creation_time is null;
update sub_accounts set creation_time = '2010-04-09 13:04:17' where id = 826 and creation_time is null;
update sub_accounts set creation_time = '2010-04-09 14:04:42' where id = 828 and creation_time is null;
update sub_accounts set creation_time = '2010-04-09 16:03:11' where id = 829 and creation_time is null;
update sub_accounts set creation_time = '2010-04-09 17:24:05' where id = 830 and creation_time is null;
update sub_accounts set creation_time = '2010-04-09 23:06:42' where id = 831 and creation_time is null;
update sub_accounts set creation_time = '2010-04-10 12:22:49' where id = 832 and creation_time is null;
update sub_accounts set creation_time = '2010-04-10 13:20:59' where id = 833 and creation_time is null;
update sub_accounts set creation_time = '2010-04-10 19:51:38' where id = 834 and creation_time is null;
update sub_accounts set creation_time = '2010-04-11 01:00:19' where id = 835 and creation_time is null;
update sub_accounts set creation_time = '2010-04-11 01:54:55' where id = 836 and creation_time is null;
update sub_accounts set creation_time = '2010-04-11 13:52:50' where id = 837 and creation_time is null;
update sub_accounts set creation_time = '2010-04-11 15:10:42' where id = 838 and creation_time is null;
update sub_accounts set creation_time = '2010-04-11 18:55:50' where id = 839 and creation_time is null;
update sub_accounts set creation_time = '2010-04-11 19:33:16' where id = 840 and creation_time is null;
update sub_accounts set creation_time = '2010-04-11 23:19:27' where id = 841 and creation_time is null;
update sub_accounts set creation_time = '2010-04-12 10:55:22' where id = 843 and creation_time is null;
update sub_accounts set creation_time = '2010-04-12 12:56:05' where id = 844 and creation_time is null;
update sub_accounts set creation_time = '2010-04-12 13:20:43' where id = 845 and creation_time is null;
update sub_accounts set creation_time = '2010-04-12 16:41:08' where id = 846 and creation_time is null;
update sub_accounts set creation_time = '2010-04-12 23:14:55' where id = 847 and creation_time is null;
update sub_accounts set creation_time = '2010-04-13 01:47:42' where id = 848 and creation_time is null;
update sub_accounts set creation_time = '2010-04-13 02:09:32' where id = 849 and creation_time is null;
update sub_accounts set creation_time = '2010-04-13 08:30:35' where id = 850 and creation_time is null;
update sub_accounts set creation_time = '2010-04-13 10:07:03' where id = 851 and creation_time is null;
update sub_accounts set creation_time = '2010-04-13 11:15:12' where id = 852 and creation_time is null;
update sub_accounts set creation_time = '2010-04-14 10:27:04' where id = 853 and creation_time is null;
update sub_accounts set creation_time = '2010-04-14 14:37:55' where id = 854 and creation_time is null;
update sub_accounts set creation_time = '2010-04-14 17:08:24' where id = 855 and creation_time is null;
update sub_accounts set creation_time = '2010-04-14 21:46:47' where id = 856 and creation_time is null;
update sub_accounts set creation_time = '2010-04-14 22:42:06' where id = 857 and creation_time is null;
update sub_accounts set creation_time = '2010-04-15 00:44:17' where id = 858 and creation_time is null;
update sub_accounts set creation_time = '2010-04-15 09:50:54' where id = 859 and creation_time is null;
update sub_accounts set creation_time = '2010-04-15 10:34:05' where id = 860 and creation_time is null;
update sub_accounts set creation_time = '2010-04-15 11:55:50' where id = 861 and creation_time is null;
update sub_accounts set creation_time = '2010-04-15 14:07:46' where id = 862 and creation_time is null;
update sub_accounts set creation_time = '2010-04-15 15:41:53' where id = 863 and creation_time is null;
update sub_accounts set creation_time = '2010-04-15 19:22:03' where id = 864 and creation_time is null;
update sub_accounts set creation_time = '2010-04-16 00:29:40' where id = 865 and creation_time is null;
update sub_accounts set creation_time = '2010-04-16 02:15:32' where id = 866 and creation_time is null;
update sub_accounts set creation_time = '2010-04-16 09:01:01' where id = 868 and creation_time is null;
update sub_accounts set creation_time = '2010-04-16 12:28:12' where id = 869 and creation_time is null;
update sub_accounts set creation_time = '2010-04-16 14:52:13' where id = 871 and creation_time is null;
update sub_accounts set creation_time = '2010-04-16 15:01:11' where id = 870 and creation_time is null;
update sub_accounts set creation_time = '2010-04-17 09:18:04' where id = 872 and creation_time is null;
update sub_accounts set creation_time = '2010-04-17 11:15:21' where id = 873 and creation_time is null;
update sub_accounts set creation_time = '2010-04-17 13:01:23' where id = 874 and creation_time is null;
update sub_accounts set creation_time = '2010-04-17 13:55:15' where id = 875 and creation_time is null;
update sub_accounts set creation_time = '2010-04-17 14:14:41' where id = 876 and creation_time is null;
update sub_accounts set creation_time = '2010-04-17 17:24:46' where id = 879 and creation_time is null;
update sub_accounts set creation_time = '2010-04-17 17:41:42' where id = 877 and creation_time is null;
update sub_accounts set creation_time = '2010-04-17 17:58:33' where id = 878 and creation_time is null;
update sub_accounts set creation_time = '2010-04-17 21:35:39' where id = 890 and creation_time is null;
update sub_accounts set creation_time = '2010-04-17 22:46:53' where id = 880 and creation_time is null;
update sub_accounts set creation_time = '2010-04-17 23:17:55' where id = 881 and creation_time is null;
update sub_accounts set creation_time = '2010-04-18 09:37:06' where id = 882 and creation_time is null;
update sub_accounts set creation_time = '2010-04-18 10:08:06' where id = 883 and creation_time is null;
update sub_accounts set creation_time = '2010-04-18 11:07:48' where id = 884 and creation_time is null;
update sub_accounts set creation_time = '2010-04-18 12:14:21' where id = 885 and creation_time is null;
update sub_accounts set creation_time = '2010-04-18 12:52:45' where id = 886 and creation_time is null;
update sub_accounts set creation_time = '2010-04-18 14:04:12' where id = 893 and creation_time is null;
update sub_accounts set creation_time = '2010-04-18 18:49:47' where id = 889 and creation_time is null;
update sub_accounts set creation_time = '2010-04-18 21:18:51' where id = 891 and creation_time is null;
update sub_accounts set creation_time = '2010-04-18 21:46:22' where id = 892 and creation_time is null;
update sub_accounts set creation_time = '2010-04-19 10:50:32' where id = 895 and creation_time is null;
update sub_accounts set creation_time = '2010-04-19 11:26:04' where id = 894 and creation_time is null;
update sub_accounts set creation_time = '2010-04-19 13:00:17' where id = 905 and creation_time is null;
update sub_accounts set creation_time = '2010-04-19 21:17:18' where id = 896 and creation_time is null;
update sub_accounts set creation_time = '2010-04-20 01:00:09' where id = 897 and creation_time is null;
update sub_accounts set creation_time = '2010-04-20 02:46:19' where id = 898 and creation_time is null;
update sub_accounts set creation_time = '2010-04-20 07:16:02' where id = 899 and creation_time is null;
update sub_accounts set creation_time = '2010-04-20 17:28:12' where id = 924 and creation_time is null;
update sub_accounts set creation_time = '2010-04-20 18:22:25' where id = 900 and creation_time is null;
update sub_accounts set creation_time = '2010-04-20 19:57:33' where id = 901 and creation_time is null;
update sub_accounts set creation_time = '2010-04-20 23:27:47' where id = 902 and creation_time is null;
update sub_accounts set creation_time = '2010-04-21 08:33:57' where id = 903 and creation_time is null;
update sub_accounts set creation_time = '2010-04-21 11:16:23' where id = 904 and creation_time is null;
update sub_accounts set creation_time = '2010-04-21 12:26:49' where id = 906 and creation_time is null;
update sub_accounts set creation_time = '2010-04-21 13:47:22' where id = 907 and creation_time is null;
update sub_accounts set creation_time = '2010-04-21 16:00:05' where id = 908 and creation_time is null;
update sub_accounts set creation_time = '2010-04-21 18:34:58' where id = 1124 and creation_time is null; 
update sub_accounts set creation_time = '2010-04-21 18:58:33' where id = 909 and creation_time is null;
update sub_accounts set creation_time = '2010-04-21 19:18:19' where id = 910 and creation_time is null;
update sub_accounts set creation_time = '2010-04-22 01:46:22' where id = 911 and creation_time is null;
update sub_accounts set creation_time = '2010-04-22 02:56:33' where id = 912 and creation_time is null;
update sub_accounts set creation_time = '2010-04-22 11:57:50' where id = 913 and creation_time is null;
update sub_accounts set creation_time = '2010-04-22 12:13:05' where id = 914 and creation_time is null;
update sub_accounts set creation_time = '2010-04-22 14:41:54' where id = 915 and creation_time is null;
update sub_accounts set creation_time = '2010-04-22 18:21:02' where id = 916 and creation_time is null;
update sub_accounts set creation_time = '2010-04-22 18:22:07' where id = 917 and creation_time is null;
update sub_accounts set creation_time = '2010-04-22 23:13:12' where id = 918 and creation_time is null;
update sub_accounts set creation_time = '2010-04-23 00:32:05' where id = 919 and creation_time is null;
update sub_accounts set creation_time = '2010-04-23 09:57:07' where id = 920 and creation_time is null;
update sub_accounts set creation_time = '2010-04-23 10:57:46' where id = 921 and creation_time is null;
update sub_accounts set creation_time = '2010-04-23 14:52:50' where id = 922 and creation_time is null;
update sub_accounts set creation_time = '2010-04-23 17:20:40' where id = 923 and creation_time is null;
update sub_accounts set creation_time = '2010-04-23 20:24:36' where id = 925 and creation_time is null;
update sub_accounts set creation_time = '2010-04-23 21:24:22' where id = 926 and creation_time is null;
update sub_accounts set creation_time = '2010-04-24 02:21:49' where id = 927 and creation_time is null;
update sub_accounts set creation_time = '2010-04-24 11:59:01' where id = 928 and creation_time is null;
update sub_accounts set creation_time = '2010-04-24 13:45:34' where id = 929 and creation_time is null;
update sub_accounts set creation_time = '2010-04-24 15:15:13' where id = 930 and creation_time is null;
update sub_accounts set creation_time = '2010-04-24 23:28:26' where id = 932 and creation_time is null;
update sub_accounts set creation_time = '2010-04-25 10:13:20' where id = 933 and creation_time is null;
update sub_accounts set creation_time = '2010-04-25 13:39:13' where id = 935 and creation_time is null;
update sub_accounts set creation_time = '2010-04-25 14:08:06' where id = 934 and creation_time is null;
update sub_accounts set creation_time = '2010-04-25 17:58:13' where id = 936 and creation_time is null;
update sub_accounts set creation_time = '2010-04-25 21:23:54' where id = 937 and creation_time is null;
update sub_accounts set creation_time = '2010-04-25 21:53:46' where id = 938 and creation_time is null;
update sub_accounts set creation_time = '2010-04-25 23:34:07' where id = 939 and creation_time is null;
update sub_accounts set creation_time = '2010-04-26 09:56:31' where id = 940 and creation_time is null;
update sub_accounts set creation_time = '2010-04-26 12:42:57' where id = 941 and creation_time is null;
update sub_accounts set creation_time = '2010-04-26 13:08:52' where id = 944 and creation_time is null;
update sub_accounts set creation_time = '2010-04-26 15:49:31' where id = 942 and creation_time is null;
update sub_accounts set creation_time = '2010-04-26 16:14:31' where id = 943 and creation_time is null;
update sub_accounts set creation_time = '2010-04-26 17:20:13' where id = 948 and creation_time is null;
update sub_accounts set creation_time = '2010-04-27 08:04:31' where id = 945 and creation_time is null;
update sub_accounts set creation_time = '2010-04-27 11:08:51' where id = 946 and creation_time is null;
update sub_accounts set creation_time = '2010-04-27 11:29:46' where id = 947 and creation_time is null;
update sub_accounts set creation_time = '2010-04-27 12:55:20' where id = 949 and creation_time is null;
update sub_accounts set creation_time = '2010-04-27 17:14:37' where id = 950 and creation_time is null;
update sub_accounts set creation_time = '2010-04-28 12:40:22' where id = 951 and creation_time is null;
update sub_accounts set creation_time = '2010-04-28 16:45:27' where id = 952 and creation_time is null;
update sub_accounts set creation_time = '2010-04-28 21:05:28' where id = 953 and creation_time is null;
update sub_accounts set creation_time = '2010-04-29 03:28:00' where id = 955 and creation_time is null;
update sub_accounts set creation_time = '2010-04-29 09:10:23' where id = 954 and creation_time is null;
update sub_accounts set creation_time = '2010-04-29 12:01:42' where id = 956 and creation_time is null;
update sub_accounts set creation_time = '2010-04-29 15:54:03' where id = 957 and creation_time is null;
update sub_accounts set creation_time = '2010-04-29 17:19:39' where id = 964 and creation_time is null;
update sub_accounts set creation_time = '2010-04-29 17:24:12' where id = 959 and creation_time is null;
update sub_accounts set creation_time = '2010-04-29 18:02:34' where id = 958 and creation_time is null;
update sub_accounts set creation_time = '2010-04-29 18:09:51' where id = 965 and creation_time is null;
update sub_accounts set creation_time = '2010-04-30 12:53:11' where id = 966 and creation_time is null;
update sub_accounts set creation_time = '2010-04-30 16:03:09' where id = 967 and creation_time is null;
update sub_accounts set creation_time = '2010-04-30 19:35:19' where id = 968 and creation_time is null;
update sub_accounts set creation_time = '2010-04-30 20:11:48' where id = 969 and creation_time is null;
update sub_accounts set creation_time = '2010-05-01 18:55:48' where id = 974 and creation_time is null;
update sub_accounts set creation_time = '2010-05-01 22:30:27' where id = 970 and creation_time is null;
update sub_accounts set creation_time = '2010-05-02 09:12:39' where id = 971 and creation_time is null;
update sub_accounts set creation_time = '2010-05-02 11:25:23' where id = 972 and creation_time is null;
update sub_accounts set creation_time = '2010-05-02 12:21:01' where id = 973 and creation_time is null;
update sub_accounts set creation_time = '2010-05-02 17:10:47' where id = 975 and creation_time is null;
update sub_accounts set creation_time = '2010-05-02 21:17:26' where id = 978 and creation_time is null;
update sub_accounts set creation_time = '2010-05-02 22:08:51' where id = 976 and creation_time is null;
update sub_accounts set creation_time = '2010-05-03 00:52:02' where id = 977 and creation_time is null;
update sub_accounts set creation_time = '2010-05-03 03:07:20' where id = 979 and creation_time is null;
update sub_accounts set creation_time = '2010-05-03 06:28:32' where id = 980 and creation_time is null;
update sub_accounts set creation_time = '2010-05-03 11:07:00' where id = 981 and creation_time is null;
update sub_accounts set creation_time = '2010-05-03 13:50:39' where id = 982 and creation_time is null;
update sub_accounts set creation_time = '2010-05-03 17:13:57' where id = 983 and creation_time is null;
update sub_accounts set creation_time = '2010-05-03 21:00:31' where id = 984 and creation_time is null;
update sub_accounts set creation_time = '2010-05-04 01:57:02' where id = 985 and creation_time is null;
update sub_accounts set creation_time = '2010-05-04 12:49:15' where id = 986 and creation_time is null;
update sub_accounts set creation_time = '2010-05-04 13:00:34' where id = 987 and creation_time is null;
update sub_accounts set creation_time = '2010-05-04 14:56:22' where id = 988 and creation_time is null;
update sub_accounts set creation_time = '2010-05-04 16:40:31' where id = 989 and creation_time is null;
update sub_accounts set creation_time = '2010-05-04 17:30:05' where id = 990 and creation_time is null;
update sub_accounts set creation_time = '2010-05-05 00:12:17' where id = 991 and creation_time is null;
update sub_accounts set creation_time = '2010-05-05 03:03:19' where id = 992 and creation_time is null;
update sub_accounts set creation_time = '2010-05-05 08:37:49' where id = 993 and creation_time is null;
update sub_accounts set creation_time = '2010-05-05 10:56:40' where id = 994 and creation_time is null;
update sub_accounts set creation_time = '2010-05-05 15:27:13' where id = 995 and creation_time is null;
update sub_accounts set creation_time = '2010-05-05 21:26:47' where id = 996 and creation_time is null;
update sub_accounts set creation_time = '2010-05-06 13:33:27' where id = 998 and creation_time is null;
update sub_accounts set creation_time = '2010-05-06 15:34:24' where id = 1005 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-06 18:03:08' where id = 999 and creation_time is null;
update sub_accounts set creation_time = '2010-05-06 23:36:41' where id = 1001 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-07 02:48:28' where id = 1002 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-07 13:37:56' where id = 1006 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-07 16:19:58' where id = 1017 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-07 18:39:17' where id = 1003 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-08 10:13:29' where id = 1004 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-08 17:07:38' where id = 1007 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-09 20:19:17' where id = 1008 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-09 22:44:14' where id = 1009 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-10 09:08:04' where id = 1010 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-10 11:41:42' where id = 1011 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-10 23:39:22' where id = 1012 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-11 12:22:20' where id = 1013 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-11 17:55:32' where id = 1018 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-11 20:56:52' where id = 1014 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-12 00:02:16' where id = 1015 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-12 09:13:12' where id = 1016 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-12 22:00:16' where id = 1019 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-13 00:03:46' where id = 1020 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-13 15:33:31' where id = 1021 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-13 16:39:58' where id = 1027 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-13 17:45:54' where id = 1022 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-13 20:20:23' where id = 1023 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-13 20:29:46' where id = 1024 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-13 21:46:00' where id = 1025 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-13 22:11:23' where id = 1026 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-13 23:54:59' where id = 1044 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-14 13:42:12' where id = 1028 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-14 14:42:41' where id = 1029 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-14 18:28:37' where id = 1030 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-14 21:35:44' where id = 1031 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-14 22:04:11' where id = 1032 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-14 22:07:25' where id = 1033 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-15 23:11:56' where id = 1034 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-16 17:40:43' where id = 1035 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-16 17:52:55' where id = 1036 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-16 18:24:16' where id = 1037 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-16 21:52:43' where id = 1039 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-16 22:08:51' where id = 1038 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-17 13:17:54' where id = 1040 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-17 18:18:12' where id = 1041 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-17 19:47:21' where id = 1042 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-17 21:40:33' where id = 1043 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-18 17:09:30' where id = 1045 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-18 17:58:55' where id = 1046 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-18 18:19:52' where id = 1047 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-18 21:12:07' where id = 1048 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-18 21:16:56' where id = 1049 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-18 23:57:58' where id = 1050 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-19 12:03:27' where id = 1051 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-19 16:45:31' where id = 1055 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-19 16:58:54' where id = 1090 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-19 16:59:32' where id = 1054 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-19 17:05:41' where id = 1053 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-19 18:10:35' where id = 1056 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-19 19:51:13' where id = 1057 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-19 21:37:28' where id = 1058 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-19 21:50:57' where id = 1059 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-20 04:39:12' where id = 1118 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-20 12:34:13' where id = 1060 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-20 14:16:00' where id = 1061 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-20 14:25:45' where id = 1062 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-20 15:21:53' where id = 1063 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-20 15:23:44' where id = 1064 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-20 17:30:56' where id = 1065 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-20 19:34:06' where id = 1066 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-20 22:10:14' where id = 1067 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-20 22:36:42' where id = 1068 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-21 01:34:14' where id = 1069 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-21 01:34:53' where id = 1070 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-21 11:16:01' where id = 1071 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-21 14:36:41' where id = 1072 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-21 14:50:10' where id = 1126 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-21 17:12:13' where id = 1073 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-21 19:20:44' where id = 1074 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-21 19:55:25' where id = 1076 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-21 20:19:07' where id = 1075 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-21 20:34:46' where id = 1077 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-22 01:29:42' where id = 1078 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-22 01:31:21' where id = 1080 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-22 01:58:04' where id = 1079 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-22 02:24:05' where id = 1082 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-22 02:56:03' where id = 1081 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-22 10:10:08' where id = 1089 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-22 12:12:26' where id = 1083 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-22 15:54:34' where id = 1084 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-22 17:35:40' where id = 1085 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-22 18:13:42' where id = 1086 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-23 01:33:01' where id = 1087 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-23 13:00:54' where id = 1088 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-24 12:51:04' where id = 1091 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-24 15:00:20' where id = 1105 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-24 23:44:22' where id = 1092 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-24 23:56:21' where id = 3008 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-25 12:15:02' where id = 1100 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-25 14:44:44' where id = 1093 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-25 15:39:28' where id = 1094 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-25 15:44:25' where id = 1095 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-25 17:54:01' where id = 1096 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-25 18:04:40' where id = 1097 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-25 18:11:26' where id = 1098 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-25 18:19:12' where id = 1099 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-25 19:39:08' where id = 1101 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-26 00:26:54' where id = 1102 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-26 14:32:28' where id = 1103 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-26 16:24:41' where id = 1104 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-26 18:22:19' where id = 1110 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-26 18:24:02' where id = 1107 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-26 19:17:51' where id = 1108 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-26 21:49:00' where id = 1109 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-26 23:11:47' where id = 1112 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-27 12:44:52' where id = 1111 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-27 15:49:22' where id = 1113 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-27 19:47:23' where id = 1114 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-28 10:52:08' where id = 1115 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-29 14:14:34' where id = 1119 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-30 22:17:43' where id = 1116 and creation_time is null; 
update sub_accounts set creation_time = '2010-05-31 09:01:14' where id = 1117 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-01 22:15:48' where id = 1120 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-01 22:17:58' where id = 1121 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-01 22:20:39' where id = 1122 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-03 14:56:18' where id = 1123 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-07 20:47:05' where id = 1127 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-07 21:13:21' where id = 1128 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-07 21:20:38' where id = 1129 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-08 00:32:41' where id = 1130 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-08 18:55:44' where id = 1789 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-08 22:47:09' where id = 1131 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-09 14:24:07' where id = 1132 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-09 14:38:30' where id = 1134 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-10 04:48:19' where id = 1133 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-11 17:12:59' where id = 1135 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-11 20:04:25' where id = 1136 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-11 20:37:01' where id = 1137 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-12 10:42:24' where id = 1138 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-13 00:16:33' where id = 1139 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-13 15:24:21' where id = 1140 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-13 19:38:35' where id = 1141 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-13 20:05:30' where id = 1142 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-14 13:37:32' where id = 1144 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-14 18:52:56' where id = 1145 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-15 18:48:54' where id = 1150 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-15 20:42:51' where id = 1146 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-16 17:16:55' where id = 1147 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-16 17:40:59' where id = 1148 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-16 18:30:40' where id = 1149 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-18 09:37:42' where id = 1151 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-18 18:11:00' where id = 1152 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-18 18:12:36' where id = 1153 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-18 18:43:06' where id = 1154 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-18 18:47:45' where id = 1155 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-18 18:49:30' where id = 1156 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-18 18:54:54' where id = 1157 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-18 20:15:24' where id = 1158 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-21 15:28:18' where id = 1159 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-21 15:30:22' where id = 1160 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-21 15:37:34' where id = 1161 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-21 15:40:17' where id = 1162 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-21 15:45:59' where id = 1163 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-21 15:46:23' where id = 1166 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-21 17:05:10' where id = 1164 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-21 17:11:58' where id = 1165 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-21 17:21:55' where id = 1167 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-21 17:22:19' where id = 1168 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-21 19:46:27' where id = 1687 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-21 19:46:42' where id = 1688 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-21 19:59:57' where id = 1689 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-21 20:00:13' where id = 1690 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-21 20:26:20' where id = 1691 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-21 20:26:45' where id = 1692 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-21 20:38:20' where id = 2895 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-21 20:38:21' where id = 1987 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-21 20:38:21' where id = 2690 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-21 22:56:22' where id = 1787 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-21 22:56:35' where id = 1788 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-21 23:43:26' where id = 1887 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-21 23:43:40' where id = 1888 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-22 21:11:00' where id = 1889 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-22 21:11:17' where id = 1790 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-22 22:04:02' where id = 1890 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-22 22:04:23' where id = 1791 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-22 22:09:14' where id = 1792 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-22 22:09:33' where id = 1793 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-22 22:41:30' where id = 1891 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-22 22:41:45' where id = 1794 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-22 22:43:59' where id = 1795 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-22 22:44:12' where id = 1796 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-22 22:49:40' where id = 1892 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-22 22:49:57' where id = 1797 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-22 22:52:58' where id = 1893 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-22 22:53:10' where id = 1798 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-22 22:59:45' where id = 1894 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-22 22:59:57' where id = 1895 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-22 23:00:51' where id = 1896 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-22 23:01:03' where id = 1799 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-22 23:02:43' where id = 1800 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-22 23:02:55' where id = 1897 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-22 23:03:51' where id = 1898 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-22 23:04:02' where id = 1801 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-22 23:06:22' where id = 1899 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-22 23:06:34' where id = 1900 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-22 23:07:20' where id = 1802 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-22 23:07:32' where id = 1901 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-22 23:08:17' where id = 1902 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-22 23:08:28' where id = 1803 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-22 23:08:47' where id = 1804 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-22 23:09:04' where id = 1805 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-22 23:15:12' where id = 1806 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-22 23:15:24' where id = 1903 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-22 23:16:21' where id = 1904 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-22 23:16:36' where id = 1807 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-22 23:26:29' where id = 1808 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-22 23:26:40' where id = 1809 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-22 23:28:52' where id = 1810 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-22 23:29:09' where id = 1811 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-22 23:30:46' where id = 1905 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-22 23:30:59' where id = 1812 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-22 23:36:47' where id = 1813 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-22 23:37:06' where id = 1906 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-22 23:41:11' where id = 1814 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-22 23:41:23' where id = 1907 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-22 23:42:13' where id = 1815 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-22 23:42:25' where id = 1816 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-22 23:43:52' where id = 1908 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-22 23:44:03' where id = 1817 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-22 23:50:21' where id = 1909 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-22 23:50:41' where id = 1818 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-23 01:12:38' where id = 1819 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-23 14:10:38' where id = 1910 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-23 14:12:30' where id = 1911 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-23 14:57:03' where id = 1820 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-23 14:57:28' where id = 1821 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-23 17:45:48' where id = 1822 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-23 19:19:20' where id = 1823 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-23 19:20:39' where id = 1824 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-23 19:22:38' where id = 1912 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-23 19:22:52' where id = 1913 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-23 19:45:33' where id = 1914 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-23 19:45:47' where id = 1825 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-23 19:59:13' where id = 1826 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-23 19:59:26' where id = 1827 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-23 20:04:26' where id = 1915 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-23 20:04:39' where id = 1916 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-23 20:06:23' where id = 1917 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-23 20:06:40' where id = 1828 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-23 20:20:54' where id = 2488 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-23 21:15:28' where id = 1829 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-23 21:15:44' where id = 1918 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-23 22:07:42' where id = 1919 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-23 22:07:57' where id = 1830 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-24 17:39:42' where id = 1988 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-24 17:39:59' where id = 1989 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-24 17:53:55' where id = 1990 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-24 17:54:12' where id = 2087 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-24 17:55:22' where id = 2088 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-24 17:55:34' where id = 2089 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-24 18:13:40' where id = 2894 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-24 18:13:41' where id = 2904 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-24 18:13:41' where id = 3287 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-24 19:00:08' where id = 2187 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-24 19:00:27' where id = 2188 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-24 19:26:16' where id = 2189 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-24 19:26:29' where id = 2287 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-24 19:33:41' where id = 2190 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-24 19:34:03' where id = 2191 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-24 20:01:53' where id = 2387 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-24 20:02:15' where id = 2388 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-25 16:21:48' where id = 2487 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-25 16:22:02' where id = 2587 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-25 18:17:51' where id = 2687 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-25 18:18:10' where id = 2787 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-25 20:05:03' where id = 2688 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-25 20:05:18' where id = 2788 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-26 09:41:02' where id = 2789 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-26 12:58:22' where id = 2689 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-27 21:01:02' where id = 2691 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-28 13:35:24' where id = 2790 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-28 13:35:41' where id = 2791 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-28 17:43:15' where id = 2792 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-28 19:03:19' where id = 2692 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-28 19:03:41' where id = 2693 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-28 19:32:17' where id = 2793 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-28 19:32:37' where id = 2694 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-28 21:10:28' where id = 2794 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-28 21:10:44' where id = 2795 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-29 16:02:02' where id = 2796 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-29 16:15:02' where id = 2797 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-29 20:39:50' where id = 2798 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-29 20:40:13' where id = 2695 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-29 20:58:34' where id = 2696 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-29 20:58:50' where id = 2799 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-30 13:12:32' where id = 2800 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-30 13:12:47' where id = 2697 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-30 17:31:06' where id = 2801 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-30 17:31:20' where id = 2802 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-30 18:37:14' where id = 2699 and creation_time is null; 
update sub_accounts set creation_time = '2010-06-30 18:37:29' where id = 2700 and creation_time is null; 
update sub_accounts set creation_time = '2010-07-01 16:59:27' where id = 2887 and creation_time is null; 
update sub_accounts set creation_time = '2010-07-01 16:59:43' where id = 2987 and creation_time is null; 
update sub_accounts set creation_time = '2010-07-01 18:14:43' where id = 2888 and creation_time is null; 
update sub_accounts set creation_time = '2010-07-01 18:14:58' where id = 2889 and creation_time is null; 
update sub_accounts set creation_time = '2010-07-01 20:25:39' where id = 2890 and creation_time is null; 
update sub_accounts set creation_time = '2010-07-01 20:26:03' where id = 2988 and creation_time is null; 
update sub_accounts set creation_time = '2010-07-02 14:09:46' where id = 2989 and creation_time is null; 
update sub_accounts set creation_time = '2010-07-02 14:10:05' where id = 2990 and creation_time is null; 
update sub_accounts set creation_time = '2010-07-02 19:40:52' where id = 2891 and creation_time is null; 
update sub_accounts set creation_time = '2010-07-02 19:41:08' where id = 2892 and creation_time is null; 
update sub_accounts set creation_time = '2010-07-02 21:37:05' where id = 2991 and creation_time is null; 
update sub_accounts set creation_time = '2010-07-02 21:37:18' where id = 2992 and creation_time is null; 
update sub_accounts set creation_time = '2010-07-02 21:42:22' where id = 2993 and creation_time is null; 
update sub_accounts set creation_time = '2010-07-02 21:42:36' where id = 2893 and creation_time is null; 
update sub_accounts set creation_time = '2010-07-03 10:45:13' where id = 2994 and creation_time is null; 
update sub_accounts set creation_time = '2010-07-04 14:07:19' where id = 2995 and creation_time is null; 
update sub_accounts set creation_time = '2010-07-06 13:45:16' where id = 2896 and creation_time is null; 
update sub_accounts set creation_time = '2010-07-06 13:45:36' where id = 2996 and creation_time is null; 
update sub_accounts set creation_time = '2010-07-06 19:56:49' where id = 2897 and creation_time is null; 
update sub_accounts set creation_time = '2010-07-06 22:00:41' where id = 2997 and creation_time is null; 
update sub_accounts set creation_time = '2010-07-06 21:18:12' where id = 2898 and creation_time is null; 
update sub_accounts set creation_time = '2010-07-06 22:31:49' where id = 2998 and creation_time is null; 
update sub_accounts set creation_time = '2010-07-06 23:14:54' where id = 2899 and creation_time is null; 
update sub_accounts set creation_time = '2010-07-06 23:15:10' where id = 2900 and creation_time is null; 
update sub_accounts set creation_time = '2010-07-07 19:08:01' where id = 2999 and creation_time is null; 
update sub_accounts set creation_time = '2010-07-07 19:08:19' where id = 3000 and creation_time is null; 
update sub_accounts set creation_time = '2010-07-07 19:17:04' where id = 2901 and creation_time is null; 
update sub_accounts set creation_time = '2010-07-08 14:45:12' where id = 3004 and creation_time is null; 
update sub_accounts set creation_time = '2010-07-08 17:27:24' where id = 2909 and creation_time is null; 
update sub_accounts set creation_time = '2010-07-07 19:16:48' where id = 3001 and creation_time is null; 
update sub_accounts set creation_time = '2010-07-07 21:27:43' where id = 2902 and creation_time is null; 
update sub_accounts set creation_time = '2010-07-07 21:27:59' where id = 2903 and creation_time is null; 
update sub_accounts set creation_time = '2010-07-07 21:59:09' where id = 3002 and creation_time is null; 
update sub_accounts set creation_time = '2010-07-07 21:59:25' where id = 3003 and creation_time is null; 
update sub_accounts set creation_time = '2010-07-08 11:26:54' where id = 2905 and creation_time is null; 
update sub_accounts set creation_time = '2010-07-08 14:45:28' where id = 2906 and creation_time is null; 
update sub_accounts set creation_time = '2010-07-08 14:55:22' where id = 3005 and creation_time is null; 
update sub_accounts set creation_time = '2010-07-08 14:55:50' where id = 2907 and creation_time is null; 
update sub_accounts set creation_time = '2010-07-08 17:27:07' where id = 2908 and creation_time is null; 
update sub_accounts set creation_time = '2010-07-09 00:17:42' where id = 2910 and creation_time is null; 
update sub_accounts set creation_time = '2010-07-09 11:28:22' where id = 3006 and creation_time is null; 
update sub_accounts set creation_time = '2010-07-09 13:38:15' where id = 3007 and creation_time is null; 
update sub_accounts set creation_time = '2010-07-09 14:02:25' where id = 2911 and creation_time is null; 
update sub_accounts set creation_time = '2010-07-09 14:02:49' where id = 2912 and creation_time is null; 
update sub_accounts set creation_time = '2010-07-09 19:29:09' where id = 3011 and creation_time is null; 
update sub_accounts set creation_time = '2010-07-09 16:36:49' where id = 3009 and creation_time is null; 
update sub_accounts set creation_time = '2010-07-09 19:28:53' where id = 3010 and creation_time is null; 
update sub_accounts set creation_time = '2010-07-09 22:50:05' where id = 3087 and creation_time is null; 
update sub_accounts set creation_time = '2010-07-09 22:50:21' where id = 3088 and creation_time is null; 
update sub_accounts set creation_time = '2010-07-10 01:29:33' where id = 3187 and creation_time is null; 
update sub_accounts set creation_time = '2010-07-10 01:29:48' where id = 3188 and creation_time is null; 
update sub_accounts set creation_time = '2010-07-10 20:31:17' where id = 3189 and creation_time is null; 
update sub_accounts set creation_time = '2010-07-12 14:23:34' where id = 3191 and creation_time is null; 
update sub_accounts set creation_time = '2010-07-10 21:07:10' where id = 3190 and creation_time is null; 
update sub_accounts set creation_time = '2010-07-12 14:23:09' where id = 3288 and creation_time is null; 
update sub_accounts set creation_time = '2010-07-12 19:00:09' where id = 3387 and creation_time is null; 
update sub_accounts set creation_time = '2010-07-12 19:00:27' where id = 3388 and creation_time is null; 
