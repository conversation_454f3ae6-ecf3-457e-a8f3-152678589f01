ALTER TABLE risk_metric_requests
    ADD COLUMN ignored_at DATETIME DEFAULT NULL,
    ADD INDEX risk_metric_requests_type_st_pt_ia_ef(type, sent_time, polled_time, ignored_at, error_flag),
    ADD INDEX risk_metric_requests_ca_st_pt(created_at, sent_time, polled_time),
    ADD INDEX risk_metric_requests_ignored_at(ignored_at),
    ADD INDEX risk_metric_requests_error_flag(error_flag);

INSERT INTO schema_log (schema_version, date) VALUES (41, now());
