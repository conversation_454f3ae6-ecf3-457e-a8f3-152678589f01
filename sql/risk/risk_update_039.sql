ALTER TABLE risk_metric_requests ADD COLUMN priority INT(11) NOT NULL DEFAULT 0;

create table toggles (
  id        BIGINT          NOT NULL AUTO_INCREMENT,
  name      VARCHAR(255)    NOT NULL,
  state     TINYINT(1)      NOT NULL,
  PRIMARY KEY (id),
  KEY name (name)
) engine=InnoDB DEFAULT charset=utf8mb4;

create table version_toggle_state (
  id            BIGINT(20)  NOT NULL AUTO_INCREMENT,
  toggle_id     BIGINT(20)  NOT NULL,
  version       INT(11)     NOT NULL,
  created_at    DATETIME    NOT NULL,
  deleted       TINYINT(1)  NOT NULL,
  value         TINYINT(1)  NOT NULL,
  PRIMARY KEY(id),
  KEY toggle_id (toggle_id),
  <PERSON>E<PERSON> created_at (created_at),
  CONSTRAINT toggle_id FOREIGN KEY (toggle_id) REFERENCES toggles (id)
) engine=InnoDB DEFAULT charset=utf8mb4;

INSERT INTO schema_log (schema_version, date) VALUES (39, now());
