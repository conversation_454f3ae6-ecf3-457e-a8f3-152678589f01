CREATE DATABASE percona
  CHARACTER SET 'utf8'
  COLLATE utf8_general_ci;

USE percona;

CREATE TABLE schema_log (
  schema_version INT      NOT NULL DEFAULT 0,
  date           DATETIME NOT NULL,
  UNIQUE (schema_version)
)
  ENGINE = InnoDB
  DEFAULT CHARSET = utf8;

CREATE TABLE `checksums` (
  `db` char(64) NOT NULL,
  `tbl` char(64) NOT NULL,
  `chunk` int(11) NOT NULL,
  `chunk_time` float DEFAULT NULL,
  `chunk_index` varchar(200) DEFAULT NULL,
  `lower_boundary` text,
  `upper_boundary` text,
  `this_crc` char(40) NOT NULL,
  `this_cnt` int(11) NOT NULL,
  `master_crc` char(40) DEFAULT NULL,
  `master_cnt` int(11) DEFAULT NULL,
  `ts` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMAR<PERSON> KEY (`db`,`tbl`,`chunk`),
  KEY `ts_db_tbl` (`ts`,`db`,`tbl`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;

INSERT INTO schema_log (schema_version, date) VALUES (1, now());
