CREATE TABLE jp_morgan_files (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  created_at DATETIME NOT NULL,
  file_name VARCHAR(255) NOT NULL,
  type VARCHAR(255) NOT NULL,
  state VARCHAR(255) NOT NULL,
  PRIMARY KEY (id),
  INDEX jp_morgan_files_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE version_jp_morgan_file_state (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  jp_morgan_file_id BIGINT(20) NOT NULL,
  version INT(11) NOT NULL,
  created_at DATETIME NOT NULL,
  deleted TINYINT(1) NOT NULL DEFAULT 0,
  value VARCHAR(255) NOT NULL,
  PRIMARY KEY (id),
  CONSTRAINT version_jp_morgan_file_state_file_id FOREIGN KEY (jp_morgan_file_id) REFERENCES jp_morgan_files (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

INSERT INTO schema_log (schema_version, date) VALUES (385, now());