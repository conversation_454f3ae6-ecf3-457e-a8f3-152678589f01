CREATE TABLE sharegain_income_distribution_report_records (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    sharegain_file_id BIGINT(20) NOT NULL,
    version INT NOT NULL DEFAULT 0,
    report_date DATE NOT NULL,
    type VA<PERSON>HAR(255) NOT NULL,
    borrower_code VA<PERSON>HAR(255) NOT NULL,
    client_borrower_code VA<PERSON>HAR(255) NOT NULL,
    intended_value_date DATE NOT NULL,
    currency VARCHAR(255) NOT NULL,
    gross_dividend_per_share DECIMAL(20, 8) NOT NULL,
    quantity_on_loan DECIMAL (20, 8) NOT NULL,
    payment_value DECIMAL(20, 2) NOT NULL,
    sharegain_corporate_actionid VARCHAR(255) NOT NULL,
    corporate_action_type VARCHAR(255) NOT NULL,
    record_date DATE NOT NULL,
    isin VARCHAR(255) NOT NULL,
    external_instrument_id VARCHAR(255) DEFAULT NULL,
    state VARCHAR(255) NOT NULL,
    PRIMARY KEY (id),
    CONSTRAINT fk_sg_income_distro_report_recs_sg_file_id
        FOREIGN KEY (sharegain_file_id) REFERENCES sharegain_files (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE version_sharegain_income_distribution_report_record_state (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    sharegain_income_distribution_report_record_id BIGINT(20) NOT NULL,
    version INT(11) NOT NULL,
    created_at DATETIME NOT NULL,
    deleted TINYINT(1) NOT NULL DEFAULT 0,
    value VARCHAR(255) NOT NULL,
    PRIMARY KEY (id),
    CONSTRAINT version_sg_income_distro_report_record_id
        FOREIGN KEY (sharegain_income_distribution_report_record_id) REFERENCES sharegain_income_distribution_report_records (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

INSERT INTO schema_log (schema_version, date) VALUES(388, now());