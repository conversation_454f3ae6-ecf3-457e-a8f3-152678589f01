ALTER TABLE fpsl_daily_client_interest_accruals
   ADD COLUMN fpsl_daily_client_interest_accrual_file_record_id BIGINT(20) NOT NULL DEFAULT 0 AFTER id,
   ADD CONSTRAINT fkey_fpsl_daily_client_interest_accrual_file_record_id
       FOREIGN KEY (fpsl_daily_client_interest_accrual_file_record_id)
       REFERENCES fpsl_daily_client_interest_accrual_file_records (id);

CREATE TABLE version_fpsl_daily_client_interest_accrual_file_record_id (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    fpsl_daily_client_interest_accrual_id BIGINT(20) NOT NULL,
    version INT(11) NOT NULL,
    created_at DATETIME NOT NULL,
    deleted TINYINT(1) NOT NULL DEFAULT 0,
    value BIGINT(20) NOT NULL,
    PRIMARY KEY (id),
    CONSTRAINT fk_version_fd_client_ia_file_record_id_fd_client_ia_id
        FOREIGN KEY (fpsl_daily_client_interest_accrual_id) REFERENCES fpsl_daily_client_interest_accruals (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

INSERT INTO schema_log (schema_version, date) VALUES (379, now());