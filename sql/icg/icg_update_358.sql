CREATE TABLE batch_queue_names
(
    id       BIGINT       NOT NULL AUTO_INCREMENT,
    version  INT          NOT NULL DEFAULT 0,
    metadata MEDIUMTEXT            DEFAULT NULL,
    name     VARCHAR(500) NOT NULL,
    PRIMARY KEY (id),
    UNIQUE KEY (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE batch_queue_items
(
    id                  BIGINT NOT NULL AUTO_INCREMENT,
    batch_queue_name_id BIGINT NOT NULL,
    externalid          VARCHAR(500) DEFAULT NULL,
    payload             TEXT   NOT NULL,
    PRIMARY KEY (id),
    UNIQUE KEY (batch_queue_name_id, externalid)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE batch_queue_batches
(
    id                  BIGINT(20)   NOT NULL AUTO_INCREMENT,
    version             INT          DEFAULT 0,
    batch_queue_name_id BIGINT(20)   NOT NULL,
    item_id_from        BIGINT(20)   NOT NULL,
    item_id_to          BIGINT(20)   NOT NULL,
    size                INT UNSIGNED NOT NULL,
    created_at          DATETIME NOT NULL,
    sendable_since      DATETIME     DEFAULT NULL,
    polled_at           DATETIME     DEFAULT NULL,
    polled_by           VARCHAR(63)  DEFAULT NULL,
    sent_at             DATETIME     DEFAULT NULL,
    errored_at          DATETIME     DEFAULT NULL,
    ignored_at          DATETIME     DEFAULT NULL,
    batch_data          TEXT         DEFAULT NULL,
    message             VARCHAR(999) DEFAULT NULL,
    PRIMARY KEY (id),
    INDEX               batch_queue_batches_polling_index (batch_queue_name_id, sendable_since, errored_at, polled_by, polled_at),
    UNIQUE KEY batch_queue_batches_item_index (batch_queue_name_id, item_id_from),
    CONSTRAINT batch_queue_batches_sendable CHECK ((sendable_since is null) = (sent_at is not null) or
                                                   (ignored_at is not null) or (errored_at is not null))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

INSERT INTO schema_log(schema_version, date) VALUES (358, now());