CREATE TABLE fpsl_accounts (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  brokerage_account_id bigint(20) NOT NULL,
  created_at datetime NOT NULL,
  opt_in varchar(255) NOT NULL,
  opt_in_update_time datetime NOT NULL,
  PRIMARY KEY (id),
  UNIQUE KEY brokerage_account_id (brokerage_account_id),
  CONSTRAINT fpsl_accounts_brokerage_account_id FOREIGN KEY (brokerage_account_id) REFERENCES brokerage_accounts (id),
  INDEX fpsl_accounts_opt_in (opt_in)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE version_fpsl_account_opt_in (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  fpsl_account_id bigint(20) NOT NULL,
  version int(11) NOT NULL,
  created_at datetime NOT NULL,
  deleted tinyint(1) DEFAULT NULL,
  value varchar(255) NOT NULL,
  opt_in_update_time datetime NOT NULL,
  PRIMARY KEY (id),
  CONSTRAINT version_fpsl_account_opt_in_fpsl_account_id FOREIGN KEY (fpsl_account_id) REFERENCES fpsl_accounts (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

INSERT INTO schema_log(schema_version, date) VALUES (365, now());