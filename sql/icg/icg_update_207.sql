CREATE TABLE capital_reserve_calculations (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  created_at DATETIME NOT NULL,
  effective_date DATE NOT NULL,
  state VARCHAR(255) NOT NULL,
  PRIMARY KEY (id),
  INDEX capital_reserve_calculations_effective_date_state (effective_date, state)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE version_capital_reserve_calculations_state (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  capital_reserve_calculation_id BIGINT(20) NOT NULL,
  version INT(11) NOT NULL,
  created_at DATETIME NOT NULL,
  deleted TINYINT(1) NOT NULL DEFAULT 0,
  value VARCHAR(255) NOT NULL,
  PRIMARY KEY (id),
  CONSTRAINT fkey_version_capital_reserve_calculations_state_calculation_id
    FOREIGN KEY (capital_reserve_calculation_id) REFERENCES capital_reserve_calculations (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE capital_reserve_factors (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  created_at DATETIME NOT NULL,
  factor_type VARCHAR(255) NOT NULL,
  capital_reserve_calculation_id BIGINT(20) NOT NULL,
  value DECIMAL(28,8) NOT NULL,
  PRIMARY KEY (id),
  CONSTRAINT fkey_capital_reserve_line_factors_calculation_id FOREIGN KEY (capital_reserve_calculation_id) REFERENCES capital_reserve_calculations (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE capital_reserve_line_items (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  created_at DATETIME NOT NULL,
  accounting_type VARCHAR(255) NOT NULL,
  item_type VARCHAR(255) NOT NULL,
  capital_reserve_calculation_id BIGINT(20) NOT NULL,
  PRIMARY KEY (id),
  CONSTRAINT fkey_capital_reserve_line_items_calculation_id FOREIGN KEY (capital_reserve_calculation_id) REFERENCES capital_reserve_calculations (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE capital_reserve_line_sub_items (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  created_at DATETIME NOT NULL,
  capital_reserve_line_item_id BIGINT(20) NOT NULL,
  sub_item_type VARCHAR(255) NOT NULL,
  amount DECIMAL(20,2),
  PRIMARY KEY (id),
  CONSTRAINT fkey_capital_reserve_line_sub_items_line_item_id FOREIGN KEY (capital_reserve_line_item_id) REFERENCES capital_reserve_line_items (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE version_capital_reserve_line_sub_items_support (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  capital_reserve_line_sub_item_id BIGINT(20) NOT NULL,
  version INT(11) NOT NULL,
  created_at DATETIME NOT NULL,
  deleted TINYINT(1) NOT NULL DEFAULT 0,
  amount DECIMAL(20,2),
  PRIMARY KEY (id),
  CONSTRAINT fkey_capital_reserve_line_sub_items_support_line_sub_item_id
    FOREIGN KEY (capital_reserve_line_sub_item_id) REFERENCES capital_reserve_line_sub_items (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE queued_capital_reserve_line_sub_items (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  capital_reserve_line_sub_item_id BIGINT(20) NOT NULL,
  created_at DATETIME NOT NULL,
  polled_time DATETIME,
  sent_time DATETIME,
  error_flag TINYINT(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (id),
  CONSTRAINT queued_capital_reserve_line_sub_items_sub_item_id
    FOREIGN KEY (capital_reserve_line_sub_item_id) REFERENCES capital_reserve_line_sub_items (id),
  INDEX queued_capital_reserve_line_sub_items_created_at (created_at),
  INDEX queued_capital_reserve_line_sub_items_polled_time (polled_time),
  INDEX queued_capital_reserve_line_sub_items_sent_time (sent_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO schema_log (schema_version, date) VALUES (207, now());