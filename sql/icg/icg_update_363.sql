CREATE TABLE mark_to_market_collateral_transfers (
    id BIGINT NOT NULL AUTO_INCREMENT,
    effective_date DATE NOT NULL,
    borrower_account_id BIGINT NOT NULL,
    net_movement DECIMAL(20,2) NOT NULL,
    UNIQUE KEY (effective_date, borrower_account_id),
    PRIMARY KEY (id),
    CONSTRAINT mark_to_market_collateral_transfers_borrower_account_id FOREIGN KEY (borrower_account_id)
        REFERENCES fpsl_borrower_accounts (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

INSERT INTO schema_log (schema_version, date) VALUES(363, now());