CREATE TABLE fpsl_daily_custodian_interest_accruals (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    sharegain_file_id BIGINT(20) NOT NULL,
    fpsl_monthly_custodian_interest_revenue_id BIGINT(20) DEFAULT NULL,
    created_at DATETIME NOT NULL,
    effective_date DATE NOT NULL,
    borrower_code VARCHAR(255) NOT NULL,
    loan_id VARCHAR(255) NOT NULL,
    instrument_id BIGINT(20) NOT NULL,
    price DECIMAL(14,2) NOT NULL,
    quantity DECIMAL(20,8) NOT NULL,
    loan_value DECIMAL(14,2) NOT NULL,
    fpsl_daily_custodian_interest_accrual_details MEDIUMTEXT NOT NULL,
    state VARCHAR(255) NOT NULL,
    PRIMARY KEY (id),
    UNIQUE INDEX fd_custodian_ia_eff_date_loan_id (effective_date, loan_id),
    INDEX fd_custodian_ia_borr_code_loan_id_instrument_id (borrower_code, loan_id, instrument_id),
    CONSTRAINT fk_fd_custodian_ia_sharegain_file_id
        FOREIGN KEY (sharegain_file_id) REFERENCES sharegain_files (id),
    CONSTRAINT fk_fd_custodian_ia_fpsl_monthly_interest_revenue_id
        FOREIGN KEY (fpsl_monthly_custodian_interest_revenue_id) REFERENCES fpsl_monthly_custodian_interest_revenues (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE version_fpsl_daily_custodian_interest_accrual_sharegain_file_id  (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    fpsl_daily_custodian_interest_accrual_id BIGINT(20) NOT NULL,
    version INT(11) NOT NULL,
    created_at DATETIME NOT NULL,
    deleted TINYINT(1) NOT NULL DEFAULT 0,
    value BIGINT(20) NOT NULL,
    PRIMARY KEY (id),
    CONSTRAINT fk_version_fd_custodian_ia_sharegain_file_id_fd_custodian_ia_id
        FOREIGN KEY (fpsl_daily_custodian_interest_accrual_id) REFERENCES fpsl_daily_custodian_interest_accruals (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE version_fpsl_daily_custodian_interest_accrual_details (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    fpsl_daily_custodian_interest_accrual_id BIGINT(20) NOT NULL,
    version INT(11) NOT NULL,
    created_at DATETIME NOT NULL,
    deleted TINYINT(1) NOT NULL DEFAULT 0,
    value MEDIUMTEXT NOT NULL,
    PRIMARY KEY (id),
    CONSTRAINT fk_version_fd_custodian_ia_details_fd_custodian_ia_id
        FOREIGN KEY (fpsl_daily_custodian_interest_accrual_id) REFERENCES fpsl_daily_custodian_interest_accruals (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE version_fpsl_daily_custodian_interest_accrual_state (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    fpsl_daily_custodian_interest_accrual_id BIGINT(20) NOT NULL,
    version INT(11) NOT NULL,
    created_at DATETIME NOT NULL,
    deleted TINYINT(1) NOT NULL DEFAULT 0,
    value VARCHAR(255) NOT NULL,
    PRIMARY KEY (id),
    CONSTRAINT fk_version_fd_custodian_ia_state_fd_custodian_ia_id
        FOREIGN KEY (fpsl_daily_custodian_interest_accrual_id) REFERENCES fpsl_daily_custodian_interest_accruals (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

INSERT INTO schema_log(schema_version, date) VALUES (376, now());