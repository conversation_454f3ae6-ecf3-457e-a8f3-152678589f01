CREATE TABLE mark_to_market_lot_adjustments (
    id BIGINT(20) NOT NULL AUTO_INCREMENT PRIMARY KEY,
    account_identifier VARCHAR(255) NOT NULL,
    created_at DATETIME NOT NULL,
    effective_date DATE NOT NULL,
    cusip VARCHAR(9) NOT NULL,
    quantity DECIMAL(20,8) NOT NULL,
    new_price DECIMAL(14,4) NOT NULL,
    is_deleted TINYINT(1) NOT NULL DEFAULT 0,
    KEY mtmla_account_identifier (account_identifier),
    KEY mtmla_effective_date (effective_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

INSERT INTO schema_log (schema_version, date) VALUES (320, now());