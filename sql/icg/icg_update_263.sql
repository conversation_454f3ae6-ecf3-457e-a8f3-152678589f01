CREATE TABLE lock_logs (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  lock_type VARCHA<PERSON>(255) NOT NULL,
  owner VARCHAR(255) NOT NULL,
  requested_at DATETIME NOT NULL,
  request_timeout_at DATETIME DEFAULT NULL,
  acquired_at DATETIME DEFAULT NULL,
  released_at DATETIME DEFAULT NULL,
  release_method VARCHAR(255) DEFAULT NULL,
  _not_null_if_acquired_and_unreleased TINYINT(1) AS
    (CASE WHEN (acquired_at IS NOT NULL AND released_at IS NULL) THEN 0 ELSE NULL END) PERSISTENT,

  PRIMARY KEY (id),
  UNIQUE INDEX lock_logs_lock_type_not_null_if_acquired_and_unreleased (lock_type, _not_null_if_acquired_and_unreleased),
  INDEX lock_logs_acquired_at_released_at (acquired_at, released_at),
  INDEX lock_logs_lock_type_owner_acquired_at_released_at (lock_type, owner, acquired_at, released_at)
)
  ENGINE=InnoDB
  DEFAULT CHARSET=utf8;

INSERT INTO schema_log (schema_version, date) VALUES (263, now());
