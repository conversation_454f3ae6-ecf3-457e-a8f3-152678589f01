ALTER TABLE fpsl_custodian_account_loans
    ADD UNIQUE INDEX idx_fpsl_custodian_account_loans_loan_id (loan_id),
    ADD UNIQUE INDEX idx_fpsl_custodian_account_loans_fpsl_loan_instruction_id (fpsl_loan_instruction_id);

 ALTER TABLE fpsl_custodian_account_loan_details
    ADD UNIQUE INDEX idx_fpsl_custodian_account_loan_details_loan_id_effective_date (loan_id, effective_date);

 ALTER TABLE fpsl_client_account_loans
    ADD UNIQUE INDEX idx_fpsl_client_account_loans_ed_acc_id_borrower_code_isin (effective_date, bi_account_id_or_firm_account, borrower_code, isin);

INSERT INTO schema_log (schema_version, date) VALUES(359, now());