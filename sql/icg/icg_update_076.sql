CREATE TABLE queued_ira_withdrawals (
  id           BIGINT(20) NOT NULL AUTO_INCREMENT,
  version      BIGINT(20) NOT NULL,
  created_at   DATETIME   NOT NULL,
  polled_time  DATETIME            DEFAULT NULL,
  sent_time    DATETIME            DEFAULT NULL,
  error_flag   TINYINT(1) NOT NULL DEFAULT '0',
  payload      MEDIUMTEXT   NOT NULL,
  withdrawalid BIGINT(20) NOT NULL,
  accountid    BIGINT(20) NOT NULL,
  PRIMARY KEY (id),
  UNIQUE KEY queued_ira_withdrawals_withdrawalid (withdrawalid),
  KEY queued_ira_withdrawals_polled_time_error_flag (polled_time, error_flag),
  <PERSON><PERSON><PERSON> queued_ira_withdrawals_sent_time_polled_time_error_flag (sent_time, polled_time, error_flag)
)
  ENGINE = InnoDB
  DEFAULT CHARSET = utf8;
INSERT INTO schema_log (schema_version, date) VALUES (76, now());
