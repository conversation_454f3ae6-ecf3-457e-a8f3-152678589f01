CREATE TABLE umb_ach_rdfi_transfers (
    id                                BIGINT(20)    NOT NULL AUTO_INCREMENT,
    created_at                        DATETIME      NOT NULL,
    bank_transfer_id                  BIGINT(20)    NOT NULL,
    umb_account_number_information_id BIGINT(20)    DEFAULT NULL,
    detail_ach_file_row_id            BIGINT(20)    NOT NULL,
    batch_header_ach_file_row_id      BIGINT(20)    NOT NULL,
    umb_file_id                       BIGINT(20)    NOT NULL,
    presentment_date                  DATE          NOT NULL,
    settlement_date                   DATE          NOT NULL,
    settlement_cycle                  VARCHAR(255)  DEFAULT NULL,
    originating_company_name          VARCHAR(255)  NOT NULL,
    sec_code                          VARCHAR(255)  NOT NULL,
    originating_routing_number        VARCHAR(255)  NOT NULL,
    transaction_code                  VARCHAR(255)  NOT NULL,
    transfer_direction                VARCHAR(255)  NOT NULL,
    amount                            DECIMAL(20,2) NOT NULL,
    trace                             VARCHAR(255)  NOT NULL,
    state                             VARCHAR(255)  NOT NULL,
    receiver_details                  MEDIUMTEXT    NOT NULL,
    <PERSON>IMARY KEY (id),
    <PERSON><PERSON><PERSON>(created_at),
    <PERSON><PERSON><PERSON>(presentment_date),
    <PERSON><PERSON><PERSON>(settlement_date, settlement_cycle),
    KEY(originating_routing_number),
    UNIQUE KEY(trace),
    KEY(state),
    KEY(transfer_direction),
    KEY(transaction_code),
    KEY(sec_code),
    CONSTRAINT uart_bank_transfer_id FOREIGN KEY (bank_transfer_id) REFERENCES bank_transfers (id),
    CONSTRAINT uart_umb_account_number_information_id FOREIGN KEY (umb_account_number_information_id) REFERENCES umb_account_number_information (id),
    CONSTRAINT uart_detail_ach_file_row_id FOREIGN KEY (detail_ach_file_row_id) REFERENCES ach_file_rows (id),
    CONSTRAINT uart_batch_header_ach_file_row_id FOREIGN KEY (batch_header_ach_file_row_id) REFERENCES ach_file_rows (id),
    CONSTRAINT uart_umb_file_id FOREIGN KEY (umb_file_id) REFERENCES umb_files (id)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;

CREATE TABLE version_umb_ach_rdfi_transfer_state(
    id                                BIGINT(20)   NOT NULL AUTO_INCREMENT,
    umb_ach_rdfi_transfer_id          BIGINT(20)   NOT NULL,
    version                           INT          NOT NULL,
    created_at                        DATETIME     NOT NULL,
    deleted                           TINYINT(1)   NOT NULL,
    value                             VARCHAR(255) NOT NULL,
    PRIMARY KEY (id),
    INDEX version_umb_ach_rdfi_transfer_state_created_at (created_at),
    CONSTRAINT vuarts_umb_ach_rdfi_transfer_id FOREIGN KEY (umb_ach_rdfi_transfer_id) REFERENCES umb_ach_rdfi_transfers (id)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;

CREATE TABLE umb_ach_rdfi_transfers_to_addenda_ach_file_rows (
    umb_ach_rdfi_transfer_id     BIGINT(20)   NOT NULL,
    ach_file_row_id              BIGINT(20)   NOT NULL,
    PRIMARY KEY (umb_ach_rdfi_transfer_id, ach_file_row_id),
    CONSTRAINT uarttaafr_umb_rdfi_transfer_id FOREIGN KEY (umb_ach_rdfi_transfer_id) REFERENCES umb_ach_rdfi_transfers (id),
    CONSTRAINT uarttaafr_ach_file_row_id FOREIGN KEY (ach_file_row_id) REFERENCES ach_file_rows (id)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;

INSERT INTO schema_log (schema_version, date) VALUES (760, now());