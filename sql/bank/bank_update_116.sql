CREATE TABLE deposit_returning_bank_transfers (
  deposit_id BIGINT(20) NOT NULL,
  bank_transfer_id BIGINT(20) NOT NULL,
  PRIMARY KEY (deposit_id, bank_transfer_id),
  CONSTRAINT deposit_returning_bank_transfers_deposit_id FOREIGN KEY (deposit_id) REFERENCES deposits (id),
  CONSTRAINT deposit_returning_bank_transfers_bank_transfer_id FOREIGN KEY (bank_transfer_id) REFERENCES bank_transfers (id));

INSERT INTO schema_log (schema_version, date) VALUES (116, now());
