CREATE TABLE transaction_monitoring_inputs (
    id                                              BIGINT(20)      NOT NULL AUTO_INCREMENT,
    created_at                                      DATETIME        NOT NULL,
    initiating_user_id                              BIGINT(20)      NOT NULL,
    account_id                                      BIGINT(20)      NOT NULL,
    transaction_type                                VARCHAR(255)    NOT NULL,
    transaction_id                                  BIGINT(20)      NOT NULL,
    PRIMARY KEY (id),
    KEY transaction_monitoring_inputs_created_at (created_at),
    KEY transaction_monitoring_inputs_initiating_user_id (initiating_user_id),
    KEY transaction_monitoring_inputs_account_id (account_id),
    UNIQUE KEY transaction_monitoring_inputs_transaction_type_transaction_id (transaction_type, transaction_id)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;

CREATE TABLE transaction_monitoring_outputs (
    id                                              BIGINT(20)      NOT NULL AUTO_INCREMENT,
    transaction_monitoring_input_id                 BIGINT(20)      NOT NULL,
    created_at                                      DATETIME        NOT NULL,
    summary                                         MEDIUMTEXT      NOT NULL,
    evaluation_result                               TEXT            NOT NULL,
    PRIMARY KEY (id),
    UNIQUE KEY transaction_monitoring_outputs_transaction_monitoring_input_id (transaction_monitoring_input_id),
    KEY transaction_monitoring_outputs_created_at (created_at),
    CONSTRAINT transaction_monitoring_outputs_transaction_monitoring_input_id FOREIGN KEY (transaction_monitoring_input_id) REFERENCES transaction_monitoring_inputs (id)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;

CREATE TABLE transaction_monitoring_offenses (
    id                                              BIGINT(20)      NOT NULL AUTO_INCREMENT,
    transaction_monitoring_input_id                 BIGINT(20)      NOT NULL,
    created_at                                      DATETIME        NOT NULL,
    evaluation_result                               TEXT            NOT NULL,
    PRIMARY KEY (id),
    UNIQUE KEY transaction_monitoring_offenses_transaction_monitoring_input_id (transaction_monitoring_input_id),
    KEY transaction_monitoring_offenses_created_at (created_at),
    CONSTRAINT transaction_monitoring_offenses_transaction_monitoring_input_id FOREIGN KEY (transaction_monitoring_input_id) REFERENCES transaction_monitoring_inputs (id)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;

INSERT INTO schema_log (schema_version, date) VALUES (708, now());