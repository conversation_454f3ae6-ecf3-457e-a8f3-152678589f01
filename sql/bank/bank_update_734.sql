DROP TABLE bank_transfer_acceptance_statuses;

CREATE TABLE `bank_transfer_acceptance_statuses` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `bank_transfer_id` bigint(20) NOT NULL,
  `acceptance_status` varchar(255) DEFAULT NULL,
  `acceptance_sub_status` varchar(255) DEFAULT NULL,
  `acceptance_notes` text DEFAULT NULL,
  `rule_accepted_by` varchar(255) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  `processed_at` datetime DEFAULT NULL,
  `reviewed_at` datetime DEFAULT NULL,
  `taos_reviewer` varchar(255) DEFAULT NULL,
  `notes` mediumtext NOT NULL,
  PRIMARY KEY (`id`),
  KEY `btas_acceptance_status_processed_at` (`acceptance_status`,`processed_at`),
  KEY `btas_created_at` (`created_at`),
  CONSTRAINT `bank_transfer_acceptance_statuses` FOREIGN KEY (`bank_transfer_id`) REFERENCES `bank_transfers` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO schema_log (schema_version, date) VALUES (734, now());
