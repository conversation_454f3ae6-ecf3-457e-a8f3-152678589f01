CREATE TABLE bank_transfer_acceptance_status(
  id                BIGINT(20)    NOT NULL AUTO_INCREMENT,
  bank_transfer_id  BIGINT(20)    NOT NULL,
  acceptance_status VARCHAR(255)  NULL,
  acceptance_notes  TEXT          NULL,
  rule_accepted_by  VARCHAR(255)  NULL,
  created_at        DATETIME      NOT NULL,
  processed_at      DATETIME      NULL,
  reviewed_at       DATETIME      NULL,
  taos_reviewer     VARCHAR(255)  NULL,

  PRIMARY KEY (id),
  KEY `btas_acceptance_status_processed_at` (acceptance_status, processed_at),
  KEY `btas_created_at` (created_at)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;

INSERT INTO schema_log (schema_version, date) VALUES (712, NOW());
