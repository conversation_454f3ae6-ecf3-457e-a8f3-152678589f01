CREATE TABLE version_outgoing_green_dot_balance_event_state (
  id                                  bigint(20)   NOT NULL AUTO_INCREMENT,
  green_dot_balance_event_id          bigint(20)   NOT NULL,
  version                             int(11)      NOT NULL,
  created_at                          datetime     NOT NULL,
  deleted                             tinyint(1)   NOT NULL DEFAULT 0,
  value                               varchar(255) NOT NULL,
  PRIMARY KEY (id),
  CONSTRAINT version_outgoing_gd_balance_event_state_gd_balance_event_id
    FOREIGN KEY (green_dot_balance_event_id) REFERENCES green_dot_balance_events (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO schema_log (schema_version, DATE) VALUES (516, NOW());