CREATE TABLE ira_transfer_contexts (
  id                             BIGINT(20)    NOT NULL AUTO_INCREMENT,
  type                           VA<PERSON>HAR(255)  NOT NULL,
  tax_year                       SMALLINT      NOT NULL,
  date_completed                 DATE          DEFAULT NULL,
  ira_distribution_instructions  TEXT          DEFAULT NULL,
  PRIMARY KEY (id),
  INDEX ira_transfer_contexts_tax_year (tax_year),
  INDEX ira_transfer_contexts_date_completed (date_completed)
) 
  ENGINE=InnoDB
  DEFAULT CHARSET=utf8;

ALTER TABLE cross_account_transfers
  ADD COLUMN ira_transfer_context_id BIGINT(20) DEFAULT NULL,
  ADD CONSTRAINT cross_account_transfers_ira_transfer_context_id
    FOREIGN KEY (ira_transfer_context_id) REFERENCES ira_transfer_contexts (id);

INSERT INTO schema_log (schema_version, date) VALUES (401, now());
