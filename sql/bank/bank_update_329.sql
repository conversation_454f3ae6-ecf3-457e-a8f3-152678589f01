CREATE TABLE wells_safe_t_statuses (
  id                              BIGINT          NOT NULL AUTO_INCREMENT,
  current_availability            VARCHAR(255)    NOT NULL,
  last_update_time                DATETIME        NOT NULL,
  last_availability_change_time   DATETIME        NOT NULL,
  PRIMARY KEY (id)
)
  ENGINE=InnoDB
  DEFAULT CHARSET=utf8;

CREATE TABLE wells_safe_t_status_updates (
  id                              BIGINT          NOT NULL AUTO_INCREMENT,
  wells_safe_t_status_id          BIGINT(20)      DEFAULT NULL,
  availability                    VARCHAR(255)    NOT NULL,
  update_source                   VARCHAR(255)    NOT NULL,
  status_message                  TEXT            DEFAULT NULL,
  created_at                      DATETIME        NOT NULL,
  PRIMARY KEY (id),
  CONSTRAINT wells_safe_t_status_updates_wells_safe_t_statuses
    FOREIGN KEY (wells_safe_t_status_id) REFERENCES wells_safe_t_statuses (id),
  INDEX wells_safe_t_status_updates_created_at (created_at)
)
  ENGINE=InnoDB
  DEFAULT CHARSET=utf8;

INSERT INTO schema_log (schema_version, date) VALUES (329, now());
