CREATE TABLE buckets_to_banking_partner_file (
  id                                BIGINT(20)   NOT NULL AUTO_INCREMENT,
  banking_partner                   VARCHAR(255) NOT NULL,
  file_id                           BIGINT(20)   NOT NULL,
  bucket_id                         BIGINT(20)   NOT NULL,
  context                           VARCHAR(255) NOT NULL,
  PRIMARY KEY (id),
  INDEX (banking_partner, file_id),
  INDEX (bucket_id, context),
  CONSTRAINT buckets_to_banking_partner_file FOREIGN KEY (bucket_id) REFERENCES buckets (id)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;

INSERT INTO schema_log (schema_version, date) VALUES (788, now());