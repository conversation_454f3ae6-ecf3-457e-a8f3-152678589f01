CREATE TABLE perfect_receivables_files (
  id                            BIGINT        NOT NULL AUTO_INCREMENT,
  created_at                    DATETIME      NOT NULL,
  processed_at                  DATETIME      DEFAULT NULL,
  effective_date                DATE          NOT NULL,
  perfect_receivables_file      LONGTEXT      NOT NULL,
  wells_file_id                 BIGINT        NOT NULL,
  PRIMARY KEY (id),
  KEY perfect_receivables_files_wells_file_id (wells_file_id),
  CONSTRAINT perfect_receivables_files_wells_file_id
  FOREIGN KEY (wells_file_id) REFERENCES wells_files (id)
)
ENGINE = InnoDB;

INSERT INTO schema_log (schema_version, date) VALUES (19, now());