ALTER TABLE umb_account_number_information
    ADD COLUMN lock_initiator  <PERSON><PERSON><PERSON><PERSON>(255) DEFAULT NULL,
    ADD COLUMN lock_reason     VARCHAR(255) DEFAULT NULL;

CREATE TABLE version_umb_account_number_lock_details(
    id                                BIGINT(20)   NOT NULL AUTO_INCREMENT,
    umb_account_number_information_id BIGINT(20)   NOT NULL,
    version                           INT          NOT NULL,
    created_at                        DATETIME     NOT NULL,
    deleted                           TINYINT(1)   NOT NULL,
    is_locked_for_withdrawals         BOOLEAN      NOT NULL,
    is_locked_for_deposits            BOOLEAN      NOT NULL,
    lock_initiator                    <PERSON><PERSON>HA<PERSON>(255) DEFAULT NULL,
    lock_reason                       VARCHAR(255) DEFAULT NULL,
    PRIMARY KEY (id),
    CONSTRAINT vuanld_umb_account_number_information_id
      FOREIGN KEY (umb_account_number_information_id) REFERENCES umb_account_number_information (id)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;

INSERT INTO schema_log (schema_version, date) VALUES (761, NOW());