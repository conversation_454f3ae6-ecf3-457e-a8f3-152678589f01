CREATE TABLE conversion_ira_contributions (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  account_name VARCHAR(20) NOT NULL,
  adp_security_number VARCHAR(255) NOT NULL,
  amount DECIMAL(20,2) NOT NULL,
  account_id BIGINT(20) DEFAULT NULL,
  created_at DATETIME NOT NULL,
  PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET = utf8;

ALTER TABLE conversion_ira_contributions
ADD INDEX conversion_ira_contributions_adp_security_number (adp_security_number);

INSERT INTO schema_log VALUES (270, now());