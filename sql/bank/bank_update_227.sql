CREATE TABLE version_omni_ira_deposit_contribution_type (
  id                  BIGINT     NOT NULL AUTO_INCREMENT,
  omni_ira_deposit_id BIGINT     NOT NULL,
  version             INTEGER    NOT NULL,
  created_at          DATETIME   NOT NULL,
  deleted             TINYINT(1) NOT NULL,
  value               TEXT       NOT NULL,
  PRIMARY KEY (id),
  CONSTRAINT version_ira_contribution_type_omni_ira_deposit_id
  FOREIGN KEY (omni_ira_deposit_id) REFERENCES omni_ira_deposits (id)
)
  ENGINE = InnoDB
  DEFAULT CHARSET = utf8;

CREATE TABLE version_omni_ira_deposit_contribution_year (
  id                  BIGINT     NOT NULL AUTO_INCREMENT,
  omni_ira_deposit_id BIGINT     NOT NULL,
  version             INTEGER    NOT NULL,
  created_at          DATETIME   NOT NULL,
  deleted             TINYINT(1) NOT NULL,
  value               TEXT       NOT NULL,
  PRIMARY KEY (id),
  CONSTRAINT version_ira_contribution_year_omni_ira_deposit_id
  FOREIGN KEY (omni_ira_deposit_id) REFERENCES omni_ira_deposits (id)
)
  ENGINE = InnoDB
  DEFAULT CHARSET = utf8;

INSERT INTO schema_log (schema_version, DATE) VALUES (227, now());
