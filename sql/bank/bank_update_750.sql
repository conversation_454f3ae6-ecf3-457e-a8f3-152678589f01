ALTER TABLE wells_account_transfer_limits
    ADD COLUMN account_name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL DEFAULT 'OMNI_FBO',
    MODIFY COLUMN wells_bank_account_id BIGINT(20) NULL;

ALTER TABLE real_time_wells_bank_account_balances
    ADD COLUMN account_name VA<PERSON>HAR(255) NOT NULL DEFAULT 'DAY<PERSON><PERSON>HT_OVERDRAFT',
    MODIFY COLUMN wells_bank_account_id BIGINT(20) NULL;

ALTER TABLE daily_rtp_withdrawal_amounts
    ADD COLUMN account_name VARCHAR(255) NOT NULL DEFAULT 'DAYLIGHT_OVERDRAFT',
    MODIFY COLUMN wells_bank_account_id BIGINT(20) NULL;

ALTER TABLE unexpected_bai2_transactions
    MODIFY COLUMN wells_bank_account_id BIGINT(20) NULL,
    ADD COLUMN account_name    VARCHAR(255) NOT NULL DEFAULT 'OMNI_FBO',
    ADD COLUMN banking_partner VARC<PERSON><PERSON>(255) NOT NULL DEFAULT 'WELLS',
    ADD COLUMN file_id         BIGINT(20)   NOT NULL DEFAULT 0;

INSERT INTO schema_log (schema_version, date) VALUES (750, now());