CREATE TABLE logicbox_requests
(
    id              BIGINT(20) NOT NULL AUTO_INCREMENT,
    userid          BIGINT(20) NOT NULL,
    risk_request_id BIGINT(20) DEFAULT NULL,
    name            <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    date_of_birth   VARCHAR(255) DEFAULT NULL,
    response        VARCHAR(255) DEFAULT NULL,
    status_code     SMALLINT     DEFAULT NULL,
    created_at      DATETIME     NOT NULL,
    PRIMARY KEY (id),
    INDEX           logicbox_requests_userid(userid),
    CONSTRAINT logicbox_requests_risk_request_id FOREIGN KEY (risk_request_id) REFERENCES risk_requests (id)
) ENGINE=InnoDB DEFAULT CHARSET=UTF8;

INSERT INTO schema_log (schema_version, date) VALUES (557, NOW());