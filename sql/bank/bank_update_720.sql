CREATE TABLE queued_wire_payment_actions (
    id                      BIGINT(20)      NOT NULL AUTO_INCREMENT,
    version                 BIGINT          NOT NULL,
    created_at              DATETIME        NOT NULL,
    polled_time             DATETIME        DEFAULT NULL,
    sent_time               DATETIME        DEFAULT NULL ,
    error_flag              TINYINT(1)      NOT NULL DEFAULT 0,
    wire_payment_action     VARCHAR(255)    NOT NULL,
    wire_withdrawal_id      BIGINT(20)      DEFAULT NULL,
    wells_wire_id           BIGINT(20)      DEFAULT NULL,

    PRIMARY KEY (id),
    INDEX queued_wire_payment_actions_sent_polled_error (sent_time, polled_time, error_flag),
    CONSTRAINT queued_wire_payment_actions_wire_withdrawal_id
        FOREIGN KEY (wire_withdrawal_id)
        REFERENCES wire_withdrawals (id),
    CONSTRAINT queued_wire_payment_actions_wells_wire_id
        FOREIGN KEY (wells_wire_id)
        REFERENCES wells_wires (id)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;

INSERT INTO schema_log (schema_version, date) VALUES (720, now());