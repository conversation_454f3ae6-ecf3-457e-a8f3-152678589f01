CREATE TABLE instant_withdrawal_throttling_reports (
  id                   BIGINT(20)   NOT NULL AUTO_INCREMENT,
  created_at           DATETIME     NOT NULL,
  throttled_count      INT          NOT NULL,
  sent_time            DATETIME     DEFAULT NULL,
  PRIMARY KEY (id),
  INDEX (created_at),
  INDEX (sent_time)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;

CREATE TABLE instant_withdrawal_throttling_report_entries (
  id                                           BIGINT(20)   NOT NULL AUTO_INCREMENT,
  withdrawal_id                                BIGINT(20)   NOT NULL,
  instant_withdrawal_throttling_report_id      BIGINT(20)   NOT NULL,
  throttled_at                                 DATETIME     NOT NULL,
  PRIMARY KEY (id),
  CONSTRAINT withdrawal_id
    FOREIGN KEY (withdrawal_id)
    REFERENCES withdrawals (id),
  CONSTRAINT instant_withdrawal_throttling_report_id
    FOREIGN KEY (instant_withdrawal_throttling_report_id)
    REFERENCES instant_withdrawal_throttling_reports (id)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;

INSERT INTO schema_log (schema_version, DATE) VALUES (717, now());