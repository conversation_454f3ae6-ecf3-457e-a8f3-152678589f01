CREATE TABLE queued_tbs_transaction_for_instant_withdrawals (
  id                   BIGINT(20)   NOT NULL AUTO_INCREMENT,
  created_at           DATETIME     NOT NULL,
  polled_time          DATETIME     DEFAULT NULL,
  sent_time            DATETIME     DEFAULT NULL,
  ignored_at           DATETIME     DEFAULT NULL,
  error_flag           TINYINT(1)   NOT NULL DEFAULT 0,
  action               VARCHAR(255) NOT NULL,
  bucket_withdrawal_id BIGINT(20)   NOT NULL,

  PRIMARY KEY (id),
  INDEX qttfiw_polled_time_error_flag (polled_time, error_flag),
  INDEX qttfiw_sent_time_polled_time_error_flag (sent_time, polled_time, error_flag),
  CONSTRAINT bucket_withdrawal_id
    FOREIGN KEY (bucket_withdrawal_id) REFERENCES bucket_withdrawals (id)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;

INSERT INTO schema_log (schema_version, DATE) VALUES (714, now());