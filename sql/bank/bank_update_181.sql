CREATE TABLE sqs_jobs (
  id                       BIGINT       NOT NULL AUTO_INCREMENT,
  start_time               DATETIME NOT NULL,
  end_time                 DATETIME,
  message_id               VARCHAR(255) NOT NULL,
  chronos_config_name      VARCHAR(255) NOT NULL,
  query_name               VARCHAR(255)  NOT NULL,
  arguments                TEXT,
  mesos_task_id            VARCHAR(255) NOT NULL,
  job_status               VARCHAR(255),
  service                  VARCHAR(255)  NOT NULL,
  node_identifier          VARCHAR(255)  NOT NULL,
  return_code              BIGINT,
  updated_chronos          BOOL,
  PRIMARY KEY (id)
)
  ENGINE = InnoDB
  DEFAULT CHARSET = utf8;

CREATE INDEX sqs_jobs_start_time ON sqs_jobs (start_time);
CREATE INDEX sqs_jobs_end_time ON sqs_jobs (end_time);
CREATE INDEX sqs_jobs_return_code ON sqs_jobs (return_code);

INSERT INTO schema_log (schema_version, date) values (181, now());