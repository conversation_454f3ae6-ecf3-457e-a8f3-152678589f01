CREATE TABLE deposit_bank_transfers (
  deposit_id BIGINT(20) NOT NULL,
  bank_transfer_id BIGINT(20) NOT NULL,
  PRIMARY KEY (deposit_id, bank_transfer_id),
  CONSTRAINT deposit_bank_transfers_deposit_id FOREIGN KEY (deposit_id) REFERENCES deposits (id),
  CONSTRAINT deposit_bank_transfers_bank_transfer_id FOREIGN KEY (bank_transfer_id) REFERENCES bank_transfers (id));

CREATE TABLE withdrawal_bank_transfers (
  withdrawal_id BIGINT(20) NOT NULL,
  bank_transfer_id BIGINT(20) NOT NULL,
  PRIMARY KEY (withdrawal_id, bank_transfer_id),
  CONSTRAINT withdrawal_bank_transfers_withdrawal_id FOREIGN KEY (withdrawal_id) REFERENCES withdrawals (id),
  CONSTRAINT withdrawal_bank_transfers_bank_transfer_id FOREIGN KEY (bank_transfer_id) REFERENCES bank_transfers (id));

INSERT INTO schema_log (schema_version, date) values (75, now());
