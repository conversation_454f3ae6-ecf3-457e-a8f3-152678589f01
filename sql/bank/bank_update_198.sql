CREATE TABLE cbrs_transfers (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  type <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
  transaction_type VARCHAR(255) NOT NULL,
  transfer_control_number VARCHAR(255) NOT NULL,
  our_firm_type VA<PERSON>HAR(255) NOT NULL,
  our_firm_number VARCHAR(255) NOT NULL,
  contra_firm_type VARCHAR(255) NOT NULL,
  contra_firm_number VARCHAR(255) NOT NULL,
  our_account_number VARCHAR(255) NOT NULL,
  contra_account_number VARCHAR(255) NOT NULL,
  state VARCHAR(255) NOT NULL,
  settlement_date DATE NOT NULL,
  created_at DATETIME NOT NULL,
  PRIMARY KEY (id),
  INDEX cbrs_transfers_created_at (created_at),
  INDEX cbrs_transfers_transfer_control_number (transfer_control_number)
) ENGINE = InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE version_cbrs_transfer_state (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  cbrs_transfer_id BIGINT(20) NOT NULL,
  version INT NOT NULL,
  created_at DATETIME NOT NULL,
  deleted TINYINT(1) NOT NULL,
  value VARCHAR(255) NOT NULL,
  PRIMARY KEY (id),
  INDEX version_cbrs_transfer_state_cbrs_transfer_id (cbrs_transfer_id),
  CONSTRAINT version_cbrs_transfer_state_cbrs_transfer_id
  FOREIGN KEY (cbrs_transfer_id) REFERENCES cbrs_transfers (id)
) ENGINE = InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE cbrs_assets (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  type VARCHAR(255) NOT NULL,
  isin VARCHAR(255) NOT NULL,
  acats_sequence_number VARCHAR(255) NOT NULL,
  category VARCHAR(255) NOT NULL,
  state VARCHAR(255) NOT NULL,
  quantity DECIMAL(20,8) NOT NULL,
  created_at DATETIME NOT NULL,
  cbrs_transfer_id BIGINT(20) NOT NULL,
  requested TINYINT(1) NOT NULL,
  PRIMARY KEY (id),
  INDEX cbrs_assets_created_at (created_at),
  INDEX cbrs_assets_type_state (type, state),
  CONSTRAINT cbrs_assets_cbrs_transfer_id
  FOREIGN KEY (cbrs_transfer_id) REFERENCES cbrs_transfers (id)
) ENGINE = InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE version_cbrs_asset_state (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  cbrs_asset_id BIGINT(20) NOT NULL,
  version INT NOT NULL,
  created_at DATETIME NOT NULL,
  deleted TINYINT(1) NOT NULL,
  value VARCHAR(255) NOT NULL,
  PRIMARY KEY (id),
  INDEX version_cbrs_asset_state_cbrs_asset_id (cbrs_asset_id),
  CONSTRAINT version_cbrs_asset_state_cbrs_asset_id
  FOREIGN KEY (cbrs_asset_id) REFERENCES cbrs_assets (id)
) ENGINE = InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE cbrs_inputs (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  type VARCHAR(255) NOT NULL,
  state VARCHAR(255) NOT NULL,
  cbrs_file_id BIGINT,
  cbrs_asset_id BIGINT,
  created_at DATETIME NOT NULL,
  PRIMARY KEY (id),
  INDEX cbrs_inputs_created_at (created_at)
) ENGINE = InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE version_cbrs_input_state (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  cbrs_input_id BIGINT(20) NOT NULL,
  version INT NOT NULL,
  created_at DATETIME NOT NULL,
  deleted TINYINT(1) NOT NULL,
  value VARCHAR(255) NOT NULL,
  PRIMARY KEY (id),
  INDEX version_cbrs_input_state_cbrs_input_id (cbrs_input_id),
  CONSTRAINT version_cbrs_input_state_cbrs_input_id
  FOREIGN KEY (cbrs_input_id) REFERENCES cbrs_inputs (id)
) ENGINE = InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE version_cbrs_input_file_id (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  cbrs_input_id BIGINT(20) NOT NULL,
  version INT NOT NULL,
  created_at DATETIME NOT NULL,
  deleted TINYINT(1) NOT NULL,
  value BIGINT(20) NOT NULL,
  PRIMARY KEY (id),
  INDEX version_cbrs_input_file_id_cbrs_input_id (cbrs_input_id),
  CONSTRAINT version_cbrs_input_file_id_cbrs_input_id
  FOREIGN KEY (cbrs_input_id) REFERENCES cbrs_inputs (id),
  CONSTRAINT version_cbrs_input_file_id_value
  FOREIGN KEY (value) REFERENCES cbrs_files (id)
) ENGINE = InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE cbrs_asset_to_tax_lots (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  type VARCHAR(255) NOT NULL,
  created_at DATETIME NOT NULL,
  cbrs_input_id BIGINT,
  cbrs_asset_id BIGINT(20) NOT NULL,
  version INT NOT NULL,
  PRIMARY KEY (id),
  INDEX cbrs_asset_to_tax_lots_created_at (created_at),
  CONSTRAINT cbrs_asset_to_tax_lots_cbrs_input_id
  FOREIGN KEY (cbrs_input_id) REFERENCES cbrs_inputs (id),
  CONSTRAINT cbrs_asset_to_tax_lots_cbrs_asset_id
  FOREIGN KEY (cbrs_asset_id) REFERENCES cbrs_assets (id)
) ENGINE = InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE cbrs_tax_lots (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  type VARCHAR(255) NOT NULL,
  quantity DECIMAL(20,8) NOT NULL,
  open_date DATE NOT NULL,
  wash_sale_disallowed_loss DECIMAL(14,2) NOT NULL,
  zero_basis VARCHAR(255),
  noncovered_pending VARCHAR(255),
  wash_sale TINYINT(1) NOT NULL,
  cbrs_asset_to_tax_lot_id BIGINT(20) NOT NULL,
  PRIMARY KEY (id),
  CONSTRAINT cbrs_tax_lots_cbrs_asset_to_tax_lot_id
  FOREIGN KEY (cbrs_asset_to_tax_lot_id) REFERENCES cbrs_asset_to_tax_lots (id)
) ENGINE = InnoDB DEFAULT CHARSET = utf8;

ALTER TABLE cbrs_outputs
  ADD COLUMN cbrs_transfer_id BIGINT(20) DEFAULT NULL,
  ADD CONSTRAINT cbrs_outputs_cbrs_transfer_id
  FOREIGN KEY (cbrs_transfer_id) REFERENCES cbrs_transfers (id);

INSERT INTO schema_log (schema_version, DATE) VALUES (198, now());

