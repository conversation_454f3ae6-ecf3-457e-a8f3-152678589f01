CREATE TABLE `toggles`
(
    `id`    bigint(20)   NOT NULL AUTO_INCREMENT,
    `name`  varchar(255) NOT NULL,
    `state` tinyint(1)   NOT NULL,
    PRIMARY KEY (`id`),
    <PERSON>EY `name` (`name`)
);
CREATE TABLE `version_toggle_state`
(
    `id`         bigint(20) NOT NULL AUTO_INCREMENT,
    `toggle_id`  bigint(20) NOT NULL,
    `version`    int(11)    NOT NULL,
    `created_at` datetime   NOT NULL,
    `deleted`    tinyint(1) NOT NULL,
    `value`      tinyint(1) NOT NULL,
    PRIMARY KEY (`id`),
    KEY `toggle_id` (`toggle_id`),
    <PERSON><PERSON>Y `created_at` (`created_at`),
    CONSTRAINT `toggle_id` FOREIGN KEY (`toggle_id`) REFERENCES `toggles` (`id`)
);

INSERT INTO schema_log (schema_version, date) VALUES (5, now());
