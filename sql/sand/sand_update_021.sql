CREATE TABLE `rocky_deployment_test` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_at` datetime NOT NULL,
  `error_flag` tinyint(1) NOT NULL DEFAULT 0,
  `is_test` tinyint(1) DEFAULT 0,
  `payload` mediumtext DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;

ALTER TABLE rocky_deployment_test ADD COLUMN owner VARCHAR(255) DEFAULT NULL;

INSERT INTO schema_log (schema_version, date) VALUES (21, now());
