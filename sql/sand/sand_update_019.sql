CREATE TABLE `green_dot_webhook_payloads` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_at` datetime NOT NULL,
  `polled_time` datetime DEFAULT NULL,
  `sent_time` datetime DEFAULT NULL,
  `ignored_at` datetime DEFAULT NULL,
  `error_flag` tinyint(1) NOT NULL DEFAULT 0,
  `sqs_message_id` varchar(255) NOT NULL,
  `webhook_type` varchar(255) DEFAULT NULL,
  `endpoint` varchar(255) DEFAULT NULL,
  `pci_scrubbed` tinyint(1) DEFAULT 0,
  `is_test` tinyint(1) DEFAULT 0,
  `cde_received_at` datetime DEFAULT NULL,
  `trusted_received_at` datetime DEFAULT NULL,
  `green_dot_requestid` varchar(255) DEFAULT NULL,
  `payload` mediumtext DEFAULT NULL,
  `scrubbed_fields` text DEFAULT NULL,
  `green_dot_account_identifier` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `green_dot_webhook_payloads_sqs_message_id` (`sqs_message_id`),
  KEY `green_dot_webhook_payloads_st_pt_ia_ef` (`sent_time`,`polled_time`,`ignored_at`,`error_flag`),
  KEY `green_dot_webhook_payloads_polled_time_ignored_at_error_flag` (`polled_time`,`ignored_at`,`error_flag`),
  KEY `green_dot_webhook_payloads_created_at` (`created_at`),
  KEY `green_dot_webhook_payloads_ignored_at` (`ignored_at`),
  KEY `green_dot_webhook_payloads_error_flag` (`error_flag`),
  KEY `green_dot_webhook_payloads_green_dot_account_identifier` (`green_dot_account_identifier`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8;

INSERT INTO schema_log (schema_version, date) VALUES (19, now());
