CREATE TABLE queued_vesta_operations_vesta_api_requests (
  queued_vesta_operation_id   BIGINT(20)  NOT NULL,
  vesta_api_request_id  BIGINT(20)  NOT NULL,

  PRIMARY KEY (queued_vesta_operation_id, vesta_api_request_id),

  CONSTRAINT queued_vesta_operations_vesta_api_requests_qvo_id
  FOREIGN KEY (queued_vesta_operation_id) REFERENCES queued_vesta_operations(id),

  CONSTRAINT queued_vesta_operations_vesta_api_requests_vesta_api_request_id
  FOREIGN KEY (vesta_api_request_id) REFERENCES vesta_api_requests(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

INSERT INTO schema_log (schema_version, date) VALUES (13, NOW());