CREATE TABLE vesta_api_requests (
  id BIGINT(20) NOT NULL AUTO_INCREMENT,
  application_id BIGINT(20) NOT NULL,
  name VA<PERSON>HAR(255) NOT NULL,
  http_method VARCHAR(255) NOT NULL,
  encrypted_request_body TEXT DEFAULT NULL,
  start_at DATETIME NOT NULL,
  end_at DATETIME DEFAULT NULL,
  duration INT(11) DEFAULT NULL,
  status_code INT(11) DEFAULT NULL,
  encrypted_response_body TEXT DEFAULT NULL,
  threw_exception BOOLEAN NOT NULL,
  idempotency_key VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY(id),
  CONSTRAINT vesta_api_request_application_id FOREIGN KEY (application_id) REFERENCES applications (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

INSERT INTO schema_log (schema_version, date) VALUES (12, NOW());