ALTER TABLE application_data_points
DROP FOR<PERSON><PERSON>N KEY `application_data_points_ibfk_1`;

ALTER TABLE application_data_points
DROP FOREIGN KEY `_application_data_points_ibfk_1`;

ALTER TABLE application_data_points
ADD CONSTRAINT `application_data_points_application_id`
FOREIGN KEY (`application_id`) REFERENCES `applications` (`id`);

ALTER TABLE application_data_points
ADD CONSTRAINT `application_data_points_queued_vesta_operation_id`
FOREIGN KEY (`queued_vesta_operation_id`) REFERENCES `queued_vesta_operations` (`id`);

ALTER TABLE application_data_points
ADD COLUMN `entity_action` VARCHAR(255) DEFAULT NULL AFTER `nullness`;

INSERT INTO schema_log (schema_version, date)
VALUES (11, NOW());