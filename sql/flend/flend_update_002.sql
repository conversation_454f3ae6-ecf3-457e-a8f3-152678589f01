create table voyager_records
(
    id             bigint(20) not null auto_increment,
    user_id        bigint(20) not null,
    created_at     datetime     not null,
    current_screen varchar(255),
    voyage_type    varchar(255) not null,
    state          varchar(255) not null,
    primary key (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

create table version_voyager_record_state
(
    id                bigint(20) not null auto_increment,
    voyager_record_id bigint(20) not null,
    version           int(11) not null,
    created_at        datetime     not null,
    deleted           tinyint(1) not null,
    value             varchar(255) not null,
    primary key (id),
    constraint foreign key (voyager_record_id) references voyager_records (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

create table version_voyager_record_current_screen
(
    id                bigint(20) not null auto_increment,
    voyager_record_id bigint(20) not null,
    version           int(11) not null,
    created_at        datetime     not null,
    deleted           tinyint(1) not null,
    value             varchar(255) not null,
    primary key (id),
    constraint foreign key (voyager_record_id) references voyager_records (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

insert into schema_log (schema_version, date)
values (2, now());