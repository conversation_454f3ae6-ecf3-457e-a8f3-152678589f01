CREATE TABLE
queued_vesta_operations_mortgage_application_data_points (
  queued_vesta_operation_id   BIGINT(20)  NOT NULL,
  application_data_point_id  BIGINT(20)  NOT NULL,

  PRIMARY KEY (queued_vesta_operation_id, application_data_point_id),

  CONSTRAINT queued_vesta_operations_mortgage_application_data_points_qvo_id
  FOREIGN KEY (queued_vesta_operation_id) REFERENCES queued_vesta_operations(id),

  CONSTRAINT queued_vesta_operations_mortgage_application_data_points_adp_id
  FOREIGN KEY (application_data_point_id) REFERENCES application_data_points(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE
queued_vesta_operations_vesta_webhooks (
  queued_vesta_operation_id   BIGINT(20)  NOT NULL,
  vesta_webhook_id  BIGINT(20)  NOT NULL,

  PRIMARY KEY (queued_vesta_operation_id, vesta_webhook_id),

  CONSTRAINT queued_vesta_operations_vesta_webhooks_qvo_id
  FOREIGN KEY (queued_vesta_operation_id) REFERENCES queued_vesta_operations(id),

  CONSTRAINT queued_vesta_operations_vesta_webhooks_vw_id
  FOREIGN KEY (vesta_webhook_id) REFERENCES vesta_webhooks(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

INSERT INTO schema_log (schema_version, date) VALUES (24, NOW());