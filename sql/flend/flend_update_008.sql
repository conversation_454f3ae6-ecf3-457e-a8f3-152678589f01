create table application_data_points (
    id               bigint(20) not null auto_increment,
    application_id   bigint(20) not null,
    created_at       datetime not null,
    path             varchar(255) not null,
    value            text null,
    source_type      varchar(255) not null,
    source_ref       varchar(255) not null,
    nullness         varchar(255) not null,
    primary key (id),
    constraint foreign key (application_id) references applications (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

insert into schema_log (schema_version, date)
values (8, now());