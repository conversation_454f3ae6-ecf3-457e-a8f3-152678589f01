package com.wealthfront.data.prod.sqlschema;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.google.common.annotations.VisibleForTesting;
import com.kaching.platform.common.Option;

public class SQLFile implements Comparable<SQLFile> {

  private static final Pattern NUMBERED_UPDATE = Pattern.compile(".*_update_(\\d+)\\.sql");

  private final String filePath;
  private final String fileName;
  private final Option<String> dbName;
  private final boolean isNumberedUpdate;
  private final Option<Integer> updateNumber;
  private static final LocalConnectionManager connectionManager = new LocalConnectionManager();

  public static SQLFile buildSQLFileFromPath(String filePath) {
    String fileName = getFileNameFromPath(filePath);
    Option<String> dbName = getDbNameFromPath(filePath);
    Option<Integer> updateNumber = getUpdateNumberFromFileName(fileName);
    return new SQLFile(filePath, fileName, dbName.getOrNull(), updateNumber.getOrNull());
  }

  @VisibleForTesting
  SQLFile(String filePath, String fileName, String dbName, Integer updateNumber) {
    this.filePath = filePath;
    this.fileName = fileName;
    this.dbName = Option.of(dbName);
    this.updateNumber = Option.of(updateNumber);
    this.isNumberedUpdate = this.updateNumber.isDefined();
  }

  @Override
  public int compareTo(SQLFile other) {
    if (this.isNumberedUpdate && !other.isNumberedUpdate()) {
      return 1;
    } else if (!this.isNumberedUpdate && other.isNumberedUpdate()) {
      return -1;
    } else if (!(this.isNumberedUpdate || other.isNumberedUpdate())) {
      return 0;
    } else {
      return this.getUpdateNumber().getOrElse(0) - other.getUpdateNumber().getOrElse(0);
    }
  }

  public String getFilePath() {
    return filePath;
  }

  public String getFileName() {
    return fileName;
  }

  public Option<String> getDbName() {
    return dbName;
  }

  public boolean isNumberedUpdate() {
    return isNumberedUpdate;
  }

  public Option<Integer> getUpdateNumber() {
    return updateNumber;
  }

  @VisibleForTesting
  static String getFileNameFromPath(String filePath) {
    String[] split = filePath.split("/");
    return split[split.length - 1];
  }

  @VisibleForTesting
  static Option<String> getDbNameFromPath(String filePath) {
    String[] split = filePath.split("/");
    return tryToMatchFolderWithService(split[1]);
  }

  @VisibleForTesting
  static Option<String> tryToMatchFolderWithService(String folderName) {
    Map<String, String> serviceDbMap = connectionManager.getServiceDbNameMap();
    return (serviceDbMap.containsKey(folderName) ? Option.of(serviceDbMap.get(folderName)) : Option.<String>none());
  }

  @VisibleForTesting
  static Option<Integer> getUpdateNumberFromFileName(String fileName) {
    Matcher matcher = NUMBERED_UPDATE.matcher(fileName);
    return (matcher.matches() ? Option.of(Integer.parseInt(matcher.group(1))) : Option.<Integer>none());
  }

  public String readAllSql() throws IOException {
    byte[] bytes = Files.readAllBytes(Paths.get(filePath));
    return new String(bytes, StandardCharsets.UTF_8);
  }

}