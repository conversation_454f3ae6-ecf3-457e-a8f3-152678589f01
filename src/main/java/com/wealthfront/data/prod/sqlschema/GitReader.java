package com.wealthfront.data.prod.sqlschema;

import java.io.File;
import java.io.IOException;
import java.io.StringReader;
import java.util.List;
import java.util.regex.Pattern;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.io.LineReader;

public class GitReader {

  private static final Pattern SQL_FILE = Pattern.compile("^sql/.*\\.sql");
  static final String MODIFIED_FILE_FILTER = "--diff-filter=M";
  static final String ADDED_FILE_FILTER = "--diff-filter=A";

  private final LinuxExecutor executor;

  public GitReader() {
    this.executor = new LinuxExecutor(new File("."));
  }

  public GitReader(LinuxExecutor executor) {
    this.executor = executor;
  }

  public List<String> getModifiedAndAddedSqlFilePaths() {
    List<String> filePaths = Lists.newArrayList();
    filePaths.addAll(getDiffFilesWithFilter(ADDED_FILE_FILTER));
    filePaths.addAll(getDiffFilesWithFilter(MODIFIED_FILE_FILTER));
    return filePaths;
  }

  @VisibleForTesting
  List<String> getDiffFilesWithFilter(String filter) {
    List<String> filePaths = Lists.newArrayList();
    try {
      LineReader reader = new LineReader(new StringReader(executor.exec(
          "git", "diff", getDestinationBranch(), "-M100%", "--name-only", filter)));
      String line;
      while ((line = reader.readLine()) != null) {
        if (SQL_FILE.matcher(line).matches()) {
          filePaths.add(line);
        }
      }
    } catch (IOException e) {
      throw Throwables.propagate(e);
    }
    return filePaths;
  }

  @VisibleForTesting
  String getDestinationBranch() {
    return "origin/master";
  }

}
