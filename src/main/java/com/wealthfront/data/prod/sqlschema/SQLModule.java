package com.wealthfront.data.prod.sqlschema;

import com.google.inject.AbstractModule;
import com.google.inject.Provides;
import com.kaching.platform.guice.ApplicationOptions;
import com.kaching.platform.guice.CommonModule;
import com.kaching.platform.guice.OptionsModule;

public class SQLModule extends AbstractModule {

  @Override
  protected void configure() {
    ApplicationOptions options = new ApplicationOptions();
    install(new OptionsModule(options));
    install(new CommonModule(options));
  }

  @Provides
  LocalConnectionManager providesLocalConnectionManager() {
    return new LocalConnectionManager();
  }

}
