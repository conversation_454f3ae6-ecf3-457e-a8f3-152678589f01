package com.wealthfront.data.prod.sqlschema;

import static com.kaching.platform.common.logging.Log.getLog;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kaching.platform.common.Option;
import com.kaching.platform.common.Strings;
import com.kaching.platform.common.logging.Log;

public class LocalConnectionManager {

  public static final Log log = getLog(LocalConnectionManager.class);

  enum ConnectionInfo {
    FEED("feed", "che16.wlth.fr", "feed"),
    FLEND("flend", "che16.wlth.fr", "flend"),
    FRAND("frand", "che16.wlth.fr", "frand"),
    HISTORICAL("htf", "che16.wlth.fr", "historical"),
    LINK("link", "che16.wlth.fr", "link"),
    LEND("lend", "che16.wlth.fr", "lend"),
    MARKET("qt", "che16.wlth.fr", "market"),
    MONITOR(null, "che16.wlth.fr", "monitor"),
    NLSNY("nlsny", "che16.wlth.fr", "nlsny"),
    REPTEST(null, "che16.wlth.fr", "reptest"),
    SUPERCRUNCHER("sc", "che16.wlth.fr", "supercruncher"),
    USER("um", "che16.wlth.fr", "user"),
    CONTACTS("contacts", "che16.wlth.fr", "contacts"),
    IRA("ira", "che16.wlth.fr", "ira"),
    WORM("worm", "che16.wlth.fr", "worm"),

    ASCENSUS("asc", "che19.wlth.fr", "ascensus"),
    ALE("ale", "che19.wlth.fr", "ale"),
    BANK("bank", "che19.wlth.fr", "bank"),
    BI("bi", "che19.wlth.fr", "bi"),
    BLOTTER("blt", "sv219.wlth.fr", "blotter"),
    CASH("cash", "sv219.wlth.fr", "cash"),
    CITADEL("cdel", "sv219.wlth.fr", "citadel"),
    CLEAR("clear", "sv219.wlth.fr", "clear"),
    FAKE("fake", "che19.wlth.fr", "fake"),
    FAKETWEB("faketweb", "sv219.wlth.fr", "faketweb"),
    FAKEX("fakex", "che19.wlth.fr", "fakex"),
    FBANK("fbank", "che19.wlth.fr", "fbank"),
    FIDELITY("fdy", "che19.wlth.fr", "fidelity"),
    FOPS("fops", "che19.wlth.fr", "fops"),
    GTS("gts", "sv219.wlth.fr", "gts"),
    IB("ib", "che19.wlth.fr", "ib"),
    ICG("icg", "che19.wlth.fr", "icg"),
    INTELICLEAR("icgtest", "che19.wlth.fr", "inteliclear"),
    KCG("kcg", "sv219.wlth.fr", "knight"),
    ORDER_ROUTER("or", "sv219.wlth.fr", "order_router"),
    PENSON("pen", "che19.wlth.fr", "penson"),
    SUNGARD(null, "che19.wlth.fr", "sungard"),
    TRACE("trace", "sv219.wlth.fr", "trace"),

    BACKUPS(null, "che20.wlth.fr", "backups"),
    DEPLOYMENT("dm", "che20.wlth.fr", "deployment"),
    DOPE("dope", "che20.wlth.fr", "dope"),
    DWI("dwi", "che20.wlth.fr", "dwi"),
    ESP("esp", "che20.wlth.fr", "esp"),
    IRIS("iris", null, "iris"),
    NOP("nop", null, "nop"),
    SAND("sand", null, "sand"),
    MASTER("im", "che20.wlth.fr", "master"),
    MARKETO("ms", "che20.wlth.fr", "marketo"),
    MMR("mmr", "che20.wlth.fr", "mmr"),
    REPORT("report", "che20.wlth.fr", "report"),
    TAOS("taos", "che20.wlth.fr", "taos"),
    TAX("tax", "che20.wlth.fr", "tax"),
    TWEB("tweb", "sv219.wlth.fr", "tweb"),
    ETL("etl", "che20.wlth.fr", "etl"),
    RISK("risk", "sv221.wlth.fr", "risk"),
    IKQ("ikq", "sv221.wlth.fr", "ikq"),
    SRS("srs", "che20.wlth.fr", "srs"),
    PERCONA("percona", "che20.wlth.fr", "percona");

    private final String service;
    private final String host;
    private final String db;

    ConnectionInfo(String service, String host, String db) {
      this.service = service;
      this.host = host;
      this.db = db;
    }

    public String getUrl() {
      return Strings.format("jdbc:mysql://%s/%s", host, db);
    }

    public String getDb() {
      return db;
    }

    public Option<String> getService() {
      return Option.<String>of(service);
    }

  }

  private static final String MYSQL_PREFIX = "jdbc:mysql://%s:%s/%s";
  private static final String MYSQL_PORT = Option.of(System.getProperty("mysql.port")).getOrElse("3306");
  private static final String MYSQL_HOST = Option.of(System.getProperty("mysql.host")).getOrElse("localhost");
  private static final String LOCALHOST_URL = Strings.format(MYSQL_PREFIX, MYSQL_HOST, MYSQL_PORT);
  private static final String LOCALHOST_USER = "root";
  private static final String LOCALHOST_PASS = "root";
  private static final String TEST_USER = "test_user";
  private static final String TEST_PASS = "test";

  public Connection getLocalConnection() throws SQLException {
    return getTargetLocalConnection("");
  }

  public Connection getTargetLocalConnection(String dbName) throws SQLException {
    Properties connProperties = new Properties();
    connProperties.put("user", LOCALHOST_USER);
    connProperties.put("password", LOCALHOST_PASS);
    String targetDbUrl = Strings.format(LOCALHOST_URL, dbName);
    Connection connection = DriverManager.getConnection(targetDbUrl, connProperties);
    log.info("Established connection to %s", targetDbUrl);
    return connection;
  }

  public Connection getTestConnection() throws SQLException {
    return getTargetTestConnection("");
  }

  public Connection getTargetTestConnection(String dbName) throws SQLException {
    Properties connProperties = new Properties();
    connProperties.put("user", TEST_USER);
    connProperties.put("password", TEST_PASS);
    String targetDbUrl = Strings.format(LOCALHOST_URL, dbName);
    Connection connection = DriverManager.getConnection(targetDbUrl, connProperties);
    log.info("Established connection to %s", targetDbUrl);
    return connection;
  }

  public List<String> getDbNameList() {
    List<String> dbNameList = Lists.newArrayList();
    for (LocalConnectionManager.ConnectionInfo info : LocalConnectionManager.ConnectionInfo.values()) {
      dbNameList.add(info.getDb());
    }
    return dbNameList;
  }

  public Map<String, String> getServiceDbNameMap() {
    Map<String, String> serviceDbMap = Maps.newHashMap();
    for (LocalConnectionManager.ConnectionInfo info : LocalConnectionManager.ConnectionInfo.values()) {
      for (String service : info.getService()) {
        serviceDbMap.put(service, info.getDb());
      }
    }
    return serviceDbMap;
  }

}
