package com.wealthfront.data.prod.sqlschema;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;

import com.amazonaws.util.IOUtils;
import com.google.common.base.Joiner;
import com.kaching.platform.common.logging.Log;

public class LinuxExecutor {

  private static final Log log = Log.getLog(LinuxExecutor.class);
  private final File workingDirectory;

  public LinuxExecutor() {
    this.workingDirectory = new File(".");
  }

  public LinuxExecutor(File workingDirectory) {
    this.workingDirectory = workingDirectory;
  }

  public String exec(String... commands) throws IOException {
    return read(open(commands));
  }

  public InputStream open(String... commands) throws IOException {
    return start(commands).getInputStream();
  }

  public Process start(String... commands) throws IOException {
    log.info(Joiner.on(" ").join(commands));
    return new ProcessBuilder(commands).directory(workingDirectory).start();
  }

  public String read(InputStream input) throws IOException {
    return IOUtils.toString(input);
  }

  public String whoami() {
    return System.getProperty("user.name").toLowerCase();
  }

}
