package com.wealthfront.data.prod.sqlschema;

import static org.junit.Assert.fail;

import java.util.List;

import org.junit.Test;

import com.google.common.collect.ImmutableSet;

public class MirroredSchemasTestTest {

  @Test
  public void testAllNewSql_notMirrored() throws Exception {
    MirroredSchemasTest mirroredSchemasTest = new MirroredSchemasTest() {
      @Override
      List<SQLFile> getSqlFiles() {
        return List.of(
            new SQLFile(null, null, "fbank", 13) {
              @Override
              public String readAllSql() {
                return "";
              }
            },
            new SQLFile(null, null, "bi", 26) {
              @Override
              public String readAllSql() {
                return "skip mirrored schemas test";
              }
            }
        );
      }
    };
    try {
      mirroredSchemasTest.testAllNewSql();
      fail("should throw AssertionError");
    } catch (AssertionError e) {
      if (!e.getMessage().contains("changes to bank and fbank must be mirrored. Found changes for [fbank]")) {
        throw e;
      }
    }
  }

  @Test
  public void testAllNewSql_skipSyncTest() throws Exception {
    MirroredSchemasTest mirroredSchemasTest = new MirroredSchemasTest() {
      @Override
      List<SQLFile> getSqlFiles() {
        return List.of(
            new SQLFile(null, null, "fbank", 13) {
              @Override
              public String readAllSql() {
                return "skip mirrored schemas test";
              }
            },
            new SQLFile(null, null, "bi", 26) {
              @Override
              public String readAllSql() {
                return "";
              }
            }
        );
      }
    };

    mirroredSchemasTest.testAllNewSql();
  }

  @Test
  public void testAllNewSql_notNumberedUpdate() throws Exception {
    MirroredSchemasTest mirroredSchemasTest = new MirroredSchemasTest() {
      @Override
      List<SQLFile> getSqlFiles() {
        return List.of(
            new SQLFile(null, null, "fbank", null) {
              @Override
              public String readAllSql() {
                return "DML";
              }
            },
            new SQLFile(null, null, "bi", null) {
              @Override
              public String readAllSql() {
                return "";
              }
            }
        );
      }
    };

    mirroredSchemasTest.testAllNewSql();
  }

  @Test
  public void assertSqlAppliedToBothOrNeither() {
    MirroredSchemasTest.assertSqlAppliedToBothOrNeither("bank", "fbank", ImmutableSet.of("bi"));
    MirroredSchemasTest.assertSqlAppliedToBothOrNeither("bank", "fbank", ImmutableSet.of("bi, icg, bank, fbank"));

    try {
      MirroredSchemasTest.assertSqlAppliedToBothOrNeither("bank", "fbank", ImmutableSet.of("bi", "bank", "icg"));
      fail("should throw an assertion error");
    } catch (AssertionError e) {
      if (!e.getMessage().contains("changes to bank and fbank must be mirrored. Found changes for [bi, bank, icg]")) {
        throw e;
      }
    }
    try {
      MirroredSchemasTest.assertSqlAppliedToBothOrNeither("bank", "fbank", ImmutableSet.of("bi", "fbank", "icg"));
      fail("should throw an assertion error");
    } catch (AssertionError e) {
      if (!e.getMessage().contains("changes to bank and fbank must be mirrored. Found changes for [bi, fbank, icg]")) {
        throw e;
      }
    }
  }

}
