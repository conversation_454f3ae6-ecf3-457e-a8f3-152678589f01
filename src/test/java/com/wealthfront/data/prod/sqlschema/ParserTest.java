package com.wealthfront.data.prod.sqlschema;

import static com.wealthfront.test.Assert.assertEmpty;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.containsInAnyOrder;
import static org.hamcrest.Matchers.empty;
import static org.junit.Assert.assertEquals;

import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.antlr.v4.runtime.CharStreams;
import org.junit.Test;

import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Multimap;
import com.kaching.platform.common.Strings;
import com.kaching.platform.functional.Unchecked;
import com.kaching.platform.functional.Unit;
import com.kaching.util.functional.Result;
import com.wealthfront.sqlparser.ChangesetBaseParser;
import com.wealthfront.sqlparser.ChangesetParser;
import com.wealthfront.sqlparser.SqlParser;
import com.wealthfront.sqlparser.SyntaxError;
import com.wealthfront.sqlparser.SyntaxErrorListener;
import com.wealthfront.sqlparser.repo.ChangesetRepoSqlFile;
import com.wealthfront.sqlparser.repo.RepoSqlFile;
import com.wealthfront.sqlparser.repo.RepoSqlFileLoader;
import com.wealthfront.sqlparser.repo.RepoSqlFileLoaderImpl;
import com.wealthfront.sqlparser.repo.RepoSqlFileUtil;
import com.wealthfront.sqlparser.repo.RepoSqlFileUtilImpl;
import com.wealthfront.sqlparser.repo.SqlHasher;
import com.wealthfront.sqlparser.repo.SqlHasherImpl;

public class ParserTest {

  private final SqlHasher hasher = new SqlHasherImpl();
  private final RepoSqlFileLoader loader = new RepoSqlFileLoaderImpl(
      Paths.get("sql"), hasher);
  private final RepoSqlFileUtil util = new RepoSqlFileUtilImpl();

  private static final List<String> BAD_SQL_EXCLUSIONS = List.of(
      // These files contain illegal comments. Most often, '--' is not followed by whitespace
      "bi/maintenance/20110107_private_site_customer_migration.sql",
      "bi/maintenance/20110111_private_site_customer_migration.sql",
      "bi/maintenance/20110113_private_site_customer_migration.sql",
      "bi/maintenance/20110114_private_site_customer_migration.sql",
      "bi/maintenance/20110118_private_site_customer_migration.sql",
      "esp/esp_update_050.sql",
      "esp/esp_update_053.sql",
      "esp/esp_update_063.sql",
      "pm/maintenance/20090626_cleaning_bad_stocks.sql",
      "pm/portfolio_manager_update_054.sql",
      "pm/portfolio_manager_update_061.sql",
      "um/maintenance/20110107_private_site_customer_migration.sql",
      "um/maintenance/20110111_private_site_customer_migration.sql",
      "um/maintenance/20110113_private_site_customer_migration.sql",
      "um/maintenance/20110114_private_site_customer_migration.sql",
      "um/maintenance/20110118_private_site_customer_migration.sql",
      "um/maintenance/20110125_private_site_customer_migration.sql",
      "um/maintenance/20140917_clean_risk_questionnaire_values.sql",
      "um/maintenance/user_manager_484.sql",
      "um/user_manager_update_023.sql",
      "um/user_manager_update_250.sql",
      "um/user_manager_update_255.sql",
      "um/user_manager_update_461.sql",
      "um/user_manager_update_467.sql",
      "bank/bank_update_515.sql",
      "bank/bank_update_524.sql",
      "bank/bank_update_536.sql",
      "bank/bank_update_547.sql",
      "bi/broker_integration_update_597.sql",
      "bi/broker_integration_update_599.sql",
      "bi/broker_integration_update_603.sql",
      "bi/broker_integration_update_615.sql",
      "bi/broker_integration_update_616.sql",
      "link/link_update_283.sql",
      "link/link_update_293.sql",
      "link/link_update_311.sql",
      "um/user_manager_update_1002.sql",
      "um/user_manager_update_1004.sql",
      "um/user_manager_update_946.sql",
      "um/user_manager_update_959.sql",
      "link_aurora/link_aurora_update_017.sql",
      "im/master_update_035.sql",
      "bi/maintenance/20110926_populate_ofa_investment_start_time.sql",
      "htf/historical_tickerfeed_schema.sql",
      "schema-htf/update-00001.sql",

      // These files contain non-SQL content, including bash prompts, line ending escapes, etc
      "um/maintenance/20120928_rename_customer_account_subtype_na.sql",
      "kfe/calculate_ratings.sql",
      "sc/maintenance/20090426_fake_portfolio_snapshot_for_finnovate.sql",
      "pm/maintenance/20090424_uncle_sam.sql",
      "pm/maintenance/20100615_expire_all_useless_portfolios.sql",
      "link/maintenance/20150515_site-180_add_missing_links.sql",

      // These files contain reserved words which are not quoted
      "um/maintenance/20090210_top_groups.sql",
      "um/user_manager_update_033.sql",
      "um/user_manager_update_034.sql",
      "um/user_manager_update_048.sql",
      "um/user_manager_update_226.sql",
      "pm/portfolio_manager_update_019.sql",
      "insider/insider_schema.sql",
      "esp/esp_update_075.sql",

      // Missing semicolons. We also catch missing terminal semicolons, but may want to relax this?
      "um/maintenance/20150413_back_populate_statement_state.sql",
      "dwh/dwh_update_028.sql",
      "sc/maintenance/usefull_counts.sql",
      "bi/maintenance/20110622_delete_fake_accounts.sql",
      "bi/maintenance/20150114_copy_investment_start_time.sql",
      "bi/maintenance/20110622_delete_fake_accounts_actual_positions.sql",
      "bi/broker_integration_update_045.sql",
      "bi/maintenance/20150224_recalculate_balances_day_gain_net.sql",
      "bank/maintenance/20161014_set_ach_deposit_refunded.sql",
      "sc/maintenance/completion_counts.sql",

      // An empty statement. Not technically invalid, but caught by the grammar. Maybe relax this?
      "um/user_manager_update_263.sql",

      // These use PERSISTENT rather than STORED. We may wish to support both.
      "icgtest/icgtest_update_283.sql",
      "icg/icg_update_263.sql",

      // These use DELIMITER which is not supported by this grammar
      "im/maintenance/20110104_import_cusips.sql",
      "im/maintenance/20110222_delete_old_version_fundamental.sql",

      // Other syntax problems and invalid statements in general
      "link/link_update_044.sql",
      "kfe/maintenance/20091028_mark_lost_articles_as_lost.sql",
      "bi/maintenance/20110621_populate_sub_accounts_trading_state_version.sql",
      "um/user_manager_update_246.sql",
      "um/user_manager_update_544.sql",
      "um/user_manager_update_417.sql",
      "um/maintenance/20100913_fixup_records_pointing_to_nonexistent_portfolio_43952.sql",
      "pm/portfolio_manager_update_032.sql",
      "bi/broker_integration_update_159.sql",
      "blt/maintenance/20221129_populate_cat_firm_designated_id_corrections_code.sql"
  );

  @Test
  public void parseSql() throws IOException {
    var files = loader
        .streamRelativePaths()
        .filter(path -> !path.startsWith("icdb"))
        .map(path -> Unchecked.get(() -> loader.byRelativePath(path)));
    Multimap<String, SyntaxError> syntaxErrorsByFilePath = ArrayListMultimap.create();

    files.forEach(file -> {
      var listener = new SyntaxErrorListener();
      SqlParser parser = Unchecked.get(() ->
          new SqlParser.Builder()
              .withCharStream(CharStreams.fromString(file.getContent()))
              .withErrorListeners(List.of(listener))
              .build());
      parser.root();
      List<SyntaxError> syntaxErrors = listener.getSyntaxErrors();
      if (!syntaxErrors.isEmpty()) {
        String filePath = file.getRelativePath().toString();
        syntaxErrorsByFilePath.putAll(filePath, syntaxErrors);
      }
    });
    BAD_SQL_EXCLUSIONS.forEach(syntaxErrorsByFilePath::removeAll);

    StringBuilder errorMessage = new StringBuilder("Syntax errors: \n");
    syntaxErrorsByFilePath.forEach((filePath, syntaxError) -> {
      String message = Strings.format("file = %s, line = %s, message = %s%s", filePath, syntaxError.getLine(), syntaxError.getMsg());
      errorMessage.append(message);
    });
    assertEquals(errorMessage.toString(), 0, syntaxErrorsByFilePath.size());
  }

  private static final List<String> NONCONFORMING_SQL_EXCLUSIONS = List.of(
      "pm/portfolio_manager_update_019.sql",
      "pm/portfolio_manager_update_033.sql",
      "pm/portfolio_manager_update_032.sql",
      "pm/portfolio_manager_update_036.sql",
      "pm/portfolio_manager_update_009.sql",
      "pm/portfolio_manager_update_154.sql",
      "pm/portfolio_manager_update_034.sql",
      "pm/portfolio_manager_update_020.sql",
      "pm/portfolio_manager_update_008.sql",
      "pm/portfolio_manager_update_090.sql",
      "pm/portfolio_manager_update_125.sql",
      "pm/portfolio_manager_update_086.sql",
      "pm/portfolio_manager_update_069.sql",
      "pm/portfolio_manager_update_097.sql",
      "pm/portfolio_manager_update_054.sql",
      "pm/portfolio_manager_update_043.sql",
      "pm/portfolio_manager_update_066.sql",
      "pm/portfolio_manager_update_067.sql",
      "pm/portfolio_manager_update_065.sql",
      "pm/portfolio_manager_update_110.sql",
      "pm/portfolio_manager_update_064.sql",
      "pm/portfolio_manager_update_074.sql",
      "pm/portfolio_manager_update_061.sql",
      "pm/portfolio_manager_update_049.sql",
      "pm/portfolio_manager_update_062.sql",
      "pm/portfolio_manager_update_005.sql",
      "pm/portfolio_manager_update_011.sql",
      "pm/portfolio_manager_update_039.sql",
      "pm/portfolio_manager_update_158.sql",
      "pm/portfolio_manager_update_010.sql",
      "pm/portfolio_manager_update_004.sql",
      "pm/portfolio_manager_update_012.sql",
      "pm/portfolio_manager_update_006.sql",
      "pm/portfolio_manager_update_007.sql",
      "pm/portfolio_manager_update_013.sql",
      "pm/portfolio_manager_update_017.sql",
      "pm/portfolio_manager_update_003.sql",
      "pm/portfolio_manager_update_002.sql",
      "pm/portfolio_manager_update_014.sql",
      "pm/portfolio_manager_update_015.sql",
      "pm/portfolio_manager_update_001.sql",
      "dwi/dwi_update_001.sql",
      "dwh/dwh_update_028.sql",
      "sc/supercruncher_update_015.sql",
      "sc/supercruncher_update_012.sql",
      "sc/supercruncher_update_006.sql",
      "sc/supercruncher_update_009.sql",
      "sc/supercruncher_update_022.sql",
      "sc/supercruncher_update_032.sql",
      "esp/esp_update_001.sql",
      "esp/esp_update_063.sql",
      "esp/esp_update_075.sql",
      "esp/esp_update_073.sql",
      "esp/esp_update_050.sql",
      "esp/esp_update_053.sql",
      "cvx/convergex_update_015.sql",
      "cvx/convergex_update_022.sql",
      "asc/asc_update_015.sql",
      "asc/asc_update_001.sql",
      "asc/asc_update_010.sql",
      "test/test_update_01.sql",
      "dope/dope_update_001.sql",
      "bank/bank_update_01.sql",
      "bank/bank_update_515.sql",
      "bank/bank_update_54.sql",
      "bank/bank_update_345.sql",
      "bank/bank_update_547.sql",
      "bank/bank_update_52.sql",
      "bank/bank_update_53.sql",
      "bank/bank_update_536.sql",
      "bank/bank_update_524.sql",
      "bank/bank_update_135.sql",
      "etl/etl_update_01.sql",
      "cash/cash_update_002.sql",
      "cash/cash_update_001.sql",
      "cdel/citadel_update_010.sql",
      "cdel/citadel_update_004.sql",
      "cdel/citadel_update_005.sql",
      "cdel/citadel_update_007.sql",
      "cdel/citadel_update_006.sql",
      "cdel/citadel_update_002.sql",
      "cdel/citadel_update_003.sql",
      "cdel/citadel_update_001.sql",
      "cdel/citadel_update_008.sql",
      "cdel/citadel_update_009.sql",
      "contacts/contacts_update_001.sql",
      "contacts/contacts_update_015.sql",
      "contacts/contacts_update_014.sql",
      "contacts/contacts_update_016.sql",
      "contacts/contacts_update_003.sql",
      "contacts/contacts_update_017.sql",
      "contacts/contacts_update_013.sql",
      "contacts/contacts_update_012.sql",
      "contacts/contacts_update_010.sql",
      "contacts/contacts_update_011.sql",
      "percona/percona_update_001.sql",
      "ale/ale_update_001.sql",
      "kfe/kfe_update_026.sql",
      "kfe/kfe_update_024.sql",
      "kfe/kfe_update_020.sql",
      "kfe/kfe_update_021.sql",
      "kfe/kfe_update_022.sql",
      "kfe/kfe_update_013.sql",
      "kfe/kfe_update_014.sql",
      "kfe/kfe_update_016.sql",
      "kfe/kfe_update_049.sql",
      "tax/tax_update_001.sql",
      "srs/srs_update_01.sql",
      "fops/fops_update_001.sql",
      "taos/taos_update_001.sql",
      "taos/taos_update_003.sql",
      "taos/taos_update_002.sql",
      "kcg/knight_update_001.sql",
      "kcg/knight_update_003.sql",
      "kcg/knight_update_002.sql",
      "um/user_manager_update_671.sql",
      "um/user_manager_update_467.sql",
      "um/user_manager_update_705.sql",
      "um/user_manager_update_089.sql",
      "um/user_manager_update_316.sql",
      "um/user_manager_update_048.sql",
      "um/user_manager_update_706.sql",
      "um/user_manager_update_263.sql",
      "um/user_manager_update_461.sql",
      "um/user_manager_update_298.sql",
      "um/user_manager_update_058.sql",
      "um/user_manager_update_064.sql",
      "um/user_manager_update_113.sql",
      "um/user_manager_update_438.sql",
      "um/user_manager_update_1004.sql",
      "um/user_manager_update_439.sql",
      "um/user_manager_update_200.sql",
      "um/user_manager_update_228.sql",
      "um/user_manager_update_412.sql",
      "um/user_manager_update_946.sql",
      "um/user_manager_update_1002.sql",
      "um/user_manager_update_417.sql",
      "um/user_manager_update_011.sql",
      "um/user_manager_update_010.sql",
      "um/user_manager_update_627.sql",
      "um/user_manager_update_237.sql",
      "um/user_manager_update_009.sql",
      "um/user_manager_update_021.sql",
      "um/user_manager_update_034.sql",
      "um/user_manager_update_008.sql",
      "um/user_manager_update_236.sql",
      "um/user_manager_update_222.sql",
      "um/user_manager_update_544.sql",
      "um/user_manager_update_154.sql",
      "um/user_manager_update_220.sql",
      "um/user_manager_update_023.sql",
      "um/user_manager_update_812.sql",
      "um/user_manager_update_033.sql",
      "um/user_manager_update_226.sql",
      "um/user_manager_update_959.sql",
      "um/user_manager_update_018.sql",
      "um/user_manager_update_255.sql",
      "um/user_manager_update_246.sql",
      "um/user_manager_update_250.sql",
      "pen/penson_update_018.sql",
      "htf/htf_update_010.sql",
      "htf/htf_update_008.sql",
      "ikq/ikq_update_001.sql",
      "link/link_update_001.sql",
      "link/link_update_010.sql",
      "link/link_update_006.sql",
      "link/link_update_311.sql",
      "link/link_update_283.sql",
      "link/link_update_293.sql",
      "link/link_update_044.sql",
      "lend/lend_update_001.sql",
      "flend/flend_update_001.sql",
      "nop/nop_update_001.sql",
      "feed/feed_update_001.sql",
      "link_aurora/link_aurora_update_017.sql",
      "link_aurora/link_aurora_update_001.sql",
      "dm/deployment_manager_update_001.sql",
      "dm/deployment_manager_update_003.sql",
      "blt/blotter_update_001.sql",
      "blt/blotter_update_002.sql",
      "blt/blotter_update_003.sql",
      "nl/notifications_listeners_update_013.sql",
      "worm/worm_update_001.sql",
      "sge/sungard_update_010.sql",
      "bi/broker_integration_update_048.sql",
      "bi/broker_integration_update_262.sql",
      "bi/broker_integration_update_049.sql",
      "bi/broker_integration_update_416.sql",
      "bi/broker_integration_update_615.sql",
      "bi/broker_integration_update_603.sql",
      "bi/broker_integration_update_159.sql",
      "bi/broker_integration_update_038.sql",
      "bi/broker_integration_update_616.sql",
      "bi/broker_integration_update_216.sql",
      "bi/broker_integration_update_175.sql",
      "bi/broker_integration_update_599.sql",
      "bi/broker_integration_update_033.sql",
      "bi/broker_integration_update_597.sql",
      "bi/broker_integration_update_169.sql",
      "bi/broker_integration_update_627.sql",
      "bi/broker_integration_update_223.sql",
      "bi/broker_integration_update_044.sql",
      "bi/broker_integration_update_291.sql",
      "bi/broker_integration_update_534.sql",
      "bi/broker_integration_update_045.sql",
      "bi/broker_integration_update_278.sql",
      "bi/broker_integration_update_456.sql",
      "bi/broker_integration_update_122.sql",
      "bi/broker_integration_update_123.sql",
      "bi/broker_integration_update_492.sql",
      "bi/broker_integration_update_121.sql",
      "report/report_service_update_015.sql",
      "report/report_service_update_016.sql",
      "report/report_service_update_008.sql",
      "ira/ira_update_01.sql",
      "im/master_update_017.sql",
      "im/master_update_007.sql",
      "im/master_update_013.sql",
      "im/master_update_021.sql",
      "im/master_update_035.sql",
      "im/master_update_034.sql",
      "im/master_update_022.sql",
      "im/master_update_033.sql",
      "im/master_update_018.sql",
      "im/master_update_025.sql",
      "im/master_update_019.sql",
      "icgtest/icgtest_update_001.sql",
      "icgtest/icgtest_update_283.sql",
      "or/order_router_update_046.sql",
      "or/order_router_update_033.sql",
      "or/order_router_update_027.sql",
      "or/order_router_update_026.sql",
      "or/order_router_update_032.sql",
      "or/order_router_update_018.sql",
      "or/order_router_update_024.sql",
      "or/order_router_update_031.sql",
      "or/order_router_update_019.sql",
      "or/order_router_update_021.sql",
      "or/order_router_update_009.sql",
      "or/order_router_update_008.sql",
      "or/order_router_update_034.sql",
      "or/order_router_update_020.sql",
      "or/order_router_update_036.sql",
      "or/order_router_update_022.sql",
      "or/order_router_update_023.sql",
      "or/order_router_update_037.sql",
      "or/order_router_update_012.sql",
      "or/order_router_update_006.sql",
      "or/order_router_update_007.sql",
      "or/order_router_update_013.sql",
      "or/order_router_update_005.sql",
      "or/order_router_update_011.sql",
      "or/order_router_update_010.sql",
      "or/order_router_update_004.sql",
      "or/order_router_update_038.sql",
      "or/order_router_update_014.sql",
      "or/order_router_update_015.sql",
      "or/order_router_update_001.sql",
      "or/order_router_update_017.sql",
      "or/order_router_update_003.sql",
      "or/order_router_update_002.sql",
      "or/order_router_update_016.sql",
      "fbank/fbank_update_001.sql",
      "fbank/fbank_update_002.sql",
      "fbank/fbank_update_003.sql",
      "fakex/fakex_update_018.sql",
      "fakex/fakex_update_016.sql",
      "fakex/fakex_update_017.sql",
      "fakex/fakex_update_015.sql",
      "fakex/fakex_update_001.sql",
      "fakex/fakex_update_014.sql",
      "fakex/fakex_update_010.sql",
      "fakex/fakex_update_011.sql",
      "fakex/fakex_update_013.sql",
      "fakex/fakex_update_012.sql",
      "risk/risk_update_001.sql",
      "icg/icg_update_001.sql",
      "icg/icg_update_263.sql",
      "iris/iris_update_001.sql",
      "mmr/mmr_update_001.sql"
  );

  @Test
  public void parseChangesets() throws IOException {
    var updateRegex = Pattern.compile("update_\\d+.sql");
    var files = loader.streamRelativePaths()
        .filter(path -> updateRegex.matcher(path.getFileName().toString()).find())
        .map(path -> Unchecked.get(() -> loader.byRelativePath(path)));
    Map<String, String> exceptionsByFilePath = new HashMap<>();

    files.forEach(file -> {
      ChangesetParser.parse(ChangesetParser::root, CharStreams.fromString(file.getContent())).visit(
          new Result.ResultVisitor<ChangesetBaseParser.RootContext, Unit>() {
            @Override
            public Unit caseSuccess(ChangesetBaseParser.RootContext result) {
              return Unit.unit;
            }

            @Override
            public Unit caseFailure(Exception error) {
              String filePath = file.getRelativePath().toString();
              exceptionsByFilePath.put(filePath, error.getMessage());
              return Unit.unit;
            }
          });
    });
    NONCONFORMING_SQL_EXCLUSIONS.forEach(exceptionsByFilePath::remove);
    assertEquals(exceptionsByFilePath.toString(), 0, exceptionsByFilePath.size());
  }

  @Test
  public void changelogFromRelativePath_parsesAllChangesets() throws IOException {
    assertEmpty(loader.streamRelativePaths().filter(path -> util.changelogFromRelativePath(path).isEmpty())
        .filter(path -> path.toString().contains("_update_")).map(Object::toString).collect(
            Collectors.toList()));
  }

  @Test
  public void getUpdateNumberFromFileName_parsesAllChangesets() throws IOException {
    assertEmpty(loader.streamRelativePaths().filter(path -> util.updateNumberFromFilename(path).isEmpty())
        .filter(path -> path.toString().contains("_update_")).map(Object::toString).collect(
            Collectors.toList()));
  }

  @Test
  public void syntacticallyInvalidFiles_MatchKnownSyntaxErrors() throws IOException {
    var relativePaths = loader.streamRelativePaths()
        .filter(util::hasPathMatchingUpdatePattern)
        .map(path -> Unchecked.get(() -> loader.byRelativePath(path)))
        .filter(file -> !util.isSyntacticallyValidChangeset(file))
        .map(RepoSqlFile::getRelativePath)
        .map(Path::toString)
        .collect(Collectors.toList());
    assertThat(relativePaths, containsInAnyOrder(RELATIVE_PATHS_WITH_SYNTAX_ERRORS.toArray()));
  }

  @Test
  public void syntacticallyValidFiles_HaveChangelogAndSequenceNumber() throws IOException {
    var relativePaths = loader.streamRelativePaths()
        .filter(util::hasPathMatchingUpdatePattern)
        .map(path -> Unchecked.get(() -> loader.byRelativePath(path)))
        .filter(util::isSyntacticallyValidChangeset)
        .filter(file -> !util.changesetFromRepoSqlFile(file).isSuccess())
        .map(RepoSqlFile::getRelativePath)
        .collect(Collectors.toList());
    assertThat(relativePaths, empty());
  }

  @Test
  public void changesetFiles_PassValidation() throws IOException {
    var changesets = loader.streamRelativePaths()
        .filter(util::hasPathMatchingUpdatePattern)
        .map(path -> Unchecked.get(() -> loader.byRelativePath(path)))
        .filter(util::isSyntacticallyValidChangeset)
        .map(util::changesetFromRepoSqlFile)
        .filter(Result::isSuccess)
        .map(Result::getOrThrow)
        .filter(c -> !util.changesetContentIsValid(c))
        .map(ChangesetRepoSqlFile::getRelativePath)
        .map(Path::toString)
        .collect(Collectors.toList());
    assertThat(changesets, containsInAnyOrder(RELATIVE_PATHS_WITH_NONCONFORMING_INSERT.toArray()));
  }

  private static final List<String> RELATIVE_PATHS_WITH_SYNTAX_ERRORS = List.of(
      "iris/iris_update_001.sql",
      "pm/portfolio_manager_update_019.sql",
      "pm/portfolio_manager_update_033.sql",
      "pm/portfolio_manager_update_032.sql",
      "pm/portfolio_manager_update_036.sql",
      "pm/portfolio_manager_update_009.sql",
      "pm/portfolio_manager_update_154.sql",
      "pm/portfolio_manager_update_034.sql",
      "pm/portfolio_manager_update_020.sql",
      "pm/portfolio_manager_update_008.sql",
      "pm/portfolio_manager_update_090.sql",
      "pm/portfolio_manager_update_125.sql",
      "pm/portfolio_manager_update_086.sql",
      "pm/portfolio_manager_update_069.sql",
      "pm/portfolio_manager_update_097.sql",
      "pm/portfolio_manager_update_054.sql",
      "pm/portfolio_manager_update_043.sql",
      "pm/portfolio_manager_update_066.sql",
      "pm/portfolio_manager_update_067.sql",
      "pm/portfolio_manager_update_065.sql",
      "pm/portfolio_manager_update_110.sql",
      "pm/portfolio_manager_update_064.sql",
      "pm/portfolio_manager_update_074.sql",
      "pm/portfolio_manager_update_061.sql",
      "pm/portfolio_manager_update_049.sql",
      "pm/portfolio_manager_update_062.sql",
      "pm/portfolio_manager_update_005.sql",
      "pm/portfolio_manager_update_011.sql",
      "pm/portfolio_manager_update_039.sql",
      "pm/portfolio_manager_update_158.sql",
      "pm/portfolio_manager_update_010.sql",
      "pm/portfolio_manager_update_004.sql",
      "pm/portfolio_manager_update_012.sql",
      "pm/portfolio_manager_update_006.sql",
      "pm/portfolio_manager_update_007.sql",
      "pm/portfolio_manager_update_013.sql",
      "pm/portfolio_manager_update_017.sql",
      "pm/portfolio_manager_update_003.sql",
      "pm/portfolio_manager_update_002.sql",
      "pm/portfolio_manager_update_014.sql",
      "pm/portfolio_manager_update_015.sql",
      "pm/portfolio_manager_update_001.sql",
      "dwi/dwi_update_001.sql",
      "dwh/dwh_update_028.sql",
      "sc/supercruncher_update_015.sql",
      "sc/supercruncher_update_012.sql",
      "sc/supercruncher_update_006.sql",
      "sc/supercruncher_update_009.sql",
      "sc/supercruncher_update_022.sql",
      "sc/supercruncher_update_032.sql",
      "esp/esp_update_001.sql",
      "esp/esp_update_063.sql",
      "esp/esp_update_075.sql",
      "esp/esp_update_073.sql",
      "esp/esp_update_050.sql",
      "esp/esp_update_053.sql",
      "cvx/convergex_update_015.sql",
      "cvx/convergex_update_022.sql",
      "asc/asc_update_015.sql",
      "asc/asc_update_001.sql",
      "asc/asc_update_010.sql",
      "test/test_update_01.sql",
      "dope/dope_update_001.sql",
      "bank/bank_update_01.sql",
      "bank/bank_update_515.sql",
      "bank/bank_update_54.sql",
      "bank/bank_update_345.sql",
      "bank/bank_update_547.sql",
      "bank/bank_update_52.sql",
      "bank/bank_update_53.sql",
      "bank/bank_update_536.sql",
      "bank/bank_update_524.sql",
      "bank/bank_update_135.sql",
      "etl/etl_update_01.sql",
      "cash/cash_update_002.sql",
      "cash/cash_update_001.sql",
      "cdel/citadel_update_010.sql",
      "cdel/citadel_update_004.sql",
      "cdel/citadel_update_005.sql",
      "cdel/citadel_update_007.sql",
      "cdel/citadel_update_006.sql",
      "cdel/citadel_update_002.sql",
      "cdel/citadel_update_003.sql",
      "cdel/citadel_update_001.sql",
      "cdel/citadel_update_008.sql",
      "cdel/citadel_update_009.sql",
      "contacts/contacts_update_001.sql",
      "contacts/contacts_update_015.sql",
      "contacts/contacts_update_014.sql",
      "contacts/contacts_update_016.sql",
      "contacts/contacts_update_003.sql",
      "contacts/contacts_update_017.sql",
      "contacts/contacts_update_013.sql",
      "contacts/contacts_update_012.sql",
      "contacts/contacts_update_010.sql",
      "contacts/contacts_update_011.sql",
      "percona/percona_update_001.sql",
      "ale/ale_update_001.sql",
      "kfe/kfe_update_026.sql",
      "kfe/kfe_update_024.sql",
      "kfe/kfe_update_020.sql",
      "kfe/kfe_update_021.sql",
      "kfe/kfe_update_022.sql",
      "kfe/kfe_update_013.sql",
      "kfe/kfe_update_014.sql",
      "kfe/kfe_update_016.sql",
      "kfe/kfe_update_049.sql",
      "tax/tax_update_001.sql",
      "srs/srs_update_01.sql",
      "fops/fops_update_001.sql",
      "taos/taos_update_001.sql",
      "taos/taos_update_003.sql",
      "taos/taos_update_002.sql",
      "kcg/knight_update_001.sql",
      "kcg/knight_update_003.sql",
      "kcg/knight_update_002.sql",
      "um/user_manager_update_671.sql",
      "um/user_manager_update_467.sql",
      "um/user_manager_update_705.sql",
      "um/user_manager_update_089.sql",
      "um/user_manager_update_316.sql",
      "um/user_manager_update_048.sql",
      "um/user_manager_update_706.sql",
      "um/user_manager_update_263.sql",
      "um/user_manager_update_461.sql",
      "um/user_manager_update_298.sql",
      "um/user_manager_update_058.sql",
      "um/user_manager_update_064.sql",
      "um/user_manager_update_113.sql",
      "um/user_manager_update_438.sql",
      "um/user_manager_update_1004.sql",
      "um/user_manager_update_439.sql",
      "um/user_manager_update_200.sql",
      "um/user_manager_update_228.sql",
      "um/user_manager_update_412.sql",
      "um/user_manager_update_946.sql",
      "um/user_manager_update_1002.sql",
      "um/user_manager_update_417.sql",
      "um/user_manager_update_011.sql",
      "um/user_manager_update_010.sql",
      "um/user_manager_update_627.sql",
      "um/user_manager_update_237.sql",
      "um/user_manager_update_009.sql",
      "um/user_manager_update_021.sql",
      "um/user_manager_update_034.sql",
      "um/user_manager_update_008.sql",
      "um/user_manager_update_236.sql",
      "um/user_manager_update_222.sql",
      "um/user_manager_update_544.sql",
      "um/user_manager_update_154.sql",
      "um/user_manager_update_220.sql",
      "um/user_manager_update_023.sql",
      "um/user_manager_update_812.sql",
      "um/user_manager_update_033.sql",
      "um/user_manager_update_226.sql",
      "um/user_manager_update_959.sql",
      "um/user_manager_update_018.sql",
      "um/user_manager_update_255.sql",
      "um/user_manager_update_246.sql",
      "um/user_manager_update_250.sql",
      "pen/penson_update_018.sql",
      "htf/htf_update_010.sql",
      "htf/htf_update_008.sql",
      "ikq/ikq_update_001.sql",
      "link/link_update_001.sql",
      "link/link_update_010.sql",
      "link/link_update_006.sql",
      "link/link_update_311.sql",
      "link/link_update_283.sql",
      "link/link_update_293.sql",
      "link/link_update_044.sql",
      "lend/lend_update_001.sql",
      "flend/flend_update_001.sql",
      "nop/nop_update_001.sql",
      "feed/feed_update_001.sql",
      "link_aurora/link_aurora_update_017.sql",
      "link_aurora/link_aurora_update_001.sql",
      "dm/deployment_manager_update_001.sql",
      "dm/deployment_manager_update_003.sql",
      "blt/blotter_update_001.sql",
      "blt/blotter_update_002.sql",
      "blt/blotter_update_003.sql",
      "nl/notifications_listeners_update_013.sql",
      "worm/worm_update_001.sql",
      "sge/sungard_update_010.sql",
      "bi/broker_integration_update_048.sql",
      "bi/broker_integration_update_262.sql",
      "bi/broker_integration_update_049.sql",
      "bi/broker_integration_update_416.sql",
      "bi/broker_integration_update_615.sql",
      "bi/broker_integration_update_603.sql",
      "bi/broker_integration_update_159.sql",
      "bi/broker_integration_update_038.sql",
      "bi/broker_integration_update_616.sql",
      "bi/broker_integration_update_216.sql",
      "bi/broker_integration_update_175.sql",
      "bi/broker_integration_update_599.sql",
      "bi/broker_integration_update_033.sql",
      "bi/broker_integration_update_597.sql",
      "bi/broker_integration_update_169.sql",
      "bi/broker_integration_update_627.sql",
      "bi/broker_integration_update_223.sql",
      "bi/broker_integration_update_044.sql",
      "bi/broker_integration_update_291.sql",
      "bi/broker_integration_update_534.sql",
      "bi/broker_integration_update_045.sql",
      "bi/broker_integration_update_278.sql",
      "bi/broker_integration_update_456.sql",
      "bi/broker_integration_update_122.sql",
      "bi/broker_integration_update_123.sql",
      "bi/broker_integration_update_492.sql",
      "bi/broker_integration_update_121.sql",
      "report/report_service_update_015.sql",
      "report/report_service_update_016.sql",
      "report/report_service_update_008.sql",
      "ira/ira_update_01.sql",
      "im/master_update_017.sql",
      "im/master_update_007.sql",
      "im/master_update_013.sql",
      "im/master_update_021.sql",
      "im/master_update_035.sql",
      "im/master_update_034.sql",
      "im/master_update_022.sql",
      "im/master_update_033.sql",
      "im/master_update_018.sql",
      "im/master_update_025.sql",
      "im/master_update_019.sql",
      "icgtest/icgtest_update_001.sql",
      "icgtest/icgtest_update_283.sql",
      "or/order_router_update_046.sql",
      "or/order_router_update_033.sql",
      "or/order_router_update_027.sql",
      "or/order_router_update_026.sql",
      "or/order_router_update_032.sql",
      "or/order_router_update_018.sql",
      "or/order_router_update_024.sql",
      "or/order_router_update_031.sql",
      "or/order_router_update_019.sql",
      "or/order_router_update_021.sql",
      "or/order_router_update_009.sql",
      "or/order_router_update_008.sql",
      "or/order_router_update_034.sql",
      "or/order_router_update_020.sql",
      "or/order_router_update_036.sql",
      "or/order_router_update_022.sql",
      "or/order_router_update_023.sql",
      "or/order_router_update_037.sql",
      "or/order_router_update_012.sql",
      "or/order_router_update_006.sql",
      "or/order_router_update_007.sql",
      "or/order_router_update_013.sql",
      "or/order_router_update_005.sql",
      "or/order_router_update_011.sql",
      "or/order_router_update_010.sql",
      "or/order_router_update_004.sql",
      "or/order_router_update_038.sql",
      "or/order_router_update_014.sql",
      "or/order_router_update_015.sql",
      "or/order_router_update_001.sql",
      "or/order_router_update_017.sql",
      "or/order_router_update_003.sql",
      "or/order_router_update_002.sql",
      "or/order_router_update_016.sql",
      "fbank/fbank_update_001.sql",
      "fbank/fbank_update_002.sql",
      "fbank/fbank_update_003.sql",
      "fakex/fakex_update_018.sql",
      "fakex/fakex_update_016.sql",
      "fakex/fakex_update_017.sql",
      "fakex/fakex_update_015.sql",
      "fakex/fakex_update_001.sql",
      "fakex/fakex_update_014.sql",
      "fakex/fakex_update_010.sql",
      "fakex/fakex_update_011.sql",
      "fakex/fakex_update_013.sql",
      "fakex/fakex_update_012.sql",
      "risk/risk_update_001.sql",
      "icg/icg_update_001.sql",
      "icg/icg_update_263.sql",
      "mmr/mmr_update_001.sql"
  );

  private static final List<String> RELATIVE_PATHS_WITH_NONCONFORMING_INSERT = List.of(
      // No field specifiers used
      "fbank/fbank_update_126.sql",
      "bank/bank_update_259.sql",
      "bank/bank_update_270.sql",
      "bank/bank_update_258.sql",
      "bank/bank_update_02.sql",
      "bank/bank_update_298.sql",
      "bank/bank_update_03.sql",
      "bank/bank_update_276.sql",
      "bank/bank_update_472.sql",
      "bank/bank_update_328.sql",
      "bank/bank_update_260.sql",
      "bank/bank_update_366.sql",
      "bank/bank_update_365.sql",
      "bank/bank_update_371.sql",
      "bank/bank_update_364.sql",
      "bank/bank_update_348.sql",
      "bank/bank_update_363.sql",
      "bank/bank_update_362.sql",
      "bank/bank_update_347.sql",
      "bank/bank_update_384.sql",
      "bank/bank_update_350.sql",
      "bank/bank_update_341.sql",
      "bank/bank_update_343.sql",
      "bank/bank_update_330.sql",
      "bank/bank_update_286.sql",
      "bank/bank_update_290.sql",
      "bank/bank_update_284.sql",
      "bank/bank_update_285.sql",
      "bank/bank_update_533.sql",
      "bank/bank_update_269.sql",
      "bank/bank_update_297.sql",
      "bank/bank_update_283.sql",
      "bank/bank_update_268.sql",
      "um/user_manager_update_329.sql",
      "um/user_manager_update_315.sql",
      "um/user_manager_update_301.sql",
      "um/user_manager_update_249.sql",
      "um/user_manager_update_261.sql",
      "um/user_manager_update_275.sql",
      "um/user_manager_update_274.sql",
      "um/user_manager_update_260.sql",
      "um/user_manager_update_248.sql",
      "um/user_manager_update_300.sql",
      "um/user_manager_update_314.sql",
      "um/user_manager_update_328.sql",
      "um/user_manager_update_302.sql",
      "um/user_manager_update_289.sql",
      "um/user_manager_update_538.sql",
      "um/user_manager_update_276.sql",
      "um/user_manager_update_262.sql",
      "um/user_manager_update_1064.sql",
      "um/user_manager_update_277.sql",
      "um/user_manager_update_539.sql",
      "um/user_manager_update_288.sql",
      "um/user_manager_update_317.sql",
      "um/user_manager_update_303.sql",
      "um/user_manager_update_307.sql",
      "um/user_manager_update_313.sql",
      "um/user_manager_update_273.sql",
      "um/user_manager_update_501.sql",
      "um/user_manager_update_267.sql",
      "um/user_manager_update_1060.sql",
      "um/user_manager_update_1061.sql",
      "um/user_manager_update_500.sql",
      "um/user_manager_update_266.sql",
      "um/user_manager_update_272.sql",
      "um/user_manager_update_299.sql",
      "um/user_manager_update_312.sql",
      "um/user_manager_update_306.sql",
      "um/user_manager_update_310.sql",
      "um/user_manager_update_304.sql",
      "um/user_manager_update_338.sql",
      "um/user_manager_update_264.sql",
      "um/user_manager_update_502.sql",
      "um/user_manager_update_270.sql",
      "um/user_manager_update_258.sql",
      "um/user_manager_update_259.sql",
      "um/user_manager_update_271.sql",
      "um/user_manager_update_265.sql",
      "um/user_manager_update_339.sql",
      "um/user_manager_update_305.sql",
      "um/user_manager_update_311.sql",
      "um/user_manager_update_835.sql",
      "um/user_manager_update_389.sql",
      "um/user_manager_update_376.sql",
      "um/user_manager_update_410.sql",
      "um/user_manager_update_404.sql",
      "um/user_manager_update_362.sql",
      "um/user_manager_update_982.sql",
      "um/user_manager_update_983.sql",
      "um/user_manager_update_405.sql",
      "um/user_manager_update_363.sql",
      "um/user_manager_update_377.sql",
      "um/user_manager_update_411.sql",
      "um/user_manager_update_388.sql",
      "um/user_manager_update_836.sql",
      "um/user_manager_update_349.sql",
      "um/user_manager_update_361.sql",
      "um/user_manager_update_407.sql",
      "um/user_manager_update_413.sql",
      "um/user_manager_update_375.sql",
      "um/user_manager_update_981.sql",
      "um/user_manager_update_994.sql",
      "um/user_manager_update_1013.sql",
      "um/user_manager_update_374.sql",
      "um/user_manager_update_360.sql",
      "um/user_manager_update_406.sql",
      "um/user_manager_update_348.sql",
      "um/user_manager_update_837.sql",
      "um/user_manager_update_402.sql",
      "um/user_manager_update_364.sql",
      "um/user_manager_update_370.sql",
      "um/user_manager_update_416.sql",
      "um/user_manager_update_358.sql",
      "um/user_manager_update_984.sql",
      "um/user_manager_update_991.sql",
      "um/user_manager_update_359.sql",
      "um/user_manager_update_371.sql",
      "um/user_manager_update_403.sql",
      "um/user_manager_update_365.sql",
      "um/user_manager_update_398.sql",
      "um/user_manager_update_415.sql",
      "um/user_manager_update_373.sql",
      "um/user_manager_update_367.sql",
      "um/user_manager_update_401.sql",
      "um/user_manager_update_429.sql",
      "um/user_manager_update_575.sql",
      "um/user_manager_update_549.sql",
      "um/user_manager_update_992.sql",
      "um/user_manager_update_548.sql",
      "um/user_manager_update_428.sql",
      "um/user_manager_update_366.sql",
      "um/user_manager_update_400.sql",
      "um/user_manager_update_414.sql",
      "um/user_manager_update_372.sql",
      "um/user_manager_update_399.sql",
      "um/user_manager_update_394.sql",
      "um/user_manager_update_380.sql",
      "um/user_manager_update_419.sql",
      "um/user_manager_update_357.sql",
      "um/user_manager_update_431.sql",
      "um/user_manager_update_425.sql",
      "um/user_manager_update_343.sql",
      "um/user_manager_update_1025.sql",
      "um/user_manager_update_424.sql",
      "um/user_manager_update_342.sql",
      "um/user_manager_update_356.sql",
      "um/user_manager_update_430.sql",
      "um/user_manager_update_418.sql",
      "um/user_manager_update_381.sql",
      "um/user_manager_update_395.sql",
      "um/user_manager_update_383.sql",
      "um/user_manager_update_397.sql",
      "um/user_manager_update_368.sql",
      "um/user_manager_update_340.sql",
      "um/user_manager_update_426.sql",
      "um/user_manager_update_432.sql",
      "um/user_manager_update_354.sql",
      "um/user_manager_update_546.sql",
      "um/user_manager_update_989.sql",
      "um/user_manager_update_433.sql",
      "um/user_manager_update_355.sql",
      "um/user_manager_update_341.sql",
      "um/user_manager_update_427.sql",
      "um/user_manager_update_369.sql",
      "um/user_manager_update_396.sql",
      "um/user_manager_update_382.sql",
      "um/user_manager_update_386.sql",
      "um/user_manager_update_392.sql",
      "um/user_manager_update_423.sql",
      "um/user_manager_update_345.sql",
      "um/user_manager_update_351.sql",
      "um/user_manager_update_437.sql",
      "um/user_manager_update_379.sql",
      "um/user_manager_update_543.sql",
      "um/user_manager_update_1036.sql",
      "um/user_manager_update_378.sql",
      "um/user_manager_update_350.sql",
      "um/user_manager_update_436.sql",
      "um/user_manager_update_422.sql",
      "um/user_manager_update_344.sql",
      "um/user_manager_update_393.sql",
      "um/user_manager_update_387.sql",
      "um/user_manager_update_391.sql",
      "um/user_manager_update_385.sql",
      "um/user_manager_update_434.sql",
      "um/user_manager_update_352.sql",
      "um/user_manager_update_346.sql",
      "um/user_manager_update_420.sql",
      "um/user_manager_update_408.sql",
      "um/user_manager_update_1020.sql",
      "um/user_manager_update_541.sql",
      "um/user_manager_update_409.sql",
      "um/user_manager_update_347.sql",
      "um/user_manager_update_421.sql",
      "um/user_manager_update_435.sql",
      "um/user_manager_update_353.sql",
      "um/user_manager_update_384.sql",
      "um/user_manager_update_390.sql",
      "um/user_manager_update_485.sql",
      "um/user_manager_update_308.sql",
      "um/user_manager_update_334.sql",
      "um/user_manager_update_320.sql",
      "um/user_manager_update_283.sql",
      "um/user_manager_update_297.sql",
      "um/user_manager_update_268.sql",
      "um/user_manager_update_254.sql",
      "um/user_manager_update_1047.sql",
      "um/user_manager_update_269.sql",
      "um/user_manager_update_296.sql",
      "um/user_manager_update_282.sql",
      "um/user_manager_update_321.sql",
      "um/user_manager_update_335.sql",
      "um/user_manager_update_309.sql",
      "um/user_manager_update_653.sql",
      "um/user_manager_update_323.sql",
      "um/user_manager_update_337.sql",
      "um/user_manager_update_294.sql",
      "um/user_manager_update_280.sql",
      "um/user_manager_update_257.sql",
      "um/user_manager_update_256.sql",
      "um/user_manager_update_281.sql",
      "um/user_manager_update_295.sql",
      "um/user_manager_update_336.sql",
      "um/user_manager_update_322.sql",
      "um/user_manager_update_656.sql",
      "um/user_manager_update_326.sql",
      "um/user_manager_update_332.sql",
      "um/user_manager_update_468.sql",
      "um/user_manager_update_291.sql",
      "um/user_manager_update_285.sql",
      "um/user_manager_update_252.sql",
      "um/user_manager_update_247.sql",
      "um/user_manager_update_253.sql",
      "um/user_manager_update_284.sql",
      "um/user_manager_update_290.sql",
      "um/user_manager_update_333.sql",
      "um/user_manager_update_327.sql",
      "um/user_manager_update_657.sql",
      "um/user_manager_update_331.sql",
      "um/user_manager_update_325.sql",
      "um/user_manager_update_319.sql",
      "um/user_manager_update_286.sql",
      "um/user_manager_update_292.sql",
      "um/user_manager_update_251.sql",
      "um/user_manager_update_279.sql",
      "um/user_manager_update_278.sql",
      "um/user_manager_update_293.sql",
      "um/user_manager_update_287.sql",
      "um/user_manager_update_318.sql",
      "um/user_manager_update_442.sql",
      "um/user_manager_update_324.sql",
      "um/user_manager_update_330.sql",
      "pen/penson_update_013.sql",
      "pen/penson_update_020.sql",
      "htf/htf_update_014.sql",
      "htf/htf_update_015.sql",
      "htf/htf_update_016.sql",
      "htf/htf_update_012.sql",
      "htf/htf_update_013.sql",
      "htf/htf_update_011.sql",
      "htf/htf_update_009.sql",
      "htf/htf_update_039.sql",
      "htf/htf_update_040.sql",
      "bi/broker_integration_update_461.sql",
      "bi/broker_integration_update_464.sql",
      "bi/broker_integration_update_349.sql",
      "bi/broker_integration_update_351.sql",
      "bi/broker_integration_update_345.sql",
      "bi/broker_integration_update_620.sql",
      "bi/broker_integration_update_344.sql",
      "bi/broker_integration_update_350.sql",
      "bi/broker_integration_update_346.sql",
      "bi/broker_integration_update_352.sql",
      "bi/broker_integration_update_353.sql",
      "bi/broker_integration_update_369.sql",
      "bi/broker_integration_update_468.sql",
      "bi/broker_integration_update_284.sql",
      "bi/broker_integration_update_469.sql",
      "bi/broker_integration_update_283.sql",
      "bi/broker_integration_update_282.sql",
      "bi/broker_integration_update_710.sql",
      "bi/broker_integration_update_766.sql",
      "bi/broker_integration_update_813.sql",
      "ira/ira_update_06.sql",
      "im/master_update_088.sql",
      "im/master_update_063.sql",
      "im/master_update_089.sql",
      "im/master_update_060.sql",
      "im/master_update_061.sql",
      "im/master_update_059.sql",
      "im/master_update_065.sql",
      "im/master_update_058.sql",
      "im/master_update_056.sql",
      "im/master_update_057.sql",
      "im/master_update_082.sql",
      "im/master_update_055.sql",
      "im/master_update_054.sql",
      "im/master_update_083.sql",
      "im/master_update_087.sql",
      "im/master_update_051.sql",
      "im/master_update_086.sql",
      "im/master_update_084.sql",
      "im/master_update_053.sql",
      "im/master_update_052.sql",
      "im/master_update_085.sql",
      "im/master_update_090.sql",

      // Use of VALUE()
      "pm/portfolio_manager_update_190.sql",
      "pm/portfolio_manager_update_188.sql",
      "pm/portfolio_manager_update_189.sql",
      "qt/market_update_002.sql",
      "qt/market_update_003.sql",
      "qt/market_update_004.sql",
      "pen/penson_update_014.sql",
      "bi/broker_integration_update_298.sql",
      "bi/broker_integration_update_299.sql",
      "bi/broker_integration_update_300.sql",
      "bi/broker_integration_update_289.sql",
      "bi/broker_integration_update_288.sql",
      "bi/broker_integration_update_285.sql",
      "bi/broker_integration_update_290.sql",
      "bi/broker_integration_update_292.sql",
      "bi/broker_integration_update_286.sql",
      "bi/broker_integration_update_287.sql",
      "bi/broker_integration_update_293.sql",
      "bi/broker_integration_update_297.sql",
      "bi/broker_integration_update_296.sql",
      "bi/broker_integration_update_294.sql",
      "bi/broker_integration_update_295.sql",
      "fake/fake_update_003.sql",

      // Wrong table
      "taos/taos_update_004.sql",

      // Mismatched version number
      "pm/portfolio_manager_update_161.sql",
      "um/user_manager_update_472.sql",
      "um/user_manager_update_772.sql",

      // Comments
      "um/user_manager_update_174.sql",
      "um/user_manager_update_179.sql",
      "um/user_manager_update_185.sql",
      "um/user_manager_update_178.sql",
      "um/user_manager_update_194.sql",
      "um/user_manager_update_180.sql",
      "um/user_manager_update_184.sql",
      "um/user_manager_update_168.sql",
      "um/user_manager_update_183.sql",
      "um/user_manager_update_197.sql",
      "um/user_manager_update_181.sql",
      "um/user_manager_update_195.sql",
      "um/user_manager_update_164.sql",
      "um/user_manager_update_170.sql",
      "um/user_manager_update_196.sql",
      "um/user_manager_update_182.sql",
      "um/user_manager_update_169.sql",
      "um/user_manager_update_167.sql",
      "um/user_manager_update_171.sql",
      "um/user_manager_update_165.sql",
      "um/user_manager_update_173.sql",
      "um/user_manager_update_166.sql",
      "um/user_manager_update_172.sql",
      "um/user_manager_update_175.sql",
      "um/user_manager_update_177.sql",
      "um/user_manager_update_188.sql",
      "um/user_manager_update_176.sql",
      "um/user_manager_update_189.sql",
      "um/user_manager_update_190.sql",
      "um/user_manager_update_191.sql",
      "um/user_manager_update_193.sql",
      "um/user_manager_update_187.sql",
      "um/user_manager_update_186.sql",
      "um/user_manager_update_192.sql",

      // Leading zeros used in the SQL
      "link/link_update_075.sql",
      "link/link_update_009.sql",
      "sc/supercruncher_update_023.sql",
      "sc/supercruncher_update_024.sql"
  );

}
