package com.wealthfront.data.prod.sqlschema;

import static com.wealthfront.data.prod.sqlschema.GitReader.ADDED_FILE_FILTER;
import static com.wealthfront.data.prod.sqlschema.GitReader.MODIFIED_FILE_FILTER;
import static org.junit.Assert.assertEquals;

import java.io.File;

import org.junit.BeforeClass;
import org.junit.Test;

import com.google.common.collect.Lists;

public class GitReaderTest {

  private static final LinuxExecutor executor = new LinuxExecutor(new File("target/GitReaderTest"));

  private final GitReader gitReader = new GitReader(executor) {
    @Override
    String getDestinationBranch() {
      return "HEAD~1";
    }
  };

  @BeforeClass
  public static void setup() throws Exception {
    Process process = new ProcessBuilder()
        .redirectOutput(new File("target/GitReaderTest_init.out.txt"))
        .redirectError(new File("target/GitReaderTest_init.err.txt"))
        .command("./src/test/resources/GitReaderTest_init.sh").start();
    process.getInputStream().close();
    process.waitFor();
  }

  @Test
  public void testGetDiffFilesWithFilter_modified() {
    assertEquals(Lists.newArrayList("sql/git_reader_test_modified.sql"),
        gitReader.getDiffFilesWithFilter(MODIFIED_FILE_FILTER));
  }

  @Test
  public void testGetDiffFilesWithFilter_added() {
    assertEquals(Lists.newArrayList("sql/git_reader_test_added.sql"),
        gitReader.getDiffFilesWithFilter(ADDED_FILE_FILTER));
  }

  @Test
  public void testGetModifiedAndAddedSqlFilePaths() {
    assertEquals(Lists.newArrayList("sql/git_reader_test_added.sql", "sql/git_reader_test_modified.sql"),
        gitReader.getModifiedAndAddedSqlFilePaths());
  }

}
