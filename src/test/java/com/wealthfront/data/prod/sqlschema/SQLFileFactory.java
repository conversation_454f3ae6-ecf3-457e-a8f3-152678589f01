package com.wealthfront.data.prod.sqlschema;

public class SQLFileFactory {

  public static PartialSQLFile createSqlFile() {
    return new PartialSQLFile();
  }

  public static class PartialSQLFile {

    private String filePath = "";
    private String fileName = "";
    private String dbName = null;
    private int updateNumber;

    public PartialSQLFile withPath(String filePath) {
      this.filePath = filePath;
      return this;
    }

    public PartialSQLFile withName(String fileName) {
      this.fileName = fileName;
      return this;
    }

    public PartialSQLFile withDbName(String dbName) {
      this.dbName = dbName;
      return this;
    }

    public PartialSQLFile withUpdateNumber(int updateNumber) {
      this.updateNumber = updateNumber;
      return this;
    }

    public SQLFile build() {
      return new SQLFile(filePath, fileName, dbName, updateNumber);
    }

  }

}
