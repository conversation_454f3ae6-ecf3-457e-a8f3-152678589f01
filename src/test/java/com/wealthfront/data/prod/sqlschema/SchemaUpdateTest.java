package com.wealthfront.data.prod.sqlschema;

import static java.util.stream.Collectors.toList;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.antlr.v4.runtime.CharStreams;
import org.antlr.v4.runtime.misc.Interval;
import org.antlr.v4.runtime.tree.ParseTreeWalker;
import org.junit.Test;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.kaching.platform.common.Option;
import com.kaching.platform.common.Strings;
import com.wealthfront.sqlparser.BaseParser;
import com.wealthfront.sqlparser.BaseParserBaseListener;
import com.wealthfront.sqlparser.SqlParser;
import com.wealthfront.sqlparser.SyntaxErrorListener;

public class SchemaUpdateTest extends SQLTestBase {

  private static final List<String> EXCLUDED_FILES =
      ImmutableList.of(
          "sql/git_reader_test_modified.sql",
          "sql/git_reader_test_renamed.sql",
          "sql/bi/broker_integration_update_450.sql",
          "sql/htf/maintenance/20200325_delete_russell_data.sql",
          "sql/icg/icg_update_011.sql",
          "sql/icgtest/icgtest_update_044.sql",
          "sql/icg/maintenance/20170111_populate_edgar_filing_entries_from_icgtest.sql",
          "sql/icg/icg_update_033.sql",
          "sql/icgtest/icgtest_update_065.sql",
          "sql/icg/icg_update_041.sql",
          "sql/icgtest/icgtest_update_073.sql",
          "sql/icg/icg_update_043.sql",
          "sql/icgtest/icgtest_update_075.sql",
          "sql/icg/icg_update_054.sql",
          "sql/icgtest/icgtest_update_086.sql",
          "sql/icg/icg_update_142.sql",
          "sql/icgtest/icgtest_update_168.sql",
          "sql/link/link_update_104.sql",
          "sql/asc/asc_update_019.sql",
          "sql/bank/bank_update_301.sql",
          "sql/bi/broker_integration_update_511.sql",
          "sql/etl/etl_update_07.sql",
          "sql/um/user_manager_update_722.sql",
          "sql/um/user_manager_update_723.sql",
          "sql/um/user_manager_update_724.sql",
          "sql/um/user_manager_update_725.sql",
          "sql/link/link_update_130.sql",
          "sql/um/user_manager_update_734.sql",
          "sql/um/user_manager_update_761.sql",
          "sql/icg/icg_update_205.sql",
          "sql/icg/icg_update_213.sql",
          "sql/icgtest/icgtest_update_234.sql",
          "sql/bi/broker_integration_update_537.sql",
          "sql/fops/fops_update_050.sql",
          "sql/um/user_manager_update_797.sql",
          "sql/link/link_update_170.sql",
          "sql/link/link_update_181.sql",
          "sql/um/user_manager_update_825.sql",
          "sql/im/maintenance/20190213_delete_idc_sourced_cbs_bbp10655.sql",
          "sql/link/link_update_199.sql",
          "sql/link/link_update_200.sql",
          "sql/link/link_update_204.sql",
          "sql/lend/lend_update_001.sql",
          "sql/flend/flend_update_001.sql",
          "sql/um/user_manager_update_852.sql",
          "sql/um/user_manager_update_1044.sql",
          "sql/icg/icg_update_243.sql",
          "sql/tax/maintenance/20200113_drop_changes_not_done_through_flyway.sql",
          "sql/icg/maintenance/20200129_TRAD_296_revert_ira_services_deposit_deletion.sql",
          "sql/fbank/fbank_update_011.sql",
          "sql/fbank/fbank_update_028.sql",
          "sql/um/user_manager_update_900.sql",
          "sql/link/link_update_229.sql",
          "sql/link/link_update_242.sql",
          "sql/link/link_update_243.sql",
          "sql/risk/risk_update_011.sql",
          "sql/fbank/fbank_update_066.sql",
          "sql/bank/bank_update_471.sql",
          "sql/fbank/fbank_update_085.sql",
          "sql/bank/bank_update_490.sql",
          "sql/icg/icg_update_263.sql",
          "sql/icgtest/icgtest_update_283.sql",
          "sql/link/link_update_274.sql",
          "sql/bi/broker_integration_update_576.sql",
          "sql/icg/icg_update_265.sql",
          "sql/icgtest/icgtest_update_285.sql",
          "sql/link/link_update_296.sql",
          "sql/bi/broker_integration_update_594.sql",
          "sql/icg/icg_update_275.sql",
          "sql/icgtest/icgtest_update_294.sql",
          "sql/htf/maintenance/20210719_delete_invalid_wfrpx_quotes.sql",
          "sql/htf/maintenance/20210816_delete_manual_tan_eido_quotes.sql",
          "sql/bi/broker_integration_update_621.sql",
          "sql/bi/broker_integration_update_622.sql",
          "sql/bi/broker_integration_update_623.sql",
          "sql/bi/broker_integration_update_624.sql",
          "sql/bi/broker_integration_update_626.sql",
          "sql/bi/broker_integration_update_686.sql",
          "sql/bi/broker_integration_update_687.sql",
          "sql/bi/broker_integration_update_688.sql",
          "sql/bi/broker_integration_update_690.sql",
          "sql/bi/broker_integration_update_693.sql",
          "sql/bi/broker_integration_update_733.sql",
          "sql/um/maintenance/20090523_restoring_lost_names.sql",
          "sql/bank/bank_update_561.sql",
          "sql/fbank/fbank_update_154.sql",
          "sql/icg/icg_update_291.sql",
          "sql/icgtest/icgtest_update_310.sql",
          "sql/icg/icg_update_292.sql",
          "sql/icgtest/icgtest_update_311.sql",
          "sql/icgtest/icgtest_update_312.sql",
          "sql/icgtest/icgtest_update_314.sql",
          "sql/nop/nop_update_002.sql",
          "sql/blt/maintenance/20221129_populate_cat_firm_designated_id_corrections_code.sql",
          "sql/fbank/fbank_update_271.sql",
          "sql/trace/trace_update_010.sql",
          "sql/icg/icg_update_355.sql",
          "sql/icgtest/icgtest_update_374.sql",
          "sql/trace/trace_update_014.sql",
          "sql/im/master_update_126.sql",
          "sql/mmr/mmr_update_001.sql",
          "sql/lend/lend_update_011.sql",
          "sql/flend/flend_update_011.sql",
          "sql/lend/lend_update_014.sql",
          "sql/flend/flend_update_014.sql"
      );

  private static final List<String> EXCLUDED_DIRECTORIES =
      ImmutableList.of(
          "sql/link_aurora",
          "sql/link_aurora/maintenance",
          "sql/icdb",
          "sql/icdb/maintenance",
          "sql/icdb-icgtest",
          "sql/icdb-icgtest/maintenance"
      );

  private static final List<String> BAD_UPDATE_PATTERNS_ALL_LOWERCASE =
      ImmutableList.of(
          "alter\\s+table\\s+customer_account_transactions"
      );

  private static final String ALTER_TABLE_PATTERN = "alter\\s+table\\s+(\\w+)";

  private static class StatementListener extends BaseParserBaseListener {

    private final List<String> statements = new ArrayList<>();

    @Override
    public void enterQuery(BaseParser.QueryContext ctx) {
      var interval = new Interval(ctx.start.getStartIndex(), ctx.stop.getStopIndex());
      statements.add(ctx.start.getInputStream().getText(interval));
    }

    public List<String> getStatements() {
      return statements;
    }

  }

  private static final String HIGH_AUTO_INCREMENT_PATTERN_ALL_LOWERCASE = "auto_increment\\s*=\\s*[1-9]\\d{7,}";
  private static final String CREATE_TABLE_PATTERN_ALL_LOWERCASE = "create\\s+table[^(]*\\([^)]*id\\s+";
  private static final String FBANK_DB_NAME = "fbank";

  private static final String GET_TABLES_QUERY = "show full tables in %s";
  private static final int TABLE_NAME_INDEX = 1;
  private static final int TABLE_TYPE_INDEX = 2;
  private static final String VIEW_TYPE = "view";

  private final Set<String> schemaAlreadySetUp = Sets.newHashSet();
  private final GitReader gitReader = new GitReader();

  @Test
  public void testAllNewSql() throws Exception {
    List<String> sqlFilePaths = gitReader.getModifiedAndAddedSqlFilePaths();
    List<SQLFile> SQLFiles = Lists.newArrayList();
    for (String path : sqlFilePaths) {
      String directory = path.substring(0, path.lastIndexOf("/"));
      if (!EXCLUDED_FILES.contains(path) && !EXCLUDED_DIRECTORIES.contains(directory)) {
        SQLFiles.add(SQLFile.buildSQLFileFromPath(path));
      }
    }
    Collections.sort(SQLFiles);
    for (SQLFile SQLFile : SQLFiles) {
      String targetDbName = SQLFile.getDbName().getOrElse("");
      if (!(targetDbName.equals("") || schemaAlreadySetUp.contains(targetDbName))) {
        schemaAlreadySetUp.add(targetDbName);
        putUpTables(targetDbName);
      }
      applyFileToTarget(SQLFile, targetDbName);
    }
  }

  private void applyFileToTarget(SQLFile sqlFile, String targetDbName) throws Exception {
    List<String> statements = parseAndVerifyStatements(sqlFile, targetDbName);
    try (Connection connection = connectionManager.getTargetLocalConnection(targetDbName)) {
      assertNoRenameTableStatementsAppliedToViews(statements, targetDbName, connection);
      try (Statement stmt = connection.createStatement()) {
        for (String statement : statements) {
          stmt.execute(statement);
        }
      }
    }
  }

  @VisibleForTesting
  List<String> parseAndVerifyStatements(SQLFile sqlFile, String targetDbName) throws Exception {
    String allFileSql = sqlFile.readAllSql();
    assertNoBadPatterns(allFileSql);
    assertOnlyOneAlterTableStatementPerTable(allFileSql);
    if (FBANK_DB_NAME.equals(targetDbName)) {
      assertHighAutoIncrementForFbank(allFileSql);
    }
    var syntax = new SyntaxErrorListener();
    var parser = new SqlParser.Builder()
        .withCharStream(CharStreams.fromString(allFileSql))
        .withErrorListeners(List.of(syntax))
        .build();
    var tree = parser.root();
    if (!syntax.getSyntaxErrors().isEmpty()) {
      throw new RuntimeException("could not parse " + sqlFile.getFilePath() + ". " + syntax.getSyntaxErrors().stream()
          .map(s -> "line " + s.getLine() + " char " + s.getCharPositionInLine() + ": " + s.getMsg())
          .collect(toList()));
    }
    StatementListener statementListener = new StatementListener();
    ParseTreeWalker.DEFAULT.walk(statementListener, tree);
    return statementListener.getStatements();
  }

  @VisibleForTesting
  static void assertNoBadPatterns(String sqlUpdateContents) {
    for (String regex : BAD_UPDATE_PATTERNS_ALL_LOWERCASE) {
      Pattern pattern = Pattern.compile(regex);
      assertFalse(Strings.format("Schema update matches the dangerous pattern [%s]", regex),
          pattern.matcher(sqlUpdateContents.toLowerCase()).find());
    }
  }

  @VisibleForTesting
  static void assertOnlyOneAlterTableStatementPerTable(String sqlUpdateContents) {
    Pattern pattern = Pattern.compile(ALTER_TABLE_PATTERN, Pattern.CASE_INSENSITIVE);
    Matcher matcher = pattern.matcher(sqlUpdateContents);
    Set<String> tableNamesSeen = new HashSet<>();
    while (matcher.find()) {
      String tableName = matcher.group(1);
      assertFalse(
          Strings.format("There are multiple ALTER TABLE statements on table \"%s\". " +
              "Please put all changes for that table under one ALTER TABLE statement.", tableName),
          tableNamesSeen.contains(tableName));
      tableNamesSeen.add(tableName);
    }
  }

  @VisibleForTesting
  static void assertHighAutoIncrementForFbank(String sqlUpdateContents) {
    Pattern createTablePattern = Pattern.compile(CREATE_TABLE_PATTERN_ALL_LOWERCASE);
    Pattern autoIncrementPattern = Pattern.compile(HIGH_AUTO_INCREMENT_PATTERN_ALL_LOWERCASE);
    String lowerCaseContents = sqlUpdateContents.toLowerCase().replaceAll("\n", "");
    Matcher createTableMatcher = createTablePattern.matcher(lowerCaseContents);
    Matcher autoIncrementMatcher = autoIncrementPattern.matcher(lowerCaseContents);
    int createTablesWithoutSpecifiedAutoIncrement = 0;
    while (createTableMatcher.find()) {
      createTablesWithoutSpecifiedAutoIncrement++;
    }
    while (autoIncrementMatcher.find()) {
      createTablesWithoutSpecifiedAutoIncrement--;
    }
    assertTrue("All new fbank tables with an `id` column should have a high starting value for auto_increment",
        createTablesWithoutSpecifiedAutoIncrement <= 0);
  }

  @VisibleForTesting
  static void assertNoRenameTableStatementsAppliedToViews(
      List<String> statements, String targetDbName, Connection connection) throws Exception {
    Set<String> alViewNames = getViews(statements, targetDbName, connection);
    statements.forEach(stmt -> {
      if (PatternMatcher.isRenameTableStatement(stmt)) {
        Set<String> renamedTableNames = PatternMatcher.getTablesBeingRenamed(stmt);
        Set<String> viewsBeingRenamed = Sets.intersection(renamedTableNames, alViewNames);
        assertTrue(Strings.format("RENAME TABLE statements cannot be executed on views %s", viewsBeingRenamed),
            viewsBeingRenamed.isEmpty());
      }
    });
  }

  @VisibleForTesting
  static Set<String> getViews(List<String> statements, String targetDbName, Connection connection) throws Exception {
    Set<String> views = new HashSet<>();
    ResultSet result = connection.createStatement().executeQuery(Strings.format(GET_TABLES_QUERY, targetDbName));
    while (result.next()) {
      if (isView(result)) {
        views.add(getViewName(result));
      }
    }
    views.addAll(getNewlyCreatedViewsFromStatements(statements));
    return views;
  }

  private static boolean isView(ResultSet result) throws Exception {
    return result.getString(TABLE_TYPE_INDEX).toLowerCase().contains(VIEW_TYPE);
  }

  private static String getViewName(ResultSet result) throws Exception {
    return result.getString(TABLE_NAME_INDEX).toLowerCase();
  }

  @VisibleForTesting
  static Set<String> getNewlyCreatedViewsFromStatements(List<String> statements) {
    Set<String> newlyCreatedViews = new HashSet<>();
    statements.forEach(stmt -> PatternMatcher.getViewNameBeingCreated(stmt).ifDefined(newlyCreatedViews::add));
    return newlyCreatedViews;
  }

  @VisibleForTesting
  static final class PatternMatcher {

    private static final Pattern RENAME_TABLE_PATTERN_ALL_LOWERCASE =
        Pattern.compile("rename\\s+table");
    private static final Pattern TABLE_NAME_CHANGE_PATTERN_ALL_LOWERCASE =
        Pattern.compile("([^\\s,;]+)\\s+to\\s+[^\\s,;]+");
    private static final int TABLE_BEING_RENAMED_CAPTURE_INDEX = 1;
    private static final Pattern CREATE_VIEW_PATTERN_ALL_LOWERCASE =
        Pattern.compile("create\\s+view\\s+([^\\s;]+)");
    private static final int VIEW_NAME_CAPTURE_INDEX = 1;

    public static boolean isRenameTableStatement(String statement) {
      return RENAME_TABLE_PATTERN_ALL_LOWERCASE.matcher(getStatementForMatching(statement)).find();
    }

    public static Set<String> getTablesBeingRenamed(String statement) {
      Matcher matcher = TABLE_NAME_CHANGE_PATTERN_ALL_LOWERCASE.matcher(getStatementForMatching(statement));
      Set<String> tablesBeingRenamed = new HashSet<>();
      while (matcher.find()) {
        tablesBeingRenamed.add(cleanTableName(matcher.group(TABLE_BEING_RENAMED_CAPTURE_INDEX)));
      }
      return tablesBeingRenamed;
    }

    public static Option<String> getViewNameBeingCreated(String statement) {
      Matcher matcher = CREATE_VIEW_PATTERN_ALL_LOWERCASE.matcher(getStatementForMatching(statement));
      if (!matcher.find()) {
        return Option.none();
      }
      return Option.some(cleanTableName(matcher.group(VIEW_NAME_CAPTURE_INDEX)));
    }

    @VisibleForTesting
    static String getStatementForMatching(String rawStatement) {
      return rawStatement.replaceAll("\n", " ").toLowerCase();
    }

    @VisibleForTesting
    static String cleanTableName(String rawTableName) {
      String removeBackticks = rawTableName.replaceAll("`", "");
      return removeBackticks.substring(removeBackticks.indexOf(".") + 1);
    }

  }

}
