package com.wealthfront.data.prod.sqlschema;

import static com.google.inject.Guice.createInjector;
import static com.kaching.platform.common.logging.Log.getLog;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.List;
import java.util.Set;
import java.util.regex.Pattern;

import org.junit.AfterClass;
import org.junit.BeforeClass;

import com.google.common.base.Throwables;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.inject.Injector;
import com.kaching.platform.common.Strings;
import com.kaching.platform.common.logging.Log;
import com.wealthfront.avro.dbmetadata.CreateTable;
import com.wealthfront.dbmetadata.fetcher.CreateTableRetriever;
import com.wealthfront.dbmetadata.fetcher.DbMetadataFetcherModule;

public class SQLTestBase {

  private static final Log LOG = getLog(SQLTestBase.class);

  private static final Pattern ALGORITHM = Pattern.compile(".*CREATE ALGORITHM.*");
  private static final Set<String> MYSQL_REQUIRED_DBS =
      ImmutableSet.of("information_schema", "performance_schema", "mysql");
  private static final Set<String> EXCLUDED_TABLES = ImmutableSet.of("lock_logs");

  protected static final Injector injector = createInjector(new SQLModule());
  protected static final Injector metadataInjector = createInjector(new DbMetadataFetcherModule());
  protected static final LocalConnectionManager connectionManager = new LocalConnectionManager();

  @BeforeClass
  public static void initLocalDbs() throws Exception {
    try (Connection connection = connectionManager.getLocalConnection()) {
      try (Statement stmt = connection.createStatement()) {
        for (LocalConnectionManager.ConnectionInfo connInfo : LocalConnectionManager.ConnectionInfo.values()) {
          LOG.info("creating " + connInfo.getDb());
          stmt.execute(Strings.format("CREATE DATABASE %s;", connInfo.getDb()));
        }
      }
    }
  }

  @AfterClass
  public static void destroyLocalDbs() {
    try (Connection connection = connectionManager.getLocalConnection()) {
      try (ResultSet dbs = connection.getMetaData().getCatalogs()) {
        while (dbs.next()) {
          final String dbName = dbs.getString("TABLE_CAT");
          if (MYSQL_REQUIRED_DBS.contains(dbName)) {
            continue;
          }
          LOG.info("dropping " + dbName);
          try (Statement stmt = connection.createStatement()) {
            stmt.execute(Strings.format("DROP DATABASE %s;", dbName));
          }
        }
      }
    } catch (SQLException exception) {
      throw Throwables.propagate(exception);
    }
  }

  protected void putUpTables(String targetDbName) throws SQLException {
    CreateTableRetriever retriever = metadataInjector.getInstance(CreateTableRetriever.class);
    List<CreateTable> createTables = retriever.getRecentAvroRecordsForDb(targetDbName);
    List<String> algorithms = Lists.newArrayList();
    try (Connection connection = connectionManager.getTargetLocalConnection(targetDbName)) {
      try (Statement stmt = connection.createStatement()) {
        stmt.execute("SET FOREIGN_KEY_CHECKS=0;");
        for (CreateTable createTable : createTables) {
          String createTableStatement = createTable.getCreateTable().toString();
          boolean skipThisTable = false;
          for (String tableName : EXCLUDED_TABLES) {
            if (createTableStatement.contains(Strings.format("CREATE TABLE `%s`", tableName))) {
              skipThisTable = true;
            }
          }
          if (skipThisTable) {
            continue;
          }
          if (ALGORITHM.matcher(createTableStatement.toUpperCase()).matches()) {
            algorithms.add(createTableStatement);
          } else {
            stmt.execute(createTable.getCreateTable().toString());
          }
        }
        for (String algorithm : algorithms) {
          if (EXCLUDED_TABLES.stream().anyMatch(table -> algorithm.contains(Strings.format("`%s`", table)))) {
           continue;
          }
          stmt.execute(algorithm);
        }
        stmt.execute("SET FOREIGN_KEY_CHECKS=1;");
      }
    }
  }

}
