package com.wealthfront.data.prod.sqlschema;

import static com.wealthfront.data.prod.sqlschema.SQLFile.buildSQLFileFromPath;
import static com.wealthfront.data.prod.sqlschema.SQLFile.getDbNameFromPath;
import static com.wealthfront.data.prod.sqlschema.SQLFile.getFileNameFromPath;
import static com.wealthfront.data.prod.sqlschema.SQLFile.getUpdateNumberFromFileName;
import static com.wealthfront.data.prod.sqlschema.SQLFile.tryToMatchFolderWithService;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

import org.junit.Test;

public class SQLFileTest {

  @Test
  public void testCompareTo() {
    SQLFile testFile1 = buildSQLFileFromPath("sql/test_update_00.sql");
    SQLFile testFile2 = buildSQLFileFromPath("sql/test_update_01.sql");
    SQLFile testFile3 = buildSQLFileFromPath("sql/test.sql");
    SQLFile testFile4 = buildSQLFileFromPath("sql/test2.sql");
    assertEquals(-1, testFile1.compareTo(testFile2));
    assertEquals(1, testFile2.compareTo(testFile1));
    assertEquals(-1, testFile3.compareTo(testFile1));
    assertEquals(1, testFile1.compareTo(testFile3));
    assertEquals(0, testFile3.compareTo(testFile4));
  }

  @Test
  public void testGetFileNameFromPath() {
    assertEquals("test.sql", getFileNameFromPath("sql/test.sql"));
    assertEquals("test_update_1.sql", getFileNameFromPath("sql/long/path/to/file/test_update_1.sql"));
  }

  @Test
  public void testGetDbNameFromPath() {
    assertTrue(getDbNameFromPath("sql/useless/package/names/useless_file_name.sql").isEmpty());
    assertEquals("ale", getDbNameFromPath("sql/ale/package/useless_file_name.sql").getOrThrow());
    assertEquals("master", getDbNameFromPath("sql/im/package/useless.sql").getOrThrow());
    assertEquals("penson", getDbNameFromPath("sql/pen/package/user_manager_update.sql").getOrThrow());
    assertEquals("esp",
        getDbNameFromPath("sql/esp/maintenance/20151221_delete_null_category_mobile_events.sql").getOrThrow());
  }

  @Test
  public void testTryToMatchFolderWithService() {
    assertEquals("user", tryToMatchFolderWithService("um").getOrThrow());
    assertEquals("master", tryToMatchFolderWithService("im").getOrThrow());
    assertEquals("supercruncher", tryToMatchFolderWithService("sc").getOrThrow());
    assertTrue(tryToMatchFolderWithService("fsx_migration").isEmpty());
    assertTrue(tryToMatchFolderWithService("mh").isEmpty());
  }

  @Test
  public void testGetUpdateNumberFromFileName() {
    assertEquals(Integer.valueOf(23), getUpdateNumberFromFileName("test_update_23.sql").getOrThrow());
    assertEquals(Integer.valueOf(0), getUpdateNumberFromFileName("user_manager_update_00.sql").getOrThrow());
    assertTrue(getUpdateNumberFromFileName("test_42.sql").isEmpty());
    assertTrue(getUpdateNumberFromFileName("test_overhaul_update.sql").isEmpty());
  }

}