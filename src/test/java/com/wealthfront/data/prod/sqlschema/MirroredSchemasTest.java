package com.wealthfront.data.prod.sqlschema;

import static org.junit.Assert.fail;

import java.util.Collections;
import java.util.List;
import java.util.Set;

import org.junit.Test;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;

public class MirroredSchemasTest {

  private final GitReader gitReader = new GitReader();

  @Test
  public void testAllNewSql() throws Exception {
    List<SQLFile> sqlFiles = getSqlFiles();
    ImmutableSet.Builder<String> targetDbNames = ImmutableSet.builder();
    for (SQLFile sqlFile : sqlFiles) {
      String targetDbName = sqlFile.getDbName().getOrElse("");
      if (sqlFile.isNumberedUpdate() && !sqlFile.readAllSql().contains("skip mirrored schemas test")) {
        targetDbNames.add(targetDbName);
      }
    }
    Set<String> allTargetDbNames = targetDbNames.build();
    assertSqlAppliedToBothOrNeither("bank", "fbank", allTargetDbNames);
    assertSqlAppliedToBothOrNeither("icg", "inteliclear", allTargetDbNames);
  }

  @VisibleForTesting
  List<SQLFile> getSqlFiles() {
    List<String> sqlFilePaths = gitReader.getModifiedAndAddedSqlFilePaths();
    List<SQLFile> sqlFiles = Lists.newArrayList();
    for (String path : sqlFilePaths) {
      sqlFiles.add(SQLFile.buildSQLFileFromPath(path));
    }
    Collections.sort(sqlFiles);
    return sqlFiles;
  }

  @VisibleForTesting
  static void assertSqlAppliedToBothOrNeither(String db1, String db2, Set<String> databaseNames) {
    if (databaseNames.contains(db1) ^ databaseNames.contains(db2)) {
      fail("Schema changes to " + db1 + " and " + db2 + " must be mirrored. Found changes for " + databaseNames
          + ". Add 'skip mirrored schemas test' anywhere in the .sql file to bypass this check.");
    }
  }

}
