---
description: 
globs: 
alwaysApply: true
---
# Cursor Rule: Add SQL Index(es)

**Goal:** Create a new SQL schema update file to add one or more simple or compound indexes to a specified table in a given sub-directory within `sql/`, incrementing the schema version number for that specific sub-directory. Each `ADD INDEX` clause will be on a new line and indented.

**Inputs needed:**

1.  `{{SUB_DIRECTORY}}`: The target sub-directory name within `sql/` (e.g., `bank`, `fbank`, `reporting`). The rule will place the file in `sql/{{SUB_DIRECTORY}}`.
2.  `{{TABLE_NAME}}`: The name of the table to add the index(es) to.
3.  `{{INDEX_COLUMNS_LIST}}`: A list of columns to index.
    *   Use **one line per index**.
    *   For a **single-column index**, just list the column name (e.g., `email`).
    *   For a **compound index**, list the column names separated by commas (e.g., `first_name,last_name`).

    **Example Input for `{{INDEX_COLUMNS_LIST}}`:**
    ```
    email
    first_name,last_name
    created_at
    ```

**AI Steps:**

1.  Construct the full target directory path: `DIRECTORY = sql/{{SUB_DIRECTORY}}`.
2.  Use the `list_dir` tool on the constructed `DIRECTORY`.
3.  Filter the results to find files matching the pattern `{{SUB_DIRECTORY}}_update_*.sql` within that directory.
4.  From the matching file list, parse the numbers (`xxx`) from the filenames and find the highest existing number. If no matching files exist, assume the highest number is 0.
5.  Calculate the next schema number: `new_number = highest_number + 1`.
6.  The filename prefix is determined by `{{SUB_DIRECTORY}}`.
7.  Construct the new filename: `{{DIRECTORY}}/{{SUB_DIRECTORY}}_update_[new_number].sql`.
8.  Parse the `{{INDEX_COLUMNS_LIST}}` input:
    *   Split the input string by newlines (`\n`) to get each index definition. Ignore empty lines.
    *   For each definition, split by comma (`,`) to get the column names for that index. Trim whitespace from each column name.
9.  Construct the `ALTER TABLE` statement:
    *   Start with `ALTER TABLE {{TABLE_NAME}}`.
    *   Initialize an empty list for `ADD INDEX` clauses.
    *   For each parsed index definition (list of column names):
        *   Generate the index name: `{{TABLE_NAME}}_` followed by the column names (trimmed) joined by `_`.
        *   Generate the column list string: `(` followed by comma-separated column names, followed by `)`.
        *   Create the clause: `ADD INDEX [index_name] [column_list_string]`
        *   Add this clause to the list.
    *   **Join the clauses:**
        *   Take all clauses *except* the last one and join them with `,\n  `.
        *   Take the *last* clause and append `;` directly to it.
        *   Combine these: `[Joined clauses except last]` + `,\n  ` + `[Last clause];` (Handle the case of only one clause separately, just appending `;`).
    *   Prepend the combined clauses string with a newline and two spaces: `\n  `.
    *   Combine everything: `ALTER TABLE {{TABLE_NAME}}` + combined clauses string.
    *   Example result for multiple indexes:
        ```sql
        ALTER TABLE my_table
          ADD INDEX my_table_col1 (col1),
          ADD INDEX my_table_col2_col3 (col2, col3);
        ```
    *   Example result for a single index:
        ```sql
        ALTER TABLE my_table
          ADD INDEX my_table_col1 (col1);
        ```
10. Construct the `INSERT INTO schema_log` statement:
    `INSERT INTO schema_log (schema_version, date) VALUES ([new_number], NOW());`
11. Use the `edit_file` tool to create the new file (`{{DIRECTORY}}/{{SUB_DIRECTORY}}_update_[new_number].sql`) with the following content (ensure a blank line between the two statements):
    ```sql
    [Constructed ALTER TABLE statement]

    [Constructed INSERT INTO schema_log statement]
    ```

**Example Usage:**

If you invoke this rule with:
*   `{{SUB_DIRECTORY}}`: `reporting`
*   `{{TABLE_NAME}}`: `daily_stats`
*   `{{INDEX_COLUMNS_LIST}}`:
    ```
    report_date
    metric_name, report_date
    ```

And the highest current schema number found in files named `sql/reporting/reporting_update_*.sql` is 42, the AI should create the file `sql/reporting/reporting_update_43.sql` with the content:

```sql
ALTER TABLE daily_stats
  ADD INDEX daily_stats_report_date (report_date),
  ADD INDEX daily_stats_metric_name_report_date (metric_name, report_date);

INSERT INTO schema_log (schema_version, date) VALUES (43, NOW());
```
```
