# General
If you submit a schema change to this directory, it is your responsibility to
make this change 100% backwards compatible. This means that we could
theoretically roll out this update to all production databases immediately
without affecting the current running software.

This means all changes in a PR. All testing and automation is based around
PRs, not the schema log. If you need to make multiple changes, make multiple
PRs.

If you believe that your schema can't be rolled out now, then you should
probably rewrite it so that it can. Schema changes *must* be compatible with
older binaries.

Every schema update MUST insert a row into the schema_log table, e.g.
```
insert into schema_log (schema_version, date) values (4, now());
```
If the update is the first one to happen on that database, you must include the
schema_log creation code, i.e.
```
create table schema_log (
    schema_version int not null default 0,
    date datetime not null,
    unique(schema_version)
) engine = InnoDB;
```
Every table creation MUST specify the engine as InnoDB. This is necessary to get
transactional semantics.

Slow queries (alters and updates) can use inline comments to declare that they
will be long running and thus avoid emergency page. All comments must be commented
in SQL (#) not in Java (// or /* */)
```
#!99999 long_running
```
# Process to update a production database
https://wiki.wlth.fr/display/EDU/Online+Database+Schema+Change+Procedure

# Building/testing this repo locally
Make sure docker is installed: https://wiki.wlth.fr/pages/viewpage.action?pageId=107898030

Start mariadb container using this command
```
docker run --name sql-schema --rm -p 3306:3306 \
-e MYSQL_ROOT_PASSWORD=root \
-e MYSQL_USER=test_user \
-e MYSQL_PASSWORD=test \
mariadb:10.5
```
Then you can run tests using Intellij or `mvn test`

